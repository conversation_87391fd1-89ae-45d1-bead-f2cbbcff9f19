import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../shared/widgets/responsive_layout.dart';

class TechnicianPerformanceReportScreen extends StatefulWidget {
  const TechnicianPerformanceReportScreen({super.key});

  @override
  State<TechnicianPerformanceReportScreen> createState() => _TechnicianPerformanceReportScreenState();
}

class _TechnicianPerformanceReportScreenState extends State<TechnicianPerformanceReportScreen> {
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  
  bool _isLoading = false;
  List<Map<String, dynamic>> _performanceData = [];

  @override
  void initState() {
    super.initState();
    _generateReport();
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get all technicians
      final employees = await _employeeRepository.getAllEmployees();
      final technicians = employees.where((emp) => 
        emp.status.toString().split('.').last == 'active' && (
          emp.position.toLowerCase().contains('فني') ||
          emp.position.toLowerCase().contains('technician') ||
          emp.position.toLowerCase().contains('maintenance') ||
          emp.position.toLowerCase().contains('صيانة')
        )
      ).toList();

      List<Map<String, dynamic>> performanceList = [];

      for (final technician in technicians) {
        // Get expenses for this technician
        final expensesData = await _serviceRequestRepository.getTechnicianExpenses(
          startDate: _startDate,
          endDate: _endDate,
          technicianId: technician.id,
        );

        final totalExpenses = expensesData['total_expenses'] as double;
        final totalServices = expensesData['total_services'] as int;
        final averageCostPerService = totalServices > 0 ? totalExpenses / totalServices : 0.0;

        // Calculate efficiency metrics
        final daysInPeriod = _endDate.difference(_startDate).inDays + 1;
        final servicesPerDay = totalServices / daysInPeriod;
        final costPerDay = totalExpenses / daysInPeriod;

        performanceList.add({
          'technician_id': technician.id,
          'technician_name': technician.name,
          'technician_position': technician.position,
          'payment_type': technician.paymentType.toString().split('.').last,
          'base_salary': technician.salary,
          'total_expenses': totalExpenses,
          'total_services': totalServices,
          'average_cost_per_service': averageCostPerService,
          'services_per_day': servicesPerDay,
          'cost_per_day': costPerDay,
          'efficiency_score': _calculateEfficiencyScore(totalServices, totalExpenses, daysInPeriod),
        });
      }

      // Sort by efficiency score
      performanceList.sort((a, b) => (b['efficiency_score'] as double).compareTo(a['efficiency_score'] as double));

      setState(() {
        _performanceData = performanceList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  double _calculateEfficiencyScore(int totalServices, double totalExpenses, int daysInPeriod) {
    if (totalServices == 0) return 0.0;
    
    // Efficiency = Services completed / (Cost per service * Days)
    // Higher services with lower cost = higher efficiency
    final servicesPerDay = totalServices / daysInPeriod;
    final costPerService = totalExpenses / totalServices;
    
    // Normalize the score (higher is better)
    return (servicesPerDay * 100) / (costPerService + 1);
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _generateReport();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير أداء الفنيين'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateReport,
          ),
        ],
      ),
      body: ResponsiveContainer(
        child: Column(
          children: [
            // Date filter
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(Icons.date_range, color: AppColors.primary),
                    const SizedBox(width: 8),
                    const Text('فترة التقرير:', style: AppTextStyles.bodyMedium),
                    const SizedBox(width: 16),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _selectDateRange,
                        child: Text(
                          '${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Performance list
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _performanceData.isEmpty
                      ? const Center(
                          child: Text(
                            'لا توجد بيانات أداء للعرض',
                            style: AppTextStyles.bodyLarge,
                          ),
                        )
                      : _buildPerformanceList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _performanceData.length,
      itemBuilder: (context, index) {
        final data = _performanceData[index];
        final efficiencyScore = data['efficiency_score'] as double;
        final rank = index + 1;
        
        Color rankColor = Colors.grey;
        IconData rankIcon = Icons.person;
        
        if (rank == 1) {
          rankColor = Colors.amber;
          rankIcon = Icons.emoji_events;
        } else if (rank == 2) {
          rankColor = Colors.grey[400]!;
          rankIcon = Icons.emoji_events;
        } else if (rank == 3) {
          rankColor = Colors.brown;
          rankIcon = Icons.emoji_events;
        }

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: rankColor,
              child: Icon(rankIcon, color: Colors.white),
            ),
            title: Text(
              data['technician_name'] as String,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '${data['technician_position']} - المرتبة #$rank',
              style: TextStyle(color: Colors.grey[600]),
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'نقاط الكفاءة',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  efficiencyScore.toStringAsFixed(1),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getEfficiencyColor(efficiencyScore),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildMetricRow('إجمالي الخدمات', '${data['total_services']} خدمة'),
                    _buildMetricRow('إجمالي التكلفة', '${(data['total_expenses'] as double).toStringAsFixed(2)} ر.س'),
                    _buildMetricRow('متوسط التكلفة لكل خدمة', '${(data['average_cost_per_service'] as double).toStringAsFixed(2)} ر.س'),
                    _buildMetricRow('الخدمات يومياً', '${(data['services_per_day'] as double).toStringAsFixed(1)} خدمة'),
                    _buildMetricRow('التكلفة يومياً', '${(data['cost_per_day'] as double).toStringAsFixed(2)} ر.س'),
                    _buildMetricRow('نوع الدفع', data['payment_type'] == 'daily' ? 'أجر يومي' : 'راتب شهري'),
                    if (data['payment_type'] == 'daily')
                      _buildMetricRow('الأجر الأساسي', '${(data['base_salary'] as double).toStringAsFixed(0)} ر.س/يوم'),
                    if (data['payment_type'] == 'monthly')
                      _buildMetricRow('الراتب الشهري', '${(data['base_salary'] as double).toStringAsFixed(0)} ر.س/شهر'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: Colors.grey[600]),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Color _getEfficiencyColor(double score) {
    if (score >= 50) return Colors.green;
    if (score >= 25) return Colors.orange;
    return Colors.red;
  }
}
