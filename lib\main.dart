import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'app.dart';
import 'config/constants.dart';
import 'core/database/database_helper.dart';
import 'core/database/migrations/add_missing_columns.dart';
import 'core/services/notification_service.dart';
import 'core/services/notification_manager.dart';
import 'core/utils/firebase_utils.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database first (doesn't require Firebase)
  await _initializeDatabaseInBackground();

  // Apply database migrations
  await _applyDatabaseMigrations();

  // Initialize Firebase with enhanced error handling
  if (kDebugMode) {
    print('🔥 بدء تهيئة Firebase...');
  }

  final firebaseInitialized = await FirebaseUtils.initializeFirebase();

  if (kDebugMode) {
    FirebaseUtils.logStatus();
    if (firebaseInitialized) {
      print('✅ تم تهيئة Firebase بنجاح');
      print('📊 معرف المشروع: fir-db-40fbe');
      print('🔑 مفتاح API محدث ومطابق للمشروع');
    } else {
      print('⚠️ فشل في تهيئة Firebase');
      print('📱 سيتم الاستمرار بدون خدمات Firebase');
    }
  }

  // Initialize notification services only after Firebase is ready
  await _initializeNotificationServices(firebaseInitialized);

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: AppColors.surface,
      systemNavigationBarIconBrightness: Brightness.dark,
      systemNavigationBarDividerColor: Colors.transparent,
    ),
  );

  // Start the app after all critical services are initialized
  runApp(const App());
}

/// Initialize the database in the background to avoid startup delay
Future<void> _initializeDatabaseInBackground() async {
  try {
    // Get the database path (this will initialize the database connection)
    final dbPath = await DatabaseHelper().getDatabasePath();

    if (kDebugMode) {
      print('Database initialized at path: $dbPath');
    }

    // Ensure admin user exists (this will be done in the background)
    DatabaseHelper().ensureAdminUserExists().then((_) {
      if (kDebugMode) {
        print('Admin user check completed');
      }
    });
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing database: $e');
    }
  }
}

/// Initialize notification services
Future<void> _initializeNotificationServices(bool firebaseInitialized) async {
  try {
    // تهيئة خدمة الإشعارات
    final notificationService = NotificationService();
    await notificationService.init();

    // تهيئة مدير الإشعارات
    final notificationManager = NotificationManager();
    await notificationManager.init();

    // التحقق من المواعيد القادمة وجدولة إشعارات لها (فقط إذا كان Firebase متاحاً)
    if (firebaseInitialized && FirebaseUtils.isInitialized) {
      try {
        await notificationManager.checkUpcomingServiceRequests();
        if (kDebugMode) {
          print('✅ تم التحقق من المواعيد القادمة بنجاح');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في التحقق من المواعيد القادمة: $e');
        }
      }
    } else {
      if (kDebugMode) {
        print('⚠️ تم تخطي التحقق من المواعيد - Firebase غير متاح');
      }
    }

    if (kDebugMode) {
      print('Notification services initialized successfully');

      // إرسال إشعار تجريبي للتأكد من عمل الإشعارات
      await notificationService.showNotification(
        id: 0,
        title: 'مرحبًا بك في تطبيق ركن الجليد',
        body: 'تم تفعيل الإشعارات بنجاح',
        payload: 'test_notification',
      );
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing notification services: $e');
    }
  }
}

/// تطبيق تحديثات قاعدة البيانات
Future<void> _applyDatabaseMigrations() async {
  try {
    if (kDebugMode) {
      print('تطبيق تحديثات قاعدة البيانات...');
    }

    final dbHelper = DatabaseHelper();
    final db = await dbHelper.database;

    // تطبيق التحديثات المطلوبة
    await AddMissingColumns.migrate(db);

    if (kDebugMode) {
      print('✅ تم تطبيق تحديثات قاعدة البيانات بنجاح');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ خطأ في تطبيق تحديثات قاعدة البيانات: $e');
    }
  }
}
