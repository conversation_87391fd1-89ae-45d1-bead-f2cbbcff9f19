import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:share_plus/share_plus.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import '../../../config/constants.dart';

class PdfPreviewScreen extends StatefulWidget {
  final String filePath;
  final String title;

  const PdfPreviewScreen({
    super.key,
    required this.filePath,
    required this.title,
  });

  @override
  State<PdfPreviewScreen> createState() => _PdfPreviewScreenState();
}

class _PdfPreviewScreenState extends State<PdfPreviewScreen> {
  int _totalPages = 0;
  int _currentPage = 0;
  bool _isLoading = true;
  PDFViewController? _pdfViewController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _sharePdf,
            tooltip: 'مشاركة',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printPdf,
            tooltip: 'طباعة',
          ),
        ],
      ),
      body: Stack(
        children: [
          PDFView(
            filePath: widget.filePath,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageFling: true,
            pageSnap: true,
            defaultPage: _currentPage,
            fitPolicy: FitPolicy.BOTH,
            preventLinkNavigation: false,
            onRender: (pages) {
              setState(() {
                _totalPages = pages!;
                _isLoading = false;
              });
            },
            onError: (error) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('حدث خطأ: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            onPageError: (page, error) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('حدث خطأ في الصفحة $page: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            onViewCreated: (PDFViewController pdfViewController) {
              setState(() {
                _pdfViewController = pdfViewController;
              });
            },
            onPageChanged: (int? page, int? total) {
              if (page != null) {
                setState(() {
                  _currentPage = page;
                });
              }
            },
          ),
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : const SizedBox.shrink(),
        ],
      ),
      floatingActionButton: _buildNavigationButtons(),
      bottomNavigationBar: _buildPageIndicator(),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (_totalPages > 1) ...[
          FloatingActionButton(
            heroTag: 'prev',
            mini: true,
            backgroundColor: AppColors.primary,
            onPressed: _currentPage > 0
                ? () {
                    _pdfViewController?.setPage(_currentPage - 1);
                  }
                : null,
            child: const Icon(Icons.arrow_upward),
          ),
          const SizedBox(width: 16),
          FloatingActionButton(
            heroTag: 'next',
            mini: true,
            backgroundColor: AppColors.primary,
            onPressed: _currentPage < _totalPages - 1
                ? () {
                    _pdfViewController?.setPage(_currentPage + 1);
                  }
                : null,
            child: const Icon(Icons.arrow_downward),
          ),
        ],
      ],
    );
  }

  Widget _buildPageIndicator() {
    return Container(
      height: 50,
      color: Colors.grey[200],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'الصفحة ${_currentPage + 1} من $_totalPages',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _sharePdf() async {
    try {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تحضير المشاركة...'),
        ),
      );

      final file = File(widget.filePath);
      if (!await file.exists()) {
        throw Exception('ملف PDF غير موجود');
      }

      final xFile = XFile(file.path);
      await Share.shareXFiles(
        [xFile],
        text: 'مشاركة ${widget.title}',
        subject: widget.title,
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء المشاركة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _printPdf() async {
    try {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تحضير الطباعة...'),
        ),
      );

      // Get the PDF file
      final file = File(widget.filePath);
      if (!await file.exists()) {
        throw Exception('ملف PDF غير موجود');
      }

      // Read the PDF file as bytes
      final pdfBytes = await file.readAsBytes();

      // Log file information
      debugPrint('PDF file exists: ${await file.exists()}');
      debugPrint('PDF file size: ${await file.length()} bytes');
      debugPrint('PDF file path: ${file.path}');

      // Print the PDF with more options
      final result = await Printing.layoutPdf(
        onLayout: (_) => pdfBytes,
        name: widget.title,
        format: PdfPageFormat.a4,
        dynamicLayout: true,
        usePrinterSettings: true,
      );

      if (!mounted) return;

      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال الملف للطباعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء الطباعة'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Error printing PDF: $e');
      debugPrint('Stack trace: $stackTrace');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء الطباعة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
