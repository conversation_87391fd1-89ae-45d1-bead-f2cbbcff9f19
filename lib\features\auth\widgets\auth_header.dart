import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_logo.dart';

class AuthHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? logoPath;

  const AuthHeader({
    super.key,
    required this.title,
    required this.subtitle,
    this.logoPath,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Logo
        if (logoPath != null)
          Padding(
            padding: const EdgeInsets.only(bottom: AppDimensions.paddingL),
            child: Image.asset(
              logoPath!,
              height: 80,
              width: 80,
            ),
          )
        else
          const AppLogo(
            size: 100, // زيادة حجم الشعار
            showText: true, // إظهار النص مع الشعار
            color: Colors.white, // لون الشعار أبيض
            backgroundColor: AppColors.primary, // لون الخلفية أزرق
          ),
        const SizedBox(height: AppDimensions.paddingM),

        // Title
        Text(
          title,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.paddingS),

        // Subtitle
        Text(
          subtitle,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
