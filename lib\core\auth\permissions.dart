/// Comprehensive permission system for HVAC Service Manager
enum Permission {
  // User Management
  viewUsers,
  createUsers,
  editUsers,
  deleteUsers,
  manageUserRoles,
  
  // Customer Management
  viewCustomers,
  createCustomers,
  editCustomers,
  deleteCustomers,
  exportCustomers,
  
  // Employee Management
  viewEmployees,
  createEmployees,
  editEmployees,
  deleteEmployees,
  manageEmployeePayroll,
  viewEmployeeSalaries,
  
  // Service Management
  viewServices,
  createServices,
  editServices,
  deleteServices,
  assignTechnicians,
  completeServices,
  cancelServices,
  
  // Financial Management
  viewFinancials,
  manageCashBoxes,
  createTransactions,
  editTransactions,
  deleteTransactions,
  viewReports,
  exportReports,
  manageOpeningBalances,
  
  // Inventory Management
  viewInventory,
  createInventoryItems,
  editInventoryItems,
  deleteInventoryItems,
  manageStock,
  
  // System Administration
  manageSettings,
  viewSystemLogs,
  manageBackups,
  manageIntegrations,
  
  // Reporting
  viewBasicReports,
  viewAdvancedReports,
  createCustomReports,
  scheduleReports,
}

/// User roles with predefined permissions
enum UserRole {
  admin,
  manager,
  technician,
  accountant,
  receptionist,
}

/// Role-based permission mapping
class RolePermissions {
  static const Map<UserRole, List<Permission>> _rolePermissions = {
    UserRole.admin: [
      // Admin has all permissions
      Permission.viewUsers,
      Permission.createUsers,
      Permission.editUsers,
      Permission.deleteUsers,
      Permission.manageUserRoles,
      Permission.viewCustomers,
      Permission.createCustomers,
      Permission.editCustomers,
      Permission.deleteCustomers,
      Permission.exportCustomers,
      Permission.viewEmployees,
      Permission.createEmployees,
      Permission.editEmployees,
      Permission.deleteEmployees,
      Permission.manageEmployeePayroll,
      Permission.viewEmployeeSalaries,
      Permission.viewServices,
      Permission.createServices,
      Permission.editServices,
      Permission.deleteServices,
      Permission.assignTechnicians,
      Permission.completeServices,
      Permission.cancelServices,
      Permission.viewFinancials,
      Permission.manageCashBoxes,
      Permission.createTransactions,
      Permission.editTransactions,
      Permission.deleteTransactions,
      Permission.viewReports,
      Permission.exportReports,
      Permission.manageOpeningBalances,
      Permission.viewInventory,
      Permission.createInventoryItems,
      Permission.editInventoryItems,
      Permission.deleteInventoryItems,
      Permission.manageStock,
      Permission.manageSettings,
      Permission.viewSystemLogs,
      Permission.manageBackups,
      Permission.manageIntegrations,
      Permission.viewBasicReports,
      Permission.viewAdvancedReports,
      Permission.createCustomReports,
      Permission.scheduleReports,
    ],
    
    UserRole.manager: [
      Permission.viewUsers,
      Permission.viewCustomers,
      Permission.createCustomers,
      Permission.editCustomers,
      Permission.exportCustomers,
      Permission.viewEmployees,
      Permission.createEmployees,
      Permission.editEmployees,
      Permission.manageEmployeePayroll,
      Permission.viewEmployeeSalaries,
      Permission.viewServices,
      Permission.createServices,
      Permission.editServices,
      Permission.assignTechnicians,
      Permission.completeServices,
      Permission.cancelServices,
      Permission.viewFinancials,
      Permission.manageCashBoxes,
      Permission.createTransactions,
      Permission.editTransactions,
      Permission.viewReports,
      Permission.exportReports,
      Permission.manageOpeningBalances,
      Permission.viewInventory,
      Permission.createInventoryItems,
      Permission.editInventoryItems,
      Permission.manageStock,
      Permission.viewBasicReports,
      Permission.viewAdvancedReports,
      Permission.createCustomReports,
    ],
    
    UserRole.technician: [
      Permission.viewCustomers,
      Permission.viewServices,
      Permission.editServices,
      Permission.completeServices,
      Permission.viewInventory,
      Permission.viewBasicReports,
    ],
    
    UserRole.accountant: [
      Permission.viewCustomers,
      Permission.viewEmployees,
      Permission.viewEmployeeSalaries,
      Permission.manageEmployeePayroll,
      Permission.viewServices,
      Permission.viewFinancials,
      Permission.manageCashBoxes,
      Permission.createTransactions,
      Permission.editTransactions,
      Permission.viewReports,
      Permission.exportReports,
      Permission.manageOpeningBalances,
      Permission.viewBasicReports,
      Permission.viewAdvancedReports,
      Permission.createCustomReports,
    ],
    
    UserRole.receptionist: [
      Permission.viewCustomers,
      Permission.createCustomers,
      Permission.editCustomers,
      Permission.viewServices,
      Permission.createServices,
      Permission.editServices,
      Permission.assignTechnicians,
      Permission.viewInventory,
      Permission.viewBasicReports,
    ],
  };

  /// Get permissions for a specific role
  static List<Permission> getPermissionsForRole(UserRole role) {
    return _rolePermissions[role] ?? [];
  }

  /// Get permissions for multiple roles
  static List<Permission> getPermissionsForRoles(List<UserRole> roles) {
    final Set<Permission> permissions = {};
    for (final role in roles) {
      permissions.addAll(getPermissionsForRole(role));
    }
    return permissions.toList();
  }

  /// Check if a role has a specific permission
  static bool roleHasPermission(UserRole role, Permission permission) {
    return getPermissionsForRole(role).contains(permission);
  }

  /// Parse role from string
  static UserRole? parseRole(String roleString) {
    try {
      return UserRole.values.firstWhere(
        (role) => role.toString().split('.').last == roleString.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get role display name in Arabic
  static String getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'مدير النظام';
      case UserRole.manager:
        return 'مدير';
      case UserRole.technician:
        return 'فني';
      case UserRole.accountant:
        return 'محاسب';
      case UserRole.receptionist:
        return 'موظف استقبال';
    }
  }

  /// Get permission display name in Arabic
  static String getPermissionDisplayName(Permission permission) {
    switch (permission) {
      case Permission.viewUsers:
        return 'عرض المستخدمين';
      case Permission.createUsers:
        return 'إنشاء مستخدمين';
      case Permission.editUsers:
        return 'تعديل المستخدمين';
      case Permission.deleteUsers:
        return 'حذف المستخدمين';
      case Permission.manageUserRoles:
        return 'إدارة أدوار المستخدمين';
      case Permission.viewCustomers:
        return 'عرض العملاء';
      case Permission.createCustomers:
        return 'إنشاء عملاء';
      case Permission.editCustomers:
        return 'تعديل العملاء';
      case Permission.deleteCustomers:
        return 'حذف العملاء';
      case Permission.exportCustomers:
        return 'تصدير العملاء';
      case Permission.viewEmployees:
        return 'عرض الموظفين';
      case Permission.createEmployees:
        return 'إنشاء موظفين';
      case Permission.editEmployees:
        return 'تعديل الموظفين';
      case Permission.deleteEmployees:
        return 'حذف الموظفين';
      case Permission.manageEmployeePayroll:
        return 'إدارة رواتب الموظفين';
      case Permission.viewEmployeeSalaries:
        return 'عرض رواتب الموظفين';
      case Permission.viewServices:
        return 'عرض الخدمات';
      case Permission.createServices:
        return 'إنشاء خدمات';
      case Permission.editServices:
        return 'تعديل الخدمات';
      case Permission.deleteServices:
        return 'حذف الخدمات';
      case Permission.assignTechnicians:
        return 'تعيين الموظف الفنيين';
      case Permission.completeServices:
        return 'إكمال الخدمات';
      case Permission.cancelServices:
        return 'إلغاء الخدمات';
      case Permission.viewFinancials:
        return 'عرض الماليات';
      case Permission.manageCashBoxes:
        return 'إدارة الصناديق';
      case Permission.createTransactions:
        return 'إنشاء معاملات';
      case Permission.editTransactions:
        return 'تعديل معاملات';
      case Permission.deleteTransactions:
        return 'حذف معاملات';
      case Permission.viewReports:
        return 'عرض التقارير';
      case Permission.exportReports:
        return 'تصدير التقارير';
      case Permission.manageOpeningBalances:
        return 'إدارة الأرصدة الافتتاحية';
      case Permission.viewInventory:
        return 'عرض المخزون';
      case Permission.createInventoryItems:
        return 'إنشاء عناصر مخزون';
      case Permission.editInventoryItems:
        return 'تعديل عناصر المخزون';
      case Permission.deleteInventoryItems:
        return 'حذف عناصر المخزون';
      case Permission.manageStock:
        return 'إدارة المخزون';
      case Permission.manageSettings:
        return 'إدارة الإعدادات';
      case Permission.viewSystemLogs:
        return 'عرض سجلات النظام';
      case Permission.manageBackups:
        return 'إدارة النسخ الاحتياطية';
      case Permission.manageIntegrations:
        return 'إدارة التكاملات';
      case Permission.viewBasicReports:
        return 'عرض التقارير الأساسية';
      case Permission.viewAdvancedReports:
        return 'عرض التقارير المتقدمة';
      case Permission.createCustomReports:
        return 'إنشاء تقارير مخصصة';
      case Permission.scheduleReports:
        return 'جدولة التقارير';
    }
  }
}
