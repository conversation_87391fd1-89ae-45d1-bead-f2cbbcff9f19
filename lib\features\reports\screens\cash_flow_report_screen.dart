import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/models/transaction.dart' as transaction_models;
import '../../../shared/models/bank_account.dart';
import '../../../shared/models/employee.dart';

import '../../../shared/models/category.dart';
import '../../../shared/models/salary.dart' as salary_models;
import '../../../shared/models/withdrawal.dart';
import '../../../shared/models/account_transfer.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/bank_account_repository.dart';
import '../../../core/repositories/employee_repository.dart';

import '../../../core/repositories/transaction_category_repository.dart';
import '../../../core/repositories/salary_repository.dart';
import '../../../core/repositories/withdrawal_repository.dart';
import '../../../core/repositories/account_transfer_repository.dart';
import '../utils/paginated_pdf_export.dart';

class CashFlowReportScreen extends StatefulWidget {
  const CashFlowReportScreen({super.key});

  @override
  State<CashFlowReportScreen> createState() => _CashFlowReportScreenState();
}

class _CashFlowReportScreenState extends State<CashFlowReportScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;

  // تاريخ البداية والنهاية
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // المعاملات المالية
  List<transaction_models.Transaction> _allTransactions = [];
  List<transaction_models.Transaction> _filteredTransactions = [];

  // الرواتب والسحبيات
  List<salary_models.Salary> _allSalaries = [];
  List<salary_models.Salary> _filteredSalaries = [];
  List<Withdrawal> _allWithdrawals = [];
  List<Withdrawal> _filteredWithdrawals = [];

  // التحويلات بين الحسابات
  List<AccountTransfer> _allTransfers = [];
  List<AccountTransfer> _filteredTransfers = [];

  // الحسابات البنكية والموظفين
  List<BankAccount> _bankAccounts = [];
  List<Employee> _employees = [];
  List<CategoryModel> _categories = [];

  // فلاتر
  int? _selectedEmployeeId;
  int? _selectedBankAccountId;
  int? _selectedCategoryId;
  int? _selectedCustomerId;
  int? _selectedSupplierId;
  String? _selectedTransactionType;

  // إجماليات
  double _totalIncome = 0;
  double _totalExpenses = 0;
  double _totalSalaries = 0;
  double _totalWithdrawals = 0;
  double _totalTransfers = 0;
  double _totalBankBalance = 0;

  // المستودعات
  final TransactionRepository _transactionRepository = TransactionRepository();
  final BankAccountRepository _bankAccountRepository = BankAccountRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final TransactionCategoryRepository _categoryRepository = TransactionCategoryRepository();
  final SalaryRepository _salaryRepository = SalaryRepository();
  final WithdrawalRepository _withdrawalRepository = WithdrawalRepository();
  final AccountTransferRepository _transferRepository = AccountTransferRepository();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {});
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل البيانات الأساسية
      final bankAccounts = await _bankAccountRepository.getAllBankAccounts();
      final employees = await _employeeRepository.getAllEmployees();
      final categories = await _categoryRepository.getAllCategories();

      // تحميل المعاملات المالية
      final transactions = await _transactionRepository.getTransactionsByDateRange(
        _startDate,
        _endDate,
      );

      // تحميل الرواتب والسحبيات
      final salaries = await _salaryRepository.getAllSalaries();
      final withdrawals = await _withdrawalRepository.getAllWithdrawals();

      // تحميل التحويلات بين الحسابات
      final transfers = await _transferRepository.getTransfersByDateRange(
        _startDate,
        _endDate,
      );

      // حساب الإجماليات
      double totalIncome = 0;
      double totalExpenses = 0;
      double totalSalaries = 0;
      double totalWithdrawals = 0;
      double totalTransfers = 0;
      double totalBankBalance = 0;

      // حساب إجمالي الإيرادات والمصروفات
      for (final transaction in transactions) {
        if (transaction.type == transaction_models.TransactionType.income) {
          totalIncome += transaction.amount;
        } else {
          totalExpenses += transaction.amount;
        }
      }

      // حساب إجمالي الرواتب (فقط المدفوعة)
      for (final salary in salaries.where((s) => s.status == salary_models.SalaryStatus.paid)) {
        // تحويل الشهر والسنة إلى تاريخ للمقارنة
        final salaryDate = DateTime(salary.year, salary.month, 1);
        if (salaryDate.isAfter(_startDate.subtract(const Duration(days: 31))) &&
            salaryDate.isBefore(_endDate.add(const Duration(days: 31)))) {
          totalSalaries += salary.paidAmount;
        }
      }

      // حساب إجمالي السحبيات (فقط المدفوعة)
      for (final withdrawal in withdrawals.where((w) => w.isPaid)) {
        if (withdrawal.date.isAfter(_startDate) && withdrawal.date.isBefore(_endDate.add(const Duration(days: 1)))) {
          totalWithdrawals += withdrawal.amount;
        }
      }

      // حساب إجمالي التحويلات (فقط المكتملة)
      for (final transfer in transfers.where((t) => t.status == TransferStatus.completed)) {
        totalTransfers += transfer.amount;
      }

      // حساب إجمالي أرصدة الحسابات البنكية
      for (final account in bankAccounts) {
        totalBankBalance += account.balance;
      }

      // تحديث الحالة
      setState(() {
        _bankAccounts = bankAccounts;
        _employees = employees;
        _categories = categories;
        _allTransactions = transactions;
        _filteredTransactions = transactions;
        _allSalaries = salaries;
        _filteredSalaries = salaries.where((s) {
          final salaryDate = DateTime(s.year, s.month, 1);
          return salaryDate.isAfter(_startDate.subtract(const Duration(days: 31))) &&
                 salaryDate.isBefore(_endDate.add(const Duration(days: 31)));
        }).toList();
        _allWithdrawals = withdrawals;
        _filteredWithdrawals = withdrawals.where((w) =>
          w.date.isAfter(_startDate) &&
          w.date.isBefore(_endDate.add(const Duration(days: 1)))
        ).toList();
        _allTransfers = transfers;
        _filteredTransfers = transfers;
        _totalIncome = totalIncome;
        _totalExpenses = totalExpenses;
        _totalSalaries = totalSalaries;
        _totalWithdrawals = totalWithdrawals;
        _totalTransfers = totalTransfers;
        _totalBankBalance = totalBankBalance;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _isLoading = true;
    });

    try {
      // تطبيق فلتر التاريخ على المعاملات
      _filteredTransactions = _allTransactions.where((transaction) {
        return transaction.date.isAfter(_startDate) &&
               transaction.date.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      // تطبيق فلتر نوع المعاملة
      if (_selectedTransactionType != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          if (_selectedTransactionType == 'income') {
            return transaction.type == transaction_models.TransactionType.income;
          } else {
            return transaction.type == transaction_models.TransactionType.expense;
          }
        }).toList();
      }

      // تطبيق فلتر الموظف
      if (_selectedEmployeeId != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.employeeId == _selectedEmployeeId;
        }).toList();
      }

      // تطبيق فلتر الحساب البنكي
      if (_selectedBankAccountId != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.bankAccountId == _selectedBankAccountId;
        }).toList();
      }

      // تطبيق فلتر الفئة
      if (_selectedCategoryId != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          // استخدام اسم الفئة بدلاً من معرف الفئة
          final categoryModel = _categories.firstWhere(
            (c) => c.id == _selectedCategoryId,
            orElse: () => CategoryModel(
              id: -1,
              name: '',
              type: 'expense',
              icon: Icons.money_off,
              color: Colors.grey,
              isActive: true,
              createdAt: DateTime.now(),
            ),
          );
          return transaction.category == categoryModel.name;
        }).toList();
      }

      // تطبيق فلتر العميل
      if (_selectedCustomerId != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.customerId == _selectedCustomerId;
        }).toList();
      }

      // تطبيق فلتر المورد
      if (_selectedSupplierId != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.supplierId == _selectedSupplierId;
        }).toList();
      }

      // تطبيق فلتر التاريخ على الرواتب
      _filteredSalaries = _allSalaries.where((salary) {
        // تحويل الشهر والسنة إلى تاريخ للمقارنة
        final salaryDate = DateTime(salary.year, salary.month, 1);
        return salaryDate.isAfter(_startDate.subtract(const Duration(days: 31))) &&
               salaryDate.isBefore(_endDate.add(const Duration(days: 31)));
      }).toList();

      // تطبيق فلتر الموظف على الرواتب
      if (_selectedEmployeeId != null) {
        _filteredSalaries = _filteredSalaries.where((salary) {
          return salary.employeeId == _selectedEmployeeId;
        }).toList();
      }

      // تطبيق فلتر التاريخ على السحبيات
      _filteredWithdrawals = _allWithdrawals.where((withdrawal) {
        return withdrawal.date.isAfter(_startDate) &&
               withdrawal.date.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      // تطبيق فلتر الموظف على السحبيات
      if (_selectedEmployeeId != null) {
        _filteredWithdrawals = _filteredWithdrawals.where((withdrawal) {
          return withdrawal.employeeId == _selectedEmployeeId;
        }).toList();
      }

      // تطبيق فلتر التاريخ على التحويلات
      _filteredTransfers = _allTransfers.where((transfer) {
        return transfer.date.isAfter(_startDate) &&
               transfer.date.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      // تطبيق فلتر الموظف أو الحساب البنكي على التحويلات
      if (_selectedEmployeeId != null) {
        _filteredTransfers = _filteredTransfers.where((transfer) {
          return (transfer.sourceType == TransferEntityType.employee &&
                  transfer.sourceId == _selectedEmployeeId) ||
                 (transfer.destinationType == TransferEntityType.employee &&
                  transfer.destinationId == _selectedEmployeeId);
        }).toList();
      }

      if (_selectedBankAccountId != null) {
        _filteredTransfers = _filteredTransfers.where((transfer) {
          return (transfer.sourceType == TransferEntityType.bankAccount &&
                  transfer.sourceId == _selectedBankAccountId) ||
                 (transfer.destinationType == TransferEntityType.bankAccount &&
                  transfer.destinationId == _selectedBankAccountId);
        }).toList();
      }

      // حساب الإجماليات بعد تطبيق الفلاتر (فقط المدفوعة)
      _totalIncome = _filteredTransactions
          .where((t) => t.type == transaction_models.TransactionType.income)
          .fold(0, (sum, t) => sum + t.amount);

      _totalExpenses = _filteredTransactions
          .where((t) => t.type == transaction_models.TransactionType.expense)
          .fold(0, (sum, t) => sum + t.amount);

      // فقط الرواتب المدفوعة
      _totalSalaries = _filteredSalaries
          .where((s) => s.status == salary_models.SalaryStatus.paid)
          .fold(0, (sum, s) => sum + s.paidAmount);

      // فقط السحبيات المدفوعة
      _totalWithdrawals = _filteredWithdrawals
          .where((w) => w.isPaid)
          .fold(0, (sum, w) => sum + w.amount);

      // فقط التحويلات المكتملة
      _totalTransfers = _filteredTransfers
          .where((t) => t.status == TransferStatus.completed)
          .fold(0, (sum, t) => sum + t.amount);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تطبيق الفلاتر: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedEmployeeId = null;
      _selectedBankAccountId = null;
      _selectedCategoryId = null;
      _selectedCustomerId = null;
      _selectedSupplierId = null;
      _selectedTransactionType = null;
    });

    _loadData();
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية البيانات'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // فلتر التاريخ
                    const Text('نطاق التاريخ'),
                    Row(
                      children: [
                        Expanded(
                          child: TextButton.icon(
                            icon: const Icon(Icons.calendar_today, size: 16),
                            label: Text(
                              DateFormat('yyyy/MM/dd').format(_startDate),
                            ),
                            onPressed: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _startDate,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                              );
                              if (date != null) {
                                setState(() {
                                  _startDate = date;
                                });
                              }
                            },
                          ),
                        ),
                        const Text(' - '),
                        Expanded(
                          child: TextButton.icon(
                            icon: const Icon(Icons.calendar_today, size: 16),
                            label: Text(
                              DateFormat('yyyy/MM/dd').format(_endDate),
                            ),
                            onPressed: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _endDate,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now().add(const Duration(days: 1)),
                              );
                              if (date != null) {
                                setState(() {
                                  _endDate = date;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // فلتر نوع المعاملة
                    DropdownButtonFormField<String?>(
                      decoration: const InputDecoration(
                        labelText: 'نوع المعاملة',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedTransactionType,
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('الكل'),
                        ),
                        const DropdownMenuItem<String>(
                          value: 'income',
                          child: Text('إيرادات'),
                        ),
                        const DropdownMenuItem<String>(
                          value: 'expense',
                          child: Text('مصروفات'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedTransactionType = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // فلتر الموظف
                    DropdownButtonFormField<int?>(
                      decoration: const InputDecoration(
                        labelText: 'الموظف',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedEmployeeId,
                      items: [
                        const DropdownMenuItem<int?>(
                          value: null,
                          child: Text('الكل'),
                        ),
                        ..._employees.map((employee) {
                          return DropdownMenuItem<int?>(
                            value: employee.id,
                            child: Text(employee.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedEmployeeId = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // فلتر الحساب البنكي
                    DropdownButtonFormField<int?>(
                      decoration: const InputDecoration(
                        labelText: 'الحساب البنكي',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedBankAccountId,
                      items: [
                        const DropdownMenuItem<int?>(
                          value: null,
                          child: Text('الكل'),
                        ),
                        ..._bankAccounts.map((account) {
                          return DropdownMenuItem<int?>(
                            value: account.id,
                            child: Text('${account.bankName} - ${account.accountName}'),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedBankAccountId = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _resetFilters();
                  },
                  child: const Text('إعادة تعيين'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير التدفقات النقدية'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الملخص'),
            Tab(text: 'الإيرادات'),
            Tab(text: 'المصروفات'),
            Tab(text: 'التحويلات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadData,
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'تصدير PDF',
            onPressed: _exportToPdf,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(),
                _buildIncomeTab(),
                _buildExpensesTab(),
                _buildTransfersTab(),
              ],
            ),
    );
  }

  Widget _buildSummaryTab() {
    // حساب صافي التدفق النقدي
    final double netCashFlow = _totalIncome - _totalExpenses - _totalSalaries;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض الفترة الزمنية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الفترة الزمنية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        DateFormat('yyyy/MM/dd').format(_startDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const Text(
                        ' - ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        DateFormat('yyyy/MM/dd').format(_endDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ملخص التدفقات النقدية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص التدفقات النقدية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الإيرادات
                  _buildSummaryItem(
                    'الإيرادات',
                    _totalIncome,
                    Icons.arrow_upward,
                    const Color(0xFF4CAF50),
                  ),
                  const Divider(),

                  // المصروفات
                  _buildSummaryItem(
                    'المصروفات',
                    _totalExpenses,
                    Icons.arrow_downward,
                    const Color(0xFFF44336),
                  ),
                  const Divider(),

                  // الرواتب
                  _buildSummaryItem(
                    'الرواتب',
                    _totalSalaries,
                    Icons.payments,
                    const Color(0xFFF44336),
                  ),
                  const Divider(),

                  // صافي التدفق النقدي
                  _buildSummaryItem(
                    'صافي التدفق النقدي',
                    netCashFlow,
                    Icons.account_balance_wallet,
                    netCashFlow >= 0 ? const Color(0xFF4CAF50) : const Color(0xFFF44336),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // أرصدة الحسابات البنكية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أرصدة الحسابات البنكية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // قائمة الحسابات البنكية
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _bankAccounts.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final account = _bankAccounts[index];
                      return _buildAccountItem(
                        account.bankName,
                        account.accountName,
                        account.balance,
                        Icons.account_balance,
                        Colors.blue,
                      );
                    },
                  ),

                  const Divider(thickness: 2),

                  // إجمالي الأرصدة البنكية
                  _buildSummaryItem(
                    'إجمالي الأرصدة البنكية',
                    _totalBankBalance,
                    Icons.account_balance,
                    Colors.blue,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // رسم بياني للإيرادات والمصروفات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'توزيع الإيرادات والمصروفات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  SizedBox(
                    height: 300,
                    child: PieChart(
                      PieChartData(
                        sectionsSpace: 2,
                        centerSpaceRadius: 40,
                        sections: [
                          PieChartSectionData(
                            color: const Color(0xFF4CAF50),
                            value: _totalIncome,
                            title: '${(_totalIncome / (_totalIncome + _totalExpenses + _totalSalaries) * 100).toStringAsFixed(1)}%',
                            radius: 100,
                            titleStyle: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          PieChartSectionData(
                            color: const Color(0xFFF44336),
                            value: _totalExpenses,
                            title: '${(_totalExpenses / (_totalIncome + _totalExpenses + _totalSalaries) * 100).toStringAsFixed(1)}%',
                            radius: 100,
                            titleStyle: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          PieChartSectionData(
                            color: Colors.orange,
                            value: _totalSalaries,
                            title: '${(_totalSalaries / (_totalIncome + _totalExpenses + _totalSalaries) * 100).toStringAsFixed(1)}%',
                            radius: 100,
                            titleStyle: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // وسيلة إيضاح الرسم البياني
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildLegendItem('الإيرادات', const Color(0xFF4CAF50)),
                      const SizedBox(width: 16),
                      _buildLegendItem('المصروفات', const Color(0xFFF44336)),
                      const SizedBox(width: 16),
                      _buildLegendItem('الرواتب', Colors.orange),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title, double amount, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(50),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(
            '${NumberFormat('#,##0.00', 'ar').format(amount)} ر.س',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: amount >= 0 ? Colors.black : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountItem(String bankName, String accountName, double balance, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(50),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bankName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  accountName,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${NumberFormat('#,##0.00', 'ar').format(balance)} ر.س',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: balance >= 0 ? Colors.black : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String title, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildIncomeTab() {
    // فلترة المعاملات للحصول على الإيرادات فقط
    final incomeTransactions = _filteredTransactions
        .where((transaction) => transaction.type == transaction_models.TransactionType.income)
        .toList();

    // تجميع الإيرادات حسب طريقة الدفع
    final Map<transaction_models.PaymentMethod, List<transaction_models.Transaction>> incomeByPaymentMethod = {};
    for (final transaction in incomeTransactions) {
      if (!incomeByPaymentMethod.containsKey(transaction.paymentMethod)) {
        incomeByPaymentMethod[transaction.paymentMethod] = [];
      }
      incomeByPaymentMethod[transaction.paymentMethod]!.add(transaction);
    }

    // تجميع الإيرادات حسب المصدر (العميل)
    final Map<int?, List<transaction_models.Transaction>> incomeByCustomer = {};
    for (final transaction in incomeTransactions) {
      if (transaction.customerId != null) {
        if (!incomeByCustomer.containsKey(transaction.customerId)) {
          incomeByCustomer[transaction.customerId] = [];
        }
        incomeByCustomer[transaction.customerId]!.add(transaction);
      }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض الفترة الزمنية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الفترة الزمنية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        DateFormat('yyyy/MM/dd').format(_startDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const Text(
                        ' - ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        DateFormat('yyyy/MM/dd').format(_endDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ملخص الإيرادات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص الإيرادات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildSummaryItem(
                    'إجمالي الإيرادات',
                    _totalIncome,
                    Icons.arrow_upward,
                    const Color(0xFF4CAF50),
                  ),

                  const Divider(),

                  // توزيع الإيرادات حسب طريقة الدفع
                  const Text(
                    'توزيع الإيرادات حسب طريقة الدفع',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  ...incomeByPaymentMethod.entries.map((entry) {
                    final paymentMethod = entry.key;
                    final transactions = entry.value;
                    final totalAmount = transactions.fold<double>(
                      0, (sum, transaction) => sum + transaction.amount);

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Icon(
                            _getPaymentMethodIcon(paymentMethod),
                            color: const Color(0xFF4CAF50),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _getPaymentMethodName(paymentMethod),
                              style: const TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          ),
                          Text(
                            '${NumberFormat('#,##0.00', 'ar').format(totalAmount)} ر.س',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // جدول الإيرادات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل الإيرادات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // عناوين الجدول
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      children: [
                        SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'التاريخ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'الوصف',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المصدر',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'طريقة الدفع',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المبلغ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        SizedBox(width: 8),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // بيانات الجدول
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: incomeTransactions.length,
                    itemBuilder: (context, index) {
                      final transaction = incomeTransactions[index];
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: Text(
                                DateFormat('yyyy/MM/dd').format(transaction.date),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(
                                transaction.description ?? '',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                transaction.customerName ?? 'غير محدد',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Row(
                                children: [
                                  Icon(
                                    _getPaymentMethodIcon(transaction.paymentMethod),
                                    size: 16,
                                    color: const Color(0xFF4CAF50),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _getPaymentMethodName(transaction.paymentMethod),
                                    style: const TextStyle(
                                      fontSize: 14,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                '${NumberFormat('#,##0.00', 'ar').format(transaction.amount)} ر.س',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF4CAF50),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // رسم بياني للإيرادات حسب المصدر
          if (incomeByCustomer.isNotEmpty)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'توزيع الإيرادات حسب العملاء',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    SizedBox(
                      height: 300,
                      child: PieChart(
                        PieChartData(
                          sectionsSpace: 2,
                          centerSpaceRadius: 40,
                          sections: _buildCustomerPieChartSections(incomeByCustomer),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildCustomerPieChartSections(Map<int?, List<transaction_models.Transaction>> incomeByCustomer) {
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      const Color(0xFF4CAF50),
      const Color(0xFF2196F3),
      const Color(0xFFFFC107),
      const Color(0xFF9C27B0),
      const Color(0xFF3F51B5),
      const Color(0xFF009688),
    ];

    int colorIndex = 0;

    // إنشاء قسم للعملاء غير المحددين
    final otherTransactions = incomeByCustomer[null] ?? [];
    if (otherTransactions.isNotEmpty) {
      final totalAmount = otherTransactions.fold<double>(
        0, (sum, transaction) => sum + transaction.amount);

      sections.add(
        PieChartSectionData(
          color: colors[colorIndex % colors.length],
          value: totalAmount,
          title: '${(totalAmount / _totalIncome * 100).toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );

      colorIndex++;
    }

    // إنشاء أقسام للعملاء المحددين
    for (final entry in incomeByCustomer.entries) {
      if (entry.key != null) {
        // استخدام معرف العميل للتعريف فقط
        // final customerId = entry.key;
        final transactions = entry.value;
        final totalAmount = transactions.fold<double>(
          0, (sum, transaction) => sum + transaction.amount);

        // تم إزالة البحث عن اسم العميل لأنه غير مستخدم

        sections.add(
          PieChartSectionData(
            color: colors[colorIndex % colors.length],
            value: totalAmount,
            title: '${(totalAmount / _totalIncome * 100).toStringAsFixed(1)}%',
            radius: 100,
            titleStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );

        colorIndex++;
      }
    }

    return sections;
  }

  IconData _getPaymentMethodIcon(transaction_models.PaymentMethod? paymentMethod) {
    if (paymentMethod == null) {
      return Icons.help_outline;
    }

    switch (paymentMethod) {
      case transaction_models.PaymentMethod.cash:
        return Icons.money;
      case transaction_models.PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case transaction_models.PaymentMethod.check:
        return Icons.payment;
      case transaction_models.PaymentMethod.creditCard:
        return Icons.credit_card;
      case transaction_models.PaymentMethod.other:
        return Icons.more_horiz;
    }
  }

  String _getPaymentMethodName(transaction_models.PaymentMethod? paymentMethod) {
    if (paymentMethod == null) {
      return 'غير معروف';
    }

    switch (paymentMethod) {
      case transaction_models.PaymentMethod.cash:
        return 'نقدي';
      case transaction_models.PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case transaction_models.PaymentMethod.check:
        return 'شيك';
      case transaction_models.PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      case transaction_models.PaymentMethod.other:
        return 'أخرى';
    }
  }

  Widget _buildExpensesTab() {
    // فلترة المعاملات للحصول على المصروفات فقط
    final expenseTransactions = _filteredTransactions
        .where((transaction) => transaction.type == transaction_models.TransactionType.expense)
        .toList();

    // تجميع المصروفات حسب الفئة
    final Map<String?, List<transaction_models.Transaction>> expensesByCategory = {};
    for (final transaction in expenseTransactions) {
      if (!expensesByCategory.containsKey(transaction.category)) {
        expensesByCategory[transaction.category] = [];
      }
      expensesByCategory[transaction.category]!.add(transaction);
    }

    // تجميع المصروفات حسب المستفيد (المورد)
    final Map<int?, List<transaction_models.Transaction>> expensesBySupplier = {};
    for (final transaction in expenseTransactions) {
      if (!expensesBySupplier.containsKey(transaction.supplierId)) {
        expensesBySupplier[transaction.supplierId] = [];
      }
      expensesBySupplier[transaction.supplierId]!.add(transaction);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض الفترة الزمنية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الفترة الزمنية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        DateFormat('yyyy/MM/dd').format(_startDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const Text(
                        ' - ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        DateFormat('yyyy/MM/dd').format(_endDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ملخص المصروفات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص المصروفات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // إجمالي المصروفات
                  _buildSummaryItem(
                    'إجمالي المصروفات',
                    _totalExpenses,
                    Icons.arrow_downward,
                    const Color(0xFFF44336),
                  ),

                  const Divider(),

                  // إجمالي الرواتب
                  _buildSummaryItem(
                    'إجمالي الرواتب',
                    _totalSalaries,
                    Icons.payments,
                    const Color(0xFFF44336),
                  ),

                  const Divider(),

                  // إجمالي السحبيات
                  _buildSummaryItem(
                    'إجمالي السحبيات',
                    _totalWithdrawals,
                    Icons.money_off,
                    const Color(0xFFF44336),
                  ),

                  const Divider(),

                  // إجمالي المصروفات الكلية
                  _buildSummaryItem(
                    'إجمالي المصروفات الكلية',
                    _totalExpenses + _totalSalaries + _totalWithdrawals,
                    Icons.account_balance_wallet,
                    const Color(0xFFF44336),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // جدول المصروفات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل المصروفات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // عناوين الجدول
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      children: [
                        SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'التاريخ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'الوصف',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'الفئة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المستفيد',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المبلغ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        SizedBox(width: 8),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // بيانات الجدول
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: expenseTransactions.length,
                    itemBuilder: (context, index) {
                      final transaction = expenseTransactions[index];
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: Text(
                                DateFormat('yyyy/MM/dd').format(transaction.date),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(
                                transaction.description ?? '',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                transaction.category ?? 'غير محدد',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                transaction.supplierName ?? 'غير محدد',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                '${NumberFormat('#,##0.00', 'ar').format(transaction.amount)} ر.س',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFFF44336),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // جدول الرواتب
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الرواتب',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // عناوين الجدول
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      children: [
                        SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'الموظف',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'التاريخ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'الراتب الأساسي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'البدلات',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'الخصومات',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'صافي الراتب',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        SizedBox(width: 8),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // بيانات الجدول
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _filteredSalaries.length,
                    itemBuilder: (context, index) {
                      final salary = _filteredSalaries[index];
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: Text(
                                salary.employeeName,
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                '${salary.month}/${salary.year}',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                NumberFormat('#,##0.00', 'ar').format(salary.basicSalary),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                NumberFormat('#,##0.00', 'ar').format(salary.allowances),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                NumberFormat('#,##0.00', 'ar').format(salary.deductions),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                '${NumberFormat('#,##0.00', 'ar').format(salary.netSalary)} ر.س',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFFF44336),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // جدول السحبيات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'السحبيات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // عناوين الجدول
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      children: [
                        SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'الموظف',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'التاريخ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'السبب',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            'الحالة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المبلغ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        SizedBox(width: 8),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // بيانات الجدول
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _filteredWithdrawals.length,
                    itemBuilder: (context, index) {
                      final withdrawal = _filteredWithdrawals[index];
                      return Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: Text(
                                withdrawal.employeeName,
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                DateFormat('yyyy/MM/dd').format(withdrawal.date),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(
                                withdrawal.reason ?? '',
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                withdrawal.isPaid ? 'مسددة' : 'غير مسددة',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: withdrawal.isPaid ? Colors.green : Colors.orange,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                '${NumberFormat('#,##0.00', 'ar').format(withdrawal.amount)} ر.س',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFFF44336),
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // رسم بياني للمصروفات حسب الفئة
          if (expensesByCategory.isNotEmpty)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'توزيع المصروفات حسب الفئة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    SizedBox(
                      height: 300,
                      child: PieChart(
                        PieChartData(
                          sectionsSpace: 2,
                          centerSpaceRadius: 40,
                          sections: _buildCategoryPieChartSections(expensesByCategory),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildCategoryPieChartSections(Map<String?, List<transaction_models.Transaction>> expensesByCategory) {
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      const Color(0xFFF44336),
      const Color(0xFFFF9800),
      const Color(0xFFFFEB3B),
      const Color(0xFF9C27B0),
      const Color(0xFF3F51B5),
      const Color(0xFF009688),
    ];

    int colorIndex = 0;
    final totalExpenses = _totalExpenses;

    // إنشاء قسم للفئات غير المحددة
    final otherTransactions = expensesByCategory[null] ?? [];
    if (otherTransactions.isNotEmpty) {
      final totalAmount = otherTransactions.fold<double>(
        0, (sum, transaction) => sum + transaction.amount);

      sections.add(
        PieChartSectionData(
          color: colors[colorIndex % colors.length],
          value: totalAmount,
          title: '${(totalAmount / totalExpenses * 100).toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );

      colorIndex++;
    }

    // إنشاء أقسام للفئات المحددة
    for (final entry in expensesByCategory.entries) {
      if (entry.key != null) {
        // استخدام اسم الفئة للتعريف فقط
        // final category = entry.key;
        final transactions = entry.value;
        final totalAmount = transactions.fold<double>(
          0, (sum, transaction) => sum + transaction.amount);

        sections.add(
          PieChartSectionData(
            color: colors[colorIndex % colors.length],
            value: totalAmount,
            title: '${(totalAmount / totalExpenses * 100).toStringAsFixed(1)}%',
            radius: 100,
            titleStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );

        colorIndex++;
      }
    }

    return sections;
  }

  Widget _buildTransfersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض الفترة الزمنية
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الفترة الزمنية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        DateFormat('yyyy/MM/dd').format(_startDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const Text(
                        ' - ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        DateFormat('yyyy/MM/dd').format(_endDate),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ملخص التحويلات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص التحويلات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildSummaryItem(
                    'إجمالي التحويلات',
                    _totalTransfers,
                    Icons.swap_horiz,
                    const Color(0xFF2196F3),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // جدول التحويلات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل التحويلات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // عناوين الجدول
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      children: [
                        SizedBox(width: 8),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'التاريخ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المصدر',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'الوجهة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'السبب',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'المبلغ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                        SizedBox(width: 8),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // بيانات الجدول
                  _filteredTransfers.isEmpty
                      ? const Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(
                            child: Text(
                              'لا توجد تحويلات في الفترة المحددة',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _filteredTransfers.length,
                          itemBuilder: (context, index) {
                            final transfer = _filteredTransfers[index];
                            return Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Colors.grey[300]!,
                                    width: 1,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  const SizedBox(width: 8),
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      DateFormat('yyyy/MM/dd').format(transfer.date),
                                      style: const TextStyle(
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Row(
                                      children: [
                                        Icon(
                                          transfer.sourceType == TransferEntityType.bankAccount
                                              ? Icons.account_balance
                                              : Icons.person,
                                          size: 16,
                                          color: const Color(0xFF2196F3),
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            transfer.sourceName,
                                            style: const TextStyle(
                                              fontSize: 14,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Row(
                                      children: [
                                        Icon(
                                          transfer.destinationType == TransferEntityType.bankAccount
                                              ? Icons.account_balance
                                              : Icons.person,
                                          size: 16,
                                          color: const Color(0xFF2196F3),
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            transfer.destinationName,
                                            style: const TextStyle(
                                              fontSize: 14,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      transfer.reason ?? '',
                                      style: const TextStyle(
                                        fontSize: 14,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      '${NumberFormat('#,##0.00', 'ar').format(transfer.amount)} ر.س',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF2196F3),
                                      ),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                ],
                              ),
                            );
                          },
                        ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // رسم بياني للتحويلات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'توزيع التحويلات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  SizedBox(
                    height: 300,
                    child: _buildTransfersChart(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransfersChart() {
    // تجميع التحويلات حسب النوع
    final Map<String, double> transfersByType = {
      'من حساب بنكي إلى حساب بنكي': 0,
      'من حساب بنكي إلى موظف': 0,
      'من موظف إلى حساب بنكي': 0,
      'من موظف إلى موظف': 0,
    };

    for (final transfer in _filteredTransfers) {
      if (transfer.sourceType == TransferEntityType.bankAccount &&
          transfer.destinationType == TransferEntityType.bankAccount) {
        transfersByType['من حساب بنكي إلى حساب بنكي'] =
            transfersByType['من حساب بنكي إلى حساب بنكي']! + transfer.amount;
      } else if (transfer.sourceType == TransferEntityType.bankAccount &&
                 transfer.destinationType == TransferEntityType.employee) {
        transfersByType['من حساب بنكي إلى موظف'] =
            transfersByType['من حساب بنكي إلى موظف']! + transfer.amount;
      } else if (transfer.sourceType == TransferEntityType.employee &&
                 transfer.destinationType == TransferEntityType.bankAccount) {
        transfersByType['من موظف إلى حساب بنكي'] =
            transfersByType['من موظف إلى حساب بنكي']! + transfer.amount;
      } else if (transfer.sourceType == TransferEntityType.employee &&
                 transfer.destinationType == TransferEntityType.employee) {
        transfersByType['من موظف إلى موظف'] =
            transfersByType['من موظف إلى موظف']! + transfer.amount;
      }
    }

    // إنشاء قائمة بالأقسام
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      const Color(0xFF2196F3),
      const Color(0xFF03A9F4),
      const Color(0xFF00BCD4),
      const Color(0xFF009688),
    ];

    int colorIndex = 0;
    final totalTransfers = _totalTransfers;

    if (totalTransfers == 0) {
      // إذا لم تكن هناك تحويلات، عرض رسالة
      return const Center(
        child: Text(
          'لا توجد تحويلات في الفترة المحددة',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    // إنشاء أقسام للتحويلات
    for (final entry in transfersByType.entries) {
      if (entry.value > 0) {
        sections.add(
          PieChartSectionData(
            color: colors[colorIndex % colors.length],
            value: entry.value,
            title: '${(entry.value / totalTransfers * 100).toStringAsFixed(1)}%',
            radius: 100,
            titleStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );

        colorIndex++;
      }
    }

    if (sections.isEmpty) {
      // إذا لم تكن هناك أقسام، عرض رسالة
      return const Center(
        child: Text(
          'لا توجد تحويلات في الفترة المحددة',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        sections: sections,
      ),
    );
  }

  void _exportToPdf() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // تحضير البيانات للتصدير
      final List<Map<String, dynamic>> tableData = [];

      // Registrar el número de registros para depuración
      debugPrint('Número de transacciones filtradas: ${_filteredTransactions.length}');
      debugPrint('Número de salarios filtrados: ${_filteredSalaries.length}');
      debugPrint('Número de retiros filtrados: ${_filteredWithdrawals.length}');
      debugPrint('Número de transferencias filtradas: ${_filteredTransfers.length}');

      // إضافة الإيرادات (فقط المدفوعة)
      for (final transaction in _filteredTransactions.where((t) => t.type == transaction_models.TransactionType.income)) {
        // نفترض أن جميع المعاملات مدفوعة لأن النموذج لا يحتوي على حقل حالة
        tableData.add({
          'type': 'income',
          'date': transaction.date,
          'reference': transaction.reference,
          'description': transaction.description ?? '',
          'source': transaction.customerName ?? 'غير محدد',
          'payment_method': _getPaymentMethodName(transaction.paymentMethod),
          'amount': transaction.amount,
          'status': 'مدفوع', // ترجمة حالة "paid" إلى "مدفوع"
        });
      }

      // إضافة المصروفات (فقط المدفوعة)
      for (final transaction in _filteredTransactions.where((t) => t.type == transaction_models.TransactionType.expense)) {
        // نفترض أن جميع المعاملات مدفوعة لأن النموذج لا يحتوي على حقل حالة
        tableData.add({
          'type': 'expense',
          'date': transaction.date,
          'reference': transaction.reference,
          'description': transaction.description ?? '',
          'category': transaction.category ?? 'غير محدد',
          'beneficiary': transaction.supplierName ?? 'غير محدد',
          'payment_method': _getPaymentMethodName(transaction.paymentMethod),
          'amount': -transaction.amount, // سالب للمصروفات
          'status': 'مدفوع', // ترجمة حالة "paid" إلى "مدفوع"
        });
      }

      // إضافة الرواتب (فقط المدفوعة)
      for (final salary in _filteredSalaries.where((s) => s.status == salary_models.SalaryStatus.paid)) {
        tableData.add({
          'type': 'salary',
          'date': DateTime(salary.year, salary.month, 1),
          'reference': 'راتب ${salary.month}/${salary.year}',
          'description': 'راتب ${salary.employeeName} لشهر ${salary.month}/${salary.year}',
          'category': 'رواتب',
          'beneficiary': salary.employeeName,
          'payment_method': '',
          'amount': -salary.netSalary, // سالب للمصروفات
          'status': 'راتب', // استخدام كلمة "راتب" بدلاً من "paid"
        });
      }

      // إضافة السحبيات (فقط المدفوعة)
      for (final withdrawal in _filteredWithdrawals.where((w) => w.isPaid)) {
        tableData.add({
          'type': 'withdrawal',
          'date': withdrawal.date,
          'reference': 'سحبية ${withdrawal.id}',
          'description': withdrawal.reason ?? 'سحبية',
          'category': 'سحبيات',
          'beneficiary': withdrawal.employeeName,
          'payment_method': '',
          'amount': -withdrawal.amount, // سالب للمصروفات
          'status': 'مدفوع', // ترجمة حالة "paid" إلى "مدفوع"
        });
      }

      // إضافة التحويلات (فقط المكتملة)
      for (final transfer in _filteredTransfers.where((t) => t.status == TransferStatus.completed)) {
        tableData.add({
          'type': 'transfer',
          'date': transfer.date,
          'reference': transfer.reference,
          'description': transfer.reason ?? 'تحويل',
          'source': '${AccountTransfer.getEntityTypeString(transfer.sourceType)}: ${transfer.sourceName}',
          'destination': '${AccountTransfer.getEntityTypeString(transfer.destinationType)}: ${transfer.destinationName}',
          'amount': transfer.amount,
          'status': 'مكتمل', // ترجمة حالة "completed" إلى "مكتمل"
        });
      }

      // ترتيب البيانات حسب التاريخ
      tableData.sort((a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));

      // Registrar el número total de registros para depuración
      debugPrint('Número total de registros en tableData: ${tableData.length}');

      // حساب الإجماليات
      final Map<String, double> summaryData = {
        'totalIncome': _totalIncome,
        'totalExpenses': _totalExpenses,
        'totalSalaries': _totalSalaries,
        'totalWithdrawals': _totalWithdrawals,
        'totalTransfers': _totalTransfers,
        'netCashFlow': _totalIncome - _totalExpenses - _totalSalaries - _totalWithdrawals,
        'totalBankBalance': _totalBankBalance,
      };

      // Verificar que todos los datos se incluyan en el PDF
      debugPrint('Verificando datos para exportación:');
      debugPrint('Número de transacciones de ingresos: ${_filteredTransactions.where((t) => t.type == transaction_models.TransactionType.income).length}');
      debugPrint('Número de transacciones de gastos: ${_filteredTransactions.where((t) => t.type == transaction_models.TransactionType.expense).length}');
      debugPrint('Número de salarios: ${_filteredSalaries.length}');
      debugPrint('Número de retiros: ${_filteredWithdrawals.length}');
      debugPrint('Número de transferencias: ${_filteredTransfers.length}');
      debugPrint('Número total de registros en tableData: ${tableData.length}');

      // تصدير التقرير
      final filePath = await PaginatedPdfExport.exportCashFlowReport(
        title: 'تقرير التدفقات النقدية',
        startDate: _startDate,
        endDate: _endDate,
        tableData: tableData,
        summaryData: summaryData,
        bankAccounts: _bankAccounts,
        context: context,
      );

      setState(() {
        _isLoading = false;
      });

      if (filePath != null && mounted) {
        // عرض مربع حوار يسأل المستخدم إذا كان يريد فتح الملف
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تم تصدير التقرير بنجاح'),
            content: const Text('هل ترغب في فتح ملف PDF الآن؟'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('لا'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // عرض ملف PDF داخل التطبيق
                  PaginatedPdfExport.showPdfInApp(
                    context,
                    filePath,
                    'تقرير التدفقات النقدية'
                  );
                },
                child: const Text('نعم'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
