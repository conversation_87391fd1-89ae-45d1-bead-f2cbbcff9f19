import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

/// مدير الذاكرة لتحسين الأداء ومنع تسريب الذاكرة
class MemoryManager {
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();

  static const String _tag = 'MemoryManager';
  
  // Cache للبيانات المتكررة
  final Map<String, CacheEntry> _cache = {};
  final int _maxCacheSize = 100;
  final Duration _cacheExpiry = const Duration(minutes: 15);
  
  // مراقبة الموارد
  Timer? _cleanupTimer;
  final Set<StreamSubscription> _activeSubscriptions = {};
  final Set<Timer> _activeTimers = {};

  /// تهيئة مدير الذاكرة
  void initialize() {
    if (kDebugMode) {
      print('$_tag: تهيئة مدير الذاكرة...');
    }

    // تشغيل تنظيف دوري كل 5 دقائق
    _cleanupTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _performCleanup();
    });

    if (kDebugMode) {
      print('$_tag: ✅ تم تهيئة مدير الذاكرة بنجاح');
    }
  }

  /// إضافة بيانات إلى الـ cache
  void cacheData(String key, dynamic data, {Duration? customExpiry}) {
    try {
      // تنظيف الـ cache إذا امتلأ
      if (_cache.length >= _maxCacheSize) {
        _cleanExpiredCache();
        
        // إذا ما زال ممتلئاً، احذف الأقدم
        if (_cache.length >= _maxCacheSize) {
          _removeOldestCacheEntry();
        }
      }

      final expiry = customExpiry ?? _cacheExpiry;
      _cache[key] = CacheEntry(
        data: data,
        timestamp: DateTime.now(),
        expiry: expiry,
      );

      if (kDebugMode) {
        print('$_tag: 💾 تم حفظ البيانات في الـ cache: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في حفظ البيانات في الـ cache: $e');
      }
    }
  }

  /// جلب بيانات من الـ cache
  T? getCachedData<T>(String key) {
    try {
      final entry = _cache[key];
      if (entry == null) return null;

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().difference(entry.timestamp) > entry.expiry) {
        _cache.remove(key);
        return null;
      }

      if (kDebugMode) {
        print('$_tag: 📖 تم جلب البيانات من الـ cache: $key');
      }

      return entry.data as T?;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في جلب البيانات من الـ cache: $e');
      }
      return null;
    }
  }

  /// مسح بيانات محددة من الـ cache
  void clearCacheEntry(String key) {
    _cache.remove(key);
    if (kDebugMode) {
      print('$_tag: 🗑️ تم مسح البيانات من الـ cache: $key');
    }
  }

  /// مسح جميع بيانات الـ cache
  void clearAllCache() {
    _cache.clear();
    if (kDebugMode) {
      print('$_tag: 🗑️ تم مسح جميع بيانات الـ cache');
    }
  }

  /// تسجيل subscription لمراقبته
  void registerSubscription(StreamSubscription subscription) {
    _activeSubscriptions.add(subscription);
    if (kDebugMode) {
      print('$_tag: 📡 تم تسجيل subscription جديد (المجموع: ${_activeSubscriptions.length})');
    }
  }

  /// إلغاء تسجيل subscription
  void unregisterSubscription(StreamSubscription subscription) {
    _activeSubscriptions.remove(subscription);
    if (kDebugMode) {
      print('$_tag: 📡 تم إلغاء تسجيل subscription (المجموع: ${_activeSubscriptions.length})');
    }
  }

  /// تسجيل timer لمراقبته
  void registerTimer(Timer timer) {
    _activeTimers.add(timer);
    if (kDebugMode) {
      print('$_tag: ⏰ تم تسجيل timer جديد (المجموع: ${_activeTimers.length})');
    }
  }

  /// إلغاء تسجيل timer
  void unregisterTimer(Timer timer) {
    _activeTimers.remove(timer);
    if (kDebugMode) {
      print('$_tag: ⏰ تم إلغاء تسجيل timer (المجموع: ${_activeTimers.length})');
    }
  }

  /// تنظيف الموارد المنتهية الصلاحية
  void _performCleanup() {
    try {
      if (kDebugMode) {
        print('$_tag: 🧹 بدء عملية التنظيف الدورية...');
      }

      // تنظيف الـ cache المنتهي الصلاحية
      _cleanExpiredCache();

      // تنظيف الـ subscriptions المغلقة
      _cleanInactiveSubscriptions();

      // تنظيف الـ timers المنتهية
      _cleanInactiveTimers();

      // طباعة إحصائيات الذاكرة
      _printMemoryStats();

      if (kDebugMode) {
        print('$_tag: ✅ تم الانتهاء من عملية التنظيف');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في عملية التنظيف: $e');
      }
    }
  }

  /// تنظيف الـ cache المنتهي الصلاحية
  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > entry.value.expiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      print('$_tag: 🗑️ تم مسح ${expiredKeys.length} عنصر منتهي الصلاحية من الـ cache');
    }
  }

  /// إزالة أقدم عنصر من الـ cache
  void _removeOldestCacheEntry() {
    if (_cache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _cache.entries) {
      if (oldestTime == null || entry.value.timestamp.isBefore(oldestTime)) {
        oldestTime = entry.value.timestamp;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _cache.remove(oldestKey);
      if (kDebugMode) {
        print('$_tag: 🗑️ تم مسح أقدم عنصر من الـ cache: $oldestKey');
      }
    }
  }

  /// تنظيف الـ subscriptions غير النشطة
  void _cleanInactiveSubscriptions() {
    final inactiveSubscriptions = <StreamSubscription>[];

    for (final subscription in _activeSubscriptions) {
      if (subscription.isPaused) {
        inactiveSubscriptions.add(subscription);
      }
    }

    for (final subscription in inactiveSubscriptions) {
      _activeSubscriptions.remove(subscription);
    }

    if (kDebugMode && inactiveSubscriptions.isNotEmpty) {
      print('$_tag: 🗑️ تم تنظيف ${inactiveSubscriptions.length} subscription غير نشط');
    }
  }

  /// تنظيف الـ timers غير النشطة
  void _cleanInactiveTimers() {
    final inactiveTimers = <Timer>[];

    for (final timer in _activeTimers) {
      if (!timer.isActive) {
        inactiveTimers.add(timer);
      }
    }

    for (final timer in inactiveTimers) {
      _activeTimers.remove(timer);
    }

    if (kDebugMode && inactiveTimers.isNotEmpty) {
      print('$_tag: 🗑️ تم تنظيف ${inactiveTimers.length} timer غير نشط');
    }
  }

  /// طباعة إحصائيات الذاكرة
  void _printMemoryStats() {
    if (kDebugMode) {
      print('$_tag: 📊 إحصائيات الذاكرة:');
      print('  - عناصر الـ cache: ${_cache.length}/$_maxCacheSize');
      print('  - Subscriptions نشطة: ${_activeSubscriptions.length}');
      print('  - Timers نشطة: ${_activeTimers.length}');
    }
  }

  /// تنظيف جميع الموارد عند إغلاق التطبيق
  void dispose() {
    try {
      if (kDebugMode) {
        print('$_tag: 🔄 تنظيف جميع الموارد...');
      }

      // إيقاف timer التنظيف
      _cleanupTimer?.cancel();
      _cleanupTimer = null;

      // إلغاء جميع الـ subscriptions
      for (final subscription in _activeSubscriptions) {
        subscription.cancel();
      }
      _activeSubscriptions.clear();

      // إلغاء جميع الـ timers
      for (final timer in _activeTimers) {
        timer.cancel();
      }
      _activeTimers.clear();

      // مسح الـ cache
      _cache.clear();

      if (kDebugMode) {
        print('$_tag: ✅ تم تنظيف جميع الموارد بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تنظيف الموارد: $e');
      }
    }
  }

  /// جلب إحصائيات الذاكرة
  Map<String, dynamic> getMemoryStats() {
    return {
      'cache_size': _cache.length,
      'max_cache_size': _maxCacheSize,
      'active_subscriptions': _activeSubscriptions.length,
      'active_timers': _activeTimers.length,
      'cache_hit_ratio': _calculateCacheHitRatio(),
    };
  }

  /// حساب نسبة نجاح الـ cache
  double _calculateCacheHitRatio() {
    // هذا مثال بسيط - يمكن تحسينه بإضافة عدادات فعلية
    return _cache.isNotEmpty ? 0.85 : 0.0;
  }
}

/// فئة لتخزين بيانات الـ cache
class CacheEntry {
  final dynamic data;
  final DateTime timestamp;
  final Duration expiry;

  CacheEntry({
    required this.data,
    required this.timestamp,
    required this.expiry,
  });
}

/// Extension لتسهيل استخدام مدير الذاكرة مع StreamSubscription
extension StreamSubscriptionMemoryManagement on StreamSubscription {
  void registerWithMemoryManager() {
    MemoryManager().registerSubscription(this);
  }

  void unregisterFromMemoryManager() {
    MemoryManager().unregisterSubscription(this);
  }
}

/// Extension لتسهيل استخدام مدير الذاكرة مع Timer
extension TimerMemoryManagement on Timer {
  void registerWithMemoryManager() {
    MemoryManager().registerTimer(this);
  }

  void unregisterFromMemoryManager() {
    MemoryManager().unregisterTimer(this);
  }
}
