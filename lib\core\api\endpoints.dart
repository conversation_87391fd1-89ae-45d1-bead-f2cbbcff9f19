import '../../config/constants.dart';

class ApiEndpoints {
  static const String baseUrl = AppConstants.baseUrl;
  
  // Auth endpoints
  static const String login = '$baseUrl/auth/login';
  static const String register = '$baseUrl/auth/register';
  static const String forgotPassword = '$baseUrl/auth/forgot-password';
  static const String resetPassword = '$baseUrl/auth/reset-password';
  static const String logout = '$baseUrl/auth/logout';
  static const String profile = '$baseUrl/auth/profile';
  
  // Dashboard endpoints
  static const String dashboardSummary = '$baseUrl/dashboard/summary';
  static const String dashboardCharts = '$baseUrl/dashboard/charts';
  
  // Invoice endpoints
  static const String invoices = '$baseUrl/invoices';
  static String invoice(int id) => '$baseUrl/invoices/$id';
  
  // Transaction endpoints
  static const String transactions = '$baseUrl/transactions';
  static String transaction(int id) => '$baseUrl/transactions/$id';
  
  // Customer endpoints
  static const String customers = '$baseUrl/customers';
  static String customer(int id) => '$baseUrl/customers/$id';
  
  // Supplier endpoints
  static const String suppliers = '$baseUrl/suppliers';
  static String supplier(int id) => '$baseUrl/suppliers/$id';
  
  // Service request endpoints
  static const String serviceRequests = '$baseUrl/service-requests';
  static String serviceRequest(int id) => '$baseUrl/service-requests/$id';
  
  // Inventory endpoints
  static const String inventory = '$baseUrl/inventory';
  static String inventoryItem(int id) => '$baseUrl/inventory/$id';
  
  // Employee endpoints
  static const String employees = '$baseUrl/employees';
  static String employee(int id) => '$baseUrl/employees/$id';
  
  // Payroll endpoints
  static const String payroll = '$baseUrl/payroll';
  static String employeePayroll(int employeeId) => '$baseUrl/payroll/employee/$employeeId';
  static String withdrawals = '$baseUrl/withdrawals';
  
  // Report endpoints
  static const String reports = '$baseUrl/reports';
  static String reportByType(String type) => '$baseUrl/reports/$type';
  
  // Settings endpoints
  static const String settings = '$baseUrl/settings';
  static const String companyProfile = '$baseUrl/settings/company';
  
  // Notification endpoints
  static const String notifications = '$baseUrl/notifications';
  static const String markNotificationsAsRead = '$baseUrl/notifications/mark-as-read';
}
