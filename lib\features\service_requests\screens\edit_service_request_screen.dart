import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_request.dart';
import '../../../shared/models/employee.dart';
import '../../../core/repositories/employee_repository.dart';

class EditServiceRequestScreen extends StatefulWidget {
  final ServiceRequest serviceRequest;

  const EditServiceRequestScreen({
    super.key,
    required this.serviceRequest,
  });

  @override
  State<EditServiceRequestScreen> createState() => _EditServiceRequestScreenState();
}

class _EditServiceRequestScreenState extends State<EditServiceRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final EmployeeRepository _employeeRepository = EmployeeRepository();

  late String _customerName;
  late String _customerPhone;
  late ServiceRequestType _requestType;
  late String _description;
  late String? _location;
  late ServiceRequestPriority _priority;
  late DateTime _scheduledDate;
  late String? _assignedToName;
  late double? _technicianDailyRate;

  // Employee selection
  Employee? _selectedTechnician;
  List<Employee> _technicians = [];
  bool _isLoadingTechnicians = true;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _customerName = widget.serviceRequest.customerName;
    _customerPhone = widget.serviceRequest.customerPhone ?? '';
    _requestType = widget.serviceRequest.requestType;
    _description = widget.serviceRequest.description;
    _location = widget.serviceRequest.location;
    _priority = widget.serviceRequest.priority;
    _scheduledDate = widget.serviceRequest.scheduledDate;
    _assignedToName = widget.serviceRequest.assignedToName;
    _technicianDailyRate = widget.serviceRequest.technicianDailyRate;

    _loadTechnicians();
  }

  Future<void> _loadTechnicians() async {
    try {
      final employees = await _employeeRepository.getAllEmployees();
      final technicians = employees.where((emp) =>
        emp.status == EmployeeStatus.active && (
          emp.position.toLowerCase().contains('فني') ||
          emp.position.toLowerCase().contains('technician') ||
          emp.position.toLowerCase().contains('maintenance') ||
          emp.position.toLowerCase().contains('صيانة') ||
          emp.position.toLowerCase().contains('تركيب') ||
          emp.position.toLowerCase().contains('تكييف')
        )
      ).toList();

      if (kDebugMode) {
        print('تم تحميل ${employees.length} موظف');
        print('تم فلترة ${technicians.length} فني نشط');
      }

      setState(() {
        _technicians = technicians;
        _isLoadingTechnicians = false;

        // Find and set the selected technician if assigned
        if (widget.serviceRequest.assignedTo != null) {
          try {
            _selectedTechnician = _technicians.firstWhere(
              (tech) => tech.id == widget.serviceRequest.assignedTo,
            );
          } catch (e) {
            // If assigned technician not found in filtered list, keep the name
            _assignedToName = widget.serviceRequest.assignedToName;
          }
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الفنيين: $e');
      }
      setState(() {
        _isLoadingTechnicians = false;
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _scheduledDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _scheduledDate) {
      setState(() {
        _scheduledDate = picked;
      });
    }
  }

  void _saveServiceRequest() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          // Create updated service request
          final updatedRequest = widget.serviceRequest.copyWith(
            customerName: _customerName,
            customerPhone: _customerPhone.isEmpty ? null : _customerPhone,
            requestType: _requestType,
            description: _description,
            location: _location,
            priority: _priority,
            scheduledDate: _scheduledDate,
            assignedToName: _assignedToName,
          );

          // Return to previous screen with updated request
          Navigator.pop(context, updatedRequest);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تعديل طلب خدمة ${widget.serviceRequest.reference}'),
        actions: [
          TextButton.icon(
            onPressed: _isLoading ? null : _saveServiceRequest,
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer information
                    const Text(
                      'معلومات العميل',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      initialValue: _customerName,
                      decoration: const InputDecoration(
                        labelText: 'اسم العميل',
                        prefixIcon: Icon(Icons.person),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم العميل';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _customerName = value!;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _customerPhone,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        prefixIcon: Icon(Icons.phone),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                      onSaved: (value) {
                        _customerPhone = value!;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Service request details
                    const Text(
                      'تفاصيل الطلب',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    DropdownButtonFormField<ServiceRequestType>(
                      decoration: const InputDecoration(
                        labelText: 'نوع الطلب',
                        prefixIcon: Icon(Icons.category),
                        border: OutlineInputBorder(),
                      ),
                      value: _requestType,
                      items: ServiceRequestType.values.map((type) {
                        return DropdownMenuItem<ServiceRequestType>(
                          value: type,
                          child: Text(ServiceRequest.getRequestTypeName(type)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _requestType = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _description,
                      decoration: const InputDecoration(
                        labelText: 'الوصف',
                        prefixIcon: Icon(Icons.description),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال وصف الطلب';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _description = value!;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _location,
                      decoration: const InputDecoration(
                        labelText: 'الموقع (اختياري)',
                        prefixIcon: Icon(Icons.location_on),
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _location = value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    DropdownButtonFormField<ServiceRequestPriority>(
                      decoration: const InputDecoration(
                        labelText: 'الأولوية',
                        prefixIcon: Icon(Icons.flag),
                        border: OutlineInputBorder(),
                      ),
                      value: _priority,
                      items: ServiceRequestPriority.values.map((priority) {
                        return DropdownMenuItem<ServiceRequestPriority>(
                          value: priority,
                          child: Row(
                            children: [
                              Icon(
                                Icons.circle,
                                size: 12,
                                color: ServiceRequest.getPriorityColor(priority),
                              ),
                              const SizedBox(width: 8),
                              Text(ServiceRequest.getPriorityName(priority)),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _priority = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    InkWell(
                      onTap: () => _selectDate(context),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الجدولة',
                          prefixIcon: Icon(Icons.calendar_today),
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          DateFormat('dd/MM/yyyy').format(_scheduledDate),
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    // Technician selection dropdown
                    _isLoadingTechnicians
                      ? const Center(child: CircularProgressIndicator())
                      : _technicians.isEmpty
                        ? Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.orange),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.orange.withOpacity(0.1),
                            ),
                            child: const Row(
                              children: [
                                Icon(Icons.warning, color: Colors.orange),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'لا يوجد فنيين متاحين. يرجى إضافة موظفين من قسم الموظفين أولاً.',
                                    style: TextStyle(color: Colors.orange),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : DropdownButtonFormField<Employee>(
                            decoration: const InputDecoration(
                              labelText: 'اختر الموظف الفني',
                              prefixIcon: Icon(Icons.engineering),
                              border: OutlineInputBorder(),
                            ),
                            value: _selectedTechnician,
                            items: _technicians.map((technician) {
                              return DropdownMenuItem<Employee>(
                                value: technician,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(technician.name),
                                    Text(
                                      '${technician.position} - ${technician.paymentType == PaymentType.daily ? 'أجر يومي' : 'راتب شهري'}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (Employee? value) {
                              setState(() {
                                _selectedTechnician = value;
                                _assignedToName = value?.name;
                                // Reset daily rate when technician changes
                                _technicianDailyRate = null;
                              });
                            },
                          ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Daily rate field (conditional)
                    if (_selectedTechnician != null && _selectedTechnician!.hasDailyWage) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.attach_money, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(
                              'أجر الموظف الفني اليومي',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        initialValue: _technicianDailyRate?.toString(),
                        decoration: InputDecoration(
                          labelText: 'أجر الموظف الفني لهذه الخدمة',
                          prefixIcon: const Icon(Icons.attach_money, color: Colors.green),
                          suffixText: 'ر.س',
                          border: const OutlineInputBorder(),
                          helperText: _selectedTechnician != null
                            ? 'الأجر المقترح: ${_selectedTechnician!.salary.toStringAsFixed(0)} ر.س'
                            : 'يتم تحديد الأجر لكل خدمة منفصلة',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        validator: (value) {
                          if (_selectedTechnician != null && _selectedTechnician!.hasDailyWage) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال أجر الموظف الفني اليومي';
                            }
                            final rate = double.tryParse(value);
                            if (rate == null || rate <= 0) {
                              return 'يرجى إدخال أجر صحيح أكبر من صفر';
                            }
                            if (rate > 1000) {
                              return 'الأجر اليومي يبدو مرتفعاً جداً (أكثر من 1000 ر.س)';
                            }
                          }
                          return null;
                        },
                        onSaved: (value) {
                          if (value != null && value.isNotEmpty) {
                            _technicianDailyRate = double.parse(value);
                          }
                        },
                      ),
                      if (_selectedTechnician != null && _selectedTechnician!.hasDailyWage)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: ElevatedButton.icon(
                            onPressed: () {
                              setState(() {
                                _technicianDailyRate = _selectedTechnician!.salary;
                              });
                            },
                            icon: const Icon(Icons.auto_fix_high, size: 16),
                            label: Text('استخدام الأجر المقترح (${_selectedTechnician!.salary.toStringAsFixed(0)} ر.س)'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade50,
                              foregroundColor: Colors.blue.shade700,
                              elevation: 0,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                          ),
                        ),
                    ],
                    const SizedBox(height: AppDimensions.paddingL),

                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveServiceRequest,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'حفظ التغييرات',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
