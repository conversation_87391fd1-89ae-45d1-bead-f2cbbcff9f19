                        -HC:\flutter_windows_3.29.1-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON>DROID_PLATFORM=android-23
-D<PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\food\HVAC Service Managers\build\app\intermediates\cxx\Debug\2z5p2e67\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\food\HVAC Service Managers\build\app\intermediates\cxx\Debug\2z5p2e67\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BD:\food\HVAC Service Managers\android\app\.cxx\Debug\2z5p2e67\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2