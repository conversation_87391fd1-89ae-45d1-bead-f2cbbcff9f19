import 'dart:convert';
import 'package:flutter/material.dart';

enum TransactionType {
  income,
  expense,
}

enum PaymentMethod {
  cash,
  bankTransfer,
  check,
  creditCard,
  other,
}

enum TransactionCategory {
  // Income categories
  sales,
  services,
  refunds,
  investments,
  otherIncome,

  // Expense categories
  purchases,
  salaries,
  rent,
  utilities,
  maintenance,
  fuel,
  tools,
  marketing,
  insurance,
  taxes,
  otherExpense,

  // Special categories
  supplierPayment,
}

enum ExpenseType {
  regularExpense,
  supplierPayment,
}

class Transaction {
  final int? id;
  final String reference;
  final TransactionType type;
  final double amount;
  final DateTime date;
  final String? category;
  final String? description;
  final PaymentMethod paymentMethod;
  final int? invoiceId;
  final int? customerId;
  final int? supplierId;
  final int? employeeId;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int? bankAccountId; // Added for database compatibility

  // Campos adicionales para mostrar información relacionada
  final String? customerName;
  final String? supplierName;
  final String? employeeName;
  final String? bankAccountName;

  Transaction({
    this.id,
    required this.reference,
    required this.type,
    required this.amount,
    required this.date,
    this.category,
    this.description,
    required this.paymentMethod,
    this.invoiceId,
    this.customerId,
    this.supplierId,
    this.employeeId,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.bankAccountId,
    this.customerName,
    this.supplierName,
    this.employeeName,
    this.bankAccountName,
  });

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] as int?,
      reference: map['reference'] as String,
      type: _parseType(map['type'] as String),
      amount: map['amount'] as double,
      date: DateTime.parse(map['date'] as String),
      category: map['category'] as String?,
      description: map['description'] as String?,
      paymentMethod: _parsePaymentMethod(map['payment_method'] as String),
      invoiceId: map['invoice_id'] as int?,
      customerId: map['customer_id'] as int?,
      supplierId: map['supplier_id'] as int?,
      employeeId: map['employee_id'] as int?,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      bankAccountId: map['bank_account_id'] as int?,
      customerName: map['customer_name'] as String?,
      supplierName: map['supplier_name'] as String?,
      employeeName: map['employee_name'] as String?,
      bankAccountName: map['bank_account_name'] as String?,
    );
  }

  factory Transaction.fromJson(String source) =>
      Transaction.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reference': reference,
      'type': type.toString().split('.').last,
      'amount': amount,
      'date': date.toIso8601String(),
      'category': category,
      'description': description,
      'payment_method': paymentMethod.toString().split('.').last,
      'invoice_id': invoiceId,
      'customer_id': customerId,
      'supplier_id': supplierId,
      'employee_id': employeeId,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'bank_account_id': bankAccountId,
      'customer_name': customerName,
      'supplier_name': supplierName,
      'employee_name': employeeName,
      'bank_account_name': bankAccountName,
    };
  }

  String toJson() => json.encode(toMap());

  Transaction copyWith({
    int? id,
    String? reference,
    TransactionType? type,
    double? amount,
    DateTime? date,
    String? category,
    String? description,
    PaymentMethod? paymentMethod,
    int? invoiceId,
    int? customerId,
    int? supplierId,
    int? employeeId,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? bankAccountId,
    String? customerName,
    String? supplierName,
    String? employeeName,
    String? bankAccountName,
  }) {
    return Transaction(
      id: id ?? this.id,
      reference: reference ?? this.reference,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      category: category ?? this.category,
      description: description ?? this.description,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      invoiceId: invoiceId ?? this.invoiceId,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      employeeId: employeeId ?? this.employeeId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      customerName: customerName ?? this.customerName,
      supplierName: supplierName ?? this.supplierName,
      employeeName: employeeName ?? this.employeeName,
      bankAccountName: bankAccountName ?? this.bankAccountName,
    );
  }

  static TransactionType _parseType(String type) {
    switch (type.toLowerCase()) {
      case 'income':
        return TransactionType.income;
      case 'expense':
        return TransactionType.expense;
      default:
        return TransactionType.expense;
    }
  }

  static PaymentMethod _parsePaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'bank_transfer':
      case 'banktransfer':
        return PaymentMethod.bankTransfer;
      case 'check':
        return PaymentMethod.check;
      case 'credit_card':
      case 'creditcard':
        return PaymentMethod.creditCard;
      case 'other':
      default:
        return PaymentMethod.other;
    }
  }

  static String getTypeName(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return 'إيراد';
      case TransactionType.expense:
        return 'مصروف';
    }
  }

  static Color getTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
    }
  }

  static String getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقداً';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethod.other:
        return 'أخرى';
    }
  }

  static IconData getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.check:
        return Icons.note;
      case PaymentMethod.creditCard:
        return Icons.credit_card;
      case PaymentMethod.other:
        return Icons.more_horiz;
    }
  }

  static String getCategoryName(TransactionCategory category) {
    switch (category) {
      // Income categories
      case TransactionCategory.sales:
        return 'مبيعات';
      case TransactionCategory.services:
        return 'خدمات';
      case TransactionCategory.refunds:
        return 'استرداد';
      case TransactionCategory.investments:
        return 'استثمارات';
      case TransactionCategory.otherIncome:
        return 'إيرادات أخرى';

      // Expense categories
      case TransactionCategory.purchases:
        return 'مشتريات';
      case TransactionCategory.salaries:
        return 'رواتب';
      case TransactionCategory.rent:
        return 'إيجار';
      case TransactionCategory.utilities:
        return 'مرافق';
      case TransactionCategory.maintenance:
        return 'صيانة';
      case TransactionCategory.fuel:
        return 'وقود';
      case TransactionCategory.tools:
        return 'أدوات';
      case TransactionCategory.marketing:
        return 'تسويق';
      case TransactionCategory.insurance:
        return 'تأمين';
      case TransactionCategory.taxes:
        return 'ضرائب';
      case TransactionCategory.otherExpense:
        return 'مصروفات أخرى';

      // Special categories
      case TransactionCategory.supplierPayment:
        return 'سداد للمورد';
    }
  }

  static String getExpenseTypeName(ExpenseType type) {
    switch (type) {
      case ExpenseType.regularExpense:
        return 'مصروفات';
      case ExpenseType.supplierPayment:
        return 'سداد للمورد';
    }
  }

  static IconData getCategoryIcon(TransactionCategory category) {
    switch (category) {
      // Income categories
      case TransactionCategory.sales:
        return Icons.shopping_cart;
      case TransactionCategory.services:
        return Icons.miscellaneous_services;
      case TransactionCategory.refunds:
        return Icons.assignment_return;
      case TransactionCategory.investments:
        return Icons.trending_up;
      case TransactionCategory.otherIncome:
        return Icons.attach_money;

      // Expense categories
      case TransactionCategory.purchases:
        return Icons.shopping_bag;
      case TransactionCategory.salaries:
        return Icons.people;
      case TransactionCategory.rent:
        return Icons.home;
      case TransactionCategory.utilities:
        return Icons.power;
      case TransactionCategory.maintenance:
        return Icons.build;
      case TransactionCategory.fuel:
        return Icons.local_gas_station;
      case TransactionCategory.tools:
        return Icons.handyman;
      case TransactionCategory.marketing:
        return Icons.campaign;
      case TransactionCategory.insurance:
        return Icons.security;
      case TransactionCategory.taxes:
        return Icons.account_balance;
      case TransactionCategory.otherExpense:
        return Icons.money_off;

      // Special categories
      case TransactionCategory.supplierPayment:
        return Icons.payment;
    }
  }

  static IconData getExpenseTypeIcon(ExpenseType type) {
    switch (type) {
      case ExpenseType.regularExpense:
        return Icons.money_off;
      case ExpenseType.supplierPayment:
        return Icons.payment;
    }
  }

  static Color getCategoryColor(TransactionCategory category) {
    switch (category) {
      // Income categories
      case TransactionCategory.sales:
        return Colors.green;
      case TransactionCategory.services:
        return Colors.teal;
      case TransactionCategory.refunds:
        return Colors.lightGreen;
      case TransactionCategory.investments:
        return Colors.greenAccent;
      case TransactionCategory.otherIncome:
        return Colors.lightGreen;

      // Expense categories
      case TransactionCategory.purchases:
        return Colors.red;
      case TransactionCategory.salaries:
        return Colors.blue;
      case TransactionCategory.rent:
        return Colors.purple;
      case TransactionCategory.utilities:
        return Colors.orange;
      case TransactionCategory.maintenance:
        return Colors.amber;
      case TransactionCategory.fuel:
        return Colors.deepOrange;
      case TransactionCategory.tools:
        return Colors.brown;
      case TransactionCategory.marketing:
        return Colors.pink;
      case TransactionCategory.insurance:
        return Colors.indigo;
      case TransactionCategory.taxes:
        return Colors.blueGrey;
      case TransactionCategory.otherExpense:
        return Colors.redAccent;

      // Special categories
      case TransactionCategory.supplierPayment:
        return Colors.deepPurple;
    }
  }

  static Color getExpenseTypeColor(ExpenseType type) {
    switch (type) {
      case ExpenseType.regularExpense:
        return Colors.red;
      case ExpenseType.supplierPayment:
        return Colors.deepPurple;
    }
  }

  static bool isIncomeCategory(TransactionCategory category) {
    return category == TransactionCategory.sales ||
           category == TransactionCategory.services ||
           category == TransactionCategory.refunds ||
           category == TransactionCategory.investments ||
           category == TransactionCategory.otherIncome;
  }

  static bool isExpenseCategory(TransactionCategory category) {
    return !isIncomeCategory(category) && category != TransactionCategory.supplierPayment;
  }

  static bool isSpecialCategory(TransactionCategory category) {
    return category == TransactionCategory.supplierPayment;
  }

  static List<TransactionCategory> getIncomeCategories() {
    return [
      TransactionCategory.sales,
      TransactionCategory.services,
      TransactionCategory.refunds,
      TransactionCategory.investments,
      TransactionCategory.otherIncome,
    ];
  }

  static List<TransactionCategory> getExpenseCategories() {
    return [
      TransactionCategory.purchases,
      TransactionCategory.salaries,
      TransactionCategory.rent,
      TransactionCategory.utilities,
      TransactionCategory.maintenance,
      TransactionCategory.fuel,
      TransactionCategory.tools,
      TransactionCategory.marketing,
      TransactionCategory.insurance,
      TransactionCategory.taxes,
      TransactionCategory.otherExpense,
    ];
  }
}
