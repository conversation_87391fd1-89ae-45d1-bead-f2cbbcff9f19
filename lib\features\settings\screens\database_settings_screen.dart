import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../core/database/database_helper.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/custom_button.dart';

class DatabaseSettingsScreen extends StatefulWidget {
  const DatabaseSettingsScreen({super.key});

  @override
  State<DatabaseSettingsScreen> createState() => _DatabaseSettingsScreenState();
}

class _DatabaseSettingsScreenState extends State<DatabaseSettingsScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  String _currentDbPath = '';
  bool _isLoading = true;
  bool _isSaving = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
    _loadDatabasePath();
  }

  Future<void> _loadDatabasePath() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final dbPath = await _dbHelper.getDatabasePath();
      setState(() {
        _currentDbPath = dbPath;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل مسار قاعدة البيانات: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDatabasePath() async {
    try {
      // Show a dialog with predefined paths
      final selectedPath = await _showDirectorySelectionDialog();

      if (selectedPath == null) {
        // User canceled the selection
        return;
      }

      // Check if directory is writable
      try {
        final directory = Directory(selectedPath);
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }

        final testFile = File(path.join(selectedPath, 'test_write.tmp'));
        await testFile.writeAsString('test');
        await testFile.delete();
      } catch (e) {
        setState(() {
          _errorMessage = 'لا يمكن الكتابة في المجلد المحدد. الرجاء اختيار مجلد آخر.';
        });
        return;
      }

      // Create the full database path
      final newDbPath = path.join(selectedPath, 'hvac_manager.db');

      setState(() {
        _currentDbPath = newDbPath;
        _errorMessage = null;
        _successMessage = 'تم اختيار المجلد بنجاح';
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء اختيار المجلد: $e';
      });
    }
  }

  Future<String?> _showDirectorySelectionDialog() async {
    // Get available directories
    final List<Directory> availableDirectories = await _getAvailableDirectories();

    if (!mounted) return null;

    return showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر مجلد لحفظ قاعدة البيانات'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableDirectories.length,
              itemBuilder: (context, index) {
                final directory = availableDirectories[index];
                final directoryPath = directory.path;
                final directoryName = directoryPath.split(Platform.isWindows ? '\\' : '/').last;
                final isExternalStorage = directoryPath.contains('storage/emulated') ||
                                         directoryPath.contains('storage/sdcard') ||
                                         directoryPath.contains('Android/data');

                return ListTile(
                  leading: Icon(
                    isExternalStorage ? Icons.sd_storage : Icons.folder,
                    color: AppColors.primary,
                  ),
                  title: Text(isExternalStorage ? 'التخزين الخارجي' : directoryName),
                  subtitle: Text(
                    directoryPath,
                    style: const TextStyle(fontSize: 12),
                  ),
                  onTap: () {
                    Navigator.pop(context, directoryPath);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  Future<List<Directory>> _getAvailableDirectories() async {
    final List<Directory> directories = [];

    try {
      // Add application documents directory
      try {
        final appDocDir = await getApplicationDocumentsDirectory();
        directories.add(appDocDir);
      } catch (e) {
        if (kDebugMode) {
          print('Error getting app documents directory: $e');
        }
      }

      // Add external storage directory (Android)
      try {
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          directories.add(externalDir);
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error getting external storage directory: $e');
        }
      }

      // Add downloads directory if available
      try {
        final downloadsDir = await getDownloadsDirectory();
        if (downloadsDir != null) {
          directories.add(downloadsDir);
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error getting downloads directory: $e');
        }
      }

      // Add temporary directory
      try {
        final tempDir = await getTemporaryDirectory();
        directories.add(tempDir);
      } catch (e) {
        if (kDebugMode) {
          print('Error getting temporary directory: $e');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available directories: $e');
      }
    }

    return directories;
  }

  /// تصدير قاعدة البيانات الحالية إلى ملف خارجي
  Future<void> _exportDatabase() async {
    setState(() {
      _isSaving = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // الحصول على مسار قاعدة البيانات الحالية
      final currentDbPath = await _dbHelper.getDatabasePath();
      final currentDbFile = File(currentDbPath);

      // التحقق من وجود قاعدة البيانات
      if (!await currentDbFile.exists()) {
        setState(() {
          _errorMessage = 'لا توجد قاعدة بيانات للتصدير';
          _isSaving = false;
        });
        return;
      }

      // اختيار مجلد الحفظ
      String? selectedDirectory = await _showDirectorySelectionDialog();
      if (selectedDirectory == null) {
        // المستخدم ألغى العملية
        setState(() {
          _isSaving = false;
        });
        return;
      }

      // إنشاء اسم ملف النسخة الاحتياطية مع التاريخ والوقت
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').replaceAll('.', '-');
      final backupFileName = 'hvac_manager_backup_$timestamp.db';
      final backupFilePath = path.join(selectedDirectory, backupFileName);

      // نسخ ملف قاعدة البيانات
      await currentDbFile.copy(backupFilePath);

      setState(() {
        _successMessage = 'تم تصدير قاعدة البيانات بنجاح إلى:\n$backupFilePath';
        _isSaving = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تصدير قاعدة البيانات: $e';
        _isSaving = false;
      });
    }
  }

  /// استيراد قاعدة بيانات من ملف خارجي
  Future<void> _importDatabase() async {
    setState(() {
      _isSaving = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // عرض رسالة تأكيد قبل الاستيراد
      if (!mounted) return;
      final bool? confirmImport = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد استيراد قاعدة البيانات'),
          content: const Text(
            'سيؤدي استيراد قاعدة بيانات جديدة إلى استبدال البيانات الحالية. هل تريد المتابعة؟\n\nملاحظة: سيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستيراد.',
            style: TextStyle(color: Colors.red),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('متابعة'),
            ),
          ],
        ),
      );

      if (confirmImport != true) {
        if (!mounted) return;
        setState(() {
          _isSaving = false;
        });
        return;
      }

      // اختيار ملف قاعدة البيانات للاستيراد
      final importFilePath = await _showDatabaseFileSelectionDialog();
      if (importFilePath == null) {
        // المستخدم ألغى العملية
        if (!mounted) return;
        setState(() {
          _isSaving = false;
        });
        return;
      }

      // الحصول على مسار قاعدة البيانات الحالية
      final currentDbPath = await _dbHelper.getDatabasePath();
      final currentDbFile = File(currentDbPath);

      // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستيراد
      if (await currentDbFile.exists()) {
        final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').replaceAll('.', '-');
        final backupFileName = 'hvac_manager_auto_backup_$timestamp.db';
        final backupDir = await getApplicationDocumentsDirectory();
        final backupFilePath = path.join(backupDir.path, backupFileName);
        await currentDbFile.copy(backupFilePath);
      }

      // إغلاق قاعدة البيانات الحالية
      await _dbHelper.closeDatabase();

      // نسخ ملف قاعدة البيانات المستوردة
      await File(importFilePath).copy(currentDbPath);

      if (!mounted) return;
      setState(() {
        _successMessage = 'تم استيراد قاعدة البيانات بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.';
        _isSaving = false;
      });

      // تحديث مسار قاعدة البيانات الحالي في واجهة المستخدم
      await _loadDatabasePath();
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = 'حدث خطأ أثناء استيراد قاعدة البيانات: $e';
        _isSaving = false;
      });
    }
  }

  /// عرض مربع حوار لاختيار ملف قاعدة البيانات
  Future<String?> _showDatabaseFileSelectionDialog() async {
    // الحصول على المجلدات المتاحة
    final List<Directory> availableDirectories = await _getAvailableDirectories();

    if (!mounted) return null;

    // عرض مربع حوار لاختيار المجلد أولاً
    final selectedDirectory = await showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر المجلد الذي يحتوي على ملف قاعدة البيانات'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableDirectories.length,
              itemBuilder: (context, index) {
                final directory = availableDirectories[index];
                final directoryPath = directory.path;
                final directoryName = directoryPath.split(Platform.isWindows ? '\\' : '/').last;
                final isExternalStorage = directoryPath.contains('storage/emulated') ||
                                         directoryPath.contains('storage/sdcard') ||
                                         directoryPath.contains('Android/data');

                return ListTile(
                  leading: Icon(
                    isExternalStorage ? Icons.sd_storage : Icons.folder,
                    color: AppColors.primary,
                  ),
                  title: Text(isExternalStorage ? 'التخزين الخارجي' : directoryName),
                  subtitle: Text(
                    directoryPath,
                    style: const TextStyle(fontSize: 12),
                  ),
                  onTap: () {
                    Navigator.pop(context, directoryPath);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );

    if (selectedDirectory == null) {
      return null;
    }

    // البحث عن ملفات قاعدة البيانات في المجلد المحدد
    final directory = Directory(selectedDirectory);
    final List<FileSystemEntity> files = await directory.list().toList();
    final List<File> dbFiles = files
        .whereType<File>()
        .where((file) => file.path.endsWith('.db'))
        .toList();

    if (dbFiles.isEmpty) {
      // عرض رسالة إذا لم يتم العثور على ملفات قاعدة بيانات
      if (!mounted) return null;

      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('لا توجد ملفات قاعدة بيانات'),
          content: const Text('لم يتم العثور على ملفات قاعدة بيانات في المجلد المحدد.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );

      return null;
    }

    if (!mounted) return null;

    // عرض مربع حوار لاختيار ملف قاعدة البيانات
    return showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر ملف قاعدة البيانات'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: dbFiles.length,
              itemBuilder: (context, index) {
                final file = dbFiles[index];
                final fileName = file.path.split(Platform.isWindows ? '\\' : '/').last;

                return ListTile(
                  leading: const Icon(
                    Icons.storage,
                    color: AppColors.primary,
                  ),
                  title: Text(fileName),
                  subtitle: Text(
                    'آخر تعديل: ${DateFormat('yyyy/MM/dd HH:mm').format(file.statSync().modified)}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  onTap: () {
                    Navigator.pop(context, file.path);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  /// إنشاء قاعدة بيانات جديدة
  Future<void> _createNewDatabase() async {
    setState(() {
      _isSaving = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // عرض رسالة تأكيد قبل إنشاء قاعدة بيانات جديدة
      if (!mounted) return;
      final String? confirmOption = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد إنشاء قاعدة بيانات جديدة'),
          content: const Text(
            'سيؤدي إنشاء قاعدة بيانات جديدة إلى حذف جميع البيانات الحالية. ماذا تريد أن تفعل؟',
            style: TextStyle(color: Colors.red),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, 'cancel'),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, 'backup_create'),
              child: const Text('إنشاء نسخة احتياطية ثم إنشاء قاعدة بيانات جديدة'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, 'create'),
              child: const Text('إنشاء قاعدة بيانات جديدة بدون نسخة احتياطية'),
            ),
          ],
        ),
      );

      if (confirmOption == 'cancel' || confirmOption == null) {
        setState(() {
          _isSaving = false;
        });
        return;
      }

      // الحصول على مسار قاعدة البيانات الحالية
      final currentDbPath = await _dbHelper.getDatabasePath();
      final currentDbFile = File(currentDbPath);

      // إنشاء نسخة احتياطية إذا طلب المستخدم ذلك
      if (confirmOption == 'backup_create' && await currentDbFile.exists()) {
        // اختيار مجلد الحفظ للنسخة الاحتياطية
        if (!mounted) return;
        final selectedDirectory = await _showDirectorySelectionDialog();

        if (selectedDirectory == null) {
          // المستخدم ألغى اختيار المجلد، نلغي العملية بالكامل
          setState(() {
            _isSaving = false;
          });
          return;
        }

        final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').replaceAll('.', '-');
        final backupFileName = 'hvac_manager_backup_before_reset_$timestamp.db';
        final backupFilePath = path.join(selectedDirectory, backupFileName);
        await currentDbFile.copy(backupFilePath);
      }

      // إغلاق قاعدة البيانات الحالية
      await _dbHelper.closeDatabase();

      // حذف ملف قاعدة البيانات الحالي إذا كان موجودًا
      if (await currentDbFile.exists()) {
        await currentDbFile.delete();
      }

      // إعادة تهيئة قاعدة البيانات (سيتم إنشاؤها تلقائيًا عند الوصول إليها)
      await _dbHelper.resetDatabase();

      if (!mounted) return;
      setState(() {
        _successMessage = 'تم إنشاء قاعدة بيانات جديدة بنجاح.';
        _isSaving = false;
      });

      // تحديث مسار قاعدة البيانات الحالي في واجهة المستخدم
      await _loadDatabasePath();
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = 'حدث خطأ أثناء إنشاء قاعدة بيانات جديدة: $e';
        _isSaving = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isSaving = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Check if the directory exists
      final directory = Directory(path.dirname(_currentDbPath));
      if (!await directory.exists()) {
        // Create the directory if it doesn't exist
        await directory.create(recursive: true);
      }

      // Save the new database path
      final success = await _dbHelper.setDatabasePath(_currentDbPath);

      if (success) {
        setState(() {
          _successMessage = 'تم حفظ إعدادات قاعدة البيانات بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.';
          _isSaving = false;
        });
      } else {
        setState(() {
          _errorMessage = 'فشل في حفظ إعدادات قاعدة البيانات';
          _isSaving = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء حفظ الإعدادات: $e';
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات قاعدة البيانات'),
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Card(
                    child: Padding(
                      padding: EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إعدادات قاعدة البيانات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: AppDimensions.paddingS),
                          Text(
                            'يمكنك تحديد مسار مخصص لقاعدة البيانات. هذا يسمح لك بتخزين البيانات في مكان آخر غير المجلد الافتراضي للتطبيق.',
                            style: TextStyle(fontSize: 14),
                          ),
                          SizedBox(height: AppDimensions.paddingS),
                          Text(
                            'ملاحظة: بعد تغيير مسار قاعدة البيانات، سيتم إنشاء قاعدة بيانات جديدة في المسار الجديد. إذا كنت ترغب في نقل البيانات الحالية، يجب عليك نسخ ملف قاعدة البيانات يدويًا.',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.paddingL),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مسار قاعدة البيانات الحالي:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppDimensions.paddingS),
                          Container(
                            padding: const EdgeInsets.all(AppDimensions.paddingS),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            width: double.infinity,
                            child: Text(
                              _currentDbPath,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 14,
                              ),
                            ),
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                          Row(
                            children: [
                              Expanded(
                                child: CustomButton(
                                  text: 'تحديد المسار',
                                  icon: Icons.folder_open,
                                  onPressed: _selectDatabasePath,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                          const Text(
                            'إدارة قاعدة البيانات:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppDimensions.paddingS),
                          Row(
                            children: [
                              Expanded(
                                child: CustomButton(
                                  text: 'تصدير قاعدة البيانات',
                                  icon: Icons.upload_file,
                                  onPressed: _exportDatabase,
                                  backgroundColor: Colors.green[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingS),
                          Row(
                            children: [
                              Expanded(
                                child: CustomButton(
                                  text: 'استيراد قاعدة البيانات',
                                  icon: Icons.download_rounded,
                                  onPressed: _importDatabase,
                                  backgroundColor: Colors.blue[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingS),
                          Row(
                            children: [
                              Expanded(
                                child: CustomButton(
                                  text: 'إنشاء قاعدة بيانات جديدة',
                                  icon: Icons.add_circle_outline,
                                  onPressed: _createNewDatabase,
                                  backgroundColor: Colors.red[700],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_errorMessage != null) ...[
                    const SizedBox(height: AppDimensions.paddingM),
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.red),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.red),
                          const SizedBox(width: AppDimensions.paddingS),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  if (_successMessage != null) ...[
                    const SizedBox(height: AppDimensions.paddingM),
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.green),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green),
                          const SizedBox(width: AppDimensions.paddingS),
                          Expanded(
                            child: Text(
                              _successMessage!,
                              style: const TextStyle(color: Colors.green),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  const SizedBox(height: AppDimensions.paddingL),
                  CustomButton(
                    text: 'حفظ الإعدادات',
                    isLoading: _isSaving,
                    onPressed: _saveSettings,
                  ),
                ],
              ),
            ),
    );
  }
}
