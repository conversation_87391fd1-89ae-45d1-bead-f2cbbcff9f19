# دليل إعداد Firebase لتطبيق ركن الجليد

## 1. إعداد Firebase Console

### الخطوة 1: الوصول إلى Firebase Console
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر المشروع `fir-db-40fbe`
3. إذا لم يكن المشروع موجوداً، أنشئ مشروع جديد

### الخطوة 2: تفعيل Authentication
1. في القائمة الجانبية، اختر **Authentication**
2. اذهب إلى تبويب **Sign-in method**
3. فعّل **Email/Password** provider:
   - انقر على Email/Password
   - فعّل "Enable"
   - احفظ التغييرات

### الخطوة 3: إنشاء مستخدم إداري
1. اذهب إلى تبويب **Users**
2. انقر **Add user**
3. أدخل البيانات:
   - Email: `<EMAIL>`
   - Password: `Admin123!`
4. احفظ المستخدم

## 2. إعداد Firestore Database

### الخطوة 1: إنشاء قاعدة البيانات
1. في القائمة الجانبية، اختر **Firestore Database**
2. انقر **Create database**
3. اختر **Start in test mode** (للتطوير)
4. اختر المنطقة: `us-central1`

### الخطوة 2: إعداد قواعد الأمان (للتطوير)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all documents for authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Allow read access to settings for all users
    match /settings/{document} {
      allow read: if true;
    }
  }
}
```

### الخطوة 3: إنشاء المجموعات الأساسية
سيتم إنشاؤها تلقائياً عند تشغيل التطبيق، أو يمكنك إنشاؤها يدوياً:

#### مجموعة `users`
```json
{
  "admin": {
    "email": "<EMAIL>",
    "fullName": "مدير النظام",
    "role": "admin",
    "phoneNumber": "0501234567",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "permissions": ["all"]
  }
}
```

#### مجموعة `settings`
```json
{
  "app_config": {
    "appName": "ركن الجليد للتكييف والتبريد",
    "version": "1.0.0",
    "defaultTaxRate": 15.0,
    "currency": "SAR",
    "language": "ar",
    "theme": "light",
    "notificationsEnabled": true,
    "backupEnabled": true
  }
}
```

## 3. إعداد Firebase Storage (اختياري)

### الخطوة 1: تفعيل Storage
1. في القائمة الجانبية، اختر **Storage**
2. انقر **Get started**
3. اقبل قواعد الأمان الافتراضية

### الخطوة 2: إعداد قواعد Storage
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 4. إعداد الفهارس (Indexes)

### فهارس مطلوبة للاستعلامات:

#### للعملاء:
- Collection: `customers`
- Fields: `isActive` (Ascending), `createdAt` (Descending)

#### لطلبات الخدمة:
- Collection: `service_requests`
- Fields: `status` (Ascending), `priority` (Ascending), `scheduledDate` (Ascending)

#### للفواتير:
- Collection: `invoices`
- Fields: `status` (Ascending), `dueDate` (Ascending)

#### للمخزون:
- Collection: `inventory_items`
- Fields: `category` (Ascending), `quantity` (Ascending)

## 5. اختبار الإعداد

### من التطبيق:
1. شغّل التطبيق
2. اذهب إلى الإعدادات
3. انقر على "اختبار Firebase"
4. شغّل جميع الاختبارات

### من Firebase Console:
1. تحقق من وجود البيانات في Firestore
2. تحقق من المستخدمين في Authentication
3. راقب الأنشطة في Usage tab

## 6. إعدادات الأمان للإنتاج

### قواعد Firestore للإنتاج:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Admin and managers can access all data
    match /{document=**} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }
    
    // Technicians can read customers and service requests
    match /customers/{document} {
      allow read: if request.auth != null;
    }
    
    match /service_requests/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Settings are read-only for all authenticated users
    match /settings/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

## 7. النسخ الاحتياطي والمراقبة

### إعداد النسخ الاحتياطي:
1. اذهب إلى **Firestore Database**
2. انقر على **Backups**
3. أنشئ جدولة نسخ احتياطية يومية

### مراقبة الأداء:
1. فعّل **Performance Monitoring**
2. فعّل **Crashlytics**
3. راقب **Usage and billing**

## 8. متغيرات البيئة

### للتطوير:
```
FIREBASE_PROJECT_ID=fir-db-40fbe
FIREBASE_API_KEY=AIzaSyCgTVyKSIGnfs_C47WskP1RsTkZ35fI6a4
FIREBASE_AUTH_DOMAIN=fir-db-40fbe.firebaseapp.com
FIREBASE_STORAGE_BUCKET=fir-db-40fbe.appspot.com
```

### للإنتاج:
- استخدم مشروع Firebase منفصل
- فعّل قواعد الأمان المشددة
- راقب الاستخدام والتكاليف

## 9. استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ "operation-not-allowed":
- تأكد من تفعيل Email/Password في Authentication

#### خطأ "permission-denied":
- تحقق من قواعد Firestore
- تأكد من تسجيل دخول المستخدم

#### خطأ "network-request-failed":
- تحقق من الاتصال بالإنترنت
- تأكد من صحة إعدادات Firebase

#### خطأ "invalid-api-key":
- تحقق من ملفات التكوين
- تأكد من مطابقة معرف المشروع

## 10. الدعم والمساعدة

### الموارد:
- [Firebase Documentation](https://firebase.google.com/docs)
- [FlutterFire Documentation](https://firebase.flutter.dev/)
- [Firebase Console](https://console.firebase.google.com/)

### الاتصال:
- للدعم التقني: Firebase Support
- للمساعدة في التطوير: Flutter Community
