import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/ui_components.dart';
import '../../../core/repositories/profitability_analysis_repository.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../shared/models/profitability_analysis.dart';
import '../../../shared/models/service_request.dart' as service_models;
import '../utils/paginated_pdf_export.dart';

/// شاشة تحليل ربحية الخدمات
class ServiceProfitabilityScreen extends StatefulWidget {
  const ServiceProfitabilityScreen({super.key});

  @override
  State<ServiceProfitabilityScreen> createState() => _ServiceProfitabilityScreenState();
}

class _ServiceProfitabilityScreenState extends State<ServiceProfitabilityScreen> {
  final ProfitabilityAnalysisRepository _profitabilityRepository = ProfitabilityAnalysisRepository();
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();

  bool _isLoading = true;
  String _selectedPeriod = 'monthly'; // monthly, quarterly, yearly
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // بيانات التقرير
  List<ProfitabilityAnalysis> _profitabilityData = [];
  List<service_models.ServiceRequest> _serviceRequests = [];

  // بيانات الرسم البياني
  List<PieChartSectionData> _profitabilitySections = [];

  // تصنيف الخدمات حسب الربحية
  List<ProfitabilityAnalysis> _highProfitabilityServices = [];
  List<ProfitabilityAnalysis> _mediumProfitabilityServices = [];
  List<ProfitabilityAnalysis> _lowProfitabilityServices = [];

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  /// تحميل بيانات التقرير
  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث نطاق التاريخ بناءً على الفترة المحددة
      _updateDateRange();

      // تحميل بيانات تحليل الربحية
      _profitabilityData = await _profitabilityRepository.getProfitabilityAnalysesByEntityType('service');

      // تحميل طلبات الخدمة
      _serviceRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(
        _startDate,
        _endDate,
      );

      // تحليل البيانات
      _analyzeData();

      // إعداد بيانات الرسم البياني
      _prepareChartData();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// تحديث نطاق التاريخ بناءً على الفترة المحددة
  void _updateDateRange() {
    final now = DateTime.now();

    switch (_selectedPeriod) {
      case 'monthly':
        _startDate = DateTime(now.year, now.month - 1, 1);
        _endDate = DateTime(now.year, now.month, 0);
        break;
      case 'quarterly':
        final currentQuarter = (now.month - 1) ~/ 3;
        _startDate = DateTime(now.year, currentQuarter * 3 + 1, 1);
        _endDate = DateTime(now.year, (currentQuarter + 1) * 3 + 1, 0);
        break;
      case 'yearly':
        _startDate = DateTime(now.year, 1, 1);
        _endDate = DateTime(now.year, 12, 31);
        break;
    }
  }

  /// تحليل البيانات
  void _analyzeData() {
    // إعادة تعيين القوائم
    _highProfitabilityServices = [];
    _mediumProfitabilityServices = [];
    _lowProfitabilityServices = [];

    // تصنيف الخدمات حسب هامش الربح
    for (final analysis in _profitabilityData) {
      if (analysis.profitMargin >= 30) {
        _highProfitabilityServices.add(analysis);
      } else if (analysis.profitMargin >= 15) {
        _mediumProfitabilityServices.add(analysis);
      } else {
        _lowProfitabilityServices.add(analysis);
      }
    }

    // ترتيب القوائم حسب هامش الربح (تنازلياً)
    _highProfitabilityServices.sort((a, b) => b.profitMargin.compareTo(a.profitMargin));
    _mediumProfitabilityServices.sort((a, b) => b.profitMargin.compareTo(a.profitMargin));
    _lowProfitabilityServices.sort((a, b) => b.profitMargin.compareTo(a.profitMargin));
  }

  /// إعداد بيانات الرسم البياني
  void _prepareChartData() {
    _profitabilitySections = [];

    // حساب إجمالي الإيرادات لكل فئة
    double highProfitabilityRevenue = 0;
    double mediumProfitabilityRevenue = 0;
    double lowProfitabilityRevenue = 0;

    for (final analysis in _highProfitabilityServices) {
      highProfitabilityRevenue += analysis.totalRevenue;
    }

    for (final analysis in _mediumProfitabilityServices) {
      mediumProfitabilityRevenue += analysis.totalRevenue;
    }

    for (final analysis in _lowProfitabilityServices) {
      lowProfitabilityRevenue += analysis.totalRevenue;
    }

    // إضافة أقسام الرسم البياني
    if (highProfitabilityRevenue > 0) {
      _profitabilitySections.add(
        PieChartSectionData(
          value: highProfitabilityRevenue,
          title: 'عالية\n${(highProfitabilityRevenue / (highProfitabilityRevenue + mediumProfitabilityRevenue + lowProfitabilityRevenue) * 100).toStringAsFixed(1)}%',
          color: AppColors.success,
          radius: 100,
          titleStyle: AppTextStyles.labelMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    if (mediumProfitabilityRevenue > 0) {
      _profitabilitySections.add(
        PieChartSectionData(
          value: mediumProfitabilityRevenue,
          title: 'متوسطة\n${(mediumProfitabilityRevenue / (highProfitabilityRevenue + mediumProfitabilityRevenue + lowProfitabilityRevenue) * 100).toStringAsFixed(1)}%',
          color: AppColors.warning,
          radius: 100,
          titleStyle: AppTextStyles.labelMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    if (lowProfitabilityRevenue > 0) {
      _profitabilitySections.add(
        PieChartSectionData(
          value: lowProfitabilityRevenue,
          title: 'منخفضة\n${(lowProfitabilityRevenue / (highProfitabilityRevenue + mediumProfitabilityRevenue + lowProfitabilityRevenue) * 100).toStringAsFixed(1)}%',
          color: AppColors.error,
          radius: 100,
          titleStyle: AppTextStyles.labelMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحليل ربحية الخدمات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _exportToPdf,
            tooltip: 'تصدير إلى PDF',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPeriodSelector(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildProfitabilityChart(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildProfitabilityTables(),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return StyledCard(
      title: 'الفترة الزمنية',
      icon: Icons.date_range,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('شهري'),
                  value: 'monthly',
                  groupValue: _selectedPeriod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                    _loadReportData();
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('ربع سنوي'),
                  value: 'quarterly',
                  groupValue: _selectedPeriod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                    _loadReportData();
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('سنوي'),
                  value: 'yearly',
                  groupValue: _selectedPeriod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                    _loadReportData();
                  },
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'من: ${DateFormat('yyyy/MM/dd').format(_startDate)}',
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
                Expanded(
                  child: Text(
                    'إلى: ${DateFormat('yyyy/MM/dd').format(_endDate)}',
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitabilityChart() {
    return StyledCard(
      title: 'توزيع ربحية الخدمات',
      icon: Icons.pie_chart,
      child: SizedBox(
        height: 300,
        child: _profitabilitySections.isEmpty
            ? const Center(
                child: Text(
                  'لا توجد بيانات كافية لعرض الرسم البياني',
                  style: AppTextStyles.bodyMedium,
                ),
              )
            : PieChart(
                PieChartData(
                  sections: _profitabilitySections,
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                  startDegreeOffset: 180,
                ),
              ),
      ),
    );
  }

  Widget _buildProfitabilityTables() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تحليل ربحية الخدمات',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildProfitabilityTable(
          'الخدمات عالية الربحية (≥ 30%)',
          _highProfitabilityServices,
          AppColors.success,
        ),
        const SizedBox(height: AppDimensions.spacingL),
        _buildProfitabilityTable(
          'الخدمات متوسطة الربحية (15% - 29%)',
          _mediumProfitabilityServices,
          AppColors.warning,
        ),
        const SizedBox(height: AppDimensions.spacingL),
        _buildProfitabilityTable(
          'الخدمات منخفضة الربحية (< 15%)',
          _lowProfitabilityServices,
          AppColors.error,
        ),
      ],
    );
  }

  Widget _buildProfitabilityTable(
    String title,
    List<ProfitabilityAnalysis> data,
    Color color,
  ) {
    return StyledCard(
      title: title,
      child: data.isEmpty
          ? const Padding(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              child: Center(
                child: Text(
                  'لا توجد بيانات',
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            )
          : StyledDataTable(
              columns: const [
                'الخدمة',
                'الإيرادات',
                'التكاليف',
                'صافي الربح',
                'هامش الربح',
                'العائد على الاستثمار',
              ],
              rows: data.map((analysis) {
                return [
                  Text(analysis.entityName),
                  Text('${analysis.totalRevenue.toStringAsFixed(2)} ر.س'),
                  Text('${(analysis.directCosts + analysis.indirectCosts).toStringAsFixed(2)} ر.س'),
                  Text('${analysis.netProfit.toStringAsFixed(2)} ر.س'),
                  Row(
                    children: [
                      Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          color: _getProfitMarginColor(analysis.profitMargin),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text('${analysis.profitMargin.toStringAsFixed(1)}%'),
                    ],
                  ),
                  Text('${analysis.roi.toStringAsFixed(1)}%'),
                ];
              }).toList(),
            ),
    );
  }

  Color _getProfitMarginColor(double margin) {
    if (margin >= 30) {
      return AppColors.success;
    } else if (margin >= 15) {
      return AppColors.warning;
    } else {
      return AppColors.error;
    }
  }

  Future<void> _exportToPdf() async {
    try {
      // إنشاء بيانات الجدول
      final tableData = [
        // عنوان الجدول
        {
          'service': 'الخدمة',
          'revenue': 'الإيرادات',
          'costs': 'التكاليف',
          'profit': 'صافي الربح',
          'margin': 'هامش الربح',
          'roi': 'العائد على الاستثمار',
          'type': 'header',
        },

        // الخدمات عالية الربحية
        {
          'service': 'الخدمات عالية الربحية (≥ 30%)',
          'revenue': '',
          'costs': '',
          'profit': '',
          'margin': '',
          'roi': '',
          'type': 'category',
        },

        ..._highProfitabilityServices.map((analysis) => {
          'service': analysis.entityName,
          'revenue': analysis.totalRevenue,
          'costs': analysis.directCosts + analysis.indirectCosts,
          'profit': analysis.netProfit,
          'margin': analysis.profitMargin,
          'roi': analysis.roi,
          'type': 'high',
        }),

        // الخدمات متوسطة الربحية
        {
          'service': 'الخدمات متوسطة الربحية (15% - 29%)',
          'revenue': '',
          'costs': '',
          'profit': '',
          'margin': '',
          'roi': '',
          'type': 'category',
        },

        ..._mediumProfitabilityServices.map((analysis) => {
          'service': analysis.entityName,
          'revenue': analysis.totalRevenue,
          'costs': analysis.directCosts + analysis.indirectCosts,
          'profit': analysis.netProfit,
          'margin': analysis.profitMargin,
          'roi': analysis.roi,
          'type': 'medium',
        }),

        // الخدمات منخفضة الربحية
        {
          'service': 'الخدمات منخفضة الربحية (< 15%)',
          'revenue': '',
          'costs': '',
          'profit': '',
          'margin': '',
          'roi': '',
          'type': 'category',
        },

        ..._lowProfitabilityServices.map((analysis) => {
          'service': analysis.entityName,
          'revenue': analysis.totalRevenue,
          'costs': analysis.directCosts + analysis.indirectCosts,
          'profit': analysis.netProfit,
          'margin': analysis.profitMargin,
          'roi': analysis.roi,
          'type': 'low',
        }),
      ];

      // إنشاء بيانات الملخص
      final summaryData = {
        'highProfitabilityCount': _highProfitabilityServices.length,
        'mediumProfitabilityCount': _mediumProfitabilityServices.length,
        'lowProfitabilityCount': _lowProfitabilityServices.length,
        'totalServices': _highProfitabilityServices.length + _mediumProfitabilityServices.length + _lowProfitabilityServices.length,
      };

      // تصدير التقرير إلى PDF
      final pdfPath = await PaginatedPdfExport.exportServiceProfitabilityReport(
        context: context,
        title: 'تحليل ربحية الخدمات',
        startDate: _startDate,
        endDate: _endDate,
        tableData: tableData,
        summaryData: summaryData,
      );

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      if (pdfPath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $pdfPath'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
