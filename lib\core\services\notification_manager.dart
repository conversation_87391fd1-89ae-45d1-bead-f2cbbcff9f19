import 'package:flutter/foundation.dart';
import 'package:icecorner/core/services/notification_service.dart' as service;
import 'package:icecorner/core/repositories/service_request_repository.dart';
import 'package:icecorner/shared/models/service_request.dart';

// تهيئة مدير الإشعارات
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;

  NotificationManager._internal();

  // تهيئة مدير الإشعارات
  Future<void> init() async {
    // لا نحتاج إلى تهيئة خاصة هنا
    if (kDebugMode) {
      print('تم تهيئة مدير الإشعارات');
    }
  }

  // جدولة إشعارات للمواعيد القادمة
  Future<void> schedulePeriodicTask() async {
    await checkUpcomingServiceRequests();
  }

  // التحقق من المواعيد القادمة وجدولة إشعارات لها
  Future<void> checkUpcomingServiceRequests() async {
    try {
      final serviceRequestRepository = ServiceRequestRepository();
      final notificationService = service.NotificationService();

      // تهيئة خدمة الإشعارات
      await notificationService.init();

      // الحصول على طلبات الخدمة المجدولة للأيام القادمة
      final upcomingRequests = await serviceRequestRepository.getUpcomingServiceRequests();

      // جدولة إشعارات للمواعيد القادمة
      for (final request in upcomingRequests) {
        await notificationService.scheduleServiceRequestReminder(request);
      }

      if (kDebugMode) {
        print('تم جدولة إشعارات لـ ${upcomingRequests.length} مواعيد قادمة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء التحقق من المواعيد القادمة: $e');
      }
    }
  }
}

// إضافة طريقة مساعدة لجدولة إشعارات لطلب خدمة جديد
Future<void> scheduleNotificationsForServiceRequest(ServiceRequest serviceRequest) async {
  try {
    final notificationService = service.NotificationService();
    await notificationService.init();

    // إلغاء أي إشعارات سابقة لنفس طلب الخدمة (في حالة التعديل)
    await notificationService.cancelServiceRequestNotifications(serviceRequest);

    // جدولة الإشعارات الجديدة
    await notificationService.scheduleServiceRequestReminder(serviceRequest);

    if (kDebugMode) {
      print('تم جدولة إشعارات متعددة لطلب الخدمة: ${serviceRequest.reference}');

      // تحويل الدقائق إلى نص مناسب للعرض
      String reminderTimeText = '';
      if (serviceRequest.reminderMinutes >= 60) {
        final int hours = serviceRequest.reminderMinutes ~/ 60;
        final int minutes = serviceRequest.reminderMinutes % 60;
        if (minutes == 0) {
          reminderTimeText = '$hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
        } else {
          reminderTimeText = '$hours ${hours == 1 ? 'ساعة' : 'ساعات'} و $minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
        }
      } else {
        reminderTimeText = '${serviceRequest.reminderMinutes} ${serviceRequest.reminderMinutes == 1 ? 'دقيقة' : 'دقائق'}';
      }

      print('التذكير الرئيسي قبل الموعد بـ: $reminderTimeText');
      print('موعد الزيارة: ${serviceRequest.scheduledDate}');
    }
  } catch (e) {
    if (kDebugMode) {
      print('خطأ في جدولة الإشعارات: $e');
    }
  }
}
