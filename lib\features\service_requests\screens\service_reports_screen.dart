import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_request.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../features/reports/utils/simple_pdf_export.dart';

class ServiceReportsScreen extends StatefulWidget {
  const ServiceReportsScreen({super.key});

  @override
  State<ServiceReportsScreen> createState() => _ServiceReportsScreenState();
}

class _ServiceReportsScreenState extends State<ServiceReportsScreen> with SingleTickerProviderStateMixin {
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();

  bool _isLoading = true;

  // بيانات التقارير
  List<ServiceRequest> _serviceRequests = [];
  List<Employee> _technicians = [];

  // فلترة التقارير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  Employee? _selectedTechnician;
  ServiceRequestStatus? _statusFilter;
  ServiceRequestType? _typeFilter;

  // تبويبات التقارير
  late TabController _tabController;

  // إحصائيات
  int _totalRequests = 0;
  int _completedRequests = 0;
  int _pendingRequests = 0;
  int _cancelledRequests = 0;
  Map<String, int> _requestsByType = {};
  Map<String, int> _requestsByTechnician = {};
  Map<String, int> _commonProblems = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل طلبات الخدمة
      final requests = await _serviceRequestRepository.getAllServiceRequests();

      // تحميل الموظف الفنيين
      final technicians = await _employeeRepository.getAllEmployees();
      final techniciansList = technicians.where((emp) => emp.position.toLowerCase().contains('فني')).toList();

      // تصفية طلبات الخدمة حسب التاريخ
      final filteredRequests = requests.where((request) {
        return request.createdAt.isAfter(_startDate) &&
               request.createdAt.isBefore(_endDate.add(const Duration(days: 1)));
      }).toList();

      // حساب الإحصائيات
      final requestsByType = <String, int>{};
      final requestsByTechnician = <String, int>{};
      final commonProblems = <String, int>{};

      int completed = 0;
      int pending = 0;
      int cancelled = 0;

      for (final request in filteredRequests) {
        // حساب حسب الحالة
        if (request.status == ServiceRequestStatus.completed) {
          completed++;
        } else if (request.status == ServiceRequestStatus.pending ||
                  request.status == ServiceRequestStatus.inProgress) {
          pending++;
        } else if (request.status == ServiceRequestStatus.cancelled) {
          cancelled++;
        }

        // حساب حسب النوع
        final typeName = ServiceRequest.getRequestTypeName(request.requestType);
        requestsByType[typeName] = (requestsByType[typeName] ?? 0) + 1;

        // حساب حسب الموظف الفني
        if (request.assignedToName != null) {
          requestsByTechnician[request.assignedToName!] =
              (requestsByTechnician[request.assignedToName!] ?? 0) + 1;
        }

        // تحليل المشاكل الشائعة (من وصف المشكلة)
        final description = request.description.toLowerCase();

        // كلمات مفتاحية للمشاكل الشائعة
        final keywords = [
          'تسريب',
          'لا يبرد',
          'صوت',
          'ضوضاء',
          'رائحة',
          'لا يعمل',
          'ضعيف',
          'صيانة دورية',
          'تنظيف',
          'تركيب',
        ];

        for (final keyword in keywords) {
          if (description.contains(keyword)) {
            commonProblems[keyword] = (commonProblems[keyword] ?? 0) + 1;
          }
        }
      }

      setState(() {
        _serviceRequests = filteredRequests;
        _technicians = techniciansList;
        _totalRequests = filteredRequests.length;
        _completedRequests = completed;
        _pendingRequests = pending;
        _cancelledRequests = cancelled;
        _requestsByType = requestsByType;
        _requestsByTechnician = requestsByTechnician;
        _commonProblems = commonProblems;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });

      _loadData();
    }
  }

  Future<void> _exportReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديد عنوان التقرير
      String title = 'تقرير طلبات الخدمة';
      if (_statusFilter != null) {
        title += ' - ${ServiceRequest.getStatusName(_statusFilter!)}';
      }
      if (_typeFilter != null) {
        title += ' - ${ServiceRequest.getRequestTypeName(_typeFilter!)}';
      }
      if (_selectedTechnician != null) {
        title += ' - ${_selectedTechnician!.name}';
      }

      // تصفية البيانات حسب الفلاتر
      final filteredRequests = _serviceRequests.where((request) {
        bool match = true;

        if (_statusFilter != null) {
          match = match && request.status == _statusFilter;
        }

        if (_typeFilter != null) {
          match = match && request.requestType == _typeFilter;
        }

        if (_selectedTechnician != null) {
          match = match && request.assignedTo == _selectedTechnician!.id;
        }

        return match;
      }).toList();



      // تصدير التقرير
      await SimplePdfExport.createSimpleReport(
        title: title,
        reportType: 'services',
        startDate: _startDate,
        endDate: _endDate,
        data: filteredRequests,
        summaryData: {
          'total': _totalRequests.toDouble(),
          'completed': _completedRequests.toDouble(),
          'inProgress': _pendingRequests.toDouble(),
          'pending': _cancelledRequests.toDouble(),
        },
        context: context,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير التقرير بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('فلترة التقرير'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلترة حسب الحالة
                    const Text(
                      'حالة الطلب:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: const Text('الكل'),
                          selected: _statusFilter == null,
                          onSelected: (selected) {
                            setState(() {
                              _statusFilter = null;
                            });
                          },
                        ),
                        ...ServiceRequestStatus.values.map((status) {
                          return FilterChip(
                            label: Text(ServiceRequest.getStatusName(status)),
                            selected: _statusFilter == status,
                            onSelected: (selected) {
                              setState(() {
                                _statusFilter = selected ? status : null;
                              });
                            },
                          );
                        }),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // فلترة حسب النوع
                    const Text(
                      'نوع الطلب:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: const Text('الكل'),
                          selected: _typeFilter == null,
                          onSelected: (selected) {
                            setState(() {
                              _typeFilter = null;
                            });
                          },
                        ),
                        ...ServiceRequestType.values.map((type) {
                          return FilterChip(
                            label: Text(ServiceRequest.getRequestTypeName(type)),
                            selected: _typeFilter == type,
                            onSelected: (selected) {
                              setState(() {
                                _typeFilter = selected ? type : null;
                              });
                            },
                          );
                        }),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // فلترة حسب الموظف الفني
                    const Text(
                      'الموظف الفني:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<Employee?>(
                      decoration: const InputDecoration(
                        labelText: 'اختر الموظف الفني',
                        prefixIcon: Icon(Icons.engineering),
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedTechnician,
                      items: [
                        const DropdownMenuItem<Employee?>(
                          value: null,
                          child: Text('جميع الموظف الفنيين'),
                        ),
                        ..._technicians.map((technician) {
                          return DropdownMenuItem<Employee?>(
                            value: technician,
                            child: Text(technician.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedTechnician = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // تطبيق الفلتر وإعادة تحميل البيانات
                    _loadData();
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الفترة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'نطاق التقرير',
                    style: AppTextStyles.heading3,
                  ),
                  const SizedBox(height: AppDimensions.paddingS),
                  Row(
                    children: [
                      const Icon(Icons.date_range, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'من ${DateFormat('yyyy/MM/dd').format(_startDate)} إلى ${DateFormat('yyyy/MM/dd').format(_endDate)}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.paddingS),
                  Text(
                    'إجمالي طلبات الخدمة: $_totalRequests',
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppDimensions.paddingM),

          // إحصائيات الحالة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة طلبات الخدمة',
                    style: AppTextStyles.heading3,
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  Row(
                    children: [
                      _buildStatusCard(
                        'مكتملة',
                        _completedRequests,
                        Colors.green,
                        Icons.check_circle,
                      ),
                      _buildStatusCard(
                        'قيد التنفيذ',
                        _pendingRequests,
                        Colors.orange,
                        Icons.pending,
                      ),
                      _buildStatusCard(
                        'ملغاة',
                        _cancelledRequests,
                        Colors.red,
                        Icons.cancel,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppDimensions.paddingM),

          // إحصائيات النوع
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أنواع طلبات الخدمة',
                    style: AppTextStyles.heading3,
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  ..._requestsByType.entries.map((entry) {
                    final percentage = _totalRequests > 0
                        ? (entry.value / _totalRequests * 100).toStringAsFixed(1)
                        : '0';

                    return Padding(
                      padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  '${entry.key} (${entry.value})',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ),
                              Text(
                                '$percentage%',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: _totalRequests > 0
                                ? entry.value / _totalRequests
                                : 0,
                            backgroundColor: Colors.grey.withAlpha(50),
                            color: AppColors.primary,
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTechniciansTab() {
    if (_requestsByTechnician.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للفنيين في الفترة المحددة',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    // ترتيب الموظف الفنيين حسب عدد الطلبات
    final sortedTechnicians = _requestsByTechnician.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: sortedTechnicians.length,
      itemBuilder: (context, index) {
        final entry = sortedTechnicians[index];
        final percentage = _totalRequests > 0
            ? (entry.value / _totalRequests * 100).toStringAsFixed(1)
            : '0';

        return Card(
          margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: AppColors.primary.withAlpha(50),
                      child: Text(
                        entry.key.substring(0, 1),
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppDimensions.paddingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entry.key,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'عدد الطلبات: ${entry.value} ($percentage%)',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.paddingM),
                LinearProgressIndicator(
                  value: _totalRequests > 0 ? entry.value / _totalRequests : 0,
                  backgroundColor: Colors.grey.withAlpha(50),
                  color: AppColors.primary,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommonProblemsTab() {
    if (_commonProblems.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للمشاكل الشائعة في الفترة المحددة',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    // ترتيب المشاكل حسب التكرار
    final sortedProblems = _commonProblems.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: sortedProblems.length,
      itemBuilder: (context, index) {
        final entry = sortedProblems[index];
        final percentage = _totalRequests > 0
            ? (entry.value / _totalRequests * 100).toStringAsFixed(1)
            : '0';

        return Card(
          margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.orange.withAlpha(50),
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppDimensions.paddingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entry.key,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'عدد الحالات: ${entry.value} ($percentage%)',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.paddingM),
                LinearProgressIndicator(
                  value: _totalRequests > 0 ? entry.value / _totalRequests : 0,
                  backgroundColor: Colors.grey.withAlpha(50),
                  color: Colors.orange,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusCard(String title, int count, Color color, IconData icon) {
    final percentage = _totalRequests > 0
        ? (count / _totalRequests * 100).toStringAsFixed(1)
        : '0';

    return Expanded(
      child: Card(
        color: color.withAlpha(20),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingS),
          child: Column(
            children: [
              Icon(icon, color: color),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                count.toString(),
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              Text(
                '$percentage%',
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الصيانة'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'ملخص'),
            Tab(text: 'أداء الموظف الفنيين'),
            Tab(text: 'المشاكل الشائعة'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'اختيار نطاق التاريخ',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'فلترة البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(),
                _buildTechniciansTab(),
                _buildCommonProblemsTab(),
              ],
            ),
    );
  }
}  