import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart' show Excel, Sheet, CellIndex, CellStyle, HorizontalAlign, TextCellValue;
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import '../models/report_models.dart';

class ExcelExportUtil {
  static Future<void> exportReport({
    required String reportType,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required List<dynamic> data,
    required Map<String, double> summaryData,
    required BuildContext context,
  }) async {
    try {
      // Create a new Excel document
      final excel = Excel.createExcel();

      // Create summary sheet
      final Sheet summarySheet = excel['ملخص التقرير'];

      // Add title and date range
      _addHeaderToSheet(
        summarySheet,
        title,
        'الفترة: ${DateFormat('dd/MM/yyyy').format(startDate)} - ${DateFormat('dd/MM/yyyy').format(endDate)}'
      );

      // Add summary data
      _addSummaryDataToSheet(summarySheet, reportType, summaryData);

      // Create data sheet based on report type
      switch (reportType) {
        case 'invoices':
        case 'customer':
          _addInvoicesSheet(excel, data.cast<Invoice>());
          break;
        case 'services':
          _addServiceRequestsSheet(excel, data.cast<ServiceRequest>());
          break;
        case 'transactions':
        case 'supplier':
        case 'employee':
          _addTransactionsSheet(excel, data.cast<Transaction>());
          break;
      }

      // Save the Excel file
      Directory? output;
      try {
        // Try to get external storage directory first
        output = await getExternalStorageDirectory();
      } catch (e) {
        // If external storage is not available, use temporary directory
        output = await getTemporaryDirectory();
      }

      if (output == null) {
        throw Exception('لا يمكن الوصول إلى مجلد التخزين');
      }

      // Create a subdirectory for our app's Excel files if it doesn't exist
      final excelDir = Directory('${output.path}/icecorner_reports');
      if (!await excelDir.exists()) {
        await excelDir.create(recursive: true);
      }

      final fileName = 'تقرير_${reportType}_${DateFormat('yyyy_MM_dd_HHmmss').format(DateTime.now())}.xlsx';
      final file = File('${excelDir.path}/$fileName');

      // Convert Excel to bytes and save
      final excelBytes = excel.encode();
      if (excelBytes != null) {
        await file.writeAsBytes(excelBytes);

        // Create share message
        final shareMessage = 'تقرير: $title\n'
            'الفترة: ${DateFormat('dd/MM/yyyy').format(startDate)} - ${DateFormat('dd/MM/yyyy').format(endDate)}\n'
            'عدد العناصر: ${data.length}';

        // Share the Excel file
        if (context.mounted) {
          final xFile = XFile(file.path);
          await Share.shareXFiles(
            [xFile],
            text: shareMessage,
            subject: 'تقرير $title - ${DateFormat('dd/MM/yyyy').format(DateTime.now())}',
          );

          // Show success message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تصدير التقرير بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } else {
        throw Exception('فشل في إنشاء ملف Excel');
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  static void _addHeaderToSheet(Sheet sheet, String title, String subtitle) {
    // Add title
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(title);
    sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Right,
    );

    // Add subtitle
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(subtitle);
    sheet.cell(CellIndex.indexByString('A2')).cellStyle = CellStyle(
      horizontalAlign: HorizontalAlign.Right,
    );

    // Add generation date
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('تم إنشاؤه في: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}');
    sheet.cell(CellIndex.indexByString('A3')).cellStyle = CellStyle(
      horizontalAlign: HorizontalAlign.Right,
      italic: true,
    );

    // Add empty row
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('');
  }

  static void _addSummaryDataToSheet(Sheet sheet, String reportType, Map<String, double> summaryData) {
    // Add summary header
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('ملخص التقرير');
    sheet.cell(CellIndex.indexByString('A5')).cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      horizontalAlign: HorizontalAlign.Right,
    );

    // Add summary data headers
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('البيان');
    sheet.cell(CellIndex.indexByString('B6')).value = TextCellValue('القيمة');

    // Style headers
    final headerStyle = CellStyle(
      bold: true,
      horizontalAlign: HorizontalAlign.Right,
      // backgroundColor: const Color(0xFFEEEEEE), // Removed unsupported property
    );

    sheet.cell(CellIndex.indexByString('A6')).cellStyle = headerStyle;
    sheet.cell(CellIndex.indexByString('B6')).cellStyle = headerStyle;

    // Add summary data based on report type
    int rowIndex = 7;

    switch (reportType) {
      case 'invoices':
      case 'customer':
        _addSummaryRow(sheet, rowIndex++, 'إجمالي الفواتير', '${summaryData['total']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        _addSummaryRow(sheet, rowIndex++, 'المدفوع', '${summaryData['paid']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        _addSummaryRow(sheet, rowIndex++, 'غير المدفوع', '${summaryData['unpaid']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        _addSummaryRow(sheet, rowIndex++, 'عدد الفواتير', '${summaryData['count']?.toInt() ?? 0}');

        if (reportType == 'customer') {
          _addSummaryRow(sheet, rowIndex++, 'الخدمات المستهلكة', '${summaryData['servicesTotal']?.toInt() ?? 0}');
          _addSummaryRow(sheet, rowIndex++, 'الرصيد المتبقي', '${summaryData['balance']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        }
        break;

      case 'services':
        _addSummaryRow(sheet, rowIndex++, 'إجمالي الطلبات', '${summaryData['total']?.toInt() ?? 0}');
        _addSummaryRow(sheet, rowIndex++, 'المكتملة', '${summaryData['completed']?.toInt() ?? 0}');
        _addSummaryRow(sheet, rowIndex++, 'قيد التنفيذ', '${summaryData['inProgress']?.toInt() ?? 0}');
        _addSummaryRow(sheet, rowIndex++, 'في الانتظار', '${summaryData['pending']?.toInt() ?? 0}');
        break;

      case 'transactions':
      case 'supplier':
      case 'employee':
        _addSummaryRow(sheet, rowIndex++, 'الإيرادات', '${summaryData['income']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        _addSummaryRow(sheet, rowIndex++, 'المصروفات', '${summaryData['expense']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        _addSummaryRow(sheet, rowIndex++, 'الرصيد', '${summaryData['balance']?.toStringAsFixed(2) ?? '0.00'} ر.س');
        _addSummaryRow(sheet, rowIndex++, 'عدد المعاملات', '${summaryData['count']?.toInt() ?? 0}');
        break;
    }
  }

  static void _addSummaryRow(Sheet sheet, int rowIndex, String label, String value) {
    sheet.cell(CellIndex.indexByString('A$rowIndex')).value = TextCellValue(label);
    sheet.cell(CellIndex.indexByString('B$rowIndex')).value = TextCellValue(value);

    sheet.cell(CellIndex.indexByString('A$rowIndex')).cellStyle = CellStyle(
      horizontalAlign: HorizontalAlign.Right,
    );

    sheet.cell(CellIndex.indexByString('B$rowIndex')).cellStyle = CellStyle(
      horizontalAlign: HorizontalAlign.Left,
    );
  }

  static void _addInvoicesSheet(Excel excel, List<Invoice> invoices) {
    final Sheet sheet = excel['الفواتير'];

    // Add headers
    final headers = ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'الحالة'];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Right,
        // backgroundColor: const Color(0xFFEEEEEE), // Removed unsupported property
      );
    }

    // Add data
    for (int i = 0; i < invoices.length; i++) {
      final invoice = invoices[i];
      final rowIndex = i + 1;

      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = TextCellValue(invoice.invoiceNumber);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = TextCellValue(invoice.customerName);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = TextCellValue(DateFormat('dd/MM/yyyy').format(invoice.date));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex)).value = TextCellValue(invoice.total.toStringAsFixed(2));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex)).value = TextCellValue(invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة');
    }
  }

  static void _addServiceRequestsSheet(Excel excel, List<ServiceRequest> requests) {
    final Sheet sheet = excel['طلبات الخدمة'];

    // Add headers
    final headers = ['رقم الطلب', 'العميل', 'نوع الخدمة', 'التاريخ', 'الحالة'];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Right,
      );
    }

    // Add data
    for (int i = 0; i < requests.length; i++) {
      final request = requests[i];
      final rowIndex = i + 1;

      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = TextCellValue(request.requestNumber);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = TextCellValue(request.customerName);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = TextCellValue(request.serviceType);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex)).value = TextCellValue(DateFormat('dd/MM/yyyy').format(request.scheduledDate));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex)).value = TextCellValue(ServiceRequest.getStatusName(request.status));
    }
  }

  static void _addTransactionsSheet(Excel excel, List<Transaction> transactions) {
    final Sheet sheet = excel['المعاملات المالية'];

    // Add headers
    final headers = ['المرجع', 'الوصف', 'الفئة', 'التاريخ', 'المبلغ', 'النوع'];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Right,
      );
    }

    // Add data
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      final rowIndex = i + 1;

      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = TextCellValue(transaction.reference);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = TextCellValue(transaction.description);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = TextCellValue(transaction.category ?? '-');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex)).value = TextCellValue(DateFormat('dd/MM/yyyy').format(transaction.date));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex)).value = TextCellValue(transaction.amount.toStringAsFixed(2));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: rowIndex)).value = TextCellValue(transaction.type == TransactionType.income ? 'إيراد' : 'مصروف');
    }
  }
}
