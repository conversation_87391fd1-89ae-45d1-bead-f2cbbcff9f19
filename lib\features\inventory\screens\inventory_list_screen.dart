import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/inventory_item.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/inventory_item_card.dart';
import '../../../core/repositories/inventory_repository.dart';

class InventoryListScreen extends StatefulWidget {
  const InventoryListScreen({super.key});

  @override
  State<InventoryListScreen> createState() => _InventoryListScreenState();
}

class _InventoryListScreenState extends State<InventoryListScreen> {
  // Método seguro para mostrar SnackBar
  void _showSnackBar(String message, {Color backgroundColor = Colors.black}) {
    // Este método debe ser llamado dentro de un bloque if (mounted)
    // No usa BuildContext a través de brechas asíncronas
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
      ),
    );
  }
  bool _isLoading = true;
  List<InventoryItem> _inventoryItems = [];
  String _searchQuery = '';
  String _categoryFilter = 'all';
  bool _showLowStockOnly = false;

  final InventoryRepository _inventoryRepository = InventoryRepository();

  @override
  void initState() {
    super.initState();
    _loadInventoryItems();
  }

  Future<void> _loadInventoryItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام InventoryRepository للحصول على عناصر المخزون من قاعدة البيانات
      final inventoryItems = await _inventoryRepository.getAllInventoryItems();

      if (mounted) {
        setState(() {
          _inventoryItems = inventoryItems;
          _isLoading = false;
        });
      }

      // إذا لم يكن هناك عناصر في المخزون، استخدم بيانات وهمية للعرض
      if (inventoryItems.isEmpty && mounted) {
        setState(() {
          _inventoryItems = [
            InventoryItem(
              id: 1,
              code: 'AC-001',
              name: 'مكيف سبليت 18000 وحدة',
              description: 'مكيف سبليت سامسونج 18000 وحدة بارد',
              category: 'مكيفات',
              quantity: 15,
              minQuantity: 5,
              unit: 'قطعة',
              costPrice: 1800,
              sellingPrice: 2200,
              supplierName: 'شركة سامسونج',
              location: 'المستودع الرئيسي - رف A1',
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 60)),
            ),
            InventoryItem(
              id: 2,
              code: 'AC-002',
              name: 'مكيف سبليت 24000 وحدة',
              description: 'مكيف سبليت سامسونج 24000 وحدة بارد',
              category: 'مكيفات',
              quantity: 8,
              minQuantity: 3,
              unit: 'قطعة',
              costPrice: 2200,
              sellingPrice: 2800,
              supplierName: 'شركة سامسونج',
              location: 'المستودع الرئيسي - رف A2',
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 45)),
            ),
            InventoryItem(
              id: 3,
              code: 'SP-001',
              name: 'قطع غيار فلتر',
              description: 'فلتر هواء لمكيف سبليت',
              category: 'قطع غيار',
              quantity: 50,
              minQuantity: 20,
              unit: 'قطعة',
              costPrice: 35,
              sellingPrice: 50,
              supplierName: 'مؤسسة الأجزاء',
              location: 'المستودع الفرعي - رف B1',
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 30)),
            ),
            InventoryItem(
              id: 4,
              code: 'TL-001',
              name: 'طقم أدوات صيانة',
              description: 'طقم أدوات صيانة مكيفات كامل',
              category: 'أدوات',
              quantity: 5,
              minQuantity: 2,
              unit: 'طقم',
              costPrice: 350,
              sellingPrice: 0, // غير للبيع
              location: 'المستودع الرئيسي - خزانة C1',
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 90)),
            ),
            InventoryItem(
              id: 5,
              code: 'RF-001',
              name: 'غاز فريون R22',
              description: 'غاز فريون R22 للمكيفات',
              category: 'مواد',
              quantity: 3,
              minQuantity: 5, // منخفض المخزون
              unit: 'اسطوانة',
              costPrice: 450,
              sellingPrice: 600,
              supplierName: 'شركة الغازات المتحدة',
              location: 'المستودع الرئيسي - قسم الغازات',
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 15)),
            ),
          ];
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading inventory items: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;

          // في حالة حدوث خطأ، استخدم بيانات وهمية للعرض
          _inventoryItems = [
            InventoryItem(
              id: 1,
              code: 'AC-001',
              name: 'مكيف سبليت 18000 وحدة',
              description: 'مكيف سبليت سامسونج 18000 وحدة بارد',
              category: 'مكيفات',
              quantity: 15,
              minQuantity: 5,
              unit: 'قطعة',
              costPrice: 1800,
              sellingPrice: 2200,
              supplierName: 'شركة سامسونج',
              location: 'المستودع الرئيسي - رف A1',
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 60)),
            ),
          ];
        });

        _showSnackBar('حدث خطأ أثناء تحميل بيانات المخزون: $e', backgroundColor: Colors.red);
      }
    }
  }

  Set<String> get _categories {
    final categories = <String>{};
    for (final item in _inventoryItems) {
      if (item.category != null) {
        categories.add(item.category!);
      }
    }
    return categories;
  }

  List<InventoryItem> get _filteredInventoryItems {
    List<InventoryItem> filtered = _inventoryItems;

    // Apply category filter
    if (_categoryFilter != 'all') {
      filtered = filtered.where((item) => item.category == _categoryFilter).toList();
    }

    // Apply low stock filter
    if (_showLowStockOnly) {
      filtered = filtered.where((item) => item.isLowStock).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        final query = _searchQuery.toLowerCase();
        return item.code.toLowerCase().contains(query) ||
            item.name.toLowerCase().contains(query) ||
            (item.description?.toLowerCase().contains(query) ?? false) ||
            (item.supplierName?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المخزون'),
        actions: [
          IconButton(
            icon: const Icon(Icons.swap_horiz),
            tooltip: 'حركة المخزون',
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.inventoryTransactions);
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث في المخزون...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          _buildFilterOptions(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredInventoryItems.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد عناصر في المخزون',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadInventoryItems,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredInventoryItems.length,
                          itemBuilder: (context, index) {
                            final item = _filteredInventoryItems[index];
                            return InventoryItemCard(
                              inventoryItem: item,
                              onTap: () {
                                _showInventoryItemActions(context, item);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Show add inventory item dialog
          _showAddInventoryItemDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFilterOptions() {
    return Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
          child: Row(
            children: [
              _buildFilterChip('all', 'الكل'),
              const SizedBox(width: 8),
              ..._categories.map((category) {
                return Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: _buildFilterChip(category, category),
                );
              }),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
          child: Row(
            children: [
              FilterChip(
                label: const Text('المخزون المنخفض فقط'),
                selected: _showLowStockOnly,
                onSelected: (selected) {
                  setState(() {
                    _showLowStockOnly = selected;
                  });
                },
                backgroundColor: Colors.white,
                selectedColor: Colors.red.withAlpha(50),
                checkmarkColor: Colors.red,
                labelStyle: TextStyle(
                  color: _showLowStockOnly ? Colors.red : AppColors.textPrimary,
                  fontWeight: _showLowStockOnly ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _categoryFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _categoryFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.white,
      selectedColor: AppColors.primary.withAlpha(50),
      checkmarkColor: AppColors.primary,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primary : AppColors.textPrimary,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية المخزون'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // More advanced filters would go here
              const Text('خيارات تصفية متقدمة'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filters
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _showInventoryItemActions(BuildContext context, InventoryItem item) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.paddingL,
            horizontal: AppDimensions.paddingM,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                item.name,
                style: AppTextStyles.heading2,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                'كود: ${item.code}',
                style: const TextStyle(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.visibility),
                title: const Text('عرض التفاصيل'),
                onTap: () {
                  Navigator.pop(context);
                  // Show inventory item details
                  _showInventoryItemDetails(context, item);
                },
              ),
              ListTile(
                leading: const Icon(Icons.swap_horiz),
                title: const Text('حركة العنصر'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to inventory transactions for this item
                  Navigator.pushNamed(
                    context,
                    AppRoutes.inventoryTransactions,
                    arguments: item.id,
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('تعديل العنصر'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to edit inventory item
                  _showEditInventoryItemDialog(context, item);
                },
              ),
              ListTile(
                leading: const Icon(Icons.add_circle),
                title: const Text('إضافة كمية'),
                onTap: () {
                  Navigator.pop(context);
                  // Show add quantity dialog
                  _showAddQuantityDialog(context, item);
                },
              ),
              ListTile(
                leading: const Icon(Icons.remove_circle),
                title: const Text('سحب كمية'),
                onTap: () {
                  Navigator.pop(context);
                  // Show remove quantity dialog
                  _showRemoveQuantityDialog(context, item);
                },
              ),
              if (item.quantity == 0)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'حذف العنصر',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // Show delete confirmation
                    _showDeleteConfirmation(context, item);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _showInventoryItemDetails(BuildContext context, InventoryItem item) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(item.name),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('الكود', item.code),
                if (item.description != null)
                  _buildDetailRow('الوصف', item.description!),
                _buildDetailRow('الفئة', item.category ?? 'غير مصنف'),
                _buildDetailRow('الكمية', '${item.quantity} ${item.unit ?? 'قطعة'}'),
                _buildDetailRow('الحد الأدنى', '${item.minQuantity} ${item.unit ?? 'قطعة'}'),
                _buildDetailRow('حالة المخزون', item.getStockStatusText()),
                _buildDetailRow('سعر التكلفة', '${item.costPrice} ر.س'),
                _buildDetailRow('سعر البيع', '${item.sellingPrice} ر.س'),
                if (item.supplierName != null)
                  _buildDetailRow('المورد', item.supplierName!),
                if (item.location != null)
                  _buildDetailRow('الموقع', item.location!),
                _buildDetailRow('الحالة', item.isActive ? 'نشط' : 'غير نشط'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  void _showAddInventoryItemDialog(BuildContext context) {
    Navigator.pushNamed(
      context,
      AppRoutes.inventoryAdd,
    ).then((result) {
      if (result == true) {
        _loadInventoryItems();
      }
    });
  }

  void _showEditInventoryItemDialog(BuildContext context, InventoryItem item) {
    Navigator.pushNamed(
      context,
      AppRoutes.inventoryEdit,
      arguments: item,
    ).then((result) {
      if (result == true) {
        _loadInventoryItems();
      }
    });
  }

  void _showAddQuantityDialog(BuildContext context, InventoryItem item) {
    final formKey = GlobalKey<FormState>();
    double quantity = 0;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('إضافة كمية للعنصر ${item.name}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'الكمية (${item.unit ?? 'قطعة'})',
                    prefixIcon: const Icon(Icons.add_circle),
                    border: const OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال الكمية';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال كمية صحيحة';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    quantity = double.parse(value!);
                  },
                ),
                // Notes field removed for simplicity
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // Update inventory item quantity
                  final updatedItem = item.copyWith(
                    quantity: item.quantity + quantity,
                  );

                  try {
                    await _inventoryRepository.updateInventoryItemQuantity(
                      item.id,
                      updatedItem.quantity
                    );

                    if (mounted) {
                      setState(() {
                        final index = _inventoryItems.indexWhere((i) => i.id == item.id);
                        if (index != -1) {
                          _inventoryItems[index] = updatedItem;
                        }
                      });

                      // Show success message
                      // Capture the BuildContext before the async gap
                      final currentContext = context;
                      ScaffoldMessenger.of(currentContext).showSnackBar(
                        SnackBar(
                          content: Text('تم إضافة $quantity ${item.unit ?? 'قطعة'} إلى ${item.name}'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (kDebugMode) {
                      print('Error updating inventory item quantity: $e');
                    }

                    if (mounted) {
                      // Capture the BuildContext before the async gap
                      final currentContext = context;
                      ScaffoldMessenger.of(currentContext).showSnackBar(
                        SnackBar(
                          content: Text('حدث خطأ أثناء تحديث الكمية: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  void _showRemoveQuantityDialog(BuildContext context, InventoryItem item) {
    final formKey = GlobalKey<FormState>();
    double quantity = 0;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('سحب كمية من العنصر ${item.name}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'الكمية (${item.unit ?? 'قطعة'})',
                    prefixIcon: const Icon(Icons.remove_circle),
                    border: const OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال الكمية';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال كمية صحيحة';
                    }
                    if (parsedValue > item.quantity) {
                      return 'الكمية المتوفرة ${item.quantity} فقط';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    quantity = double.parse(value!);
                  },
                ),
                // Notes field removed for simplicity
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // Update inventory item quantity
                  final updatedItem = item.copyWith(
                    quantity: item.quantity - quantity,
                  );

                  try {
                    await _inventoryRepository.updateInventoryItemQuantity(
                      item.id,
                      updatedItem.quantity
                    );

                    if (mounted) {
                      setState(() {
                        final index = _inventoryItems.indexWhere((i) => i.id == item.id);
                        if (index != -1) {
                          _inventoryItems[index] = updatedItem;
                        }
                      });

                      // Show success message
                      // Capture the BuildContext before the async gap
                      final currentContext = context;
                      ScaffoldMessenger.of(currentContext).showSnackBar(
                        SnackBar(
                          content: Text('تم سحب $quantity ${item.unit ?? 'قطعة'} من ${item.name}'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    }
                  } catch (e) {
                    if (kDebugMode) {
                      print('Error updating inventory item quantity: $e');
                    }

                    if (mounted) {
                      // Capture the BuildContext before the async gap
                      final currentContext = context;
                      ScaffoldMessenger.of(currentContext).showSnackBar(
                        SnackBar(
                          content: Text('حدث خطأ أثناء تحديث الكمية: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('سحب'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, InventoryItem item) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من رغبتك في حذف العنصر ${item.name}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);

                try {
                  // Delete inventory item
                  await _inventoryRepository.deleteInventoryItem(item.id);

                  if (mounted) {
                    setState(() {
                      _inventoryItems.removeWhere((i) => i.id == item.id);
                    });

                    // Show success message
                    // Capture the BuildContext before the async gap
                    final currentContext = context;
                    ScaffoldMessenger.of(currentContext).showSnackBar(
                      SnackBar(
                        content: Text('تم حذف العنصر ${item.name}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } catch (e) {
                  if (kDebugMode) {
                    print('Error deleting inventory item: $e');
                  }

                  if (mounted) {
                    // Capture the BuildContext before the async gap
                    final currentContext = context;
                    ScaffoldMessenger.of(currentContext).showSnackBar(
                      SnackBar(
                        content: Text('حدث خطأ أثناء حذف العنصر: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
