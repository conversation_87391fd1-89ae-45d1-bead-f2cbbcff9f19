import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/invoice.dart';
import '../../shared/models/invoice_item.dart';
import '../../shared/models/customer.dart';
import './cash_box_repository.dart';
import '../../shared/models/cash_box.dart';

class InvoiceRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();

  // Generate unique invoice number
  Future<String> generateInvoiceNumber() async {
    try {
      final db = await _dbHelper.database;
      final now = DateTime.now();
      final datePrefix = 'INV-${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      // Get count of invoices created today
      final List<Map<String, dynamic>> result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM invoices WHERE invoice_number LIKE ?',
        ['$datePrefix%']
      );

      final count = result.first['count'] as int;
      final sequenceNumber = (count + 1).toString().padLeft(3, '0');

      return '$datePrefix-$sequenceNumber';
    } catch (e) {
      if (kDebugMode) {
        print('Error generating invoice number: $e');
      }
      // Fallback to timestamp-based number if there's an error
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'INV-$timestamp';
    }
  }

  // Check if invoice number exists
  Future<bool> invoiceNumberExists(String invoiceNumber) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> result = await db.query(
        'invoices',
        where: 'invoice_number = ?',
        whereArgs: [invoiceNumber],
      );
      return result.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking invoice number: $e');
      }
      return false;
    }
  }

  // Generate unique invoice number with retry mechanism
  Future<String> generateUniqueInvoiceNumber() async {
    String invoiceNumber;
    int attempts = 0;
    const maxAttempts = 10;

    do {
      invoiceNumber = await generateInvoiceNumber();
      attempts++;

      if (attempts >= maxAttempts) {
        // If we can't generate a unique number after max attempts, use timestamp
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        invoiceNumber = 'INV-$timestamp';
        break;
      }
    } while (await invoiceNumberExists(invoiceNumber));

    return invoiceNumber;
  }

  // Get all invoices
  Future<List<Invoice>> getAllInvoices() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('invoices');

      List<Invoice> invoices = [];
      for (var map in maps) {
        final invoice = await _mapInvoice(map);
        if (invoice != null) {
          invoices.add(invoice);
        }
      }

      return invoices;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting invoices: $e');
      }
      return [];
    }
  }

  // Get invoice by ID
  Future<Invoice?> getInvoiceById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return await _mapInvoice(maps.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting invoice by ID: $e');
      }
      return null;
    }
  }

  // Get invoices by customer ID
  Future<List<Invoice>> getInvoicesByCustomerId(int customerId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'invoices',
        where: 'customer_id = ?',
        whereArgs: [customerId],
      );

      List<Invoice> invoices = [];
      for (var map in maps) {
        final invoice = await _mapInvoice(map);
        if (invoice != null) {
          invoices.add(invoice);
        }
      }

      return invoices;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting invoices by customer ID: $e');
      }
      return [];
    }
  }

  // Insert a new invoice
  Future<int> insertInvoice(Invoice invoice) async {
    try {
      final db = await _dbHelper.database;

      // Begin transaction
      return await db.transaction((txn) async {
        // Insert invoice
        final invoiceId = await txn.insert(
          'invoices',
          {
            'invoice_number': invoice.invoiceNumber,
            'customer_id': invoice.customer.id,
            'date': invoice.issueDate.toIso8601String(),
            'due_date': invoice.dueDate.toIso8601String(),
            'subtotal': invoice.subtotal,
            'tax': invoice.taxAmount,
            'discount': invoice.discount,
            'total': invoice.total,
            'notes': invoice.notes,
            'status': invoice.status.toString().split('.').last,
            'payment_date': invoice.status == InvoiceStatus.paid ? DateTime.now().toIso8601String() : null,
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        // Insert invoice items
        for (var item in invoice.items) {
          await txn.insert(
            'invoice_items',
            {
              'invoice_id': invoiceId,
              'description': item.description ?? item.name,
              'quantity': item.quantity,
              'unit_price': item.unitPrice,
              'total': item.quantity * item.unitPrice,
              'created_at': DateTime.now().toIso8601String(),
            },
          );
        }

        return invoiceId;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting invoice: $e');
      }
      return -1;
    }
  }

  // Update an existing invoice
  Future<int> updateInvoice(Invoice invoice) async {
    try {
      final db = await _dbHelper.database;

      // Begin transaction
      return await db.transaction((txn) async {
        // Update invoice
        await txn.update(
          'invoices',
          {
            'invoice_number': invoice.invoiceNumber,
            'customer_id': invoice.customer.id,
            'date': invoice.issueDate.toIso8601String(),
            'due_date': invoice.dueDate.toIso8601String(),
            'subtotal': invoice.subtotal,
            'tax': invoice.taxAmount,
            'discount': invoice.discount,
            'total': invoice.total,
            'notes': invoice.notes,
            'status': invoice.status.toString().split('.').last,
            'payment_date': invoice.status == InvoiceStatus.paid ? DateTime.now().toIso8601String() : null,
          },
          where: 'id = ?',
          whereArgs: [invoice.id],
        );

        // Delete existing invoice items
        await txn.delete(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [invoice.id],
        );

        // Insert updated invoice items
        for (var item in invoice.items) {
          await txn.insert(
            'invoice_items',
            {
              'invoice_id': invoice.id,
              'description': item.description ?? item.name,
              'quantity': item.quantity,
              'unit_price': item.unitPrice,
              'total': item.quantity * item.unitPrice,
              'created_at': DateTime.now().toIso8601String(),
            },
          );
        }

        return 1; // Success
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error updating invoice: $e');
      }
      return 0;
    }
  }

  // Delete an invoice
  Future<int> deleteInvoice(int id) async {
    try {
      final db = await _dbHelper.database;

      // Begin transaction
      return await db.transaction((txn) async {
        // Delete invoice items
        await txn.delete(
          'invoice_items',
          where: 'invoice_id = ?',
          whereArgs: [id],
        );

        // Delete invoice
        return await txn.delete(
          'invoices',
          where: 'id = ?',
          whereArgs: [id],
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting invoice: $e');
      }
      return 0;
    }
  }

  // Helper method to parse customer type
  CustomerType _parseCustomerType(String? type) {
    if (type == null) return CustomerType.individual;

    switch (type.toLowerCase()) {
      case 'company':
        return CustomerType.company;
      case 'individual':
      default:
        return CustomerType.individual;
    }
  }

  // Helper method to parse invoice status
  InvoiceStatus _parseInvoiceStatus(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return InvoiceStatus.draft;
      case 'issued':
        return InvoiceStatus.issued;
      case 'paid':
        return InvoiceStatus.paid;
      case 'partially_paid':
      case 'partiallypaid':
        return InvoiceStatus.partiallyPaid;
      case 'overdue':
        return InvoiceStatus.overdue;
      case 'cancelled':
        return InvoiceStatus.cancelled;
      default:
        return InvoiceStatus.issued;
    }
  }

  // Helper method to map database result to Invoice object
  Future<Invoice?> _mapInvoice(Map<String, dynamic> map) async {
    try {
      final db = await _dbHelper.database;

      // Get customer
      final List<Map<String, dynamic>> customerMaps = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [map['customer_id']],
      );

      if (customerMaps.isEmpty) {
        return null;
      }

      final customer = Customer(
        localId: customerMaps[0]['id'] as int,
        name: customerMaps[0]['name'] as String,
        email: customerMaps[0]['email'] as String?,
        phone: customerMaps[0]['phone'] as String?,
        address: customerMaps[0]['address'] as String?,
        contactPerson: customerMaps[0]['contact_person'] as String?,
        type: _parseCustomerType(customerMaps[0]['type'] as String?),
        notes: customerMaps[0]['notes'] as String?,
        createdAt: DateTime.parse(customerMaps[0]['created_at'] as String),
        updatedAt: null,
      );

      // Get invoice items
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [map['id']],
      );

      final List<InvoiceItem> items = itemMaps.map((itemMap) {
        return InvoiceItem(
          id: itemMap['id'] as int,
          name: itemMap['description'] as String, // Using description as name
          description: itemMap['description'] as String?,
          quantity: itemMap['quantity'] as double,
          unitPrice: itemMap['unit_price'] as double,
          taxRate: 0.15, // Default tax rate
          discount: 0.0, // Default discount
          unit: null, // Unit not stored in the current schema
          inventoryItemId: null, // Inventory item not stored in the current schema
        );
      }).toList();

      // Determine invoice status
      InvoiceStatus status;
      if (map['status'] != null) {
        // Parse status from the database
        status = _parseInvoiceStatus(map['status'] as String);
      } else {
        // Fallback logic if status is not set
        final dueDate = DateTime.parse(map['due_date'] as String);
        if (dueDate.isBefore(DateTime.now())) {
          status = InvoiceStatus.overdue;
        } else {
          status = InvoiceStatus.issued;
        }
      }

      return Invoice(
        id: map['id'] as int,
        invoiceNumber: map['invoice_number'] as String,
        customer: customer,
        issueDate: DateTime.parse(map['date'] as String),
        dueDate: DateTime.parse(map['due_date'] as String),
        items: items,
        status: status,
        subtotal: map['subtotal'] as double,
        taxRate: 0.15, // Default tax rate
        taxAmount: map['tax'] as double,
        discount: map['discount'] as double,
        total: map['total'] as double,
        notes: map['notes'] as String?,
        createdAt: DateTime.parse(map['created_at'] as String),
        updatedAt: null,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error mapping invoice: $e');
      }
      return null;
    }
  }

  // Mark an invoice as paid
  Future<int> markInvoiceAsPaid(int invoiceId) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'invoices',
        {
          'status': 'paid',
          'payment_date': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [invoiceId],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error marking invoice as paid: $e');
      }
      return 0;
    }
  }

  // Get invoices by date range
  Future<List<Invoice>> getInvoicesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'invoices',
        where: 'date BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
      );

      List<Invoice> invoices = [];
      for (var map in maps) {
        final invoice = await _mapInvoice(map);
        if (invoice != null) {
          invoices.add(invoice);
        }
      }

      return invoices;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting invoices by date range: $e');
      }
      return [];
    }
  }

  // Record invoice payment through cash box
  Future<bool> recordInvoicePayment({
    required int invoiceId,
    required int cashBoxId,
    required double amount,
    String? paymentMethod,
    String? notes,
  }) async {
    try {
      final db = await _dbHelper.database;

      return await db.transaction((txn) async {
        // Get invoice details
        final invoiceData = await txn.query(
          'invoices',
          where: 'id = ?',
          whereArgs: [invoiceId],
        );

        if (invoiceData.isEmpty) {
          throw Exception('Invoice not found');
        }

        final invoice = invoiceData.first;
        final totalAmount = invoice['total_amount'] as double;
        final paidAmount = (invoice['paid_amount'] as double?) ?? 0.0;
        final remainingAmount = totalAmount - paidAmount;

        if (amount > remainingAmount) {
          throw Exception('Payment amount exceeds remaining balance');
        }

        // Update invoice paid amount
        final newPaidAmount = paidAmount + amount;
        final newStatus = newPaidAmount >= totalAmount ? 'paid' : 'partially_paid';

        await txn.update(
          'invoices',
          {
            'paid_amount': newPaidAmount,
            'status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [invoiceId],
        );

        // Create cash box transaction
        final cashBoxTransaction = CashBoxTransaction(
          cashBoxId: cashBoxId,
          type: CashBoxTransactionType.income,
          amount: amount,
          description: notes ?? 'Payment for Invoice #${invoice['invoice_number']}',
          reference: invoiceId.toString(),
          referenceType: CashBoxReferenceType.invoice,
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
        );

        // Add transaction to cash box (this will update the balance)
        await _cashBoxRepository.addTransaction(cashBoxTransaction);

        if (kDebugMode) {
          print('Invoice payment recorded: Invoice $invoiceId, Amount: $amount');
        }

        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error recording invoice payment: $e');
      }
      rethrow;
    }
  }
}
