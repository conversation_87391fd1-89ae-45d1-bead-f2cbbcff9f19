import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// بطاقة منسقة بتصميم موحد
class StyledCard extends StatelessWidget {
  /// محتوى البطاقة
  final Widget child;
  
  /// عنوان البطاقة (اختياري)
  final String? title;
  
  /// أيقونة البطاقة (اختياري)
  final IconData? icon;
  
  /// إجراءات البطاقة (اختياري)
  final List<Widget>? actions;
  
  /// تباعد داخلي
  final EdgeInsetsGeometry padding;
  
  /// هامش خارجي
  final EdgeInsetsGeometry margin;
  
  /// ارتفاع الظل
  final double elevation;
  
  /// نصف قطر الزوايا
  final double borderRadius;
  
  /// لون الخلفية
  final Color? backgroundColor;
  
  /// لون الحدود
  final Color? borderColor;
  
  /// سماكة الحدود
  final double borderWidth;
  
  /// عرض البطاقة (اختياري)
  final double? width;
  
  /// ارتفاع البطاقة (اختياري)
  final double? height;
  
  /// تمكين تأثير النقر
  final bool enableTap;
  
  /// دالة تنفذ عند النقر على البطاقة
  final VoidCallback? onTap;
  
  /// إنشاء بطاقة منسقة
  const StyledCard({
    super.key,
    required this.child,
    this.title,
    this.icon,
    this.actions,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(8.0),
    this.elevation = 2.0,
    this.borderRadius = 12.0,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 0.0,
    this.width,
    this.height,
    this.enableTap = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final hasHeader = title != null || icon != null || (actions != null && actions!.isNotEmpty);
    
    final card = Card(
      elevation: elevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: borderColor != null
            ? BorderSide(color: borderColor!, width: borderWidth)
            : BorderSide.none,
      ),
      color: backgroundColor ?? AppColors.card,
      margin: margin,
      child: Container(
        width: width,
        height: height,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // رأس البطاقة (إذا كان موجوداً)
            if (hasHeader) _buildCardHeader(),
            
            // محتوى البطاقة
            Padding(
              padding: hasHeader
                  ? EdgeInsets.only(
                      left: padding.horizontal / 2,
                      right: padding.horizontal / 2,
                      bottom: padding.vertical / 2,
                    )
                  : padding,
              child: child,
            ),
          ],
        ),
      ),
    );
    
    // تطبيق تأثير النقر إذا كان مفعلاً
    if (enableTap || onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: card,
      );
    }
    
    return card;
  }

  /// بناء رأس البطاقة
  Widget _buildCardHeader() {
    return Padding(
      padding: EdgeInsets.only(
        left: padding.horizontal / 2,
        right: padding.horizontal / 2,
        top: padding.vertical / 2,
        bottom: padding.vertical / 4,
      ),
      child: Row(
        children: [
          // أيقونة البطاقة
          if (icon != null) ...[
            Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: 8),
          ],
          
          // عنوان البطاقة
          if (title != null)
            Expanded(
              child: Text(
                title!,
                style: AppTextStyles.headingSmall,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          
          // إجراءات البطاقة
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 8),
            ...actions!,
          ],
        ],
      ),
    );
  }
}

/// امتداد لتسهيل استخدام التباعد الداخلي
extension PaddingExtension on EdgeInsetsGeometry {
  /// الحصول على مجموع التباعد الأفقي
  double get horizontal {
    if (this is EdgeInsets) {
      final edgeInsets = this as EdgeInsets;
      return edgeInsets.left + edgeInsets.right;
    }
    return 32.0; // قيمة افتراضية
  }

  /// الحصول على مجموع التباعد الرأسي
  double get vertical {
    if (this is EdgeInsets) {
      final edgeInsets = this as EdgeInsets;
      return edgeInsets.top + edgeInsets.bottom;
    }
    return 32.0; // قيمة افتراضية
  }
}
