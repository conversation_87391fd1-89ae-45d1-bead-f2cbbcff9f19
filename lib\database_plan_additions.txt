-- Ad<PERSON>ones al plan de base de datos para mejoras contables

-- Tabla de impuestos
CREATE TABLE taxes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    rate REAL NOT NULL,
    is_active INTEGER NOT NULL DEFAULT 1,
    is_default INTEGER NOT NULL DEFAULT 0,
    description TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT
);

-- Tabla de análisis de rentabilidad
CREATE TABLE profitability_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type TEXT NOT NULL,
    entity_id INTEGER NOT NULL,
    entity_name TEXT NOT NULL,
    period TEXT NOT NULL,
    start_date TEXT NOT NULL,
    end_date TEXT NOT NULL,
    total_revenue REAL NOT NULL,
    direct_costs REAL NOT NULL,
    indirect_costs REAL NOT NULL,
    gross_profit REAL NOT NULL,
    net_profit REAL NOT NULL,
    profit_margin REAL NOT NULL,
    roi REAL NOT NULL,
    notes TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT
);

-- Tabla de flujo de caja
CREATE TABLE cash_flows (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL,
    category TEXT NOT NULL,
    date TEXT NOT NULL,
    amount REAL NOT NULL,
    description TEXT NOT NULL,
    related_entity_type TEXT,
    related_entity_id INTEGER,
    related_entity_name TEXT,
    bank_account_id INTEGER,
    bank_account_name TEXT,
    notes TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id)
);

-- Tabla de comisiones
CREATE TABLE commissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id INTEGER NOT NULL,
    employee_name TEXT NOT NULL,
    type TEXT NOT NULL,
    value REAL NOT NULL,
    amount REAL NOT NULL,
    date TEXT NOT NULL,
    status TEXT NOT NULL,
    related_entity_type TEXT NOT NULL,
    related_entity_id INTEGER NOT NULL,
    related_entity_name TEXT NOT NULL,
    description TEXT,
    notes TEXT,
    payment_date TEXT,
    payment_transaction_id INTEGER,
    created_at TEXT NOT NULL,
    updated_at TEXT,
    FOREIGN KEY (employee_id) REFERENCES employees (id),
    FOREIGN KEY (payment_transaction_id) REFERENCES financial_transactions (id)
);

-- Actualización de la tabla de facturas para incluir impuestos
ALTER TABLE invoices ADD COLUMN tax_id INTEGER;
ALTER TABLE invoices ADD COLUMN tax_amount REAL DEFAULT 0;
ALTER TABLE invoices ADD COLUMN tax_included INTEGER DEFAULT 0;
ALTER TABLE invoices ADD COLUMN subtotal REAL DEFAULT 0;

-- Actualización de la tabla de elementos de factura para incluir impuestos
ALTER TABLE invoice_items ADD COLUMN tax_id INTEGER;
ALTER TABLE invoice_items ADD COLUMN tax_amount REAL DEFAULT 0;
ALTER TABLE invoice_items ADD COLUMN tax_included INTEGER DEFAULT 0;
ALTER TABLE invoice_items ADD COLUMN subtotal REAL DEFAULT 0;

-- Índices para mejorar el rendimiento
CREATE INDEX idx_taxes_type ON taxes(type);
CREATE INDEX idx_taxes_is_active ON taxes(is_active);
CREATE INDEX idx_profitability_entity ON profitability_analysis(entity_type, entity_id);
CREATE INDEX idx_profitability_period ON profitability_analysis(period, start_date, end_date);
CREATE INDEX idx_cash_flows_date ON cash_flows(date);
CREATE INDEX idx_cash_flows_type ON cash_flows(type);
CREATE INDEX idx_cash_flows_category ON cash_flows(category);
CREATE INDEX idx_cash_flows_related ON cash_flows(related_entity_type, related_entity_id);
CREATE INDEX idx_commissions_employee ON commissions(employee_id);
CREATE INDEX idx_commissions_date ON commissions(date);
CREATE INDEX idx_commissions_status ON commissions(status);
CREATE INDEX idx_commissions_related ON commissions(related_entity_type, related_entity_id);
