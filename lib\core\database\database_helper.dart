import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../shared/models/user.dart';
import '../repositories/settings_repository.dart';
import 'migrations/add_is_paid_to_withdrawals.dart';
import 'migrations/add_technical_fields_to_service_requests.dart';
import 'migrations/add_service_categories_table.dart';
import 'migrations/add_category_to_service_requests.dart';
import 'migrations/add_permissions_to_users.dart';
import 'migrations/fix_users_table.dart';
import 'migrations/fix_service_requests_table.dart';
import 'migrations/add_reminder_minutes_to_service_requests.dart';
import 'migrations/add_subscription_fields_to_customers.dart';
import 'migrations/add_service_amount_to_service_requests.dart';
import 'migrations/add_payment_info_to_payroll.dart';
import 'migrations/add_cash_boxes_table.dart';
import 'migrations/add_default_employees.dart';
import 'migrations/add_missing_columns.dart';
import 'performance_optimizations.dart';
import '../performance/memory_manager.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;
  final SettingsRepository _settingsRepository = SettingsRepository();

  factory DatabaseHelper() {
    return _instance;
  }

  DatabaseHelper._internal();

  /// Get the current database path
  Future<String> getDatabasePath() async {
    return await _settingsRepository.getDatabasePath();
  }

  /// Set a custom database path
  Future<bool> setDatabasePath(String dbPath) async {
    final success = await _settingsRepository.setDatabasePath(dbPath);
    if (success) {
      // Close the current database if it's open
      await closeDatabase();
    }
    return success;
  }

  /// Close the current database connection
  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      if (kDebugMode) {
        print('Database connection closed');
      }
    }
  }

  Future<Database> get database async {
    if (_database != null) {
      return _database!;
    }
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Get the database path from settings
    final dbPath = await _settingsRepository.getDatabasePath();

    // For debugging purposes, print the database path
    if (kDebugMode) {
      print('Opening database at path: $dbPath');
    }

    // Ensure the directory exists
    final dbDir = Directory(dirname(dbPath));
    if (!await dbDir.exists()) {
      if (kDebugMode) {
        print('Creating database directory: ${dbDir.path}');
      }
      await dbDir.create(recursive: true);
    }

    final db = await openDatabase(
      dbPath,
      version: 16, // Upgraded to version 16 with error logs
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );

    // Apply migrations
    await AddIsPaidToWithdrawals.migrate(db);
    await AddTechnicalFieldsToServiceRequests.migrate(db);
    await AddServiceCategoriesTable.migrate(db);
    await AddCategoryToServiceRequests.migrate(db);
    await AddPermissionsToUsers.migrate(db);
    await FixUsersTable.migrate(db);
    await FixServiceRequestsTable.migrate(db);
    await AddReminderMinutesToServiceRequests.migrate(db);
    await AddSubscriptionFieldsToCustomers.migrate(db);
    await AddServiceAmountToServiceRequests.migrate(db);
    await AddPaymentInfoToPayroll.migrate(db);
    await AddCashBoxesTable.migrate(db);
    await AddDefaultEmployees.migrate(db);

    // إضافة الأعمدة المفقودة
    await AddMissingColumns.migrate(db);

    return db;
  }

  // Method to reset the database (for debugging)
  Future<void> resetDatabase() async {
    // Get the current database path
    final dbPath = await getDatabasePath();

    // Close the database if it's open
    await closeDatabase();

    // Delete the database file
    if (await File(dbPath).exists()) {
      await deleteDatabase(dbPath);
      if (kDebugMode) {
        print('Database file deleted: $dbPath');
      }
    } else {
      if (kDebugMode) {
        print('Database file does not exist: $dbPath');
      }
    }

    // Reinitialize the database
    _database = await _initDatabase();

    if (kDebugMode) {
      print('Database reset completed');
    }

    // Ensure admin user exists
    await ensureAdminUserExists();
  }

  // Method to ensure admin user exists
  Future<void> ensureAdminUserExists() async {
    try {
      final db = await database;

      // Check if admin user exists
      final List<Map<String, dynamic>> adminUsers = await db.query(
        'users',
        where: 'name = ?',
        whereArgs: ['admin'],
      );

      if (adminUsers.isEmpty) {
        if (kDebugMode) {
          print('Admin user not found, creating one...');
        }

        // Insert admin user
        final adminUserId = await db.insert('users', {
          'name': 'admin',
          'email': '<EMAIL>',
          'password': '1',
          'is_active': 1,
          'created_at': DateTime.now().toIso8601String(),
        });

        if (kDebugMode) {
          print('Admin user created with ID: $adminUserId');
        }

        // Get admin role id
        final List<Map<String, dynamic>> adminRoleMaps = await db.query(
          'roles',
          where: 'name = ?',
          whereArgs: ['admin'],
        );

        if (adminRoleMaps.isNotEmpty) {
          final adminRoleId = adminRoleMaps.first['id'];
          // Assign admin role to admin user
          await db.insert('user_roles', {
            'user_id': adminUserId,
            'role_id': adminRoleId,
          });

          if (kDebugMode) {
            print('Admin role assigned to admin user');
          }
        } else {
          if (kDebugMode) {
            print('Admin role not found, creating roles...');
          }

          // Insert roles if they don't exist
          final adminRoleId = await db.insert('roles', {'name': 'admin'});
          await db.insert('roles', {'name': 'employee'});

          // Assign admin role to admin user
          await db.insert('user_roles', {
            'user_id': adminUserId,
            'role_id': adminRoleId,
          });

          if (kDebugMode) {
            print('Roles created and admin role assigned to admin user');
          }
        }
      } else {
        if (kDebugMode) {
          print('Admin user already exists');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error ensuring admin user exists: $e');
      }
    }
  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (kDebugMode) {
      print('Upgrading database from version $oldVersion to $newVersion');
    }

    // Enable foreign keys
    await db.execute('PRAGMA foreign_keys = ON');

    // Complete database reset for version 5 (new schema)
    if (oldVersion < 5) {
      if (kDebugMode) {
        print('Performing complete database schema upgrade to version 5');
      }

      // Get list of all tables
      final List<Map<String, dynamic>> tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT IN ('android_metadata', 'sqlite_sequence')"
      );

      // Backup important data before dropping tables
      Map<String, List<Map<String, dynamic>>> backupData = {};

      try {
        // Backup users
        if (tables.any((table) => table['name'] == 'users')) {
          backupData['users'] = await db.query('users');
          if (kDebugMode) {
            print('Backed up ${backupData['users']?.length ?? 0} users');
          }
        }

        // Backup roles
        if (tables.any((table) => table['name'] == 'roles')) {
          backupData['roles'] = await db.query('roles');
          if (kDebugMode) {
            print('Backed up ${backupData['roles']?.length ?? 0} roles');
          }
        }

        // Backup user_roles
        if (tables.any((table) => table['name'] == 'user_roles')) {
          backupData['user_roles'] = await db.query('user_roles');
          if (kDebugMode) {
            print('Backed up ${backupData['user_roles']?.length ?? 0} user_roles');
          }
        }

        // Backup customers
        if (tables.any((table) => table['name'] == 'customers')) {
          backupData['customers'] = await db.query('customers');
          if (kDebugMode) {
            print('Backed up ${backupData['customers']?.length ?? 0} customers');
          }
        }

        // Backup suppliers
        if (tables.any((table) => table['name'] == 'suppliers')) {
          backupData['suppliers'] = await db.query('suppliers');
          if (kDebugMode) {
            print('Backed up ${backupData['suppliers']?.length ?? 0} suppliers');
          }
        }

        // Backup employees
        if (tables.any((table) => table['name'] == 'employees')) {
          backupData['employees'] = await db.query('employees');
          if (kDebugMode) {
            print('Backed up ${backupData['employees']?.length ?? 0} employees');
          }
        }

        // Backup company_info
        if (tables.any((table) => table['name'] == 'company_info')) {
          backupData['company_info'] = await db.query('company_info');
          if (kDebugMode) {
            print('Backed up ${backupData['company_info']?.length ?? 0} company_info records');
          }
        }

        // Backup categories
        if (tables.any((table) => table['name'] == 'categories')) {
          backupData['categories'] = await db.query('categories');
          if (kDebugMode) {
            print('Backed up ${backupData['categories']?.length ?? 0} categories');
          }
        }

        // Drop all existing tables
        for (final table in tables) {
          await db.execute('DROP TABLE IF EXISTS ${table['name']}');
          if (kDebugMode) {
            print('Dropped table ${table['name']}');
          }
        }

        // Create all tables with new schema
        await _onCreate(db, newVersion);
        if (kDebugMode) {
          print('Created new database schema');
        }

        // Restore backed up data with schema adaptations
        await _restoreBackupData(db, backupData);
        if (kDebugMode) {
          print('Restored backed up data');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error during database upgrade: $e');
        }
        // If upgrade fails, ensure we at least have the basic tables
        await _onCreate(db, newVersion);
      }
    }
  }

  // Helper method to restore backed up data
  Future<void> _restoreBackupData(Database db, Map<String, List<Map<String, dynamic>>> backupData) async {
    try {
      // Restore users
      if (backupData.containsKey('users') && backupData['users']!.isNotEmpty) {
        for (final user in backupData['users']!) {
          try {
            // Convert is_active to status
            final Map<String, dynamic> newUser = {
              'id': user['id'],
              'name': user['name'],
              'email': user['email'],
              'password': user['password'],
              'phone': user['phone'],
              'avatar': user['avatar'],
              'status': user['is_active'] == 1 ? 'active' : 'inactive',
              'created_at': user['created_at'],
              'last_login': user['last_login'],
              'permissions': user['permissions'],
            };
            await db.insert('users', newUser, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring user: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored users');
        }
      }

      // Restore roles
      if (backupData.containsKey('roles') && backupData['roles']!.isNotEmpty) {
        for (final role in backupData['roles']!) {
          try {
            await db.insert('roles', role, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring role: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored roles');
        }
      }

      // Restore user_roles
      if (backupData.containsKey('user_roles') && backupData['user_roles']!.isNotEmpty) {
        for (final userRole in backupData['user_roles']!) {
          try {
            await db.insert('user_roles', userRole, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring user_role: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored user_roles');
        }
      }

      // Restore customers
      if (backupData.containsKey('customers') && backupData['customers']!.isNotEmpty) {
        for (final customer in backupData['customers']!) {
          try {
            // Convert is_active to status
            final Map<String, dynamic> newCustomer = {
              'id': customer['id'],
              'name': customer['name'],
              'type': customer['type'],
              'email': customer['email'],
              'phone': customer['phone'],
              'address': customer['address'],
              'contact_person': customer['contact_person'],
              'status': customer['is_active'] == 1 ? 'active' : 'inactive',
              'created_at': customer['created_at'],
              'notes': customer['notes'],
            };
            await db.insert('customers', newCustomer, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring customer: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored customers');
        }
      }

      // Restore suppliers
      if (backupData.containsKey('suppliers') && backupData['suppliers']!.isNotEmpty) {
        for (final supplier in backupData['suppliers']!) {
          try {
            // Convert is_active to status
            final Map<String, dynamic> newSupplier = {
              'id': supplier['id'],
              'name': supplier['name'],
              'email': supplier['email'],
              'phone': supplier['phone'],
              'address': supplier['address'],
              'contact_person': supplier['contact_person'],
              'category': supplier['category'],
              'status': supplier['is_active'] == 1 ? 'active' : 'inactive',
              'balance': supplier['balance'] ?? 0.0,
              'created_at': supplier['created_at'],
              'tax_number': supplier['tax_number'],
              'website': supplier['website'],
              'notes': supplier['notes'],
            };
            await db.insert('suppliers', newSupplier, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring supplier: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored suppliers');
        }
      }

      // Restore employees
      if (backupData.containsKey('employees') && backupData['employees']!.isNotEmpty) {
        for (final employee in backupData['employees']!) {
          try {
            // Convert status field
            final Map<String, dynamic> newEmployee = {
              'id': employee['id'],
              'name': employee['name'],
              'email': employee['email'],
              'phone': employee['phone'],
              'address': employee['address'],
              'position': employee['position'],
              'join_date': employee['join_date'],
              'salary': employee['salary'],
              'status': employee['status'] ?? 'active',
              'national_id': employee['national_id'],
              'bank_account': employee['bank_account'],
              'created_at': employee['created_at'],
              'updated_at': employee['updated_at'],
              'notes': employee['notes'],
            };
            await db.insert('employees', newEmployee, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring employee: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored employees');
        }
      }

      // Restore company_info
      if (backupData.containsKey('company_info') && backupData['company_info']!.isNotEmpty) {
        for (final info in backupData['company_info']!) {
          try {
            await db.insert('company_info', info, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring company_info: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored company_info');
        }
      }

      // Restore categories
      if (backupData.containsKey('categories') && backupData['categories']!.isNotEmpty) {
        for (final category in backupData['categories']!) {
          try {
            // Convert is_active to status
            final Map<String, dynamic> newCategory = {
              'id': category['id'],
              'name': category['name'],
              'type': category['type'],
              'description': category['description'],
              'status': category['is_active'] == 1 ? 'active' : 'inactive',
              'created_at': category['created_at'],
              'parent_id': null, // No parent_id in old schema
            };
            await db.insert('categories', newCategory, conflictAlgorithm: ConflictAlgorithm.replace);
          } catch (e) {
            if (kDebugMode) {
              print('Error restoring category: $e');
            }
          }
        }
        if (kDebugMode) {
          print('Restored categories');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error restoring backup data: $e');
      }
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // Enable foreign keys
    await db.execute('PRAGMA foreign_keys = ON');

    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        password TEXT NOT NULL,
        phone TEXT,
        avatar TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        created_at TEXT NOT NULL,
        last_login TEXT,
        permissions TEXT
      )
    ''');

    // Create roles table
    await db.execute('''
      CREATE TABLE roles(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL
      )
    ''');

    // Create user_roles table (many-to-many relationship)
    await db.execute('''
      CREATE TABLE user_roles(
        user_id INTEGER NOT NULL,
        role_id INTEGER NOT NULL,
        PRIMARY KEY (user_id, role_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
      )
    ''');

    // Create customers table
    await db.execute('''
      CREATE TABLE customers(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT,
        email TEXT,
        phone TEXT,
        address TEXT,
        contact_person TEXT,
        opening_balance REAL NOT NULL DEFAULT 0.0,
        status TEXT NOT NULL DEFAULT 'active',
        created_at TEXT NOT NULL,
        notes TEXT,
        preferred_service_types TEXT,
        payment_method TEXT,
        monthly_subscription_amount REAL,
        subscription_start_date TEXT,
        subscription_end_date TEXT,
        is_subscription_active INTEGER
      )
    ''');

    // Create index on customers
    await db.execute('CREATE INDEX idx_customers_name ON customers(name)');
    await db.execute('CREATE INDEX idx_customers_status ON customers(status)');

    // Create suppliers table
    await db.execute('''
      CREATE TABLE suppliers(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        contact_person TEXT,
        category TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        balance REAL NOT NULL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        tax_number TEXT,
        website TEXT,
        notes TEXT
      )
    ''');

    // Create index on suppliers
    await db.execute('CREATE INDEX idx_suppliers_name ON suppliers(name)');
    await db.execute('CREATE INDEX idx_suppliers_status ON suppliers(status)');
    await db.execute('CREATE INDEX idx_suppliers_category ON suppliers(category)');

    // Create employees table
    await db.execute('''
      CREATE TABLE employees(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE,
        phone TEXT,
        address TEXT,
        position TEXT,
        join_date TEXT,
        salary REAL,
        payment_type TEXT NOT NULL DEFAULT 'monthly',
        status TEXT NOT NULL DEFAULT 'active',
        national_id TEXT UNIQUE,
        bank_account TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        notes TEXT
      )
    ''');

    // Create index on employees
    await db.execute('CREATE INDEX idx_employees_name ON employees(name)');
    await db.execute('CREATE INDEX idx_employees_status ON employees(status)');
    await db.execute('CREATE INDEX idx_employees_payment_type ON employees(payment_type)');

    // Insert default employees (technicians)
    await db.insert('employees', {
      'name': 'أحمد محمد - فني تكييف',
      'email': '<EMAIL>',
      'phone': '**********',
      'address': 'الرياض',
      'position': 'فني تكييف',
      'join_date': DateTime.now().subtract(const Duration(days: 365)).toIso8601String(),
      'salary': 3000.0,
      'payment_type': 'monthly',
      'status': 'active',
      'national_id': '**********',
      'created_at': DateTime.now().toIso8601String(),
      'notes': 'فني خبير في أنظمة التكييف المركزي',
    });

    await db.insert('employees', {
      'name': 'محمد علي - فني صيانة',
      'email': '<EMAIL>',
      'phone': '0507654321',
      'address': 'جدة',
      'position': 'فني صيانة',
      'join_date': DateTime.now().subtract(const Duration(days: 180)).toIso8601String(),
      'salary': 150.0,
      'payment_type': 'daily',
      'status': 'active',
      'national_id': '0987654321',
      'created_at': DateTime.now().toIso8601String(),
      'notes': 'فني صيانة متخصص في الإصلاحات السريعة',
    });

    await db.insert('employees', {
      'name': 'خالد أحمد - فني تركيب',
      'email': '<EMAIL>',
      'phone': '**********',
      'address': 'الدمام',
      'position': 'فني تركيب',
      'join_date': DateTime.now().subtract(const Duration(days: 90)).toIso8601String(),
      'salary': 120.0,
      'payment_type': 'daily',
      'status': 'active',
      'national_id': '**********',
      'created_at': DateTime.now().toIso8601String(),
      'notes': 'متخصص في تركيب وحدات التكييف الجديدة',
    });

    // Create bank_accounts table
    await db.execute('''
      CREATE TABLE bank_accounts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bank_name TEXT NOT NULL,
        account_number TEXT NOT NULL UNIQUE,
        account_name TEXT NOT NULL,
        type TEXT,
        iban TEXT,
        swift_code TEXT,
        branch_name TEXT,
        opening_balance REAL NOT NULL DEFAULT 0.0,
        current_balance REAL NOT NULL DEFAULT 0.0,
        status TEXT NOT NULL DEFAULT 'active',
        balance REAL NOT NULL DEFAULT 0.0,
        created_at TEXT NOT NULL
      )
    ''');

    // Create index on bank_accounts
    await db.execute('CREATE INDEX idx_bank_accounts_status ON bank_accounts(status)');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        created_at TEXT NOT NULL,
        parent_id INTEGER,
        FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
      )
    ''');

    // Create index on categories
    await db.execute('CREATE INDEX idx_categories_type ON categories(type)');
    await db.execute('CREATE INDEX idx_categories_status ON categories(status)');
    await db.execute('CREATE INDEX idx_categories_parent_id ON categories(parent_id)');

    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        reference TEXT UNIQUE,
        date TEXT NOT NULL,
        amount REAL NOT NULL,
        type TEXT NOT NULL,
        category_id INTEGER,
        description TEXT,
        payment_method TEXT NOT NULL,
        bank_account_id INTEGER,
        cash_box_id INTEGER,
        supplier_id INTEGER,
        employee_id INTEGER,
        customer_id INTEGER,
        invoice_id INTEGER,
        created_by INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE SET NULL,
        FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
      )
    ''');

    // Create indexes on transactions
    await db.execute('CREATE INDEX idx_transactions_date ON transactions(date)');
    await db.execute('CREATE INDEX idx_transactions_type ON transactions(type)');
    await db.execute('CREATE INDEX idx_transactions_category_id ON transactions(category_id)');
    await db.execute('CREATE INDEX idx_transactions_bank_account_id ON transactions(bank_account_id)');
    await db.execute('CREATE INDEX idx_transactions_cash_box_id ON transactions(cash_box_id)');
    await db.execute('CREATE INDEX idx_transactions_supplier_id ON transactions(supplier_id)');
    await db.execute('CREATE INDEX idx_transactions_employee_id ON transactions(employee_id)');
    await db.execute('CREATE INDEX idx_transactions_customer_id ON transactions(customer_id)');

    // Create service_requests table
    await db.execute('''
      CREATE TABLE service_requests(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        request_number TEXT NOT NULL UNIQUE,
        customer_id INTEGER NOT NULL,
        service_type TEXT,
        description TEXT,
        address TEXT,
        scheduled_date TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        assigned_to INTEGER,
        technician_daily_rate REAL,
        completion_date TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL
      )
    ''');

    // Create indexes on service_requests
    await db.execute('CREATE INDEX idx_service_requests_customer_id ON service_requests(customer_id)');
    await db.execute('CREATE INDEX idx_service_requests_status ON service_requests(status)');
    await db.execute('CREATE INDEX idx_service_requests_assigned_to ON service_requests(assigned_to)');
    await db.execute('CREATE INDEX idx_service_requests_scheduled_date ON service_requests(scheduled_date)');

    // Create invoices table
    await db.execute('''
      CREATE TABLE invoices(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        customer_id INTEGER NOT NULL,
        date TEXT NOT NULL,
        due_date TEXT,
        subtotal REAL NOT NULL,
        tax REAL NOT NULL DEFAULT 0.0,
        discount REAL NOT NULL DEFAULT 0.0,
        total REAL NOT NULL,
        notes TEXT,
        status TEXT NOT NULL DEFAULT 'unpaid',
        payment_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
      )
    ''');

    // Create indexes on invoices
    await db.execute('CREATE INDEX idx_invoices_customer_id ON invoices(customer_id)');
    await db.execute('CREATE INDEX idx_invoices_date ON invoices(date)');
    await db.execute('CREATE INDEX idx_invoices_status ON invoices(status)');

    // Create invoice_items table
    await db.execute('''
      CREATE TABLE invoice_items(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        description TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total REAL NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
      )
    ''');

    // Create index on invoice_items
    await db.execute('CREATE INDEX idx_invoice_items_invoice_id ON invoice_items(invoice_id)');

    // Create company_info table
    await db.execute('''
      CREATE TABLE company_info(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name_ar TEXT NOT NULL,
        name_en TEXT,
        address_ar TEXT,
        address_en TEXT,
        phone_ar TEXT,
        phone_en TEXT,
        email TEXT,
        website TEXT,
        tax_number TEXT,
        commercial_register TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create inventory_items table
    await db.execute('''
      CREATE TABLE inventory_items(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        category_id INTEGER,
        quantity REAL NOT NULL DEFAULT 0.0,
        min_quantity REAL NOT NULL DEFAULT 0.0,
        unit TEXT,
        cost_price REAL,
        selling_price REAL,
        supplier_id INTEGER,
        location TEXT,
        status TEXT NOT NULL DEFAULT 'available',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
      )
    ''');

    // Create indexes on inventory_items
    await db.execute('CREATE INDEX idx_inventory_items_category_id ON inventory_items(category_id)');
    await db.execute('CREATE INDEX idx_inventory_items_supplier_id ON inventory_items(supplier_id)');
    await db.execute('CREATE INDEX idx_inventory_items_name ON inventory_items(name)');

    // Create salaries table
    await db.execute('''
      CREATE TABLE salaries(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        month INTEGER NOT NULL,
        year INTEGER NOT NULL,
        basic_salary REAL NOT NULL,
        allowances REAL NOT NULL DEFAULT 0.0,
        deductions REAL NOT NULL DEFAULT 0.0,
        net_salary REAL NOT NULL,
        paid_amount REAL NOT NULL DEFAULT 0.0,
        status TEXT NOT NULL DEFAULT 'pending',
        notes TEXT,
        created_by INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE (employee_id, month, year)
      )
    ''');

    // Create indexes on salaries
    await db.execute('CREATE INDEX idx_salaries_employee_id ON salaries(employee_id)');
    await db.execute('CREATE INDEX idx_salaries_month_year ON salaries(month, year)');

    // Create withdrawals table
    await db.execute('''
      CREATE TABLE withdrawals(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        reason TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        notes TEXT,
        approved_by INTEGER,
        approved_at TEXT,
        created_by INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
      )
    ''');

    // Create indexes on withdrawals
    await db.execute('CREATE INDEX idx_withdrawals_employee_id ON withdrawals(employee_id)');
    await db.execute('CREATE INDEX idx_withdrawals_date ON withdrawals(date)');
    await db.execute('CREATE INDEX idx_withdrawals_status ON withdrawals(status)');

    // Create notifications table
    await db.execute('''
      CREATE TABLE notifications(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT,
        is_read INTEGER NOT NULL DEFAULT 0,
        related_id INTEGER,
        related_type TEXT,
        user_id INTEGER,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    ''');

    // Create indexes on notifications
    await db.execute('CREATE INDEX idx_notifications_user_id ON notifications(user_id)');
    await db.execute('CREATE INDEX idx_notifications_is_read ON notifications(is_read)');
    await db.execute('CREATE INDEX idx_notifications_created_at ON notifications(created_at)');

    // Create reports table
    await db.execute('''
      CREATE TABLE reports(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        parameters TEXT,
        created_by INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
      )
    ''');

    // Create indexes on reports
    await db.execute('CREATE INDEX idx_reports_created_by ON reports(created_by)');
    await db.execute('CREATE INDEX idx_reports_type ON reports(type)');
    await db.execute('CREATE INDEX idx_reports_created_at ON reports(created_at)');

    // Create cash_boxes table
    await db.execute('''
      CREATE TABLE cash_boxes(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        opening_balance REAL NOT NULL DEFAULT 0.0,
        current_balance REAL NOT NULL DEFAULT 0.0,
        is_active INTEGER NOT NULL DEFAULT 1,
        currency TEXT NOT NULL DEFAULT 'SAR',
        created_at TEXT NOT NULL,
        updated_at TEXT,
        firestore_id TEXT,
        last_sync_time TEXT,
        sync_status TEXT DEFAULT 'pending',
        version INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Create cash_box_transactions table
    await db.execute('''
      CREATE TABLE cash_box_transactions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cash_box_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        reference TEXT,
        reference_type TEXT,
        transaction_date TEXT NOT NULL,
        created_at TEXT NOT NULL,
        firestore_id TEXT,
        FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for cash boxes
    await db.execute('CREATE INDEX idx_cash_boxes_name ON cash_boxes(name)');
    await db.execute('CREATE INDEX idx_cash_boxes_is_active ON cash_boxes(is_active)');
    await db.execute('CREATE INDEX idx_cash_boxes_firestore_id ON cash_boxes(firestore_id)');

    // Create indexes for cash box transactions
    await db.execute('CREATE INDEX idx_cash_box_transactions_cash_box_id ON cash_box_transactions(cash_box_id)');
    await db.execute('CREATE INDEX idx_cash_box_transactions_type ON cash_box_transactions(type)');
    await db.execute('CREATE INDEX idx_cash_box_transactions_date ON cash_box_transactions(transaction_date)');
    await db.execute('CREATE INDEX idx_cash_box_transactions_reference ON cash_box_transactions(reference, reference_type)');

    // Create security_logs table
    await db.execute('''
      CREATE TABLE security_logs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        event TEXT NOT NULL,
        user_id INTEGER,
        user_email TEXT,
        details TEXT,
        ip_address TEXT,
        timestamp TEXT NOT NULL,
        created_at TEXT NOT NULL DEFAULT (datetime('now'))
      )
    ''');

    // Create indexes for security logs
    await db.execute('CREATE INDEX idx_security_logs_event ON security_logs(event)');
    await db.execute('CREATE INDEX idx_security_logs_user_id ON security_logs(user_id)');
    await db.execute('CREATE INDEX idx_security_logs_timestamp ON security_logs(timestamp)');

    // Create error_logs table
    await db.execute('''
      CREATE TABLE error_logs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        error_type TEXT NOT NULL,
        severity TEXT NOT NULL,
        user_message TEXT NOT NULL,
        technical_message TEXT,
        context TEXT,
        stack_trace TEXT,
        additional_data TEXT,
        timestamp TEXT NOT NULL,
        created_at TEXT NOT NULL DEFAULT (datetime('now'))
      )
    ''');

    // Create indexes for error logs
    await db.execute('CREATE INDEX idx_error_logs_type ON error_logs(error_type)');
    await db.execute('CREATE INDEX idx_error_logs_severity ON error_logs(severity)');
    await db.execute('CREATE INDEX idx_error_logs_timestamp ON error_logs(timestamp)');

    // Create employee_financials table
    await db.execute('''
      CREATE TABLE employee_financials(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        payment_date TEXT NOT NULL,
        description TEXT,
        payment_method TEXT,
        cash_box_id INTEGER,
        status TEXT NOT NULL DEFAULT 'pending',
        created_at TEXT NOT NULL,
        updated_at TEXT,
        firestore_id TEXT,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE SET NULL
      )
    ''');

    // Create indexes for employee financials
    await db.execute('CREATE INDEX idx_employee_financials_employee_id ON employee_financials(employee_id)');
    await db.execute('CREATE INDEX idx_employee_financials_type ON employee_financials(type)');
    await db.execute('CREATE INDEX idx_employee_financials_status ON employee_financials(status)');
    await db.execute('CREATE INDEX idx_employee_financials_payment_date ON employee_financials(payment_date)');
    await db.execute('CREATE INDEX idx_employee_financials_cash_box_id ON employee_financials(cash_box_id)');

    // Insert initial roles
    await db.insert('roles', {'name': 'admin'});
    await db.insert('roles', {'name': 'employee'});

    // Insert admin user with username "admin" and password "1"
    if (kDebugMode) {
      print('Creating admin user...');
    }

    final adminUserId = await db.insert('users', {
      'name': 'admin',
      'email': '<EMAIL>',
      'password': '1',
      'status': 'active',
      'created_at': DateTime.now().toIso8601String(),
    });

    if (kDebugMode) {
      print('Admin user created with ID: $adminUserId');
    }

    // Get admin role id
    final List<Map<String, dynamic>> adminRoleMaps = await db.query(
      'roles',
      where: 'name = ?',
      whereArgs: ['admin'],
    );

    if (adminRoleMaps.isNotEmpty) {
      final adminRoleId = adminRoleMaps.first['id'];
      // Assign admin role to admin user
      await db.insert('user_roles', {
        'user_id': adminUserId,
        'role_id': adminRoleId,
      });

      if (kDebugMode) {
        print('Admin role assigned to admin user');
      }
    } else {
      if (kDebugMode) {
        print('ERROR: Admin role not found!');
      }
    }

    // Insert default categories
    final now = DateTime.now().toIso8601String();

    // Income categories
    await db.insert('categories', {
      'name': 'مبيعات',
      'type': 'income',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'خدمات',
      'type': 'income',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'استرداد',
      'type': 'income',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'استثمارات',
      'type': 'income',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'إيرادات أخرى',
      'type': 'income',
      'status': 'active',
      'created_at': now,
    });

    // Expense categories
    await db.insert('categories', {
      'name': 'مشتريات',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'رواتب',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'إيجار',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'مرافق',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'صيانة',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'وقود',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'أدوات',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'تسويق',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'تأمين',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'ضرائب',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    await db.insert('categories', {
      'name': 'مصروفات أخرى',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    // Special categories
    await db.insert('categories', {
      'name': 'سداد للمورد',
      'type': 'expense',
      'status': 'active',
      'created_at': now,
    });

    // تطبيق تحسينات الأداء
    try {
      if (kDebugMode) {
        print('تطبيق تحسينات الأداء لقاعدة البيانات...');
      }

      await DatabasePerformanceOptimizer.applyAllOptimizations(db);

      if (kDebugMode) {
        print('✅ تم تطبيق تحسينات الأداء بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تطبيق تحسينات الأداء: $e');
      }
    }
  }

  // Insert a user
  Future<int> insertUser(User user) async {
    final db = await database;

    // Insert user into users table
    final Map<String, dynamic> userData = {
      'name': user.name,
      'email': user.email,
      'password': user.password,
      'phone': user.phone,
      'avatar': user.avatar,
      'status': user.isActive ? 'active' : 'inactive',
      'created_at': user.createdAt.toIso8601String(),
      'permissions': user.permissions != null ? json.encode(user.permissions) : null,
    };

    // Add last_login if it exists
    if (user.lastLogin != null) {
      userData['last_login'] = user.lastLogin!.toIso8601String();
    }

    // If user has an ID and it's not a new user, include it
    if (user.id > 0) {
      userData['id'] = user.id;
    }

    final userId = await db.insert('users', userData);

    // Get the actual user ID (either the one we provided or the auto-generated one)
    final actualUserId = user.id > 0 ? user.id : userId;

    // Insert user roles into user_roles table
    for (final roleName in user.roles) {
      final List<Map<String, dynamic>> roleMaps = await db.query(
        'roles',
        where: 'name = ?',
        whereArgs: [roleName],
      );
      if (roleMaps.isNotEmpty) {
        final roleId = roleMaps.first['id'];
        await db.insert('user_roles', {
          'user_id': actualUserId,
          'role_id': roleId,
        });
      }
    }

    return userId;
  }

  // Update a user
  Future<int> updateUser(User user) async {
    final db = await database;

    // Prepare user data for update
    final Map<String, dynamic> userData = {
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'avatar': user.avatar,
      'status': user.isActive ? 'active' : 'inactive',
      'permissions': user.permissions != null ? json.encode(user.permissions) : null,
    };

    // Only update password if it's provided
    if (user.password != null && user.password!.isNotEmpty) {
      userData['password'] = user.password;
    }

    // Add last_login if it exists
    if (user.lastLogin != null) {
      userData['last_login'] = user.lastLogin!.toIso8601String();
    }

    // Update user in users table
    final rowsAffected = await db.update(
      'users',
      userData,
      where: 'id = ?',
      whereArgs: [user.id],
    );

    // Remove existing roles for the user
    await db.delete(
      'user_roles',
      where: 'user_id = ?',
      whereArgs: [user.id],
    );

    // Insert updated roles for the user
    for (final roleName in user.roles) {
      final List<Map<String, dynamic>> roleMaps = await db.query(
        'roles',
        where: 'name = ?',
        whereArgs: [roleName],
      );
      if (roleMaps.isNotEmpty) {
        final roleId = roleMaps.first['id'];
        await db.insert('user_roles', {
          'user_id': user.id,
          'role_id': roleId,
        });
      }
    }

    return rowsAffected;
  }

  // Get all users
  Future<List<User>> getUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> userMaps = await db.query('users');

    final List<User> users = [];
    for (final userMap in userMaps) {
      // Fetch roles for each user
      final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
        SELECT r.name FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
      ''', [userMap['id']]);

      final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

      // Create User object using fromMap constructor and add roles
      users.add(User.fromMap({...userMap, 'roles': roles}));
    }

    return users;
  }

  // Get a user by ID
  Future<User?> getUserById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> userMaps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (userMaps.isNotEmpty) {
      final userMap = userMaps.first;

      // Fetch roles for the user
      final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
        SELECT r.name FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
      ''', [userMap['id']]);

      final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

      // Create User object using fromMap constructor and add roles
      return User.fromMap({...userMap, 'roles': roles});
    }
    return null;
  }

  // Get a user by email
  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> userMaps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (userMaps.isNotEmpty) {
      final userMap = userMaps.first;

      // Fetch roles for the user
      final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
        SELECT r.name FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
      ''', [userMap['id']]);

      final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

      // Create User object using fromMap constructor and add roles
      return User.fromMap({...userMap, 'roles': roles});
    }
    return null;
  }

  // Get a user by username
  Future<User?> getUserByUsername(String username) async {
    final db = await database;
    final List<Map<String, dynamic>> userMaps = await db.query(
      'users',
      where: 'name = ?',
      whereArgs: [username],
    );

    if (userMaps.isNotEmpty) {
      final userMap = userMaps.first;

      // Fetch roles for the user
      final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
        SELECT r.name FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
      ''', [userMap['id']]);

      final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

      // Create User object using fromMap constructor and add roles
      return User.fromMap({...userMap, 'roles': roles});
    }
    return null;
  }

  // Update user permissions
  Future<int> updateUserPermissions(int userId, String permissionsJson) async {
    final db = await database;
    return await db.update(
      'users',
      {'permissions': permissionsJson},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  // Insert or update a user (for sync purposes)
  Future<int> insertOrUpdateUser(User user) async {
    final db = await database;

    // For existing users, check by email since id is int in this model
    if (user.email != null && user.email!.isNotEmpty) {
      final existingUsers = await db.query(
        'users',
        where: 'email = ?',
        whereArgs: [user.email],
      );

      if (existingUsers.isNotEmpty) {
        // Update existing user
        final userData = {
          'name': user.name,
          'email': user.email,
          'phone': user.phone,
          'is_active': user.isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        };

        await db.update(
          'users',
          userData,
          where: 'email = ?',
          whereArgs: [user.email],
        );

        return existingUsers.first['id'] as int;
      }
    }

    // Insert new user
    final userData = {
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'is_active': user.isActive ? 1 : 0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    return await db.insert('users', userData);
  }

  // Delete a user
  Future<int> deleteUser(int id) async {
    final db = await database;

    // First delete user_roles entries
    await db.delete(
      'user_roles',
      where: 'user_id = ?',
      whereArgs: [id],
    );

    // Then delete the user
    return await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Close the database
  Future<void> close() async {
    final db = await database;
    db.close();
  }

  // ============================================================================
  // SYNC-RELATED METHODS
  // ============================================================================

  /// Update the Firestore ID for a local record
  Future<void> updateFirestoreId(String tableName, int localId, String firestoreId) async {
    final db = await database;
    await db.update(
      tableName,
      {'firestore_id': firestoreId},
      where: 'id = ?',
      whereArgs: [localId],
    );
  }

  /// Update the sync status of a record
  Future<void> updateSyncStatus(String tableName, int localId, String syncStatus, {DateTime? lastSyncTime}) async {
    final db = await database;
    final updateData = <String, dynamic>{
      'sync_status': syncStatus,
    };

    if (lastSyncTime != null) {
      updateData['last_sync_time'] = lastSyncTime.toIso8601String();
    }

    await db.update(
      tableName,
      updateData,
      where: 'id = ?',
      whereArgs: [localId],
    );
  }

  /// Add a sync conflict to the database
  Future<void> addSyncConflict(Map<String, dynamic> conflict) async {
    final db = await database;

    // Ensure sync_conflicts table exists
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_conflicts (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        local_data TEXT NOT NULL,
        remote_data TEXT NOT NULL,
        detected_at TEXT NOT NULL,
        resolution TEXT,
        is_resolved INTEGER DEFAULT 0,
        resolved_at TEXT,
        metadata TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    await db.insert('sync_conflicts', conflict);
  }

  /// Get all unresolved sync conflicts
  Future<List<Map<String, dynamic>>> getUnresolvedConflicts() async {
    final db = await database;

    // Ensure sync_conflicts table exists
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_conflicts (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        local_data TEXT NOT NULL,
        remote_data TEXT NOT NULL,
        detected_at TEXT NOT NULL,
        resolution TEXT,
        is_resolved INTEGER DEFAULT 0,
        resolved_at TEXT,
        metadata TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    return await db.query(
      'sync_conflicts',
      where: 'is_resolved = ?',
      whereArgs: [0],
      orderBy: 'detected_at DESC',
    );
  }

  /// Resolve a sync conflict
  Future<void> resolveSyncConflict(String conflictId, String resolution) async {
    final db = await database;
    await db.update(
      'sync_conflicts',
      {
        'resolution': resolution,
        'is_resolved': 1,
        'resolved_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [conflictId],
    );
  }

  /// Add an operation to the sync queue
  Future<void> addToSyncQueue(String entityType, String entityId, String operation, Map<String, dynamic> data) async {
    final db = await database;

    // Ensure sync_queue table exists
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        operation TEXT NOT NULL,
        data TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0,
        last_error TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    await db.insert('sync_queue', {
      'entity_type': entityType,
      'entity_id': entityId,
      'operation': operation,
      'data': json.encode(data),
      'retry_count': 0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  /// Get all pending sync operations
  Future<List<Map<String, dynamic>>> getPendingSyncOperations() async {
    final db = await database;

    // Ensure sync_queue table exists
    await db.execute('''
      CREATE TABLE IF NOT EXISTS sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        operation TEXT NOT NULL,
        data TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0,
        last_error TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    return await db.query(
      'sync_queue',
      orderBy: 'created_at ASC',
    );
  }

  /// Remove an operation from the sync queue
  Future<void> removeFromSyncQueue(int queueId) async {
    final db = await database;
    await db.delete(
      'sync_queue',
      where: 'id = ?',
      whereArgs: [queueId],
    );
  }

  /// Update sync queue retry information
  Future<void> updateSyncQueueRetry(int queueId, String errorMessage) async {
    final db = await database;
    await db.update(
      'sync_queue',
      {
        'retry_count': 'retry_count + 1',
        'last_error': errorMessage,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [queueId],
    );
  }

  /// Get records that need synchronization from a specific table
  Future<List<Map<String, dynamic>>> getRecordsNeedingSync(String tableName) async {
    final db = await database;
    return await db.query(
      tableName,
      where: 'sync_status IN (?, ?, ?) AND is_deleted = ?',
      whereArgs: ['pendingUpload', 'pendingDownload', 'failed', 0],
      orderBy: 'updated_at ASC',
    );
  }

  /// Get a record by its Firestore ID
  Future<Map<String, dynamic>?> getRecordByFirestoreId(String tableName, String firestoreId) async {
    final db = await database;
    final results = await db.query(
      tableName,
      where: 'firestore_id = ?',
      whereArgs: [firestoreId],
      limit: 1,
    );

    return results.isNotEmpty ? results.first : null;
  }

  /// Get all records from a table with sync information
  Future<List<Map<String, dynamic>>> getAllRecordsWithSync(String tableName) async {
    final db = await database;
    return await db.query(tableName);
  }

  /// Update record version for optimistic locking
  Future<void> updateRecordVersion(String tableName, int localId, int newVersion) async {
    final db = await database;
    await db.update(
      tableName,
      {
        'version': newVersion,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [localId],
    );
  }

  /// Mark record as deleted (soft delete)
  Future<void> markRecordAsDeleted(String tableName, int localId) async {
    final db = await database;
    await db.update(
      tableName,
      {
        'is_deleted': 1,
        'sync_status': 'pendingUpload',
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [localId],
    );
  }

  /// Get sync statistics for a table
  Future<Map<String, int>> getSyncStats(String tableName) async {
    final db = await database;

    final syncedCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableName WHERE sync_status = ? AND is_deleted = ?',
      ['synced', 0],
    );

    final pendingCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableName WHERE sync_status IN (?, ?) AND is_deleted = ?',
      ['pendingUpload', 'pendingDownload', 0],
    );

    final failedCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableName WHERE sync_status = ? AND is_deleted = ?',
      ['failed', 0],
    );

    final conflictCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableName WHERE sync_status = ? AND is_deleted = ?',
      ['conflict', 0],
    );

    return {
      'synced': syncedCount.first['count'] as int,
      'pending': pendingCount.first['count'] as int,
      'failed': failedCount.first['count'] as int,
      'conflicts': conflictCount.first['count'] as int,
    };
  }
}
