import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/models/company_info.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../repositories/company_info_repository.dart';

class CompanyInfoScreen extends StatefulWidget {
  const CompanyInfoScreen({super.key});

  @override
  State<CompanyInfoScreen> createState() => _CompanyInfoScreenState();
}

class _CompanyInfoScreenState extends State<CompanyInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _companyInfoRepository = CompanyInfoRepository();
  bool _isLoading = true;
  bool _isSaving = false;

  // Form controllers
  final _nameArController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _addressArController = TextEditingController();
  final _addressEnController = TextEditingController();
  final _phoneArController = TextEditingController();
  final _phoneEnController = TextEditingController();
  final _emailController = TextEditingController();
  final _websiteController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _commercialRegisterController = TextEditingController();

  CompanyInfo? _companyInfo;

  @override
  void initState() {
    super.initState();
    _loadCompanyInfo();
  }

  @override
  void dispose() {
    _nameArController.dispose();
    _nameEnController.dispose();
    _addressArController.dispose();
    _addressEnController.dispose();
    _phoneArController.dispose();
    _phoneEnController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _taxNumberController.dispose();
    _commercialRegisterController.dispose();
    super.dispose();
  }

  Future<void> _loadCompanyInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // First ensure the table exists
      await _companyInfoRepository.ensureTableExists();

      // Then load the company info
      final companyInfo = await _companyInfoRepository.getCompanyInfo();

      if (companyInfo != null) {
        _companyInfo = companyInfo;
        _nameArController.text = companyInfo.nameAr;
        _nameEnController.text = companyInfo.nameEn ?? '';
        _addressArController.text = companyInfo.addressAr ?? '';
        _addressEnController.text = companyInfo.addressEn ?? '';
        _phoneArController.text = companyInfo.phoneAr ?? '';
        _phoneEnController.text = companyInfo.phoneEn ?? '';
        _emailController.text = companyInfo.email ?? '';
        _websiteController.text = companyInfo.website ?? '';
        _taxNumberController.text = companyInfo.taxNumber ?? '';
        _commercialRegisterController.text = companyInfo.commercialRegister ?? '';
      }
    } catch (e) {
      debugPrint('Error loading company info: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء تحميل بيانات الشركة')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveCompanyInfo() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final companyInfo = CompanyInfo(
        id: _companyInfo?.id,
        nameAr: _nameArController.text,
        nameEn: _nameEnController.text.isEmpty ? null : _nameEnController.text,
        addressAr: _addressArController.text.isEmpty ? null : _addressArController.text,
        addressEn: _addressEnController.text.isEmpty ? null : _addressEnController.text,
        phoneAr: _phoneArController.text.isEmpty ? null : _phoneArController.text,
        phoneEn: _phoneEnController.text.isEmpty ? null : _phoneEnController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        website: _websiteController.text.isEmpty ? null : _websiteController.text,
        taxNumber: _taxNumberController.text.isEmpty ? null : _taxNumberController.text,
        commercialRegister: _commercialRegisterController.text.isEmpty ? null : _commercialRegisterController.text,
        createdAt: _companyInfo?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await _companyInfoRepository.saveCompanyInfo(companyInfo);

      if (!mounted) return;

      if (result > 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ بيانات الشركة بنجاح')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء حفظ بيانات الشركة')),
        );
      }
    } catch (e) {
      debugPrint('Error saving company info: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('حدث خطأ أثناء حفظ بيانات الشركة')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'معلومات الشركة'),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Logo preview
                    Center(
                      child: Column(
                        children: [
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              shape: BoxShape.circle,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                'assets/images/snowflake_logo.png',
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                          const Text(
                            'شعار الشركة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Company name section
                    const Text(
                      'اسم الشركة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Row(
                      children: [
                        // Arabic name
                        Expanded(
                          child: CustomTextField(
                            controller: _nameArController,
                            label: 'الاسم بالعربية',
                            hint: 'أدخل اسم الشركة بالعربية',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء إدخال اسم الشركة بالعربية';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        // English name
                        Expanded(
                          child: CustomTextField(
                            controller: _nameEnController,
                            label: 'الاسم بالإنجليزية',
                            hint: 'أدخل اسم الشركة بالإنجليزية',
                            textDirection: TextDirection.ltr,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Address section
                    const Text(
                      'العنوان',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Row(
                      children: [
                        // Arabic address
                        Expanded(
                          child: CustomTextField(
                            controller: _addressArController,
                            label: 'العنوان بالعربية',
                            hint: 'أدخل عنوان الشركة بالعربية',
                            maxLines: 2,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        // English address
                        Expanded(
                          child: CustomTextField(
                            controller: _addressEnController,
                            label: 'العنوان بالإنجليزية',
                            hint: 'أدخل عنوان الشركة بالإنجليزية',
                            textDirection: TextDirection.ltr,
                            maxLines: 2,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Contact section
                    const Text(
                      'معلومات الاتصال',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Row(
                      children: [
                        // Arabic phone
                        Expanded(
                          child: CustomTextField(
                            controller: _phoneArController,
                            label: 'رقم الهاتف بالعربية',
                            hint: 'أدخل رقم الهاتف بالعربية',
                            keyboardType: TextInputType.phone,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        // English phone
                        Expanded(
                          child: CustomTextField(
                            controller: _phoneEnController,
                            label: 'رقم الهاتف بالإنجليزية',
                            hint: 'أدخل رقم الهاتف بالإنجليزية',
                            textDirection: TextDirection.ltr,
                            keyboardType: TextInputType.phone,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    Row(
                      children: [
                        // Email
                        Expanded(
                          child: CustomTextField(
                            controller: _emailController,
                            label: 'البريد الإلكتروني',
                            hint: 'أدخل البريد الإلكتروني',
                            textDirection: TextDirection.ltr,
                            keyboardType: TextInputType.emailAddress,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        // Website
                        Expanded(
                          child: CustomTextField(
                            controller: _websiteController,
                            label: 'الموقع الإلكتروني',
                            hint: 'أدخل الموقع الإلكتروني',
                            textDirection: TextDirection.ltr,
                            keyboardType: TextInputType.url,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Legal information section
                    const Text(
                      'المعلومات القانونية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Row(
                      children: [
                        // Tax number
                        Expanded(
                          child: CustomTextField(
                            controller: _taxNumberController,
                            label: 'الرقم الضريبي',
                            hint: 'أدخل الرقم الضريبي',
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        // Commercial register
                        Expanded(
                          child: CustomTextField(
                            controller: _commercialRegisterController,
                            label: 'السجل التجاري',
                            hint: 'أدخل رقم السجل التجاري',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingXL),

                    // Save button
                    Center(
                      child: CustomButton(
                        text: 'حفظ البيانات',
                        onPressed: _isSaving ? null : _saveCompanyInfo,
                        isLoading: _isSaving,
                        width: 200,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingL),
                  ],
                ),
              ),
            ),
    );
  }
}
