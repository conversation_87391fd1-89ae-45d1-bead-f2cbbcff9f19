import 'dart:convert';
import 'package:flutter/material.dart';

/// Represents a permission operation (view, add, edit, delete)
enum PermissionOperation {
  view,
  add,
  edit,
  delete,
}

/// Represents a module in the application
enum PermissionModule {
  dashboard,
  invoices,
  transactions,
  customers,
  serviceRequests,
  inventory,
  employees,
  suppliers,
  payroll,
  reports,
  bankAccounts,
  settings,
  users,
}

/// Represents a permission in the system
class Permission {
  final PermissionModule module;
  final PermissionOperation operation;
  final bool granted;

  Permission({
    required this.module,
    required this.operation,
    this.granted = false,
  });

  /// Creates a permission from a map
  factory Permission.fromMap(Map<String, dynamic> map) {
    return Permission(
      module: _parseModule(map['module']),
      operation: _parseOperation(map['operation']),
      granted: map['granted'] ?? false,
    );
  }

  /// Creates a permission from JSON
  factory Permission.fromJson(String source) =>
      Permission.fromMap(json.decode(source) as Map<String, dynamic>);

  /// Converts the permission to a map
  Map<String, dynamic> toMap() {
    return {
      'module': module.toString().split('.').last,
      'operation': operation.toString().split('.').last,
      'granted': granted,
    };
  }

  /// Converts the permission to JSON
  String toJson() => json.encode(toMap());

  /// Creates a copy of the permission with the given fields replaced
  Permission copyWith({
    PermissionModule? module,
    PermissionOperation? operation,
    bool? granted,
  }) {
    return Permission(
      module: module ?? this.module,
      operation: operation ?? this.operation,
      granted: granted ?? this.granted,
    );
  }

  /// Parses a module string to a PermissionModule enum
  static PermissionModule _parseModule(String module) {
    switch (module.toLowerCase()) {
      case 'dashboard':
        return PermissionModule.dashboard;
      case 'invoices':
        return PermissionModule.invoices;
      case 'transactions':
        return PermissionModule.transactions;
      case 'customers':
        return PermissionModule.customers;
      case 'servicerequests':
        return PermissionModule.serviceRequests;
      case 'inventory':
        return PermissionModule.inventory;
      case 'employees':
        return PermissionModule.employees;
      case 'suppliers':
        return PermissionModule.suppliers;
      case 'payroll':
        return PermissionModule.payroll;
      case 'reports':
        return PermissionModule.reports;
      case 'bankaccounts':
        return PermissionModule.bankAccounts;
      case 'settings':
        return PermissionModule.settings;
      case 'users':
        return PermissionModule.users;
      default:
        return PermissionModule.dashboard;
    }
  }

  /// Parses an operation string to a PermissionOperation enum
  static PermissionOperation _parseOperation(String operation) {
    switch (operation.toLowerCase()) {
      case 'view':
        return PermissionOperation.view;
      case 'add':
        return PermissionOperation.add;
      case 'edit':
        return PermissionOperation.edit;
      case 'delete':
        return PermissionOperation.delete;
      default:
        return PermissionOperation.view;
    }
  }

  /// Gets the module name in Arabic
  static String getModuleName(PermissionModule module) {
    switch (module) {
      case PermissionModule.dashboard:
        return 'لوحة التحكم';
      case PermissionModule.invoices:
        return 'الفواتير';
      case PermissionModule.transactions:
        return 'المعاملات المالية';
      case PermissionModule.customers:
        return 'العملاء';
      case PermissionModule.serviceRequests:
        return 'طلبات الخدمة';
      case PermissionModule.inventory:
        return 'المخزون';
      case PermissionModule.employees:
        return 'الموظفين';
      case PermissionModule.suppliers:
        return 'الموردين';
      case PermissionModule.payroll:
        return 'الرواتب';
      case PermissionModule.reports:
        return 'التقارير';
      case PermissionModule.bankAccounts:
        return 'الحسابات البنكية';
      case PermissionModule.settings:
        return 'الإعدادات';
      case PermissionModule.users:
        return 'المستخدمين';
    }
  }

  /// Gets the operation name in Arabic
  static String getOperationName(PermissionOperation operation) {
    switch (operation) {
      case PermissionOperation.view:
        return 'عرض';
      case PermissionOperation.add:
        return 'إضافة';
      case PermissionOperation.edit:
        return 'تعديل';
      case PermissionOperation.delete:
        return 'حذف';
    }
  }

  /// Gets the operation icon
  static IconData getOperationIcon(PermissionOperation operation) {
    switch (operation) {
      case PermissionOperation.view:
        return Icons.visibility;
      case PermissionOperation.add:
        return Icons.add_circle;
      case PermissionOperation.edit:
        return Icons.edit;
      case PermissionOperation.delete:
        return Icons.delete;
    }
  }
}
