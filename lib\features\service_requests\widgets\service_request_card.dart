import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_request.dart';

class ServiceRequestCard extends StatelessWidget {
  final ServiceRequest serviceRequest;
  final VoidCallback? onTap;

  const ServiceRequestCard({
    super.key,
    required this.serviceRequest,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: ServiceRequest.getPriorityColor(serviceRequest.priority).withAlpha(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                    child: Center(
                      child: Icon(
                        _getRequestIcon(),
                        color: ServiceRequest.getPriorityColor(serviceRequest.priority),
                        size: 28,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          serviceRequest.reference,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          serviceRequest.customerName,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: ServiceRequest.getStatusColor(serviceRequest.status).withAlpha(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      border: Border.all(
                        color: ServiceRequest.getStatusColor(serviceRequest.status),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      ServiceRequest.getStatusName(serviceRequest.status),
                      style: TextStyle(
                        fontSize: 12,
                        color: ServiceRequest.getStatusColor(serviceRequest.status),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(height: 1),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                children: [
                  _buildInfoItem(
                    Icons.build,
                    ServiceRequest.getRequestTypeName(serviceRequest.requestType),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  _buildInfoItem(
                    Icons.calendar_today,
                    DateFormat('dd/MM/yyyy').format(serviceRequest.scheduledDate),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Row(
                children: [
                  _buildInfoItem(
                    Icons.priority_high,
                    ServiceRequest.getPriorityName(serviceRequest.priority),
                    color: ServiceRequest.getPriorityColor(serviceRequest.priority),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  if (serviceRequest.assignedToName != null)
                    _buildInfoItem(
                      Icons.person,
                      serviceRequest.assignedToName!,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getRequestIcon() {
    switch (serviceRequest.requestType) {
      case ServiceRequestType.maintenance:
        return Icons.ac_unit;
      case ServiceRequestType.installation:
        return Icons.add_box;
      case ServiceRequestType.inspection:
        return Icons.repeat;
      case ServiceRequestType.repair:
        return Icons.water_damage;
      case ServiceRequestType.other:
        return Icons.build;
    }
  }

  Widget _buildInfoItem(IconData icon, String text, {Color? color}) {
    return Expanded(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color ?? AppColors.textSecondary,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: color ?? AppColors.textSecondary,
                fontWeight: color != null ? FontWeight.bold : FontWeight.normal,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
