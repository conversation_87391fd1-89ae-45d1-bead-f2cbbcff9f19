import 'package:flutter/material.dart';
import '../../../config/constants.dart';

class SettingsSelectionTile extends StatelessWidget {
  final String title;
  final String value;
  final VoidCallback onTap;
  final bool enabled;

  const SettingsSelectionTile({
    super.key,
    required this.title,
    required this.value,
    required this.onTap,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: enabled ? AppColors.textPrimary : AppColors.textSecondary,
        ),
      ),
      subtitle: Text(
        value,
        style: TextStyle(
          color: enabled ? AppColors.textSecondary : Colors.grey,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
      ),
      enabled: enabled,
      onTap: enabled ? onTap : null,
    );
  }
}
