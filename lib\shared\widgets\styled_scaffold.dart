import 'package:flutter/material.dart';
import '../../config/constants.dart';
import 'ui_components.dart';
import 'app_drawer.dart';
import 'app_title_with_logo.dart';

/// سكافولد منسق مع علامة مائية وتأثيرات انتقالية
class StyledScaffold extends StatelessWidget {
  /// عنوان الشاشة
  final String title;
  
  /// محتوى الشاشة
  final Widget body;
  
  /// زر الإجراء العائم (اختياري)
  final Widget? floatingActionButton;
  
  /// موضع زر الإجراء العائم
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  
  /// إجراءات شريط التطبيق (اختياري)
  final List<Widget>? actions;
  
  /// زر الرجوع (اختياري)
  final Widget? leading;
  
  /// إظهار الدرج
  final bool showDrawer;
  
  /// إظهار العلامة المائية
  final bool showWatermark;
  
  /// حجم العلامة المائية
  final double watermarkSize;
  
  /// شفافية العلامة المائية
  final double watermarkOpacity;
  
  /// دوران العلامة المائية
  final double watermarkRotation;
  
  /// لون خلفية شريط التطبيق
  final Color? appBarBackgroundColor;
  
  /// لون نص شريط التطبيق
  final Color? appBarForegroundColor;
  
  /// ارتفاع شريط التطبيق
  final double? appBarHeight;
  
  /// محتوى أسفل شريط التطبيق
  final PreferredSizeWidget? bottomWidget;
  
  /// إظهار أيقونة الثلج في العنوان
  final bool showTitleIcon;
  
  /// حجم أيقونة الثلج في العنوان
  final double titleIconSize;
  
  /// إنشاء سكافولد منسق
  const StyledScaffold({
    super.key,
    required this.title,
    required this.body,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.actions,
    this.leading,
    this.showDrawer = true,
    this.showWatermark = true,
    this.watermarkSize = 200,
    this.watermarkOpacity = 0.05,
    this.watermarkRotation = 15,
    this.appBarBackgroundColor,
    this.appBarForegroundColor,
    this.appBarHeight,
    this.bottomWidget,
    this.showTitleIcon = true,
    this.titleIconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AppTitleWithLogo(
          title: title,
          showIcon: showTitleIcon,
          iconSize: titleIconSize,
          textColor: appBarForegroundColor,
        ),
        actions: actions,
        leading: leading,
        backgroundColor: appBarBackgroundColor,
        foregroundColor: appBarForegroundColor,
        toolbarHeight: appBarHeight,
        bottom: bottomWidget,
      ),
      drawer: showDrawer ? const AppDrawer() : null,
      body: Stack(
        children: [
          // علامة مائية في الخلفية
          if (showWatermark)
            Positioned.fill(
              child: WatermarkLogo(
                size: watermarkSize,
                opacity: watermarkOpacity,
                rotation: watermarkRotation,
              ),
            ),
          
          // محتوى الشاشة
          body,
        ],
      ),
      floatingActionButton: floatingActionButton != null
          ? ScaleOnTap(
              onTap: floatingActionButton is FloatingActionButton
                  ? (floatingActionButton as FloatingActionButton).onPressed
                  : null,
              child: floatingActionButton!,
            )
          : null,
      floatingActionButtonLocation: floatingActionButtonLocation,
    );
  }
}

/// سكافولد منسق مع تمرير
class StyledScrollScaffold extends StatelessWidget {
  /// عنوان الشاشة
  final String title;
  
  /// محتوى الشاشة
  final List<Widget> children;
  
  /// تباعد داخلي
  final EdgeInsetsGeometry padding;
  
  /// زر الإجراء العائم (اختياري)
  final Widget? floatingActionButton;
  
  /// موضع زر الإجراء العائم
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  
  /// إجراءات شريط التطبيق (اختياري)
  final List<Widget>? actions;
  
  /// زر الرجوع (اختياري)
  final Widget? leading;
  
  /// إظهار الدرج
  final bool showDrawer;
  
  /// إظهار العلامة المائية
  final bool showWatermark;
  
  /// حجم العلامة المائية
  final double watermarkSize;
  
  /// شفافية العلامة المائية
  final double watermarkOpacity;
  
  /// دوران العلامة المائية
  final double watermarkRotation;
  
  /// لون خلفية شريط التطبيق
  final Color? appBarBackgroundColor;
  
  /// لون نص شريط التطبيق
  final Color? appBarForegroundColor;
  
  /// ارتفاع شريط التطبيق
  final double? appBarHeight;
  
  /// محتوى أسفل شريط التطبيق
  final PreferredSizeWidget? bottomWidget;
  
  /// دالة تنفذ عند السحب للتحديث
  final Future<void> Function()? onRefresh;
  
  /// إظهار أيقونة الثلج في العنوان
  final bool showTitleIcon;
  
  /// حجم أيقونة الثلج في العنوان
  final double titleIconSize;
  
  /// إنشاء سكافولد منسق مع تمرير
  const StyledScrollScaffold({
    super.key,
    required this.title,
    required this.children,
    this.padding = const EdgeInsets.all(AppDimensions.spacingM),
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.actions,
    this.leading,
    this.showDrawer = true,
    this.showWatermark = true,
    this.watermarkSize = 200,
    this.watermarkOpacity = 0.05,
    this.watermarkRotation = 15,
    this.appBarBackgroundColor,
    this.appBarForegroundColor,
    this.appBarHeight,
    this.bottomWidget,
    this.onRefresh,
    this.showTitleIcon = true,
    this.titleIconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    final scrollView = SingleChildScrollView(
      padding: padding,
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children.map((child) {
          // تطبيق تأثير FadeInAnimation على كل عنصر
          final index = children.indexOf(child);
          return FadeInAnimation(
            delay: Duration(milliseconds: 100 * (index + 1)),
            child: Padding(
              padding: EdgeInsets.only(
                bottom: index < children.length - 1 ? AppDimensions.spacingM : 0,
              ),
              child: child,
            ),
          );
        }).toList(),
      ),
    );

    return StyledScaffold(
      title: title,
      actions: actions,
      leading: leading,
      showDrawer: showDrawer,
      showWatermark: showWatermark,
      watermarkSize: watermarkSize,
      watermarkOpacity: watermarkOpacity,
      watermarkRotation: watermarkRotation,
      appBarBackgroundColor: appBarBackgroundColor,
      appBarForegroundColor: appBarForegroundColor,
      appBarHeight: appBarHeight,
      bottomWidget: bottomWidget,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      showTitleIcon: showTitleIcon,
      titleIconSize: titleIconSize,
      body: onRefresh != null
          ? RefreshIndicator(
              onRefresh: onRefresh!,
              color: AppColors.primary,
              backgroundColor: AppColors.surface,
              child: scrollView,
            )
          : scrollView,
    );
  }
}
