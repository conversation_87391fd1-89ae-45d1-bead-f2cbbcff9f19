import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_request.dart';
import '../../../shared/models/cash_box.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/cash_box_repository.dart';
import '../../../shared/widgets/responsive_layout.dart';

class CompleteServiceRequestScreen extends StatefulWidget {
  final ServiceRequest serviceRequest;

  const CompleteServiceRequestScreen({
    super.key,
    required this.serviceRequest,
  });

  @override
  State<CompleteServiceRequestScreen> createState() => _CompleteServiceRequestScreenState();
}

class _CompleteServiceRequestScreenState extends State<CompleteServiceRequestScreen> {
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();
  final _formKey = GlobalKey<FormState>();
  
  String _solution = '';
  String _notes = '';
  int? _selectedCashBoxId;
  List<CashBox> _cashBoxes = [];
  bool _isLoading = false;
  bool _isLoadingCashBoxes = true;

  @override
  void initState() {
    super.initState();
    _loadCashBoxes();
  }

  Future<void> _loadCashBoxes() async {
    try {
      final cashBoxes = await _cashBoxRepository.getActiveCashBoxes();
      setState(() {
        _cashBoxes = cashBoxes;
        _isLoadingCashBoxes = false;
        // Select first cash box by default
        if (cashBoxes.isNotEmpty) {
          _selectedCashBoxId = cashBoxes.first.id;
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingCashBoxes = false;
      });
      if (kDebugMode) {
        print('❌ خطأ في تحميل الصناديق: $e');
      }
    }
  }

  Future<void> _completeServiceRequest() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCashBoxId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صندوق للدفع'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    _formKey.currentState!.save();

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _serviceRequestRepository.completeServiceRequestWithPayment(
        serviceRequestId: widget.serviceRequest.id!,
        solution: _solution,
        cashBoxId: _selectedCashBoxId!,
        notes: _notes.isEmpty ? null : _notes,
      );

      if (success && mounted) {
        // Show success dialog
        await _showSuccessDialog();
        
        // Return to previous screen with success result
        Navigator.pop(context, true);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إكمال طلب الخدمة: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _showSuccessDialog() async {
    final technicianDailyRate = widget.serviceRequest.technicianDailyRate;
    final hasPayment = technicianDailyRate != null && technicianDailyRate > 0;
    
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 64,
        ),
        title: const Text(
          'تم إكمال طلب الخدمة بنجاح',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.green,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'طلب الخدمة رقم: ${widget.serviceRequest.reference}',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            if (hasPayment) ...[
              const Divider(),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.payment, color: Colors.orange, size: 20),
                        SizedBox(width: 8),
                        Text(
                          'تم خصم أجر الفني',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('الفني:'),
                        Text(
                          widget.serviceRequest.assignedToName ?? 'غير محدد',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('المبلغ:'),
                        Text(
                          '${technicianDailyRate!.toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إكمال طلب الخدمة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: ResponsiveContainer(
        child: _isLoadingCashBoxes
            ? const Center(child: CircularProgressIndicator())
            : Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service request info
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'معلومات طلب الخدمة',
                                style: AppTextStyles.heading3,
                              ),
                              const SizedBox(height: 12),
                              _buildInfoRow('رقم الطلب:', widget.serviceRequest.reference),
                              _buildInfoRow('العميل:', widget.serviceRequest.customerName),
                              _buildInfoRow('نوع الخدمة:', _getServiceTypeText(widget.serviceRequest.requestType)),
                              if (widget.serviceRequest.assignedToName != null)
                                _buildInfoRow('الفني المسؤول:', widget.serviceRequest.assignedToName!),
                              if (widget.serviceRequest.technicianDailyRate != null && widget.serviceRequest.technicianDailyRate! > 0)
                                _buildInfoRow(
                                  'أجر الفني:',
                                  '${widget.serviceRequest.technicianDailyRate!.toStringAsFixed(2)} ر.س',
                                  valueColor: Colors.orange,
                                ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Solution field
                      const Text(
                        'الحل المطبق',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'وصف الحل والأعمال المنجزة',
                          hintText: 'اكتب تفاصيل الحل والإجراءات التي تم تطبيقها...',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.build_circle),
                        ),
                        maxLines: 4,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال وصف الحل المطبق';
                          }
                          return null;
                        },
                        onSaved: (value) {
                          _solution = value!.trim();
                        },
                      ),
                      const SizedBox(height: 16),

                      // Cash box selection (if payment required)
                      if (widget.serviceRequest.technicianDailyRate != null && widget.serviceRequest.technicianDailyRate! > 0) ...[
                        const Text(
                          'اختيار الصندوق للدفع',
                          style: AppTextStyles.heading3,
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<int>(
                          decoration: const InputDecoration(
                            labelText: 'الصندوق',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.account_balance_wallet),
                          ),
                          value: _selectedCashBoxId,
                          items: _cashBoxes.map((cashBox) => DropdownMenuItem<int>(
                            value: cashBox.id,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(cashBox.name),
                                Text(
                                  'الرصيد: ${cashBox.currentBalance.toStringAsFixed(2)} ر.س',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: cashBox.currentBalance >= (widget.serviceRequest.technicianDailyRate ?? 0)
                                        ? Colors.green
                                        : Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          )).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCashBoxId = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'يرجى اختيار صندوق للدفع';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Notes field
                      const Text(
                        'ملاحظات إضافية (اختياري)',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات',
                          hintText: 'أي ملاحظات إضافية حول الخدمة...',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                        onSaved: (value) {
                          _notes = value?.trim() ?? '';
                        },
                      ),
                      const SizedBox(height: 24),

                      // Complete button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _isLoading ? null : _completeServiceRequest,
                          icon: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.check_circle),
                          label: Text(_isLoading ? 'جاري الإكمال...' : 'إكمال طلب الخدمة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor,
                fontWeight: valueColor != null ? FontWeight.bold : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getServiceTypeText(ServiceRequestType type) {
    switch (type) {
      case ServiceRequestType.installation:
        return 'تركيب';
      case ServiceRequestType.maintenance:
        return 'صيانة';
      case ServiceRequestType.repair:
        return 'إصلاح';
      case ServiceRequestType.inspection:
        return 'فحص';
      case ServiceRequestType.other:
        return 'أخرى';
    }
  }
}
