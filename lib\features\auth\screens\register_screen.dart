import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../state/auth_state.dart';
import '../widgets/auth_header.dart';
import '../../../shared/widgets/custom_button.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  
  @override
  Widget build(BuildContext context) {
    final authState = Provider.of<AuthState>(context);
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إنشاء حساب جديد'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 450,
                minHeight: size.height - 2 * AppDimensions.paddingL - kToolbarHeight,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(
                    title: 'إنشاء حساب جديد',
                    subtitle: 'أدخل بياناتك لإنشاء حساب جديد',
                  ),
                  const SizedBox(height: AppDimensions.paddingL),
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingL),
                      child: FormBuilder(
                        key: _formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Full Name field
                            FormBuilderTextField(
                              name: 'name',
                              decoration: const InputDecoration(
                                labelText: 'الاسم الكامل',
                                prefixIcon: Icon(Icons.person),
                                hintText: 'أدخل الاسم الكامل',
                              ),
                              textDirection: TextDirection.rtl,
                              textInputAction: TextInputAction.next,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'يرجى إدخال الاسم الكامل',
                                ),
                              ]),
                            ),
                            const SizedBox(height: AppDimensions.paddingM),
                            
                            // Email field
                            FormBuilderTextField(
                              name: 'email',
                              decoration: const InputDecoration(
                                labelText: 'البريد الإلكتروني',
                                prefixIcon: Icon(Icons.email),
                                hintText: 'أدخل البريد الإلكتروني',
                              ),
                              textDirection: TextDirection.ltr,
                              keyboardType: TextInputType.emailAddress,
                              textInputAction: TextInputAction.next,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'يرجى إدخال البريد الإلكتروني',
                                ),
                                FormBuilderValidators.email(
                                  errorText: 'يرجى إدخال بريد إلكتروني صحيح',
                                ),
                              ]),
                            ),
                            const SizedBox(height: AppDimensions.paddingM),
                            
                            // Password field
                            FormBuilderTextField(
                              name: 'password',
                              decoration: InputDecoration(
                                labelText: 'كلمة المرور',
                                prefixIcon: const Icon(Icons.lock),
                                hintText: 'أدخل كلمة المرور',
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                              ),
                              textDirection: TextDirection.ltr,
                              obscureText: _obscurePassword,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'يرجى إدخال كلمة المرور',
                                ),
                                FormBuilderValidators.minLength(
                                  6,
                                  errorText: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
                                ),
                              ]),
                            ),
                            const SizedBox(height: AppDimensions.paddingM),
                            
                            // Confirm Password field
                            FormBuilderTextField(
                              name: 'confirm_password',
                              decoration: InputDecoration(
                                labelText: 'تأكيد كلمة المرور',
                                prefixIcon: const Icon(Icons.lock_outline),
                                hintText: 'أعد إدخال كلمة المرور',
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscureConfirmPassword
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscureConfirmPassword = !_obscureConfirmPassword;
                                    });
                                  },
                                ),
                              ),
                              textDirection: TextDirection.ltr,
                              obscureText: _obscureConfirmPassword,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'يرجى تأكيد كلمة المرور',
                                ),
                                (value) {
                                  if (value != _formKey.currentState?.fields['password']?.value) {
                                    return 'كلمات المرور غير متطابقة';
                                  }
                                  return null;
                                },
                              ]),
                            ),
                            const SizedBox(height: AppDimensions.paddingM),
                            
                            // Terms and conditions checkbox
                            FormBuilderCheckbox(
                              name: 'agree_terms',
                              initialValue: false,
                              title: const Text(
                                'أوافق على الشروط والأحكام',
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              validator: FormBuilderValidators.equal(
                                true,
                                errorText: 'يجب الموافقة على الشروط والأحكام',
                              ),
                              controlAffinity: ListTileControlAffinity.leading,
                            ),
                            
                            // Error message
                            if (authState.errorMessage != null)
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: AppDimensions.paddingS,
                                ),
                                child: Text(
                                  authState.errorMessage!,
                                  style: const TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            
                            const SizedBox(height: AppDimensions.paddingL),
                            
                            // Register button
                            CustomButton(
                              text: 'إنشاء حساب',
                              isLoading: _isLoading,
                              onPressed: _isLoading ? null : _handleRegister,
                            ),
                            
                            const SizedBox(height: AppDimensions.paddingM),
                            
                            // Login link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  'لديك حساب بالفعل؟',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.pushReplacementNamed(
                                      context,
                                      AppRoutes.login,
                                    );
                                  },
                                  child: const Text('تسجيل الدخول'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Future<void> _handleRegister() async {
    // Clear any previous errors
    Provider.of<AuthState>(context, listen: false).clearError();
    
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });
      
      final formData = _formKey.currentState!.value;
      final name = formData['name'] as String;
      final email = formData['email'] as String;
      final password = formData['password'] as String;
      
      final success = await Provider.of<AuthState>(context, listen: false)
          .register(name, email, password);
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        if (success) {
          // Navigate to dashboard on successful registration
          Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        }
      }
    }
  }
}
