import 'dart:convert';
import 'package:flutter/material.dart';

enum SalaryStatus {
  pending,
  paid,
  partiallyPaid,
  cancelled,
}

enum PaymentMethod {
  cash,
  bankTransfer,
}

class Salary {
  final int? id;
  final int employeeId;
  final String employeeName; // This is a display-only field, not stored in the database
  final int month;
  final int year;
  final double basicSalary;
  final double allowances;
  final double deductions;
  final double netSalary;
  final double paidAmount;
  final SalaryStatus status;
  final String? notes;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final PaymentMethod? paymentMethod;
  final int? paymentEmployeeId;
  final String? paymentEmployeeName; // This is a display-only field, not stored in the database
  final int? paymentBankAccountId;
  final String? paymentBankAccountName; // This is a display-only field, not stored in the database

  Salary({
    this.id,
    required this.employeeId,
    required this.employeeName,
    required this.month,
    required this.year,
    required this.basicSalary,
    required this.allowances,
    required this.deductions,
    required this.netSalary,
    required this.paidAmount,
    required this.status,
    this.notes,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.paymentMethod,
    this.paymentEmployeeId,
    this.paymentEmployeeName,
    this.paymentBankAccountId,
    this.paymentBankAccountName,
  });

  factory Salary.fromMap(Map<String, dynamic> map) {
    return Salary(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      employeeName: map['employee_name'] as String,
      month: map['month'] as int,
      year: map['year'] as int,
      basicSalary: map['basic_salary'] as double,
      allowances: map['allowances'] as double,
      deductions: map['deductions'] as double,
      netSalary: map['net_salary'] as double,
      paidAmount: map['paid_amount'] as double,
      status: _parseStatus(map['status'] as String),
      notes: map['notes'] as String?,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      paymentMethod: map['payment_method'] != null
          ? _parsePaymentMethod(map['payment_method'] as String)
          : null,
      paymentEmployeeId: map['payment_employee_id'] as int?,
      paymentEmployeeName: map['payment_employee_name'] as String?,
      paymentBankAccountId: map['payment_bank_account_id'] as int?,
      paymentBankAccountName: map['payment_bank_account_name'] as String?,
    );
  }

  factory Salary.fromJson(String source) =>
      Salary.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'employee_name': employeeName,
      'month': month,
      'year': year,
      'basic_salary': basicSalary,
      'allowances': allowances,
      'deductions': deductions,
      'net_salary': netSalary,
      'paid_amount': paidAmount,
      'status': status.toString().split('.').last,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'payment_method': paymentMethod?.toString().split('.').last,
      'payment_employee_id': paymentEmployeeId,
      'payment_bank_account_id': paymentBankAccountId,
    };
  }

  String toJson() => json.encode(toMap());

  Salary copyWith({
    int? id,
    int? employeeId,
    String? employeeName,
    int? month,
    int? year,
    double? basicSalary,
    double? allowances,
    double? deductions,
    double? netSalary,
    double? paidAmount,
    SalaryStatus? status,
    String? notes,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    PaymentMethod? paymentMethod,
    int? paymentEmployeeId,
    String? paymentEmployeeName,
    int? paymentBankAccountId,
    String? paymentBankAccountName,
  }) {
    return Salary(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      month: month ?? this.month,
      year: year ?? this.year,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      deductions: deductions ?? this.deductions,
      netSalary: netSalary ?? this.netSalary,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentEmployeeId: paymentEmployeeId ?? this.paymentEmployeeId,
      paymentEmployeeName: paymentEmployeeName ?? this.paymentEmployeeName,
      paymentBankAccountId: paymentBankAccountId ?? this.paymentBankAccountId,
      paymentBankAccountName: paymentBankAccountName ?? this.paymentBankAccountName,
    );
  }

  static SalaryStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return SalaryStatus.pending;
      case 'paid':
        return SalaryStatus.paid;
      case 'partially_paid':
      case 'partiallypaid':
        return SalaryStatus.partiallyPaid;
      case 'cancelled':
        return SalaryStatus.cancelled;
      default:
        return SalaryStatus.pending;
    }
  }

  static PaymentMethod _parsePaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'banktransfer':
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      default:
        return PaymentMethod.cash;
    }
  }

  static String getStatusName(SalaryStatus status) {
    switch (status) {
      case SalaryStatus.pending:
        return 'معلق';
      case SalaryStatus.paid:
        return 'مدفوع';
      case SalaryStatus.partiallyPaid:
        return 'مدفوع جزئياً';
      case SalaryStatus.cancelled:
        return 'ملغى';
    }
  }

  static Color getStatusColor(SalaryStatus status) {
    switch (status) {
      case SalaryStatus.pending:
        return Colors.amber;
      case SalaryStatus.paid:
        return Colors.green;
      case SalaryStatus.partiallyPaid:
        return Colors.blue;
      case SalaryStatus.cancelled:
        return Colors.red;
    }
  }

  static String getMonthName(int month) {
    switch (month) {
      case 1:
        return 'يناير';
      case 2:
        return 'فبراير';
      case 3:
        return 'مارس';
      case 4:
        return 'أبريل';
      case 5:
        return 'مايو';
      case 6:
        return 'يونيو';
      case 7:
        return 'يوليو';
      case 8:
        return 'أغسطس';
      case 9:
        return 'سبتمبر';
      case 10:
        return 'أكتوبر';
      case 11:
        return 'نوفمبر';
      case 12:
        return 'ديسمبر';
      default:
        return '';
    }
  }

  static String getPaymentMethodName(PaymentMethod? method) {
    if (method == null) return '';

    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
    }
  }

  static IconData getPaymentMethodIcon(PaymentMethod? method) {
    if (method == null) return Icons.help_outline;

    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
    }
  }
}
