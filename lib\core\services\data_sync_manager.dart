import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../database/database_helper.dart';
import '../../shared/models/sync_model.dart';
import 'firebase_service.dart';
import 'connectivity_service.dart';

/// Enhanced data synchronization manager with conflict resolution
class DataSyncManager {
  static final DataSyncManager _instance = DataSyncManager._internal();
  factory DataSyncManager() => _instance;
  DataSyncManager._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final ConnectivityService _connectivityService = ConnectivityService();

  /// Sync a single model with conflict resolution
  Future<SyncResult> syncModel<T extends SyncModel>(
    T model,
    String collectionName,
    T Function(Map<String, dynamic>) fromFirestore,
  ) async {
    try {
      if (!await _connectivityService.shouldSync()) {
        return SyncResult.noConnection();
      }

      // Check if model exists in Firestore
      if (model.firestoreId != null) {
        return await _updateExistingModel(model, collectionName, fromFirestore);
      } else {
        return await _createNewModel(model, collectionName);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing model: $e');
      }
      return SyncResult.error(e.toString());
    }
  }

  /// Create new model in Firestore
  Future<SyncResult> _createNewModel<T extends SyncModel>(
    T model,
    String collectionName,
  ) async {
    try {
      final firestoreData = model.toFirestoreMap();
      final docId = await _firebaseService.createDocument(collectionName, firestoreData);
      
      // Update local model with Firestore ID
      await _updateLocalModelWithFirestoreId(model, docId);
      
      if (kDebugMode) {
        print('✅ Created new model in Firestore: $docId');
      }
      
      return SyncResult.success();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating model in Firestore: $e');
      }
      return SyncResult.error(e.toString());
    }
  }

  /// Update existing model with conflict resolution
  Future<SyncResult> _updateExistingModel<T extends SyncModel>(
    T localModel,
    String collectionName,
    T Function(Map<String, dynamic>) fromFirestore,
  ) async {
    try {
      // Get current Firestore version
      final firestoreDoc = await _firebaseService.getDocument(
        collectionName,
        localModel.firestoreId!,
      );

      if (firestoreDoc == null) {
        // Document doesn't exist in Firestore, create it
        return await _createNewModel(localModel, collectionName);
      }

      final firestoreModel = fromFirestore(firestoreDoc);
      
      // Check for conflicts
      final conflictResult = _detectConflict(localModel, firestoreModel);
      
      if (conflictResult.hasConflict) {
        return await _resolveConflict(
          localModel,
          firestoreModel,
          collectionName,
          conflictResult,
        );
      } else {
        // No conflict, update Firestore with local changes
        return await _updateFirestore(localModel, collectionName);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating existing model: $e');
      }
      return SyncResult.error(e.toString());
    }
  }

  /// Detect conflicts between local and Firestore models
  ConflictDetectionResult _detectConflict<T extends SyncModel>(
    T localModel,
    T firestoreModel,
  ) {
    final result = ConflictDetectionResult();

    // Check if both models were modified after last sync
    if (localModel.updatedAt != null && 
        firestoreModel.updatedAt != null &&
        localModel.lastSyncTime != null) {
      
      final localModifiedAfterSync = localModel.updatedAt!.isAfter(localModel.lastSyncTime!);
      final firestoreModifiedAfterSync = firestoreModel.updatedAt!.isAfter(localModel.lastSyncTime!);
      
      if (localModifiedAfterSync && firestoreModifiedAfterSync) {
        result.hasConflict = true;
        result.conflictType = ConflictType.bothModified;
        result.localVersion = localModel.version;
        result.firestoreVersion = firestoreModel.version;
      }
    }

    // Check version conflicts
    if (localModel.version != firestoreModel.version) {
      result.hasConflict = true;
      result.conflictType = ConflictType.versionMismatch;
      result.localVersion = localModel.version;
      result.firestoreVersion = firestoreModel.version;
    }

    return result;
  }

  /// Resolve conflicts using timestamp-based resolution
  Future<SyncResult> _resolveConflict<T extends SyncModel>(
    T localModel,
    T firestoreModel,
    String collectionName,
    ConflictDetectionResult conflictResult,
  ) async {
    try {
      // Use timestamp-based resolution: latest wins
      final useLocal = localModel.updatedAt != null &&
          firestoreModel.updatedAt != null &&
          localModel.updatedAt!.isAfter(firestoreModel.updatedAt!);

      if (useLocal) {
        // Local version is newer, update Firestore
        if (kDebugMode) {
          print('🔄 Resolving conflict: Using local version (newer)');
        }
        return await _updateFirestore(localModel, collectionName);
      } else {
        // Firestore version is newer, update local
        if (kDebugMode) {
          print('🔄 Resolving conflict: Using Firestore version (newer)');
        }
        return await _updateLocal(firestoreModel);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resolving conflict: $e');
      }
      return SyncResult.error(e.toString());
    }
  }

  /// Update Firestore with local model
  Future<SyncResult> _updateFirestore<T extends SyncModel>(
    T model,
    String collectionName,
  ) async {
    try {
      final firestoreData = model.toFirestoreMap();
      firestoreData['version'] = model.version + 1;
      firestoreData['updated_at'] = FieldValue.serverTimestamp();
      
      await _firebaseService.updateDocument(
        collectionName,
        model.firestoreId!,
        firestoreData,
      );
      
      // Update local model with new version and sync time
      await _updateLocalSyncMetadata(model, model.version + 1);
      
      if (kDebugMode) {
        print('✅ Updated Firestore document: ${model.firestoreId}');
      }
      
      return SyncResult.success();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating Firestore: $e');
      }
      return SyncResult.error(e.toString());
    }
  }

  /// Update local database with Firestore model
  Future<SyncResult> _updateLocal<T extends SyncModel>(T model) async {
    try {
      // This would need to be implemented per model type
      // For now, just update sync metadata
      await _updateLocalSyncMetadata(model, model.version);
      
      if (kDebugMode) {
        print('✅ Updated local model from Firestore');
      }
      
      return SyncResult.success();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating local model: $e');
      }
      return SyncResult.error(e.toString());
    }
  }

  /// Update local model with Firestore ID
  Future<void> _updateLocalModelWithFirestoreId<T extends SyncModel>(
    T model,
    String firestoreId,
  ) async {
    // This would need to be implemented per model type
    // Update the local database record with the Firestore ID
    if (kDebugMode) {
      print('📝 Updating local model with Firestore ID: $firestoreId');
    }
  }

  /// Update local sync metadata
  Future<void> _updateLocalSyncMetadata<T extends SyncModel>(
    T model,
    int newVersion,
  ) async {
    // This would need to be implemented per model type
    // Update version, last_sync_time, and sync_status in local database
    if (kDebugMode) {
      print('📝 Updating local sync metadata: version $newVersion');
    }
  }
}

/// Conflict detection result
class ConflictDetectionResult {
  bool hasConflict = false;
  ConflictType? conflictType;
  int? localVersion;
  int? firestoreVersion;
}

/// Types of conflicts
enum ConflictType {
  bothModified,
  versionMismatch,
  deletedLocally,
  deletedRemotely,
}

/// Sync result for individual operations
class SyncResult {
  final bool success;
  final String? error;
  final ConflictDetectionResult? conflict;

  SyncResult._(this.success, this.error, this.conflict);

  factory SyncResult.success() => SyncResult._(true, null, null);
  factory SyncResult.error(String error) => SyncResult._(false, error, null);
  factory SyncResult.noConnection() => SyncResult._(false, 'No internet connection', null);
  factory SyncResult.conflict(ConflictDetectionResult conflict) => 
      SyncResult._(false, 'Conflict detected', conflict);

  bool get hasError => !success;
  bool get hasConflict => conflict != null;
}
