import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// Migration to add subscription fields to customers table
class AddSubscriptionFieldsToCustomers {
  static Future<void> migrate(Database db) async {
    try {
      // Check if the columns already exist
      final List<Map<String, dynamic>> columns = await db.rawQuery(
        "PRAGMA table_info(customers)"
      );
      
      final columnNames = columns.map((c) => c['name'] as String).toList();
      
      // Add preferred_service_types column if it doesn't exist
      if (!columnNames.contains('preferred_service_types')) {
        await db.execute(
          'ALTER TABLE customers ADD COLUMN preferred_service_types TEXT'
        );
        if (kDebugMode) {
          print('Added preferred_service_types column to customers table');
        }
      }
      
      // Add payment_method column if it doesn't exist
      if (!columnNames.contains('payment_method')) {
        await db.execute(
          'ALTER TABLE customers ADD COLUMN payment_method TEXT'
        );
        if (kDebugMode) {
          print('Added payment_method column to customers table');
        }
      }
      
      // Add monthly_subscription_amount column if it doesn't exist
      if (!columnNames.contains('monthly_subscription_amount')) {
        await db.execute(
          'ALTER TABLE customers ADD COLUMN monthly_subscription_amount REAL'
        );
        if (kDebugMode) {
          print('Added monthly_subscription_amount column to customers table');
        }
      }
      
      // Add subscription_start_date column if it doesn't exist
      if (!columnNames.contains('subscription_start_date')) {
        await db.execute(
          'ALTER TABLE customers ADD COLUMN subscription_start_date TEXT'
        );
        if (kDebugMode) {
          print('Added subscription_start_date column to customers table');
        }
      }
      
      // Add subscription_end_date column if it doesn't exist
      if (!columnNames.contains('subscription_end_date')) {
        await db.execute(
          'ALTER TABLE customers ADD COLUMN subscription_end_date TEXT'
        );
        if (kDebugMode) {
          print('Added subscription_end_date column to customers table');
        }
      }
      
      // Add is_subscription_active column if it doesn't exist
      if (!columnNames.contains('is_subscription_active')) {
        await db.execute(
          'ALTER TABLE customers ADD COLUMN is_subscription_active INTEGER'
        );
        if (kDebugMode) {
          print('Added is_subscription_active column to customers table');
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Error adding subscription fields to customers table: $e');
      }
    }
  }
}
