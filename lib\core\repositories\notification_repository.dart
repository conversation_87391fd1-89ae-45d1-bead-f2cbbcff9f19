import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/notification_model.dart';

class NotificationRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all notifications
  Future<List<NotificationModel>> getAllNotifications() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'notifications',
        orderBy: 'created_at DESC',
      );
      
      return List.generate(maps.length, (i) {
        return NotificationModel.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting notifications: $e');
      }
      return [];
    }
  }

  // Get unread notifications count
  Future<int> getUnreadNotificationsCount() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM notifications WHERE is_read = 0',
      );
      
      return result.first['count'] as int;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting unread notifications count: $e');
      }
      return 0;
    }
  }

  // Get notification by ID
  Future<NotificationModel?> getNotificationById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'notifications',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (maps.isNotEmpty) {
        return NotificationModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting notification by ID: $e');
      }
      return null;
    }
  }

  // Insert a new notification
  Future<int> insertNotification(NotificationModel notification) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'notifications',
        notification.toMap(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting notification: $e');
      }
      return -1;
    }
  }

  // Mark notification as read
  Future<int> markNotificationAsRead(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'notifications',
        {'is_read': 1},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error marking notification as read: $e');
      }
      return 0;
    }
  }

  // Mark all notifications as read
  Future<int> markAllNotificationsAsRead() async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'notifications',
        {'is_read': 1},
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error marking all notifications as read: $e');
      }
      return 0;
    }
  }

  // Delete a notification
  Future<int> deleteNotification(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'notifications',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting notification: $e');
      }
      return 0;
    }
  }

  // Delete all notifications
  Future<int> deleteAllNotifications() async {
    try {
      final db = await _dbHelper.database;
      return await db.delete('notifications');
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting all notifications: $e');
      }
      return 0;
    }
  }
}
