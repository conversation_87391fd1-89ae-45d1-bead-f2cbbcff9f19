import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/constants.dart';

/// مدير الثيم الموحد للتطبيق
class AppThemeManager {
  static const String _tag = 'AppThemeManager';

  /// الثيم الفاتح
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // الألوان الأساسية
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      
      // الخطوط
      fontFamily: 'Cairo',
      textTheme: _buildTextTheme(Brightness.light),
      
      // شريط التطبيق
      appBarTheme: _buildAppBarTheme(Brightness.light),
      
      // الأزرار
      elevatedButtonTheme: _buildElevatedButtonTheme(),
      outlinedButtonTheme: _buildOutlinedButtonTheme(),
      textButtonTheme: _buildTextButtonTheme(),
      
      // البطاقات
      cardTheme: _buildCardTheme(),
      
      // حقول الإدخال
      inputDecorationTheme: _buildInputDecorationTheme(Brightness.light),
      
      // القوائم
      listTileTheme: _buildListTileTheme(),
      
      // الحوارات
      dialogTheme: _buildDialogTheme(),
      
      // شريط التنقل السفلي
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(),
      
      // الأيقونات
      iconTheme: _buildIconTheme(Brightness.light),
      
      // الفواصل
      dividerTheme: _buildDividerTheme(),
      
      // التبديل والمربعات
      switchTheme: _buildSwitchTheme(),
      checkboxTheme: _buildCheckboxTheme(),
      
      // شريط التقدم
      progressIndicatorTheme: _buildProgressIndicatorTheme(),
      
      // الرقائق (Chips)
      chipTheme: _buildChipTheme(Brightness.light),
      
      // التبويبات
      tabBarTheme: _buildTabBarTheme(Brightness.light),
      
      // القوائم المنسدلة
      dropdownMenuTheme: _buildDropdownMenuTheme(),
      
      // دعم RTL
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  /// الثيم الداكن
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // الألوان الأساسية
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
      ),
      
      // الخطوط
      fontFamily: 'Cairo',
      textTheme: _buildTextTheme(Brightness.dark),
      
      // شريط التطبيق
      appBarTheme: _buildAppBarTheme(Brightness.dark),
      
      // الأزرار
      elevatedButtonTheme: _buildElevatedButtonTheme(),
      outlinedButtonTheme: _buildOutlinedButtonTheme(),
      textButtonTheme: _buildTextButtonTheme(),
      
      // البطاقات
      cardTheme: _buildCardTheme(),
      
      // حقول الإدخال
      inputDecorationTheme: _buildInputDecorationTheme(Brightness.dark),
      
      // القوائم
      listTileTheme: _buildListTileTheme(),
      
      // الحوارات
      dialogTheme: _buildDialogTheme(),
      
      // شريط التنقل السفلي
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(),
      
      // الأيقونات
      iconTheme: _buildIconTheme(Brightness.dark),
      
      // الفواصل
      dividerTheme: _buildDividerTheme(),
      
      // التبديل والمربعات
      switchTheme: _buildSwitchTheme(),
      checkboxTheme: _buildCheckboxTheme(),
      
      // شريط التقدم
      progressIndicatorTheme: _buildProgressIndicatorTheme(),
      
      // الرقائق (Chips)
      chipTheme: _buildChipTheme(Brightness.dark),
      
      // التبويبات
      tabBarTheme: _buildTabBarTheme(Brightness.dark),
      
      // القوائم المنسدلة
      dropdownMenuTheme: _buildDropdownMenuTheme(),
      
      // دعم RTL
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  /// بناء نمط النصوص
  static TextTheme _buildTextTheme(Brightness brightness) {
    final baseColor = brightness == Brightness.light ? Colors.black87 : Colors.white70;
    
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: baseColor,
        height: 1.2,
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: baseColor,
        height: 1.2,
      ),
      displaySmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),
      headlineLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),
      headlineMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.4,
      ),
      headlineSmall: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.4,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.5,
      ),
      titleMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.5,
      ),
      titleSmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.5,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: baseColor,
        height: 1.5,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: baseColor,
        height: 1.5,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: baseColor.withOpacity(0.7),
        height: 1.5,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.4,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: baseColor,
        height: 1.4,
      ),
      labelSmall: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: baseColor.withOpacity(0.7),
        height: 1.4,
      ),
    );
  }

  /// بناء نمط شريط التطبيق
  static AppBarTheme _buildAppBarTheme(Brightness brightness) {
    return AppBarTheme(
      elevation: 0,
      scrolledUnderElevation: 1,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      titleTextStyle: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
      iconTheme: const IconThemeData(
        color: Colors.white,
        size: 24,
      ),
      actionsIconTheme: const IconThemeData(
        color: Colors.white,
        size: 24,
      ),
      systemOverlayStyle: brightness == Brightness.light
          ? SystemUiOverlayStyle.light
          : SystemUiOverlayStyle.dark,
    );
  }

  /// بناء نمط الأزرار المرفوعة
  static ElevatedButtonThemeData _buildElevatedButtonTheme() {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  /// بناء نمط الأزرار المحددة
  static OutlinedButtonThemeData _buildOutlinedButtonTheme() {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        side: BorderSide(color: AppColors.primary, width: 1.5),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  /// بناء نمط الأزرار النصية
  static TextButtonThemeData _buildTextButtonTheme() {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  /// بناء نمط البطاقات
  static CardTheme _buildCardTheme() {
    return CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  /// بناء نمط حقول الإدخال
  static InputDecorationTheme _buildInputDecorationTheme(Brightness brightness) {
    return InputDecorationTheme(
      filled: true,
      fillColor: brightness == Brightness.light 
          ? Colors.grey[50] 
          : Colors.grey[900],
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey[300]!),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey[300]!),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      labelStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
        color: brightness == Brightness.light ? Colors.grey[700] : Colors.white70,
      ),
      hintStyle: TextStyle(
        fontSize: 14,
        fontFamily: 'Cairo',
        color: brightness == Brightness.light ? Colors.grey[500] : Colors.grey[400],
      ),
    );
  }

  /// بناء نمط عناصر القائمة
  static ListTileThemeData _buildListTileTheme() {
    return const ListTileThemeData(
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      minLeadingWidth: 40,
      iconColor: AppColors.primary,
    );
  }

  /// بناء نمط الحوارات
  static DialogTheme _buildDialogTheme() {
    return DialogTheme(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      titleTextStyle: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      contentTextStyle: const TextStyle(
        fontSize: 14,
        fontFamily: 'Cairo',
        height: 1.5,
      ),
    );
  }

  /// بناء نمط شريط التنقل السفلي
  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme() {
    return BottomNavigationBarThemeData(
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: Colors.grey[600],
      selectedLabelStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 12,
        fontFamily: 'Cairo',
      ),
    );
  }

  /// بناء نمط الأيقونات
  static IconThemeData _buildIconTheme(Brightness brightness) {
    return IconThemeData(
      color: brightness == Brightness.light ? Colors.grey[700] : Colors.grey[300],
      size: 24,
    );
  }

  /// بناء نمط الفواصل
  static DividerThemeData _buildDividerTheme() {
    return DividerThemeData(
      thickness: 1,
      space: 1,
      color: Colors.grey[300],
    );
  }

  /// بناء نمط التبديل
  static SwitchThemeData _buildSwitchTheme() {
    return SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return Colors.grey[400];
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary.withOpacity(0.5);
        }
        return Colors.grey[300];
      }),
    );
  }

  /// بناء نمط مربعات الاختيار
  static CheckboxThemeData _buildCheckboxTheme() {
    return CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.primary;
        }
        return Colors.transparent;
      }),
      checkColor: WidgetStateProperty.all(Colors.white),
      side: BorderSide(color: AppColors.primary, width: 2),
    );
  }

  /// بناء نمط شريط التقدم
  static ProgressIndicatorThemeData _buildProgressIndicatorTheme() {
    return ProgressIndicatorThemeData(
      color: AppColors.primary,
      linearTrackColor: AppColors.primary.withOpacity(0.2),
      circularTrackColor: AppColors.primary.withOpacity(0.2),
    );
  }

  /// بناء نمط الرقائق
  static ChipThemeData _buildChipTheme(Brightness brightness) {
    return ChipThemeData(
      backgroundColor: brightness == Brightness.light 
          ? Colors.grey[100] 
          : Colors.grey[800],
      selectedColor: AppColors.primary.withOpacity(0.2),
      labelStyle: TextStyle(
        fontSize: 12,
        fontFamily: 'Cairo',
        color: brightness == Brightness.light ? Colors.black87 : Colors.white70,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  /// بناء نمط التبويبات
  static TabBarTheme _buildTabBarTheme(Brightness brightness) {
    return TabBarTheme(
      labelColor: AppColors.primary,
      unselectedLabelColor: brightness == Brightness.light 
          ? Colors.grey[600] 
          : Colors.grey[400],
      labelStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 14,
        fontFamily: 'Cairo',
      ),
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(color: AppColors.primary, width: 2),
      ),
    );
  }

  /// بناء نمط القوائم المنسدلة
  static DropdownMenuThemeData _buildDropdownMenuTheme() {
    return DropdownMenuThemeData(
      textStyle: const TextStyle(
        fontSize: 14,
        fontFamily: 'Cairo',
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// تطبيق الثيم حسب النظام
  static ThemeMode getThemeMode() {
    // يمكن إضافة منطق لحفظ واسترجاع تفضيل المستخدم
    return ThemeMode.system;
  }
}
