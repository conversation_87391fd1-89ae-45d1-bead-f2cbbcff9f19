import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/inventory_transaction.dart';
import '../../../shared/models/inventory_item.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../core/repositories/inventory_transaction_repository.dart';
import '../../../core/repositories/inventory_repository.dart';

class InventoryTransactionsScreen extends StatefulWidget {
  final int? inventoryItemId;

  const InventoryTransactionsScreen({
    super.key,
    this.inventoryItemId,
  });

  @override
  State<InventoryTransactionsScreen> createState() => _InventoryTransactionsScreenState();
}

class _InventoryTransactionsScreenState extends State<InventoryTransactionsScreen> {
  final InventoryTransactionRepository _transactionRepository = InventoryTransactionRepository();
  final InventoryRepository _inventoryRepository = InventoryRepository();

  bool _isLoading = true;
  List<InventoryTransaction> _transactions = [];
  InventoryItem? _selectedItem;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.inventoryItemId != null) {
        // Load transactions for a specific inventory item
        final item = await _inventoryRepository.getInventoryItemById(widget.inventoryItemId!);
        final transactions = await _transactionRepository.getInventoryTransactionsByItemId(widget.inventoryItemId!);

        setState(() {
          _selectedItem = item;
          _transactions = transactions;
          _isLoading = false;
        });
      } else {
        // Load all transactions
        final transactions = await _transactionRepository.getAllInventoryTransactions();

        setState(() {
          _transactions = transactions;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<InventoryTransaction> get _filteredTransactions {
    if (_searchQuery.isEmpty) {
      return _transactions;
    }

    final query = _searchQuery.toLowerCase();
    return _transactions.where((transaction) {
      return transaction.inventoryItemName.toLowerCase().contains(query) ||
          (transaction.inventoryItemCode?.toLowerCase().contains(query) ?? false) ||
          (transaction.reference?.toLowerCase().contains(query) ?? false) ||
          (transaction.serviceRequestReference?.toLowerCase().contains(query) ?? false) ||
          (transaction.userName?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_selectedItem != null
            ? 'حركة المخزون - ${_selectedItem!.name}'
            : 'حركة المخزون'),
      ),
      drawer: widget.inventoryItemId == null ? const AppDrawer() : null,
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث في حركة المخزون...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTransactions.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد حركات مخزون',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredTransactions.length,
                          itemBuilder: (context, index) {
                            final transaction = _filteredTransactions[index];
                            return _buildTransactionCard(transaction);
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddTransactionDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTransactionCard(InventoryTransaction transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: InkWell(
        onTap: () {
          _showTransactionDetails(context, transaction);
        },
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: transaction.getTypeColor().withAlpha(50),
                          child: Icon(
                            transaction.getTypeIcon(),
                            color: transaction.getTypeColor(),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingS),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                transaction.inventoryItemName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (transaction.inventoryItemCode != null)
                                Text(
                                  'كود: ${transaction.inventoryItemCode}',
                                  style: const TextStyle(
                                    color: AppColors.textSecondary,
                                    fontSize: 12,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingS,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: transaction.getTypeColor().withAlpha(50),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      transaction.getTypeText(),
                      style: TextStyle(
                        color: transaction.getTypeColor(),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(height: 1),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الكمية',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${transaction.quantity} ${transaction.unit ?? 'قطعة'}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: transaction.type == InventoryTransactionType.subtraction
                              ? Colors.red
                              : transaction.type == InventoryTransactionType.addition
                                  ? Colors.green
                                  : null,
                        ),
                      ),
                    ],
                  ),
                  if (transaction.reference != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'المرجع',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        Text(
                          transaction.reference!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'التاريخ',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        DateFormat('yyyy/MM/dd').format(transaction.transactionDate),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTransactionDetails(BuildContext context, InventoryTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تفاصيل الحركة - ${transaction.getTypeText()}'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('العنصر', transaction.inventoryItemName),
                if (transaction.inventoryItemCode != null)
                  _buildDetailRow('كود العنصر', transaction.inventoryItemCode!),
                _buildDetailRow('نوع الحركة', transaction.getTypeText()),
                _buildDetailRow('الكمية', '${transaction.quantity} ${transaction.unit ?? 'قطعة'}'),
                if (transaction.reference != null)
                  _buildDetailRow('المرجع', transaction.reference!),
                if (transaction.reason != null)
                  _buildDetailRow('السبب', transaction.reason!),
                if (transaction.serviceRequestReference != null)
                  _buildDetailRow('طلب الخدمة', transaction.serviceRequestReference!),
                if (transaction.userName != null)
                  _buildDetailRow('المستخدم', transaction.userName!),
                _buildDetailRow('التاريخ', DateFormat('yyyy/MM/dd HH:mm').format(transaction.transactionDate)),
                if (transaction.notes != null)
                  _buildDetailRow('ملاحظات', transaction.notes!),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showAddTransactionDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    InventoryTransactionType transactionType = InventoryTransactionType.addition;
    InventoryItem? selectedItem = _selectedItem;
    double quantity = 1;
    String reference = '';
    String reason = '';
    String notes = '';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('إضافة حركة مخزون جديدة'),
              content: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نوع الحركة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      SegmentedButton<InventoryTransactionType>(
                        segments: [
                          ButtonSegment<InventoryTransactionType>(
                            value: InventoryTransactionType.addition,
                            label: const Text('إضافة'),
                            icon: const Icon(Icons.add_circle),
                          ),
                          ButtonSegment<InventoryTransactionType>(
                            value: InventoryTransactionType.subtraction,
                            label: const Text('سحب'),
                            icon: const Icon(Icons.remove_circle),
                          ),
                          ButtonSegment<InventoryTransactionType>(
                            value: InventoryTransactionType.adjustment,
                            label: const Text('تعديل'),
                            icon: const Icon(Icons.edit),
                          ),
                        ],
                        selected: {transactionType},
                        onSelectionChanged: (Set<InventoryTransactionType> newSelection) {
                          setState(() {
                            transactionType = newSelection.first;
                          });
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      if (selectedItem == null) ...[
                        const Text(
                          'العنصر',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppDimensions.paddingS),
                        ElevatedButton(
                          onPressed: () {
                            _showInventoryItemSelectionDialog(context).then((item) {
                              if (item != null) {
                                setState(() {
                                  selectedItem = item;
                                });
                              }
                            });
                          },
                          child: Text(
                            selectedItem != null
                                ? selectedItem!.name
                                : 'اختر عنصر من المخزون',
                          ),
                        ),
                      ] else ...[
                        const Text(
                          'العنصر',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppDimensions.paddingS),
                        Text(selectedItem!.name),
                        if (selectedItem!.code.isNotEmpty)
                          Text(
                            'كود: ${selectedItem!.code}',
                            style: const TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        Text(
                          'الكمية الحالية: ${selectedItem!.quantity} ${selectedItem!.unit ?? 'قطعة'}',
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                      const SizedBox(height: AppDimensions.paddingM),
                      const Text(
                        'الكمية',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      TextFormField(
                        decoration: InputDecoration(
                          hintText: 'أدخل الكمية',
                          suffixText: selectedItem?.unit ?? 'قطعة',
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        initialValue: quantity.toString(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الكمية';
                          }
                          final qty = double.tryParse(value);
                          if (qty == null || qty <= 0) {
                            return 'يرجى إدخال كمية صحيحة';
                          }
                          if (transactionType == InventoryTransactionType.subtraction &&
                              selectedItem != null &&
                              qty > selectedItem!.quantity) {
                            return 'الكمية المتاحة غير كافية';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          quantity = double.tryParse(value) ?? 1;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      const Text(
                        'المرجع',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      TextFormField(
                        decoration: const InputDecoration(
                          hintText: 'أدخل المرجع (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          reference = value;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      const Text(
                        'السبب',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      TextFormField(
                        decoration: const InputDecoration(
                          hintText: 'أدخل سبب الحركة (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          reason = value;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      const Text(
                        'ملاحظات',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      TextFormField(
                        decoration: const InputDecoration(
                          hintText: 'أدخل ملاحظات إضافية (اختياري)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                        onChanged: (value) {
                          notes = value;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (selectedItem == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى اختيار عنصر من المخزون'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    if (formKey.currentState!.validate()) {
                      final transaction = InventoryTransaction(
                        inventoryItemId: selectedItem!.id!,
                        inventoryItemName: selectedItem!.name,
                        inventoryItemCode: selectedItem!.code,
                        type: transactionType,
                        quantity: quantity,
                        unit: selectedItem!.unit,
                        reference: reference.isNotEmpty ? reference : null,
                        reason: reason.isNotEmpty ? reason : null,
                        userId: 1, // Replace with actual user ID
                        userName: 'Admin', // Replace with actual user name
                        transactionDate: DateTime.now(),
                        notes: notes.isNotEmpty ? notes : null,
                      );

                      // Capture the context before the async gap
                      final scaffoldMessenger = ScaffoldMessenger.of(context);

                      Navigator.pop(context);

                      // Use the outer setState from the StatefulBuilder
                      setState(() {
                        _isLoading = true;
                      });

                      _transactionRepository.insertInventoryTransaction(transaction).then((success) {
                        if (mounted) {
                          // Use the outer setState from the StatefulBuilder
                          setState(() {
                            _isLoading = false;
                          });

                          if (success) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content: Text('تمت إضافة حركة المخزون بنجاح'),
                                backgroundColor: Colors.green,
                              ),
                            );

                            _loadData();
                          } else {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content: Text('حدث خطأ أثناء إضافة حركة المخزون'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      });
                    }
                  },
                  child: const Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<InventoryItem?> _showInventoryItemSelectionDialog(BuildContext dialogContext) async {
    // Capture the context before the async gap
    final currentContext = dialogContext;

    final inventoryItems = await _inventoryRepository.getAllInventoryItems();
    String searchQuery = '';

    if (!mounted) return null;

    return showDialog<InventoryItem>(
      context: currentContext,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            final filteredItems = inventoryItems.where((item) {
              if (searchQuery.isEmpty) {
                return true;
              }

              final query = searchQuery.toLowerCase();
              return item.name.toLowerCase().contains(query) ||
                  item.code.toLowerCase().contains(query) ||
                  (item.category?.toLowerCase().contains(query) ?? false);
            }).toList();

            return AlertDialog(
              title: const Text('اختر عنصر من المخزون'),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      decoration: const InputDecoration(
                        hintText: 'بحث...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          searchQuery = value;
                        });
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    Expanded(
                      child: ListView.builder(
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];
                          return ListTile(
                            title: Text(item.name),
                            subtitle: Text(
                              'كود: ${item.code} - الكمية: ${item.quantity} ${item.unit ?? 'قطعة'}',
                            ),
                            onTap: () {
                              Navigator.pop(context, item);
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
