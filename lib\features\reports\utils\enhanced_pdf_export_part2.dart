import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../models/report_models.dart';
import 'arabic_pdf_helpers.dart';


/// Additional methods for EnhancedPdfExport
class EnhancedPdfExportPart2 {
  /// Add service requests pages to the PDF
  static Future<void> addServiceRequestsPages(
    pw.Document pdf,
    List<ServiceRequest> requests,
    pw.Font arabicFont,
    pw.Font arabicFontBold,
  ) async {
    final int itemsPerPage = 10;
    final int pageCount = (requests.length / itemsPerPage).ceil();

    for (int i = 0; i < pageCount; i++) {
      final int startIndex = i * itemsPerPage;
      final int endIndex = (startIndex + itemsPerPage) > requests.length
          ? requests.length
          : startIndex + itemsPerPage;
      final List<ServiceRequest> pageRequests = requests.sublist(startIndex, endIndex);

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                ArabicPdfHelper.arabicHeading(
                  'قائمة طلبات الخدمة (${startIndex + 1} - $endIndex من ${requests.length})',
                  font: arabicFontBold,
                  fontSize: 16,
                ),
                pw.SizedBox(height: 10),
                buildServiceRequestsTable(pageRequests, arabicFont, arabicFontBold),
                pw.SizedBox(height: 10),
                ArabicPdfHelper.arabicText(
                  'صفحة ${i + 1} من $pageCount',
                  font: arabicFont,
                  fontSize: 12,
                  textAlign: pw.TextAlign.center,
                ),
              ],
            );
          },
        ),
      );
    }
  }

  /// Build service requests table
  static pw.Widget buildServiceRequestsTable(
    List<ServiceRequest> requests,
    pw.Font arabicFont,
    pw.Font arabicFontBold,
  ) {
    final List<List<String>> tableData = requests.map((request) => [
      request.requestNumber,
      request.customerName,
      request.serviceType,
      DateFormat('dd/MM/yyyy').format(request.scheduledDate),
      ServiceRequest.getStatusName(request.status),
    ]).toList();

    // Fix Arabic text in headers and data
    final headers = ['رقم الطلب', 'العميل', 'نوع الخدمة', 'التاريخ', 'الحالة'];

    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: arabicFont,
      boldFont: arabicFontBold,
      customAlignments: {
        0: pw.Alignment.centerRight,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.centerRight,
        3: pw.Alignment.center,
        4: pw.Alignment.center,
      },
    );
  }

  /// Add transactions pages to the PDF
  static Future<void> addTransactionsPages(
    pw.Document pdf,
    List<Transaction> transactions,
    pw.Font arabicFont,
    pw.Font arabicFontBold,
  ) async {
    final int itemsPerPage = 10;
    final int pageCount = (transactions.length / itemsPerPage).ceil();

    for (int i = 0; i < pageCount; i++) {
      final int startIndex = i * itemsPerPage;
      final int endIndex = (startIndex + itemsPerPage) > transactions.length
          ? transactions.length
          : startIndex + itemsPerPage;
      final List<Transaction> pageTransactions = transactions.sublist(startIndex, endIndex);

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                ArabicPdfHelper.arabicHeading(
                  'قائمة المعاملات المالية (${startIndex + 1} - $endIndex من ${transactions.length})',
                  font: arabicFontBold,
                  fontSize: 16,
                ),
                pw.SizedBox(height: 10),
                buildTransactionsTable(pageTransactions, arabicFont, arabicFontBold),
                pw.SizedBox(height: 10),
                ArabicPdfHelper.arabicText(
                  'صفحة ${i + 1} من $pageCount',
                  font: arabicFont,
                  fontSize: 12,
                  textAlign: pw.TextAlign.center,
                ),
              ],
            );
          },
        ),
      );
    }
  }

  /// Build transactions table
  static pw.Widget buildTransactionsTable(
    List<Transaction> transactions,
    pw.Font arabicFont,
    pw.Font arabicFontBold,
  ) {
    final List<List<String>> tableData = transactions.map((transaction) => [
      transaction.reference,
      transaction.description,
      transaction.category ?? '-',
      DateFormat('dd/MM/yyyy').format(transaction.date),
      transaction.amount.toStringAsFixed(2),
      transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
    ]).toList();

    // Fix Arabic text in headers and data
    final headers = ['المرجع', 'الوصف', 'الفئة', 'التاريخ', 'المبلغ', 'النوع'];

    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: arabicFont,
      boldFont: arabicFontBold,
      customAlignments: {
        0: pw.Alignment.centerRight,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.centerRight,
        3: pw.Alignment.center,
        4: pw.Alignment.centerLeft,
        5: pw.Alignment.center,
      },
    );
  }
}
