import 'package:flutter/material.dart';

/// Responsive layout system for different screen sizes
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= tabletBreakpoint && desktop != null) {
          return desktop!;
        } else if (constraints.maxWidth >= mobileBreakpoint && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// Responsive grid system
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16,
    this.runSpacing = 16,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int columns;
        if (constraints.maxWidth >= tabletBreakpoint) {
          columns = desktopColumns;
        } else if (constraints.maxWidth >= mobileBreakpoint) {
          columns = tabletColumns;
        } else {
          columns = mobileColumns;
        }

        return Wrap(
          spacing: spacing,
          runSpacing: runSpacing,
          children: children.map((child) {
            final itemWidth = (constraints.maxWidth - (spacing * (columns - 1))) / columns;
            return SizedBox(
              width: itemWidth,
              child: child,
            );
          }).toList(),
        );
      },
    );
  }
}

/// Responsive container with max width constraints
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool centerContent;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth = 1200,
    this.padding,
    this.margin,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget container = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: maxWidth),
      padding: padding,
      margin: margin,
      child: child,
    );

    if (centerContent) {
      return Center(child: container);
    }

    return container;
  }
}

/// Responsive padding based on screen size
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry mobilePadding;
  final EdgeInsetsGeometry? tabletPadding;
  final EdgeInsetsGeometry? desktopPadding;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsivePadding({
    super.key,
    required this.child,
    required this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        EdgeInsetsGeometry padding;
        if (constraints.maxWidth >= tabletBreakpoint && desktopPadding != null) {
          padding = desktopPadding!;
        } else if (constraints.maxWidth >= mobileBreakpoint && tabletPadding != null) {
          padding = tabletPadding!;
        } else {
          padding = mobilePadding;
        }

        return Padding(
          padding: padding,
          child: child,
        );
      },
    );
  }
}

/// Screen size helper
class ScreenSize {
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 1024;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1024;
  }

  static double getWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double getHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static Orientation getOrientation(BuildContext context) {
    return MediaQuery.of(context).orientation;
  }
}

/// Responsive text that scales based on screen size
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double mobileScale;
  final double tabletScale;
  final double desktopScale;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.mobileScale = 1.0,
    this.tabletScale = 1.1,
    this.desktopScale = 1.2,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double scale;
        if (constraints.maxWidth >= tabletBreakpoint) {
          scale = desktopScale;
        } else if (constraints.maxWidth >= mobileBreakpoint) {
          scale = tabletScale;
        } else {
          scale = mobileScale;
        }

        final scaledStyle = style?.copyWith(
          fontSize: (style?.fontSize ?? 14) * scale,
        ) ?? TextStyle(fontSize: 14 * scale);

        return Text(
          text,
          style: scaledStyle,
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
}

/// Responsive spacing widget
class ResponsiveSpacing extends StatelessWidget {
  final double mobileSpacing;
  final double? tabletSpacing;
  final double? desktopSpacing;
  final Axis direction;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsiveSpacing({
    super.key,
    required this.mobileSpacing,
    this.tabletSpacing,
    this.desktopSpacing,
    this.direction = Axis.vertical,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1024,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double spacing;
        if (constraints.maxWidth >= tabletBreakpoint && desktopSpacing != null) {
          spacing = desktopSpacing!;
        } else if (constraints.maxWidth >= mobileBreakpoint && tabletSpacing != null) {
          spacing = tabletSpacing!;
        } else {
          spacing = mobileSpacing;
        }

        return SizedBox(
          width: direction == Axis.horizontal ? spacing : null,
          height: direction == Axis.vertical ? spacing : null,
        );
      },
    );
  }
}

/// Responsive column that changes to row on larger screens
class ResponsiveColumnRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final double breakpoint;
  final double spacing;

  const ResponsiveColumnRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.breakpoint = 600,
    this.spacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= breakpoint) {
          // Use Row for larger screens
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            mainAxisSize: mainAxisSize,
            children: _addSpacing(children, Axis.horizontal),
          );
        } else {
          // Use Column for smaller screens
          return Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            mainAxisSize: mainAxisSize,
            children: _addSpacing(children, Axis.vertical),
          );
        }
      },
    );
  }

  List<Widget> _addSpacing(List<Widget> children, Axis direction) {
    if (children.isEmpty) return children;

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(
          SizedBox(
            width: direction == Axis.horizontal ? spacing : null,
            height: direction == Axis.vertical ? spacing : null,
          ),
        );
      }
    }
    return spacedChildren;
  }
}
