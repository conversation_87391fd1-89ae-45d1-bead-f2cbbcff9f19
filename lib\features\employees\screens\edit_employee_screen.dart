import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/employee.dart';

class EditEmployeeScreen extends StatefulWidget {
  final Employee? employee;

  const EditEmployeeScreen({
    super.key,
    this.employee,
  });

  @override
  State<EditEmployeeScreen> createState() => _EditEmployeeScreenState();
}

class _EditEmployeeScreenState extends State<EditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();
  late String _name;
  late String? _email;
  late String? _phone;
  late String _position;
  late double _salary;
  late PaymentType _paymentType;
  late DateTime _joinDate;
  late EmployeeStatus _status;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.employee != null) {
      // Edit mode
      _name = widget.employee!.name;
      _email = widget.employee!.email;
      _phone = widget.employee!.phone;
      _position = widget.employee!.position;
      _salary = widget.employee!.salary;
      _paymentType = widget.employee!.paymentType;
      _joinDate = widget.employee!.joinDate;
      _status = widget.employee!.status;
    } else {
      // Add mode
      _name = '';
      _email = '';
      _phone = '';
      _position = '';
      _salary = 0;
      _paymentType = PaymentType.monthly;
      _joinDate = DateTime.now();
      _status = EmployeeStatus.active;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _joinDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _joinDate) {
      setState(() {
        _joinDate = picked;
      });
    }
  }

  void _saveEmployee() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isLoading = true;
      });

      if (widget.employee != null) {
        // Update existing employee
        final updatedEmployee = widget.employee!.copyWith(
          name: _name,
          email: _email,
          phone: _phone,
          position: _position,
          salary: _salary,
          paymentType: _paymentType,
          joinDate: _joinDate,
          status: _status,
        );

        Navigator.pop(context, updatedEmployee);
      } else {
        // Create new employee
        final newEmployee = Employee(
          name: _name,
          email: _email,
          phone: _phone,
          position: _position,
          salary: _salary,
          paymentType: _paymentType,
          joinDate: _joinDate,
          status: _status,
          createdAt: DateTime.now(),
        );

        Navigator.pop(context, newEmployee);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditMode = widget.employee != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditMode ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'),
        actions: [
          TextButton.icon(
            onPressed: _isLoading ? null : _saveEmployee,
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Personal information
                    const Text(
                      'المعلومات الشخصية',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      initialValue: _name,
                      decoration: const InputDecoration(
                        labelText: 'الاسم',
                        prefixIcon: Icon(Icons.person),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم الموظف';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _name = value!;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _email,
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        prefixIcon: Icon(Icons.email),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      onSaved: (value) {
                        _email = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _phone,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        prefixIcon: Icon(Icons.phone),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                      onSaved: (value) {
                        _phone = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Job information
                    const Text(
                      'معلومات الوظيفة',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      initialValue: _position,
                      decoration: const InputDecoration(
                        labelText: 'المسمى الوظيفي',
                        prefixIcon: Icon(Icons.work),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المسمى الوظيفي';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _position = value!;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Payment Type Dropdown
                    DropdownButtonFormField<PaymentType>(
                      value: _paymentType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الأجر',
                        prefixIcon: Icon(Icons.payment),
                        border: OutlineInputBorder(),
                      ),
                      items: PaymentType.values.map((PaymentType type) {
                        return DropdownMenuItem<PaymentType>(
                          value: type,
                          child: Text(Employee.getPaymentTypeName(type)),
                        );
                      }).toList(),
                      onChanged: (PaymentType? newValue) {
                        setState(() {
                          _paymentType = newValue!;
                          // Reset salary to 0 for daily wage employees
                          if (_paymentType == PaymentType.daily) {
                            _salary = 0;
                          }
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار نوع الأجر';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Conditional Salary Field (only for monthly payment type)
                    if (_paymentType == PaymentType.monthly)
                      TextFormField(
                        initialValue: _salary.toString(),
                        decoration: const InputDecoration(
                          labelText: 'الراتب الشهري',
                          prefixIcon: Icon(Icons.attach_money),
                          suffixText: 'ر.س',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الراتب الشهري';
                          }
                          final salary = double.tryParse(value);
                          if (salary == null || salary <= 0) {
                            return 'يرجى إدخال راتب صحيح';
                          }
                          return null;
                        },
                        onSaved: (value) {
                          _salary = double.parse(value!);
                        },
                      ),

                    // Note for daily wage employees
                    if (_paymentType == PaymentType.daily)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.info, color: Colors.orange),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'الموظفون ذوو الأجر اليومي لا يحتاجون إلى راتب ثابت. سيتم تحديد الأجر عند تعيينهم للخدمات.',
                                style: TextStyle(
                                  color: Colors.orange.shade700,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(height: AppDimensions.paddingM),
                    InkWell(
                      onTap: () => _selectDate(context),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ التعيين',
                          prefixIcon: Icon(Icons.calendar_today),
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          DateFormat('dd/MM/yyyy').format(_joinDate),
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    DropdownButtonFormField<EmployeeStatus>(
                      decoration: const InputDecoration(
                        labelText: 'الحالة',
                        prefixIcon: Icon(Icons.person_outline),
                        border: OutlineInputBorder(),
                      ),
                      value: _status,
                      items: EmployeeStatus.values.map((status) {
                        return DropdownMenuItem<EmployeeStatus>(
                          value: status,
                          child: Row(
                            children: [
                              Icon(
                                _getStatusIcon(status),
                                size: 16,
                                color: _getStatusColor(status),
                              ),
                              const SizedBox(width: 8),
                              Text(Employee.getStatusName(status)),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _status = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveEmployee,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          isEditMode ? 'حفظ التغييرات' : 'إضافة الموظف',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  IconData _getStatusIcon(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return Icons.check_circle;
      case EmployeeStatus.inactive:
        return Icons.cancel;
      case EmployeeStatus.onLeave:
        return Icons.beach_access;
      case EmployeeStatus.terminated:
        return Icons.block;
    }
  }

  Color _getStatusColor(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return Colors.green;
      case EmployeeStatus.inactive:
        return Colors.red;
      case EmployeeStatus.onLeave:
        return Colors.orange;
      case EmployeeStatus.terminated:
        return Colors.grey.shade800;
    }
  }
}
