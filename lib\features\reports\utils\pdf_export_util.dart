import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import '../models/report_models.dart';
import '../screens/pdf_preview_screen.dart';
import 'arabic_pdf_helpers.dart'; // Import ArabicPdfHelper

class PdfExportUtil {
  // Primary color for reports
  static const PdfColor primaryColor = PdfColor(0.2, 0.6, 0.86); // #3498DB
  static const PdfColor accentColor = PdfColor(0.16, 0.5, 0.73); // #2980B9
  static const PdfColor incomeColor = PdfColor(0.2, 0.7, 0.4); // Green
  static const PdfColor expenseColor = PdfColor(0.9, 0.3, 0.3); // Red

  static Future<void> exportReport({
    required String reportType,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required List<dynamic> data,
    required Map<String, double> summaryData,
    required BuildContext context,
  }) async {
    final pdf = pw.Document();

    // Load Arabic fonts
    final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    final arabicRegularFont = pw.Font.ttf(fontData);

    final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
    final arabicBoldFont = pw.Font.ttf(fontDataBold);

    // Load logo image
    Uint8List? logoImage;
    try {
      final logoBytes = await rootBundle.load('assets/images/snowflake_logo.png');
      logoImage = logoBytes.buffer.asUint8List();
    } catch (e) {
      // Logo not found, will use fallback
      debugPrint('Logo image not found: $e');
    }

    // Date range string
    final dateRangeText = 'الفترة: ${DateFormat('dd/MM/yyyy').format(startDate)} - ${DateFormat('dd/MM/yyyy').format(endDate)}';

    // Create a single consolidated report page
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header with logo
              ArabicPdfHelper.reportHeader(
                title: title,
                boldFont: arabicBoldFont,
                regularFont: arabicRegularFont,
                logoImage: logoImage,
                subtitle: dateRangeText,
                date: 'تم إنشاؤه في: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
              ),

              pw.SizedBox(height: 20),

              // Summary section
              ArabicPdfHelper.arabicHeading(
                'ملخص التقرير',
                font: arabicBoldFont,
                fontSize: 16,
                color: primaryColor,
              ),

              pw.SizedBox(height: 10),

              // Summary cards in a row
              _buildSummaryCards(reportType, summaryData, arabicRegularFont, arabicBoldFont),

              pw.SizedBox(height: 20),

              // Details section
              ArabicPdfHelper.arabicHeading(
                'تفاصيل التقرير (${data.length} عنصر)',
                font: arabicBoldFont,
                fontSize: 16,
                color: primaryColor,
              ),

              pw.SizedBox(height: 10),

              // Data table
              pw.Expanded(
                child: _buildDataTable(reportType, data, arabicRegularFont, arabicBoldFont),
              ),

              // Footer
              ArabicPdfHelper.reportFooter(
                font: arabicRegularFont,
                currentPage: 1,
                totalPages: 1,
              ),
            ],
          );
        },
      ),
    );

    // Save the PDF
    Directory output;
    try {
      // Try to get external storage directory first
      final externalDir = await getExternalStorageDirectory();
      // If external storage is not available, try documents directory
      output = externalDir ?? await getApplicationDocumentsDirectory();
    } catch (e) {
      // If both fail, use temporary directory as last resort
      debugPrint('Error accessing storage directories: $e');
      output = await getTemporaryDirectory();
    }

    debugPrint('PDF output directory: ${output.path}');

    // Create a subdirectory for our app's PDFs if it doesn't exist
    final pdfDir = Directory('${output.path}/icecorner_reports');
    if (!await pdfDir.exists()) {
      await pdfDir.create(recursive: true);
    }

    final fileName = 'تقرير_${reportType}_${DateFormat('yyyy_MM_dd_HHmmss').format(DateTime.now())}.pdf';
    final file = File('${pdfDir.path}/$fileName');

    // Save the PDF file
    try {
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);
      debugPrint('PDF saved successfully to: ${file.path}');
    } catch (e, stackTrace) {
      debugPrint('Error saving PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      throw Exception('فشل في حفظ ملف PDF: $e');
    }

    // Create a more descriptive share message
    final shareMessage = 'تقرير: $title\n'
        'الفترة: ${DateFormat('dd/MM/yyyy').format(startDate)} - ${DateFormat('dd/MM/yyyy').format(endDate)}\n'
        'عدد العناصر: ${data.length}';

    // Navigate to PDF preview screen
    if (context.mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PdfPreviewScreen(
            filePath: file.path,
            title: title,
          ),
        ),
      );
    } else {
      // Share directly if context is not mounted
      final xFile = XFile(file.path);
      await Share.shareXFiles(
        [xFile],
        text: shareMessage,
        subject: 'تقرير $title - ${DateFormat('dd/MM/yyyy').format(DateTime.now())}',
      );
    }
  }

  /// Builds a row of summary cards for the report
  static pw.Widget _buildSummaryCards(String reportType, Map<String, double> summaryData, pw.Font regularFont, pw.Font boldFont) {
    final List<pw.Widget> cards = [];

    switch (reportType) {
      case 'invoices':
      case 'customer':
        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'إجمالي الفواتير',
            value: '${(summaryData['total'] ?? 0.0).isFinite ? (summaryData['total'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(0.95, 0.95, 1.0), // Light blue
            valueColor: primaryColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'المدفوع',
            value: '${(summaryData['paid'] ?? 0.0).isFinite ? (summaryData['paid'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(0.95, 1.0, 0.95), // Light green
            valueColor: incomeColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'غير المدفوع',
            value: '${(summaryData['unpaid'] ?? 0.0).isFinite ? (summaryData['unpaid'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(1.0, 0.95, 0.95), // Light red
            valueColor: expenseColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'عدد الفواتير',
            value: '${summaryData['count']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
          ),
        );

        if (reportType == 'customer') {
          cards.add(
            ArabicPdfHelper.summaryCard(
              title: 'الخدمات المستهلكة',
              value: '${summaryData['servicesTotal']?.toInt() ?? 0}',
              titleFont: boldFont,
              valueFont: boldFont,
            ),
          );

          cards.add(
            ArabicPdfHelper.summaryCard(
              title: 'الرصيد المتبقي',
              value: '${(summaryData['balance'] ?? 0.0).isFinite ? (summaryData['balance'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
              titleFont: boldFont,
              valueFont: boldFont,
              valueColor: (summaryData['balance'] ?? 0.0).isFinite && (summaryData['balance'] ?? 0.0) < 0
                  ? expenseColor
                  : incomeColor,
            ),
          );
        }
        break;

      case 'services':
        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'إجمالي الطلبات',
            value: '${summaryData['total']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(0.95, 0.95, 1.0), // Light blue
            valueColor: primaryColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'المكتملة',
            value: '${summaryData['completed']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(0.95, 1.0, 0.95), // Light green
            valueColor: incomeColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'قيد التنفيذ',
            value: '${summaryData['inProgress']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(1.0, 0.98, 0.9), // Light yellow
            valueColor: const PdfColor(0.9, 0.6, 0.0), // Orange
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'في الانتظار',
            value: '${summaryData['pending']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(1.0, 0.95, 0.95), // Light red
            valueColor: expenseColor,
          ),
        );
        break;

      case 'transactions':
      case 'supplier':
      case 'employee':
        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'الإيرادات',
            value: '${(summaryData['income'] ?? 0.0).isFinite ? (summaryData['income'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(0.95, 1.0, 0.95), // Light green
            valueColor: incomeColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'المصروفات',
            value: '${(summaryData['expense'] ?? 0.0).isFinite ? (summaryData['expense'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            backgroundColor: const PdfColor(1.0, 0.95, 0.95), // Light red
            valueColor: expenseColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'الرصيد',
            value: '${(summaryData['balance'] ?? 0.0).isFinite ? (summaryData['balance'] ?? 0.0).toStringAsFixed(2) : '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            valueColor: (summaryData['balance'] ?? 0.0).isFinite && (summaryData['balance'] ?? 0.0) < 0
                ? expenseColor
                : incomeColor,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'عدد المعاملات',
            value: '${summaryData['count']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
          ),
        );
        break;
    }

    // Arrange cards in a row with wrapping
    return pw.Wrap(
      spacing: 10,
      runSpacing: 10,
      children: cards,
    );
  }

  /// Builds the appropriate data table based on report type
  static pw.Widget _buildDataTable(String reportType, List<dynamic> data, pw.Font regularFont, pw.Font boldFont) {
    switch (reportType) {
      case 'invoices':
      case 'customer':
        return _buildInvoicesTable(data.cast<Invoice>(), regularFont, boldFont);
      case 'services':
        return _buildServiceRequestsTable(data.cast<ServiceRequest>(), regularFont, boldFont);
      case 'transactions':
      case 'supplier':
      case 'employee':
        return _buildTransactionsTable(data.cast<Transaction>(), regularFont, boldFont);
      default:
        return pw.Container(); // Empty container as fallback
    }
  }

  /// Builds a table for invoices
  static pw.Widget _buildInvoicesTable(List<Invoice> invoices, pw.Font regularFont, pw.Font boldFont) {
    final List<List<String>> tableData = invoices.map((invoice) => [
      invoice.invoiceNumber.toString(),
      invoice.customerName.toString(),
      DateFormat('dd/MM/yyyy').format(invoice.date),
      invoice.total.isFinite ? invoice.total.toStringAsFixed(2) : '0.00',
      invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
    ]).toList();

    List<String> headers = ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ', 'الحالة'];
    Map<int, pw.Alignment> customAlignments = {
      0: pw.Alignment.centerRight,
      1: pw.Alignment.centerRight,
      2: pw.Alignment.center,
      3: pw.Alignment.centerLeft,
      4: pw.Alignment.center,
    };

    // Define conditional colors for status column
    final Map<int, Map<String, PdfColor>> conditionalColors = {
      4: {
        'مدفوعة': incomeColor,
        'غير مدفوعة': expenseColor,
      },
    };

    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: regularFont,
      boldFont: boldFont,
      customAlignments: customAlignments,
      headerColor: primaryColor,
      headerTextColor: PdfColors.white,
      conditionalColors: conditionalColors,
    );
  }

  /// Builds a table for service requests
  static pw.Widget _buildServiceRequestsTable(List<ServiceRequest> requests, pw.Font regularFont, pw.Font boldFont) {
    final List<List<String>> tableData = requests.map((request) => [
      request.requestNumber.toString(),
      request.customerName.toString(),
      request.serviceType.toString(),
      DateFormat('dd/MM/yyyy').format(request.scheduledDate),
      ServiceRequest.getStatusName(request.status).toString(),
      request.assignedTo != null ? request.assignedTo.toString() : 'غير محدد',
    ]).toList();

    List<String> headers = ['رقم الطلب', 'العميل', 'نوع الخدمة', 'التاريخ المجدول', 'الحالة', 'المسؤول'];
    Map<int, pw.Alignment> customAlignments = {
      0: pw.Alignment.centerRight,
      1: pw.Alignment.centerRight,
      2: pw.Alignment.centerRight,
      3: pw.Alignment.center,
      4: pw.Alignment.center,
      5: pw.Alignment.centerRight,
    };

    // Define conditional colors for status column
    final Map<int, Map<String, PdfColor>> conditionalColors = {
      4: {
        'مكتمل': incomeColor,
        'قيد التنفيذ': const PdfColor(0.9, 0.6, 0.0), // Orange
        'في الانتظار': expenseColor,
      },
    };

    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: regularFont,
      boldFont: boldFont,
      customAlignments: customAlignments,
      headerColor: primaryColor,
      headerTextColor: PdfColors.white,
      conditionalColors: conditionalColors,
    );
  }

  /// Builds a table for financial transactions
  static pw.Widget _buildTransactionsTable(List<Transaction> transactions, pw.Font regularFont, pw.Font boldFont) {
    final List<List<String>> tableData = transactions.map((transaction) => [
      transaction.reference.toString(),
      DateFormat('dd/MM/yyyy').format(transaction.date),
      transaction.amount.isFinite ? transaction.amount.toStringAsFixed(2) : '0.00',
      transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
      transaction.category?.toString() ?? '',
      transaction.description.toString(),
    ]).toList();

    List<String> headers = ['المرجع', 'التاريخ', 'المبلغ', 'النوع', 'التصنيف', 'الوصف'];
    Map<int, pw.Alignment> customAlignments = {
      0: pw.Alignment.centerRight,
      1: pw.Alignment.center,
      2: pw.Alignment.centerLeft,
      3: pw.Alignment.center,
      4: pw.Alignment.centerRight,
      5: pw.Alignment.centerRight,
    };

    // Define conditional colors for transaction type column
    final Map<int, Map<String, PdfColor>> conditionalColors = {
      3: {
        'إيراد': incomeColor,
        'مصروف': expenseColor,
      },
    };

    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: regularFont,
      boldFont: boldFont,
      customAlignments: customAlignments,
      headerColor: primaryColor,
      headerTextColor: PdfColors.white,
      conditionalColors: conditionalColors,
    );
  }
}
