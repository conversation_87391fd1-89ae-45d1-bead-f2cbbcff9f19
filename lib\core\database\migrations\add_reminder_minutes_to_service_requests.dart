import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// Migration to add reminder_minutes column to service_requests table
class AddReminderMinutesToServiceRequests {
  /// Apply the migration
  static Future<void> migrate(Database db) async {
    try {
      // Check if the column already exists
      final List<Map<String, dynamic>> columns = await db.rawQuery(
        "PRAGMA table_info(service_requests)"
      );
      
      final bool columnExists = columns.any((column) => column['name'] == 'reminder_minutes');
      
      if (!columnExists) {
        if (kDebugMode) {
          print('Adding reminder_minutes column to service_requests table');
        }
        
        // Add the reminder_minutes column with default value of 60 (1 hour)
        await db.execute(
          "ALTER TABLE service_requests ADD COLUMN reminder_minutes INTEGER DEFAULT 60"
        );
        
        if (kDebugMode) {
          print('Successfully added reminder_minutes column to service_requests table');
        }
      } else {
        if (kDebugMode) {
          print('reminder_minutes column already exists in service_requests table');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding reminder_minutes column to service_requests table: $e');
      }
    }
  }
}
