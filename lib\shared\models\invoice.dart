import 'dart:convert';
import 'package:flutter/material.dart';
import 'customer.dart';
import 'invoice_item.dart';

enum InvoiceStatus {
  draft,
  issued,
  paid,
  partiallyPaid,
  overdue,
  cancelled,
}

class Invoice {
  final int? id;
  final String invoiceNumber;
  final Customer customer;
  final DateTime issueDate;
  final DateTime dueDate;
  final List<InvoiceItem> items;
  final InvoiceStatus status;
  final double subtotal;
  final double taxRate;
  final double taxAmount;
  final double discount;
  final double total;
  final String? notes;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  // الخصائص المضافة للتقارير
  int get customerId => customer.localId ?? 0;
  String get customerName => customer.name;
  DateTime get date => issueDate;
  bool get isPaid => status == InvoiceStatus.paid;

  Invoice({
    this.id,
    required this.invoiceNumber,
    required this.customer,
    required this.issueDate,
    required this.dueDate,
    required this.items,
    required this.status,
    required this.subtotal,
    required this.taxRate,
    required this.taxAmount,
    required this.discount,
    required this.total,
    this.notes,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'] as int?,
      invoiceNumber: map['invoice_number'] as String,
      customer: Customer.fromMap(map['customer'] as Map<String, dynamic>),
      issueDate: DateTime.parse(map['issue_date'] as String),
      dueDate: DateTime.parse(map['due_date'] as String),
      items: List<InvoiceItem>.from(
        (map['items'] as List<dynamic>).map<InvoiceItem>(
          (x) => InvoiceItem.fromMap(x as Map<String, dynamic>),
        ),
      ),
      status: _parseStatus(map['status'] as String),
      subtotal: map['subtotal'] as double,
      taxRate: map['tax_rate'] as double,
      taxAmount: map['tax_amount'] as double,
      discount: map['discount'] as double,
      total: map['total'] as double,
      notes: map['notes'] as String?,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory Invoice.fromJson(String source) =>
      Invoice.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'customer': customer.toMap(),
      'issue_date': issueDate.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'items': items.map((x) => x.toMap()).toList(),
      'status': status.toString().split('.').last,
      'subtotal': subtotal,
      'tax_rate': taxRate,
      'tax_amount': taxAmount,
      'discount': discount,
      'total': total,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    Customer? customer,
    DateTime? issueDate,
    DateTime? dueDate,
    List<InvoiceItem>? items,
    InvoiceStatus? status,
    double? subtotal,
    double? taxRate,
    double? taxAmount,
    double? discount,
    double? total,
    String? notes,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customer: customer ?? this.customer,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      items: items ?? this.items,
      status: status ?? this.status,
      subtotal: subtotal ?? this.subtotal,
      taxRate: taxRate ?? this.taxRate,
      taxAmount: taxAmount ?? this.taxAmount,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static InvoiceStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return InvoiceStatus.draft;
      case 'issued':
        return InvoiceStatus.issued;
      case 'paid':
        return InvoiceStatus.paid;
      case 'partially_paid':
      case 'partiallypaid':
        return InvoiceStatus.partiallyPaid;
      case 'overdue':
        return InvoiceStatus.overdue;
      case 'cancelled':
        return InvoiceStatus.cancelled;
      default:
        return InvoiceStatus.draft;
    }
  }

  static String getStatusName(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.issued:
        return 'صادرة';
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.overdue:
        return 'متأخرة';
      case InvoiceStatus.cancelled:
        return 'ملغاة';
    }
  }

  static Color getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.issued:
        return Colors.blue;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.partiallyPaid:
        return Colors.amber;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.red.shade900;
    }
  }
}
