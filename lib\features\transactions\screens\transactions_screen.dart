import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/transaction.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/models/supplier.dart';
import '../../../shared/models/bank_account.dart';
import '../../../shared/models/category.dart';
import '../../../shared/models/account_transfer.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/transaction_card.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/account_transfer_repository.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Transaction> _transactions = [];
  List<AccountTransfer> _transfers = [];
  String _searchQuery = '';
  String _categoryFilter = 'all';

  final TransactionRepository _transactionRepository = TransactionRepository();
  final AccountTransferRepository _transferRepository = AccountTransferRepository();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadTransactions();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging || _tabController.animation!.value == _tabController.index) {
      setState(() {
        // Reset search and filter when tab changes
        _searchQuery = '';
        _categoryFilter = 'all';
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام TransactionRepository للحصول على المعاملات من قاعدة البيانات
      final transactions = await _transactionRepository.getAllTransactions();

      // استخدام AccountTransferRepository للحصول على التحويلات من قاعدة البيانات
      final transfers = await _transferRepository.getAllTransfers();

      if (mounted) {
        setState(() {
          _transactions = transactions;
          _transfers = transfers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading transactions: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل المعاملات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Transaction> get _filteredTransactions {
    List<Transaction> filtered = _transactions;

    // Filter by type based on tab
    if (_tabController.index == 0) {
      // الإيرادات (Income)
      filtered = filtered.where((transaction) {
        return transaction.type == TransactionType.income;
      }).toList();
    } else if (_tabController.index == 1) {
      // المصروفات (Expenses)
      filtered = filtered.where((transaction) {
        return transaction.type == TransactionType.expense;
      }).toList();
    } else {
      // التحويلات (Transfers)
      // في هذه الحالة، نعرض قائمة فارغة لأن التحويلات ليست من نوع Transaction
      // سنقوم بتحميل التحويلات بشكل منفصل
      filtered = [];
    }

    // Apply category filter
    if (_categoryFilter != 'all') {
      filtered = filtered.where((transaction) {
        return transaction.category == _categoryFilter;
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final query = _searchQuery.toLowerCase();
        return transaction.reference.toLowerCase().contains(query) ||
            (transaction.description != null &&
             transaction.description!.toLowerCase().contains(query)) ||
            (transaction.category != null &&
             transaction.category!.toLowerCase().contains(query));
      }).toList();
    }

    return filtered;
  }

  List<AccountTransfer> get _filteredTransfers {
    List<AccountTransfer> filtered = _transfers;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transfer) {
        final query = _searchQuery.toLowerCase();
        return transfer.reference.toLowerCase().contains(query) ||
            (transfer.reason != null &&
             transfer.reason!.toLowerCase().contains(query)) ||
            transfer.sourceName.toLowerCase().contains(query) ||
            transfer.destinationName.toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }

  Set<String> get _categories {
    final categories = <String>{};
    for (final transaction in _transactions) {
      if (transaction.category != null) {
        categories.add(transaction.category!);
      }
    }
    return categories;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المعاملات المالية'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الإيرادات'),
            Tab(text: 'المصروفات'),
            Tab(text: 'التحويلات'),
          ],
          onTap: (_) {
            // Force rebuild when tab changes
            setState(() {});
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            tooltip: 'تقرير المعاملات',
            onPressed: () {
              if (_tabController.index == 2) {
                // Show transfer report
                Navigator.pushNamed(
                  context,
                  AppRoutes.cashFlowReport,
                );
              } else {
                // Show transaction report
                Navigator.pushNamed(
                  context,
                  AppRoutes.transactionReport,
                  arguments: _tabController.index == 0 ? 'income' : 'expense',
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: InputDecoration(
                hintText: _tabController.index == 0
                    ? 'بحث عن إيراد...'
                    : _tabController.index == 1
                        ? 'بحث عن مصروف...'
                        : 'بحث عن تحويل...',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          _buildCategoryFilterChips(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _tabController.index == 2
                    // عرض التحويلات
                    ? _filteredTransfers.isEmpty
                        ? const Center(
                            child: Text(
                              'لا توجد تحويلات',
                              style: AppTextStyles.heading3,
                            ),
                          )
                        : RefreshIndicator(
                            onRefresh: _loadTransactions,
                            child: ListView.builder(
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              itemCount: _filteredTransfers.length,
                              itemBuilder: (context, index) {
                                final transfer = _filteredTransfers[index];
                                return Card(
                                  elevation: 2,
                                  margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                                  ),
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
                                    title: Text(
                                      'تحويل ${transfer.reference}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            const Icon(Icons.arrow_forward, size: 16, color: Colors.grey),
                                            const SizedBox(width: 4),
                                            Expanded(
                                              child: Text(
                                                'من: ${transfer.sourceName}',
                                                style: const TextStyle(color: Colors.grey),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 4),
                                        Row(
                                          children: [
                                            const Icon(Icons.arrow_back, size: 16, color: Colors.grey),
                                            const SizedBox(width: 4),
                                            Expanded(
                                              child: Text(
                                                'إلى: ${transfer.destinationName}',
                                                style: const TextStyle(color: Colors.grey),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              DateFormat('dd/MM/yyyy').format(transfer.date),
                                              style: const TextStyle(color: Colors.grey),
                                            ),
                                            Text(
                                              '${transfer.amount} ر.س',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.blue,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    onTap: () {
                                      _showTransferDetails(context, transfer);
                                    },
                                    leading: const CircleAvatar(
                                      backgroundColor: Colors.blue,
                                      child: Icon(Icons.swap_horiz, color: Colors.white),
                                    ),
                                  ),
                                );
                              },
                            ),
                          )
                    // عرض المعاملات (إيرادات أو مصروفات)
                    : _filteredTransactions.isEmpty
                        ? Center(
                            child: Text(
                              _tabController.index == 0
                                  ? 'لا توجد إيرادات'
                                  : 'لا توجد مصروفات',
                              style: AppTextStyles.heading3,
                            ),
                          )
                        : RefreshIndicator(
                            onRefresh: _loadTransactions,
                            child: ListView.builder(
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              itemCount: _filteredTransactions.length,
                              itemBuilder: (context, index) {
                                final transaction = _filteredTransactions[index];
                                return TransactionCard(
                                  transaction: transaction,
                                  onTap: () {
                                    _showTransactionDetails(context, transaction);
                                  },
                                );
                              },
                            ),
                          ),
          ),
        ],
      ),
      floatingActionButton: _tabController.index == 2
          // زر إضافة تحويل جديد في تبويب "التحويلات"
          ? FloatingActionButton(
              heroTag: 'add',
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.accountTransfer);
              },
              backgroundColor: Colors.blue,
              child: const Icon(Icons.add),
              tooltip: 'إضافة تحويل جديد',
            )
          // أزرار إضافة معاملة وتحويل في تبويبي "الإيرادات" و"المصروفات"
          : Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [

                FloatingActionButton(
                  heroTag: 'add',
                  onPressed: () {
                    // Show add transaction dialog
                    _showAddTransactionDialog(context);
                  },
                  tooltip: 'إضافة معاملة',
                  child: const Icon(Icons.add),
                ),
              ],
            ),
    );
  }

  Widget _buildCategoryFilterChips() {
    // Only show category filter chips for income and expense tabs
    if (_tabController.index == 2 || _categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Row(
        children: [
          _buildFilterChip('all', 'الكل'),
          const SizedBox(width: 8),
          ..._categories.map((category) {
            return Padding(
              padding: const EdgeInsets.only(left: 8),
              child: _buildFilterChip(category, category),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _categoryFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _categoryFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.white,
      selectedColor: AppColors.primary.withAlpha(50),
      checkmarkColor: AppColors.primary,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primary : AppColors.textPrimary,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية المعاملات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Date range filter would go here
              const Text('فلترة حسب التاريخ'),
              // Payment method filter would go here
              const Text('فلترة حسب طريقة الدفع'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filters
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  // Helper method to build detail rows in transaction details dialog
  Widget buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showTransactionDetails(BuildContext context, Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تفاصيل الإيراد ${transaction.reference}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              buildDetailRow('النوع', Transaction.getTypeName(transaction.type)),
              buildDetailRow('المبلغ', '${transaction.amount} ر.س'),
              buildDetailRow('التاريخ', DateFormat('dd/MM/yyyy').format(transaction.date)),
              buildDetailRow('الفئة', transaction.category ?? 'غير محدد'),
              buildDetailRow('الوصف', transaction.description ?? 'غير محدد'),
              buildDetailRow('طريقة الدفع', Transaction.getPaymentMethodName(transaction.paymentMethod)),
              if (transaction.invoiceId != null)
                buildDetailRow('رقم الفاتورة', '${transaction.invoiceId}'),
              buildDetailRow('تاريخ الإنشاء', DateFormat('dd/MM/yyyy').format(transaction.createdAt)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Show delete confirmation
                showDeleteConfirmation(context, transaction);
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showTransferDetails(BuildContext context, AccountTransfer transfer) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تفاصيل التحويل ${transfer.reference}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              buildDetailRow('المبلغ', '${transfer.amount} ر.س'),
              buildDetailRow('التاريخ', DateFormat('dd/MM/yyyy').format(transfer.date)),
              buildDetailRow(
                'المصدر',
                '${_getEntityTypeString(transfer.sourceType)}: ${transfer.sourceName}'
              ),
              buildDetailRow(
                'الوجهة',
                '${_getEntityTypeString(transfer.destinationType)}: ${transfer.destinationName}'
              ),
              buildDetailRow('السبب', transfer.reason ?? 'غير محدد'),
              buildDetailRow('الحالة', _getTransferStatusString(transfer.status)),
              buildDetailRow('تاريخ الإنشاء', DateFormat('dd/MM/yyyy').format(transfer.createdAt)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  String _getEntityTypeString(TransferEntityType type) {
    switch (type) {
      case TransferEntityType.bankAccount:
        return 'حساب بنكي';
      case TransferEntityType.employee:
        return 'موظف';
    }
  }

  String _getTransferStatusString(TransferStatus status) {
    switch (status) {
      case TransferStatus.completed:
        return 'مكتمل';
      case TransferStatus.pending:
        return 'قيد الانتظار';
      case TransferStatus.cancelled:
        return 'ملغي';
    }
  }

  // Method to show delete confirmation dialog
  void showDeleteConfirmation(BuildContext context, Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف الإيراد ${transaction.reference}؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);

                // Delete the transaction
                deleteTransaction(transaction);
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  // Method to add a transaction
  Future<void> addTransaction({
    required TransactionType transactionType,
    required double amount,
    String? category,
    String? description,
    required PaymentMethod paymentMethod,
    int? customerId,
    int? supplierId,
    int? employeeId,
    int? bankAccountId,
    required ExpenseType expenseType,
  }) async {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      // Generate a reference number for the transaction
      final now = DateTime.now();
      final reference = 'TRX-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch % 10000}';

      // Create a new transaction object
      final transaction = Transaction(
        reference: reference,
        type: transactionType,
        amount: amount,
        date: now,
        category: category,
        description: description,
        paymentMethod: paymentMethod,
        customerId: customerId,
        supplierId: supplierId,
        employeeId: employeeId,
        bankAccountId: bankAccountId,
        createdAt: now,
      );

      // Insert the transaction into the database
      final result = await _transactionRepository.insertTransaction(transaction);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result > 0) {
          // Reload transactions
          await _loadTransactions();

          // Show success message
          final message = transactionType == TransactionType.income
              ? 'تم إضافة الإيراد بنجاح'
              : 'تم إضافة المصروف بنجاح';

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في إضافة الإيراد'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding transaction: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة الإيراد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Method to delete a transaction
  Future<void> deleteTransaction(Transaction transaction) async {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      // Delete the transaction from the database
      final result = await _transactionRepository.deleteTransaction(transaction.id!);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result > 0) {
          // Reload transactions
          await _loadTransactions();

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم حذف الإيراد ${transaction.reference}'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في حذف الإيراد'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting transaction: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حذف الإيراد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddTransactionDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final transactionType = _tabController.index == 0
        ? TransactionType.income
        : TransactionType.expense;

    // Fetch data from database
    setState(() {
      _isLoading = true;
    });

    _loadDataAndShowDialog(context, formKey, transactionType);
  }

  Future<void> _loadDataAndShowDialog(BuildContext context, GlobalKey<FormState> formKey, TransactionType transactionType) async {
      // Fetch all required data from database
      List<CategoryModel> incomeCategories = [];
      List<CategoryModel> expenseCategories = [];
      List<Customer> customers = [];
      List<Supplier> suppliers = [];
      List<Employee> employees = [];
      List<BankAccount> bankAccounts = [];

      try {
        // Load all data in parallel
        incomeCategories = await _transactionRepository.getIncomeCategories();
        expenseCategories = await _transactionRepository.getExpenseCategories();
        customers = await _transactionRepository.getCustomersForTransactions();
        suppliers = await _transactionRepository.getSuppliersForTransactions();
        employees = await _transactionRepository.getEmployeesForTransactions();
        bankAccounts = await _transactionRepository.getBankAccountsForTransactions();

        if (kDebugMode) {
          print('Loaded ${incomeCategories.length} income categories');
          print('Loaded ${expenseCategories.length} expense categories');
          print('Loaded ${customers.length} customers');
          print('Loaded ${suppliers.length} suppliers');
          print('Loaded ${employees.length} employees');
          print('Loaded ${bankAccounts.length} bank accounts');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error loading data: $e');
        }
      }

      setState(() {
        _isLoading = false;
      });

      // Check if widget is still mounted before showing dialog
      if (!mounted) return;

      // Variables for the form
      double amount = 0;
      String? category;
      String? description;
      PaymentMethod paymentMethod = PaymentMethod.cash;
      int? customerId;
      int? supplierId;
      int? employeeId;
      int? bankAccountId;
      ExpenseType expenseType = ExpenseType.regularExpense;

      // Use a BuildContext that won't be used across async gaps
      if (mounted) {
        final BuildContext currentContext = context;
        showDialog(
          context: currentContext,
          builder: (dialogContext) {
        return AlertDialog(
          title: Text(
            transactionType == TransactionType.income
                ? 'إضافة إيراد جديد'
                : 'إضافة مصروف جديد',
          ),
          content: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            child: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'المبلغ',
                    prefixIcon: Icon(Icons.money),
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    amount = double.parse(value!);
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),

                // تم إزالة الكود المكرر هنا

                StatefulBuilder(
                  builder: (context, setState) {
                    // Get appropriate categories based on transaction type
                    List<CategoryModel> categories = [];

                    if (transactionType == TransactionType.income) {
                      categories = incomeCategories;
                    } else {
                      if (expenseType == ExpenseType.supplierPayment) {
                        // For supplier payment, we can use a special category or filter expense categories
                        // For now, just use all expense categories
                        categories = expenseCategories;
                      } else {
                        categories = expenseCategories;
                      }
                    }

                    return SizedBox(
                      width: double.infinity,
                      child: DropdownButtonFormField<CategoryModel>(
                        isExpanded: true,
                        decoration: InputDecoration(
                          labelText: 'الفئة',
                          prefixIcon: const Icon(Icons.category),
                          border: const OutlineInputBorder(),
                          labelStyle: TextStyle(
                            color: transactionType == TransactionType.income
                                ? AppColors.primary
                                : expenseType == ExpenseType.supplierPayment
                                    ? Colors.green
                                    : Colors.red,
                          ),
                        ),
                      items: categories.map((category) {
                        return DropdownMenuItem<CategoryModel>(
                          value: category,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                category.icon,
                                size: 20,
                                color: category.color,
                              ),
                              const SizedBox(width: 8),
                              Flexible(
                                child: Text(
                                  category.name,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار الفئة';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        // Just update the dropdown
                      },
                      onSaved: (value) {
                        if (value != null) {
                          category = value.name;
                        }
                      },
                      ),
                    );
                    },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    prefixIcon: Icon(Icons.description),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  onSaved: (value) {
                    description = value;
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),

                // Client selector for income transactions
                if (transactionType == TransactionType.income)
                  StatefulBuilder(
                    builder: (context, setState) {
                      return Column(
                        children: [
                          SizedBox(
                            width: double.infinity,
                            child: DropdownButtonFormField<int?>(
                              isExpanded: true,
                              decoration: const InputDecoration(
                                labelText: 'العميل',
                                border: OutlineInputBorder(),
                              ),
                            items: [
                              const DropdownMenuItem<int?>(
                                value: null,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.money,
                                      size: 20,
                                      color: Colors.green,
                                    ),
                                    SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        'عميل نقد',
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ...customers.map((customer) {
                                return DropdownMenuItem<int?>(
                                  value: customer.localId,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        customer.type == CustomerType.company
                                            ? Icons.business
                                            : Icons.person,
                                        size: 20,
                                        color: AppColors.primary,
                                      ),
                                      const SizedBox(width: 8),
                                      Flexible(
                                        child: Text(
                                          customer.name,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                customerId = value;
                              });
                            },
                            value: customerId,
                            ),
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                        ],
                      );
                    },
                  ),

                // Expense type selector for expense transactions
                if (transactionType == TransactionType.expense)
                  StatefulBuilder(
                    builder: (context, setState) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 4.0),
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: Text(
                                'نوع المصروف',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: expenseType == ExpenseType.regularExpense
                                      ? Colors.red.shade700
                                      : Colors.green.shade700,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withAlpha(38),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    decoration: BoxDecoration(
                                      color: expenseType == ExpenseType.regularExpense ? Colors.red[100] : Colors.transparent,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: expenseType == ExpenseType.regularExpense ? Colors.red : Colors.transparent,
                                        width: 1.2,
                                      ),
                                    ),
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          expenseType = ExpenseType.regularExpense;
                                          supplierId = null;
                                        });
                                      },
                                      borderRadius: BorderRadius.circular(12),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                                        child: Column(
                                          children: [
                                            Column(
                                              children: [
                                                // Icono de verificación en la parte superior
                                                Icon(
                                                  expenseType == ExpenseType.regularExpense
                                                      ? Icons.check_circle
                                                      : Icons.circle_outlined,
                                                  color: expenseType == ExpenseType.regularExpense
                                                      ? Colors.red
                                                      : Colors.grey,
                                                  size: 18,
                                                ),
                                                const SizedBox(height: 4),
                                                // Texto centrado debajo del icono
                                                Text(
                                                  'مصروفات',
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: expenseType == ExpenseType.regularExpense
                                                        ? Colors.red
                                                        : AppColors.textPrimary,
                                                    fontWeight: expenseType == ExpenseType.regularExpense
                                                        ? FontWeight.bold
                                                        : FontWeight.normal,
                                                  ),
                                                  maxLines: 1,
                                                  overflow: TextOverflow.visible,
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 4),
                                            Icon(
                                              Icons.arrow_downward,
                                              color: expenseType == ExpenseType.regularExpense
                                                  ? Colors.red.shade400
                                                  : Colors.grey.shade400,
                                              size: 16,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    decoration: BoxDecoration(
                                      color: expenseType == ExpenseType.supplierPayment ? Colors.green[100] : Colors.transparent,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: expenseType == ExpenseType.supplierPayment ? Colors.green : Colors.transparent,
                                        width: 1.2,
                                      ),
                                    ),
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          expenseType = ExpenseType.supplierPayment;
                                        });
                                      },
                                      borderRadius: BorderRadius.circular(12),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                                        child: Column(
                                          children: [
                                            Column(
                                              children: [
                                                // Icono de verificación en la parte superior
                                                Icon(
                                                  expenseType == ExpenseType.supplierPayment
                                                      ? Icons.check_circle
                                                      : Icons.circle_outlined,
                                                  color: expenseType == ExpenseType.supplierPayment
                                                      ? Colors.green
                                                      : Colors.grey,
                                                  size: 18,
                                                ),
                                                const SizedBox(height: 4),
                                                // Texto centrado debajo del icono
                                                Text(
                                                  'سداد للمورد',
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: expenseType == ExpenseType.supplierPayment
                                                        ? Colors.green[800]
                                                        : AppColors.textPrimary,
                                                    fontWeight: expenseType == ExpenseType.supplierPayment
                                                        ? FontWeight.bold
                                                        : FontWeight.normal,
                                                  ),
                                                  maxLines: 1,
                                                  overflow: TextOverflow.visible,
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 4),
                                            Icon(
                                              Icons.local_shipping,
                                              color: expenseType == ExpenseType.supplierPayment
                                                  ? Colors.green.shade400
                                                  : Colors.grey.shade400,
                                              size: 16,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Show supplier selector only for supplier payment
                          if (expenseType == ExpenseType.supplierPayment)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
                                  child: Align(
                                    alignment: Alignment.centerRight,
                             
                                  ),
                                ),
                                Container(
                                  margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withAlpha(38),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: DropdownButtonFormField<int?>(
                                    isExpanded: true,
                                    decoration: InputDecoration(
                                      labelText: 'اختر المورد ',
                                      labelStyle: TextStyle(
                                        color: Colors.green.shade700,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                          color: Colors.green.shade200,
                                          width: 1.2,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                          color: Colors.green.shade200,
                                          width: 1.2,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                          color: Colors.green.shade700,
                                          width: 1.5,
                                        ),
                                      ),
                                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                      fillColor: Colors.white,
                                      filled: true,
                                    ),
                                    items: [
                                      ...suppliers.map((supplier) {
                                        return DropdownMenuItem<int?>(
                                          value: supplier.id,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Supplier.getCategoryIcon(supplier.category),
                                                size: 20,
                                                color: Supplier.getCategoryColor(supplier.category),
                                              ),
                                              const SizedBox(width: 8),
                                              Flexible(
                                                child: Text(
                                                  supplier.name,
                                                  overflow: TextOverflow.ellipsis,
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }),
                                    ],
                                    onChanged: (value) {
                                      setState(() {
                                        supplierId = value;
                                      });
                                    },
                                    value: supplierId,
                                    validator: (value) {
                                      if (expenseType == ExpenseType.supplierPayment && value == null) {
                                        return 'يرجى اختيار المورد';
                                      }
                                      return null;
                                    },
                                    icon: Icon(
                                      Icons.arrow_drop_down_circle,
                                      color: Colors.green.shade700,
                                    ),
                                    dropdownColor: Colors.white,
                                    style: TextStyle(
                                      color: Colors.green.shade900,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: AppDimensions.paddingM),
                              ],
                            ),
                        ],
                      );
                    },
                  ),

                // Payment method selector
                StatefulBuilder(
                  builder: (context, setState) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
                          child: Align(
                            alignment: Alignment.centerRight,
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withAlpha(38),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: DropdownButtonFormField<PaymentMethod>(
                            isExpanded: true,
                            decoration: InputDecoration(
                              labelText: 'طريقة الدفع',
                              labelStyle: TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Colors.blue.shade200,
                                  width: 1.2,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Colors.blue.shade200,
                                  width: 1.2,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: AppColors.primary,
                                  width: 1.5,
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              fillColor: Colors.white,
                              filled: true,
                            ),
                            items: PaymentMethod.values.map((method) {
                              return DropdownMenuItem<PaymentMethod>(
                                value: method,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Transaction.getPaymentMethodIcon(method),
                                      size: 20,
                                      color: AppColors.primary,
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        Transaction.getPaymentMethodName(method),
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                paymentMethod = value!;
                                // Reset related IDs when payment method changes
                                if (paymentMethod == PaymentMethod.cash) {
                                  bankAccountId = null;
                                } else if (paymentMethod == PaymentMethod.bankTransfer) {
                                  employeeId = null;
                                }
                              });
                            },
                            value: paymentMethod,
                            icon: Icon(
                              Icons.arrow_drop_down_circle,
                              color: AppColors.primary,
                            ),
                            dropdownColor: Colors.white,
                            style: TextStyle(
                              color: AppColors.primary,
                              fontSize: 14,
                            ),
                          ),
                        ),

                        // Show employee selector if payment method is cash
                        if (paymentMethod == PaymentMethod.cash)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Align(
                                  alignment: Alignment.centerRight,

                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withAlpha(38),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: DropdownButtonFormField<int?>(
                                  isExpanded: true,
                                  decoration: InputDecoration(
                                    labelText: 'الصندوق ',
                                    labelStyle: TextStyle(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: Colors.blue.shade200,
                                        width: 1.2,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: Colors.blue.shade200,
                                        width: 1.2,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: AppColors.primary,
                                        width: 1.5,
                                      ),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                    fillColor: Colors.white,
                                    filled: true,
                                  ),
                                  items: [
                                    const DropdownMenuItem<int?>(
                                      value: null,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Flexible(
                                            child: Text(
                                              'اختر الموظف',
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    ...employees.map((employee) {
                                      return DropdownMenuItem<int?>(
                                        value: employee.id,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.person,
                                              size: 20,
                                              color: AppColors.primary,
                                            ),
                                            const SizedBox(width: 8),
                                            Flexible(
                                              child: Text(
                                                employee.name,
                                                overflow: TextOverflow.ellipsis,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      employeeId = value;
                                    });
                                  },
                                  value: employeeId,
                                  icon: Icon(
                                    Icons.arrow_drop_down_circle,
                                    color: AppColors.primary,
                                  ),
                                  dropdownColor: Colors.white,
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),

                        // Show bank account selector if payment method is bank transfer
                        if (paymentMethod == PaymentMethod.bankTransfer)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Align(
                                  alignment: Alignment.centerRight,
                                  child: Text(
                                    'الحساب البنكي',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: AppColors.primary,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withAlpha(38),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: DropdownButtonFormField<int?>(
                                  isExpanded: true,
                                  decoration: InputDecoration(
                                    labelText: 'الحساب البنكي',
                                    labelStyle: TextStyle(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: Colors.blue.shade200,
                                        width: 1.2,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: Colors.blue.shade200,
                                        width: 1.2,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: AppColors.primary,
                                        width: 1.5,
                                      ),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                    fillColor: Colors.white,
                                    filled: true,
                                  ),
                                  items: [
                                    const DropdownMenuItem<int?>(
                                      value: null,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Flexible(
                                            child: Text(
                                              'اختر الحساب البنكي',
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    ...bankAccounts.map((account) {
                                      return DropdownMenuItem<int?>(
                                        value: account.id,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              BankAccount.getTypeIcon(account.type),
                                              size: 20,
                                              color: BankAccount.getTypeColor(account.type),
                                            ),
                                            const SizedBox(width: 8),
                                            Flexible(
                                              child: Text(
                                                '${account.bankName} - ${account.accountNumber}',
                                                overflow: TextOverflow.ellipsis,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      bankAccountId = value;
                                    });
                                  },
                                  value: bankAccountId,
                                  validator: (value) {
                                    if (paymentMethod == PaymentMethod.bankTransfer && value == null) {
                                      return 'يرجى اختيار الحساب البنكي';
                                    }
                                    return null;
                                  },
                                  icon: Icon(
                                    Icons.arrow_drop_down_circle,
                                    color: AppColors.primary,
                                  ),
                                  dropdownColor: Colors.white,
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                      ],
                    );
                  },
                ),
              ],
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // استخدام دالة منفصلة لإضافة الإيراد
                  addTransaction(
                    transactionType: transactionType,
                    amount: amount,
                    category: category,
                    description: description,
                    paymentMethod: paymentMethod,
                    customerId: customerId,
                    supplierId: supplierId,
                    employeeId: employeeId,
                    bankAccountId: bankAccountId,
                    expenseType: expenseType,
                  );
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  // Función para mostrar el diálogo de confirmación de eliminación
  // Esta función se utiliza cuando el usuario quiere eliminar una transacción
  void _showDeleteConfirmation(BuildContext context, Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من رغبتك في حذف الإيراد ${transaction.reference}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);

                // Eliminar la transacción directamente
                setState(() {
                  _isLoading = true;
                });

                // Eliminar la transacción de la base de datos
                _transactionRepository.deleteTransaction(transaction.id!).then((result) {
                  if (result > 0 && mounted) {
                    // Actualizar la lista
                    _loadTransactions();

                    // Mostrar mensaje de éxito
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم حذف الإيراد ${transaction.reference}'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                }).catchError((e) {
                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });

                    // Mostrar mensaje de error
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('حدث خطأ أثناء حذف الإيراد: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                });
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _addTransaction({
    required TransactionType transactionType,
    required double amount,
    String? category,
    String? description,
    required PaymentMethod paymentMethod,
    int? customerId,
    int? supplierId,
    int? employeeId,
    int? bankAccountId,
    ExpenseType? expenseType,
  }) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء كائن الإيراد
      Transaction newTransaction;

      if (transactionType == TransactionType.expense && expenseType == ExpenseType.supplierPayment) {
        // إنشاء إيراد دفع للمورد
        newTransaction = Transaction(
          reference: 'TRX-${DateTime.now().year}-${DateTime.now().millisecondsSinceEpoch}',
          type: transactionType,
          amount: amount,
          date: DateTime.now(),
          category: Transaction.getCategoryName(TransactionCategory.supplierPayment),
          description: description ?? 'سداد للمورد',
          paymentMethod: paymentMethod,
          supplierId: supplierId,
          bankAccountId: bankAccountId,
          createdAt: DateTime.now(),
        );
      } else {
        // إنشاء إيراد عادية
        newTransaction = Transaction(
          reference: 'TRX-${DateTime.now().year}-${DateTime.now().millisecondsSinceEpoch}',
          type: transactionType,
          amount: amount,
          date: DateTime.now(),
          category: category,
          description: description,
          paymentMethod: paymentMethod,
          customerId: customerId,
          supplierId: transactionType == TransactionType.expense ? supplierId : null,
          employeeId: employeeId,
          bankAccountId: bankAccountId,
          createdAt: DateTime.now(),
        );
      }

      // حفظ الإيراد في قاعدة البيانات
      final result = await _transactionRepository.insertTransaction(newTransaction);

      if (mounted) {
        if (result > 0) {
          // تم الإضافة بنجاح، قم بتحديث القائمة
          _loadTransactions(); // إعادة تحميل المعاملات من قاعدة البيانات

          setState(() {
            _isLoading = false;
          });

          // إعداد رسالة النجاح
          String message;
          if (transactionType == TransactionType.income) {
            message = 'تم إضافة إيراد جديد بمبلغ $amount ر.س';
          } else if (expenseType == ExpenseType.supplierPayment) {
            message = 'تم تسجيل سداد للمورد بمبلغ $amount ر.س';
          } else {
            message = 'تم إضافة مصروف جديد بمبلغ $amount ر.س';
          }

          // عرض رسالة النجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // فشل في الإضافة
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إضافة الإيراد'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // حدث خطأ أثناء الإضافة
      if (kDebugMode) {
        print('Error adding transaction: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة الإيراد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteTransaction(Transaction transaction) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // حذف الإيراد من قاعدة البيانات
      if (transaction.id != null) {
        final result = await _transactionRepository.deleteTransaction(transaction.id!);

        if (mounted) {
          if (result > 0) {
            // تم الحذف بنجاح، قم بتحديث القائمة
            setState(() {
              _transactions.removeWhere((t) => t.id == transaction.id);
              _isLoading = false;
            });

            // عرض رسالة نجاح
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم حذف الإيراد ${transaction.reference}'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // فشل في الحذف
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في حذف الإيراد'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // الإيراد ليس لها معرف
        if (mounted) {
          setState(() {
            _transactions.removeWhere((t) => t.reference == transaction.reference);
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الإيراد ${transaction.reference}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      // حدث خطأ أثناء الحذف
      if (kDebugMode) {
        print('Error deleting transaction: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حذف الإيراد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
}