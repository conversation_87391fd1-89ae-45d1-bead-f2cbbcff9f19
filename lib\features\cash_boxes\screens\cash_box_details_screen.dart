import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../core/repositories/cash_box_repository.dart';
import '../../../shared/models/cash_box.dart';
import '../../../shared/widgets/enhanced_card.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../../../shared/widgets/rtl_aware_widget.dart';
import '../../../core/error_handling/error_handler.dart';
import 'cash_box_form_screen.dart';

/// Cash Box details screen showing transactions and balance information
class CashBoxDetailsScreen extends StatefulWidget {
  final CashBox cashBox;

  const CashBoxDetailsScreen({super.key, required this.cashBox});

  @override
  State<CashBoxDetailsScreen> createState() => _CashBoxDetailsScreenState();
}

class _CashBoxDetailsScreenState extends State<CashBoxDetailsScreen> {
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();
  
  late CashBox _cashBox;
  List<CashBoxTransaction> _transactions = [];
  Map<String, double> _summary = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _cashBox = widget.cashBox;
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);
      
      // Load updated cash box data
      final updatedCashBox = await _cashBoxRepository.getCashBoxById(_cashBox.localId!);
      if (updatedCashBox != null) {
        _cashBox = updatedCashBox;
      }
      
      // Load transactions
      final transactions = await _cashBoxRepository.getCashBoxTransactions(
        _cashBox.localId!,
        limit: 50, // Load last 50 transactions
      );
      
      // Load summary
      final summary = await _cashBoxRepository.getCashBoxSummary(_cashBox.localId!);
      
      setState(() {
        _transactions = transactions;
        _summary = summary;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ErrorHandler.handleError(context, e);
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return RTLAwareWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text(_cashBox.name),
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _editCashBox,
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadData,
            ),
          ],
        ),
        body: ResponsiveContainer(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadData,
                  child: ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      _buildBalanceCard(localizations, theme),
                      const SizedBox(height: 16),
                      _buildSummaryCard(localizations, theme),
                      const SizedBox(height: 16),
                      _buildTransactionsSection(localizations, theme),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildBalanceCard(AppLocalizations localizations, ThemeData theme) {
    final isPositiveBalance = _cashBox.currentBalance >= 0;
    
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                localizations.translate('balance_information'),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isPositiveBalance 
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('opening_balance'),
                      style: theme.textTheme.bodyMedium,
                    ),
                    Text(
                      localizations.formatCurrency(_cashBox.openingBalance, symbol: _cashBox.currency),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('current_balance'),
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      localizations.formatCurrency(_cashBox.currentBalance, symbol: _cashBox.currency),
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isPositiveBalance ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          if (_cashBox.description != null) ...[
            const SizedBox(height: 16),
            Text(
              localizations.translate('description'),
              style: theme.textTheme.labelMedium,
            ),
            const SizedBox(height: 4),
            Text(
              _cashBox.description!,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(AppLocalizations localizations, ThemeData theme) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                localizations.translate('transaction_summary'),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  localizations.translate('total_income'),
                  _summary['total_income'] ?? 0.0,
                  Colors.green,
                  Icons.trending_up,
                  localizations,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  localizations.translate('total_expense'),
                  _summary['total_expense'] ?? 0.0,
                  Colors.red,
                  Icons.trending_down,
                  localizations,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildSummaryItem(
            localizations.translate('total_transactions'),
            _summary['transaction_count'] ?? 0.0,
            Colors.blue,
            Icons.receipt_long,
            localizations,
            isCount: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    double value,
    Color color,
    IconData icon,
    AppLocalizations localizations, {
    bool isCount = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            isCount 
                ? value.toInt().toString()
                : localizations.formatCurrency(value, symbol: _cashBox.currency),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsSection(AppLocalizations localizations, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              localizations.translate('recent_transactions'),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_transactions.length >= 50)
              TextButton(
                onPressed: () {
                  // Navigate to full transactions list
                },
                child: Text(localizations.translate('view_all')),
              ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        if (_transactions.isEmpty)
          EnhancedCard(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_long_outlined,
                      size: 48,
                      color: theme.colorScheme.outline,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      localizations.translate('no_transactions'),
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          ...List.generate(_transactions.length, (index) {
            final transaction = _transactions[index];
            return _buildTransactionCard(transaction, localizations, theme);
          }),
      ],
    );
  }

  Widget _buildTransactionCard(
    CashBoxTransaction transaction,
    AppLocalizations localizations,
    ThemeData theme,
  ) {
    final isIncome = transaction.type == CashBoxTransactionType.income;
    final color = isIncome ? Colors.green : Colors.red;
    final icon = isIncome ? Icons.add_circle : Icons.remove_circle;
    
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: color.withOpacity(0.1),
            child: Icon(icon, color: color),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  localizations.formatDate(transaction.transactionDate),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${isIncome ? '+' : '-'}${localizations.formatCurrency(transaction.amount, symbol: _cashBox.currency)}',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _editCashBox() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => CashBoxFormScreen(cashBox: _cashBox),
      ),
    );
    
    if (result == true) {
      _loadData();
      // Return true to parent to refresh the list
      Navigator.pop(context, true);
    }
  }
}
