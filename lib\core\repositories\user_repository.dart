import '../../shared/models/user.dart';
import '../database/database_helper.dart';

/// مستودع المستخدمين
class UserRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على مستخدم بواسطة اسم المستخدم
  Future<User?> getUserByUsername(String username) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة مستخدم جديد
  Future<int> insertUser(User user) async {
    final db = await _databaseHelper.database;

    // استخراج البيانات من الكائن
    final Map<String, dynamic> userData = {
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'avatar': user.avatar,
      'status': user.isActive ? 'active' : 'inactive',
      'created_at': user.createdAt.toIso8601String(),
      'permissions': user.permissions,
    };

    // إضافة كلمة المرور إذا كانت موجودة
    if (user.password != null) {
      userData['password'] = user.password;
    }

    // إضافة آخر تسجيل دخول إذا كان موجودًا
    if (user.lastLogin != null) {
      userData['last_login'] = user.lastLogin!.toIso8601String();
    }

    return await db.insert('users', userData);
  }

  /// تحديث مستخدم
  Future<int> updateUser(User user) async {
    final db = await _databaseHelper.database;

    // استخراج البيانات من الكائن
    final Map<String, dynamic> userData = {
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'avatar': user.avatar,
      'status': user.isActive ? 'active' : 'inactive',
      'created_at': user.createdAt.toIso8601String(),
      'permissions': user.permissions,
    };

    // إضافة كلمة المرور إذا كانت موجودة
    if (user.password != null) {
      userData['password'] = user.password;
    }

    // إضافة آخر تسجيل دخول إذا كان موجودًا
    if (user.lastLogin != null) {
      userData['last_login'] = user.lastLogin!.toIso8601String();
    }

    return await db.update(
      'users',
      userData,
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  /// تحديث صلاحيات المستخدم فقط
  Future<int> updateUserPermissions(int userId, String permissions) async {
    final db = await _databaseHelper.database;

    return await db.update(
      'users',
      {'permissions': permissions},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  /// حذف مستخدم
  Future<int> deleteUser(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // تم نقل هذه الدالة إلى أعلى

  /// التحقق من صحة بيانات تسجيل الدخول
  Future<User?> authenticate(String username, String password) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'username = ? AND password = ?',
      whereArgs: [username, password],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  /// تحديث آخر تسجيل دخول للمستخدم
  Future<int> updateLastLogin(int userId) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'users',
      {'last_login': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }
}
