import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'dart:io';
import 'package:path/path.dart' as path;

/// Repository for managing application settings
class SettingsRepository {
  static const String _dbPathKey = 'database_path';
  static const String _defaultDbName = 'hvac_manager.db';

  /// Get the current database path
  /// Returns the custom path if set, otherwise returns the default path
  Future<String> getDatabasePath() async {
    final prefs = await SharedPreferences.getInstance();
    final customPath = prefs.getString(_dbPathKey);

    if (customPath != null && customPath.isNotEmpty) {
      // Verify the directory exists
      final directory = Directory(path.dirname(customPath));
      if (await directory.exists()) {
        if (kDebugMode) {
          print('Using custom database path: $customPath');
        }
        return customPath;
      } else {
        if (kDebugMode) {
          print('Custom database directory does not exist: ${directory.path}');
        }
        // Fall back to default path if directory doesn't exist
        return await _getDefaultDatabasePath();
      }
    }

    // Use default path if no custom path is set
    return await _getDefaultDatabasePath();
  }

  /// Set a custom database path
  Future<bool> setDatabasePath(String dbPath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_dbPathKey, dbPath);

      if (kDebugMode) {
        print('Database path set to: $dbPath');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error setting database path: $e');
      }
      return false;
    }
  }

  /// Clear the custom database path setting
  Future<bool> clearDatabasePath() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dbPathKey);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing database path: $e');
      }
      return false;
    }
  }

  /// Get the default database path
  Future<String> _getDefaultDatabasePath() async {
    final dbDir = await getDatabasesPath();
    final dbPath = path.join(dbDir, _defaultDbName);

    if (kDebugMode) {
      print('Using default database path: $dbPath');
    }

    return dbPath;
  }

  /// Get available storage directories for database
  Future<List<Directory>> getAvailableStorageDirectories() async {
    final List<Directory> directories = [];

    try {
      // Add application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      directories.add(appDocDir);

      // Try to add external storage directory (Android)
      try {
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          directories.add(externalDir);
        }
      } catch (e) {
        // Ignore errors for platforms that don't support external storage
      }

      // Add downloads directory if available
      try {
        final downloadsDir = await getDownloadsDirectory();
        if (downloadsDir != null) {
          directories.add(downloadsDir);
        }
      } catch (e) {
        // Ignore errors for platforms that don't support downloads directory
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting storage directories: $e');
      }
    }

    return directories;
  }
}
