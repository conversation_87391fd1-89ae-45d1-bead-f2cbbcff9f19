import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../core/repositories/cash_box_repository.dart';
import '../../../shared/models/cash_box.dart';
import '../../../shared/widgets/enhanced_card.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../../../shared/widgets/rtl_aware_widget.dart';
import '../../../core/error_handling/error_handler.dart';
import '../../../shared/widgets/feedback_widgets.dart';
import 'cash_box_form_screen.dart';
import 'cash_box_details_screen.dart';

/// Cash Boxes management screen with Arabic RTL support
class CashBoxesScreen extends StatefulWidget {
  const CashBoxesScreen({super.key});

  @override
  State<CashBoxesScreen> createState() => _CashBoxesScreenState();
}

class _CashBoxesScreenState extends State<CashBoxesScreen> {
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();
  
  List<CashBox> _cashBoxes = [];
  bool _isLoading = true;
  bool _showInactiveOnly = false;

  @override
  void initState() {
    super.initState();
    _loadCashBoxes();
  }

  Future<void> _loadCashBoxes() async {
    try {
      setState(() => _isLoading = true);
      
      final cashBoxes = _showInactiveOnly 
          ? await _cashBoxRepository.getAllCashBoxes()
          : await _cashBoxRepository.getActiveCashBoxes();
      
      setState(() {
        _cashBoxes = cashBoxes;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ErrorHandler.handleError(context, e);
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return RTLAwareWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text(localizations.translate('cash_boxes')),
          actions: [
            IconButton(
              icon: Icon(_showInactiveOnly ? Icons.visibility : Icons.visibility_off),
              onPressed: () {
                setState(() => _showInactiveOnly = !_showInactiveOnly);
                _loadCashBoxes();
              },
              tooltip: _showInactiveOnly 
                  ? localizations.translate('show_active_only')
                  : localizations.translate('show_all'),
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _addCashBox,
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadCashBoxes,
            ),
          ],
        ),
        body: ResponsiveContainer(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _cashBoxes.isEmpty
                  ? _buildEmptyState(localizations, theme)
                  : _buildCashBoxesList(localizations, theme),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _addCashBox,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 64,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            localizations.translate('no_cash_boxes'),
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _addCashBox,
            icon: const Icon(Icons.add),
            label: Text(localizations.translate('add_cash_box')),
          ),
        ],
      ),
    );
  }

  Widget _buildCashBoxesList(AppLocalizations localizations, ThemeData theme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _cashBoxes.length,
      itemBuilder: (context, index) {
        final cashBox = _cashBoxes[index];
        return _buildCashBoxCard(cashBox, localizations, theme);
      },
    );
  }

  Widget _buildCashBoxCard(CashBox cashBox, AppLocalizations localizations, ThemeData theme) {
    final isPositiveBalance = cashBox.currentBalance >= 0;
    
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _viewCashBoxDetails(cashBox),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: cashBox.isActive 
                    ? theme.colorScheme.primaryContainer
                    : theme.colorScheme.surfaceVariant,
                child: Icon(
                  Icons.account_balance_wallet,
                  color: cashBox.isActive 
                      ? theme.colorScheme.onPrimaryContainer
                      : theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            cashBox.name,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (!cashBox.isActive)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              localizations.translate('inactive'),
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: Colors.orange,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (cashBox.description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        cashBox.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Balance Information
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isPositiveBalance 
                  ? Colors.green.withOpacity(0.1)
                  : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('opening_balance'),
                      style: theme.textTheme.bodySmall,
                    ),
                    Text(
                      localizations.formatCurrency(cashBox.openingBalance, symbol: cashBox.currency),
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.translate('current_balance'),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      localizations.formatCurrency(cashBox.currentBalance, symbol: cashBox.currency),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isPositiveBalance ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
                if (cashBox.totalTransactions != 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        localizations.translate('net_transactions'),
                        style: theme.textTheme.bodySmall,
                      ),
                      Text(
                        localizations.formatCurrency(cashBox.totalTransactions, symbol: cashBox.currency),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: cashBox.totalTransactions >= 0 ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _addTransaction(cashBox, CashBoxTransactionType.income),
                  icon: const Icon(Icons.add, size: 16),
                  label: Text(localizations.translate('add_income')),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.green,
                    side: const BorderSide(color: Colors.green),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _addTransaction(cashBox, CashBoxTransactionType.expense),
                  icon: const Icon(Icons.remove, size: 16),
                  label: Text(localizations.translate('add_expense')),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addCashBox() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const CashBoxFormScreen(),
      ),
    );
    
    if (result == true) {
      _loadCashBoxes();
    }
  }

  void _viewCashBoxDetails(CashBox cashBox) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => CashBoxDetailsScreen(cashBox: cashBox),
      ),
    );
    
    if (result == true) {
      _loadCashBoxes();
    }
  }

  void _addTransaction(CashBox cashBox, String type) async {
    final localizations = AppLocalizations.of(context)!;
    
    final amount = await FeedbackWidgets.showInputDialog(
      context,
      title: type == CashBoxTransactionType.income 
          ? localizations.translate('add_income')
          : localizations.translate('add_expense'),
      message: localizations.translate('enter_amount'),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return localizations.translate('amount_required');
        }
        final amount = double.tryParse(value);
        if (amount == null || amount <= 0) {
          return localizations.translate('invalid_amount');
        }
        if (type == CashBoxTransactionType.expense && !cashBox.canWithdraw(amount)) {
          return localizations.translate('insufficient_balance');
        }
        return null;
      },
    );
    
    if (amount == null) return;
    
    final description = await FeedbackWidgets.showInputDialog(
      context,
      title: localizations.translate('transaction_description'),
      message: localizations.translate('enter_description'),
      maxLines: 3,
    );
    
    if (description == null || description.trim().isEmpty) return;
    
    try {
      final transaction = CashBoxTransaction(
        cashBoxId: cashBox.localId!,
        type: type,
        amount: double.parse(amount),
        description: description.trim(),
        referenceType: CashBoxReferenceType.manual,
        transactionDate: DateTime.now(),
        createdAt: DateTime.now(),
      );
      
      await _cashBoxRepository.addTransaction(transaction);
      
      if (mounted) {
        ErrorHandler.showSuccess(
          context,
          localizations.translate('transaction_added_successfully'),
        );
        _loadCashBoxes();
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.handleError(context, e);
      }
    }
  }
}
