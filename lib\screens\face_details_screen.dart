import 'package:flutter/material.dart';

class FaceDetailsScreen extends StatefulWidget {
  const FaceDetailsScreen({Key? key}) : super(key: key);

  @override
  State<FaceDetailsScreen> createState() => _FaceDetailsScreenState();
}

class _FaceDetailsScreenState extends State<FaceDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Details'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: NetworkImage('https://via.placeholder.com/200'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Detailed Description: This is a beautiful place to visit. It has a lot of history and culture.',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const Text('Price: \$100'),
            IconButton(
              icon: const Icon(Icons.public),
              onPressed: () {
                // Implement globe integration
              },
            ),
            const Text('Reviews: 4.5/5'),
            ElevatedButton(
              onPressed: () {
                // Implement booking logic
              },
              child: const Text('Book Now'),
            ),
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: () {
                // Implement save to options logic
              },
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                // Implement share function
              },
            ),
          ],
        ),
      ),
    );
  }
}
