import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../../shared/models/category.dart';

class TransactionCategoryRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Obtener todas las categorías de ingresos
  Future<List<CategoryModel>> getIncomeCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'categories',
        where: 'type = ? AND status = ?',
        whereArgs: ['income', 'active'],
      );

      return List.generate(maps.length, (i) {
        return CategoryModel(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          type: maps[i]['type'] as String,
          icon: Icons.arrow_upward, // Icono predeterminado para ingresos
          color: Colors.green, // Color predeterminado para ingresos
          isActive: maps[i]['status'] == 'active',
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          description: maps[i]['description'] as String?,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo categorías de ingresos: $e');
      }
      return [];
    }
  }

  // Obtener todas las categorías de gastos
  Future<List<CategoryModel>> getExpenseCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'categories',
        where: 'type = ? AND status = ?',
        whereArgs: ['expense', 'active'],
      );

      return List.generate(maps.length, (i) {
        return CategoryModel(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          type: maps[i]['type'] as String,
          icon: Icons.arrow_downward, // Icono predeterminado para gastos
          color: Colors.red, // Color predeterminado para gastos
          isActive: maps[i]['status'] == 'active',
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          description: maps[i]['description'] as String?,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo categorías de gastos: $e');
      }
      return [];
    }
  }

  // Obtener una categoría por su ID
  Future<CategoryModel?> getCategoryById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return CategoryModel(
          id: maps[0]['id'] as int,
          name: maps[0]['name'] as String,
          type: maps[0]['type'] as String,
          icon: maps[0]['type'] == 'income' ? Icons.arrow_upward : Icons.arrow_downward,
          color: maps[0]['type'] == 'income' ? Colors.green : Colors.red,
          isActive: maps[0]['status'] == 'active',
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
          description: maps[0]['description'] as String?,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo categoría por ID: $e');
      }
      return null;
    }
  }

  // Insertar una nueva categoría
  Future<int> insertCategory(CategoryModel category) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'categories',
        {
          'name': category.name,
          'type': category.type,
          'description': category.description,
          'status': category.isActive ? 'active' : 'inactive',
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error insertando categoría: $e');
      }
      return -1;
    }
  }

  // Actualizar una categoría existente
  Future<int> updateCategory(CategoryModel category) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'categories',
        {
          'name': category.name,
          'type': category.type,
          'description': category.description,
          'status': category.isActive ? 'active' : 'inactive',
        },
        where: 'id = ?',
        whereArgs: [category.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error actualizando categoría: $e');
      }
      return 0;
    }
  }

  // Eliminar una categoría
  Future<int> deleteCategory(int id) async {
    try {
      final db = await _dbHelper.database;

      // Check if the category is used in any transaction
      final List<Map<String, dynamic>> transactionMaps = await db.query(
        'transactions',
        where: 'category_id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (transactionMaps.isNotEmpty) {
        // If the category is used, mark it as inactive instead of deleting
        return await db.update(
          'categories',
          {'status': 'inactive'},
          where: 'id = ?',
          whereArgs: [id],
        );
      } else {
        // If the category is not used, delete it
        return await db.delete(
          'categories',
          where: 'id = ?',
          whereArgs: [id],
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error eliminando categoría: $e');
      }
      return 0;
    }
  }

  // Get all categories
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('categories');

      return List.generate(maps.length, (i) {
        return CategoryModel(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          type: maps[i]['type'] as String,
          icon: maps[i]['type'] == 'income' ? Icons.arrow_upward : Icons.arrow_downward,
          color: maps[i]['type'] == 'income' ? Colors.green : Colors.red,
          isActive: maps[i]['status'] == 'active',
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          description: maps[i]['description'] as String?,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all categories: $e');
      }
      return [];
    }
  }
}
