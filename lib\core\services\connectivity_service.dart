import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

/// Service to monitor internet connectivity and determine sync availability
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  
  // Stream controllers
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  final StreamController<ConnectivityStatus> _statusController = StreamController<ConnectivityStatus>.broadcast();
  
  // Current state
  bool _isConnected = false;
  ConnectivityStatus _currentStatus = ConnectivityStatus.unknown;
  
  // Subscription
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  Timer? _connectivityTimer;

  /// Initialize the connectivity service
  Future<void> initialize() async {
    try {
      // Check initial connectivity
      await _checkConnectivity();
      
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          if (kDebugMode) {
            print('❌ Connectivity subscription error: $error');
          }
        },
      );

      // Start periodic connectivity checks
      _startPeriodicChecks();
      
      if (kDebugMode) {
        print('✅ ConnectivityService initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing ConnectivityService: $e');
      }
    }
  }

  /// Dispose the service
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityTimer?.cancel();
    _connectivityController.close();
    _statusController.close();
  }

  // Getters

  /// Current connectivity status
  bool get isConnected => _isConnected;

  /// Current detailed connectivity status
  ConnectivityStatus get currentStatus => _currentStatus;

  /// Stream of connectivity changes (true/false)
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Stream of detailed connectivity status changes
  Stream<ConnectivityStatus> get statusStream => _statusController.stream;

  /// Check if device has internet connectivity
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }

      // Perform actual internet connectivity test
      return await _performInternetCheck();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking internet connection: $e');
      }
      return false;
    }
  }

  /// Check if sync should be performed based on connectivity and settings
  Future<bool> shouldSync({bool requireWifi = false}) async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      
      // No connectivity
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }

      // If WiFi is required and we're not on WiFi
      if (requireWifi && connectivityResult != ConnectivityResult.wifi) {
        return false;
      }

      // Check actual internet connectivity
      return await _performInternetCheck();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking sync conditions: $e');
      }
      return false;
    }
  }

  /// Get current connection type
  Future<ConnectionType> getConnectionType() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      
      switch (connectivityResult) {
        case ConnectivityResult.wifi:
          return ConnectionType.wifi;
        case ConnectivityResult.mobile:
          return ConnectionType.mobile;
        case ConnectivityResult.ethernet:
          return ConnectionType.ethernet;
        case ConnectivityResult.bluetooth:
          return ConnectionType.bluetooth;
        case ConnectivityResult.vpn:
          return ConnectionType.vpn;
        case ConnectivityResult.other:
          return ConnectionType.other;
        case ConnectivityResult.none:
        default:
          return ConnectionType.none;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting connection type: $e');
      }
      return ConnectionType.none;
    }
  }

  /// Wait for internet connection with timeout
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (_isConnected) {
      return true;
    }

    final completer = Completer<bool>();
    late StreamSubscription subscription;
    Timer? timeoutTimer;

    subscription = connectivityStream.listen((isConnected) {
      if (isConnected) {
        subscription.cancel();
        timeoutTimer?.cancel();
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    });

    timeoutTimer = Timer(timeout, () {
      subscription.cancel();
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    return completer.future;
  }

  // Private methods

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) async {
    if (kDebugMode) {
      print('📶 Connectivity changed: $result');
    }
    
    await _checkConnectivity();
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      final hasInternet = await _performInternetCheck();
      
      final wasConnected = _isConnected;
      _isConnected = hasInternet;
      
      // Update detailed status
      _currentStatus = _getDetailedStatus(connectivityResult, hasInternet);
      
      // Notify listeners if status changed
      if (wasConnected != _isConnected) {
        _connectivityController.add(_isConnected);
        
        if (kDebugMode) {
          print('📶 Connectivity status changed: ${_isConnected ? 'Connected' : 'Disconnected'}');
        }
      }
      
      _statusController.add(_currentStatus);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking connectivity: $e');
      }
    }
  }

  /// Perform actual internet connectivity test
  Future<bool> _performInternetCheck() async {
    try {
      // Try to connect to multiple reliable hosts
      final hosts = [
        'google.com',
        'cloudflare.com',
        'firebase.google.com',
      ];

      for (final host in hosts) {
        try {
          final result = await InternetAddress.lookup(host).timeout(
            const Duration(seconds: 5),
          );
          
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            return true;
          }
        } catch (e) {
          // Continue to next host
          continue;
        }
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Internet check failed: $e');
      }
      return false;
    }
  }

  /// Get detailed connectivity status
  ConnectivityStatus _getDetailedStatus(ConnectivityResult result, bool hasInternet) {
    if (!hasInternet) {
      return ConnectivityStatus.disconnected;
    }

    switch (result) {
      case ConnectivityResult.wifi:
        return ConnectivityStatus.connectedWifi;
      case ConnectivityResult.mobile:
        return ConnectivityStatus.connectedMobile;
      case ConnectivityResult.ethernet:
        return ConnectivityStatus.connectedEthernet;
      case ConnectivityResult.bluetooth:
        return ConnectivityStatus.connectedBluetooth;
      case ConnectivityResult.vpn:
        return ConnectivityStatus.connectedVpn;
      case ConnectivityResult.other:
        return ConnectivityStatus.connectedOther;
      case ConnectivityResult.none:
      default:
        return ConnectivityStatus.disconnected;
    }
  }

  /// Start periodic connectivity checks
  void _startPeriodicChecks() {
    _connectivityTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkConnectivity(),
    );
  }
}

/// Enum for detailed connectivity status
enum ConnectivityStatus {
  unknown,
  disconnected,
  connectedWifi,
  connectedMobile,
  connectedEthernet,
  connectedBluetooth,
  connectedVpn,
  connectedOther,
}

/// Enum for connection types
enum ConnectionType {
  none,
  wifi,
  mobile,
  ethernet,
  bluetooth,
  vpn,
  other,
}

/// Extension methods for connectivity status
extension ConnectivityStatusExtension on ConnectivityStatus {
  bool get isConnected {
    switch (this) {
      case ConnectivityStatus.connectedWifi:
      case ConnectivityStatus.connectedMobile:
      case ConnectivityStatus.connectedEthernet:
      case ConnectivityStatus.connectedBluetooth:
      case ConnectivityStatus.connectedVpn:
      case ConnectivityStatus.connectedOther:
        return true;
      case ConnectivityStatus.unknown:
      case ConnectivityStatus.disconnected:
        return false;
    }
  }

  bool get isWifi => this == ConnectivityStatus.connectedWifi;
  bool get isMobile => this == ConnectivityStatus.connectedMobile;

  String get displayName {
    switch (this) {
      case ConnectivityStatus.unknown:
        return 'Unknown';
      case ConnectivityStatus.disconnected:
        return 'Disconnected';
      case ConnectivityStatus.connectedWifi:
        return 'WiFi';
      case ConnectivityStatus.connectedMobile:
        return 'Mobile Data';
      case ConnectivityStatus.connectedEthernet:
        return 'Ethernet';
      case ConnectivityStatus.connectedBluetooth:
        return 'Bluetooth';
      case ConnectivityStatus.connectedVpn:
        return 'VPN';
      case ConnectivityStatus.connectedOther:
        return 'Other';
    }
  }
}
