import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../database/database_helper.dart';
import '../services/connectivity_service.dart';
import '../utils/firebase_utils.dart';
import '../../shared/models/cash_box.dart';

/// Repository for managing cash boxes and their transactions
class CashBoxRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final ConnectivityService _connectivityService = ConnectivityService();

  /// Get Firestore instance (lazy initialization to avoid Firebase errors)
  FirebaseFirestore? get _firestore {
    return FirebaseUtils.safeFirebaseOperationSync<FirebaseFirestore>(
      () => FirebaseFirestore.instance,
      operationName: 'Get Firestore instance',
      fallbackValue: null,
    );
  }

  /// Get all cash boxes
  Future<List<CashBox>> getAllCashBoxes() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'cash_boxes',
        orderBy: 'created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return CashBox.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting cash boxes: $e');
      }
      rethrow;
    }
  }

  /// Get active cash boxes only
  Future<List<CashBox>> getActiveCashBoxes() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'cash_boxes',
        where: 'is_active = ?',
        whereArgs: [1],
        orderBy: 'created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return CashBox.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting active cash boxes: $e');
      }
      rethrow;
    }
  }

  /// Get cash box by ID
  Future<CashBox?> getCashBoxById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'cash_boxes',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return CashBox.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting cash box by ID: $e');
      }
      rethrow;
    }
  }

  /// Insert new cash box
  Future<int> insertCashBox(CashBox cashBox) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert('cash_boxes', cashBox.toMap());
      
      if (kDebugMode) {
        print('✅ Cash box inserted with ID: $id');
      }
      
      return id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error inserting cash box: $e');
      }
      rethrow;
    }
  }

  /// Update cash box
  Future<int> updateCashBox(CashBox cashBox) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'cash_boxes',
        cashBox.toMap(),
        where: 'id = ?',
        whereArgs: [cashBox.id],
      );
      
      if (kDebugMode) {
        print('✅ Cash box updated: ${cashBox.name}');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating cash box: $e');
      }
      rethrow;
    }
  }

  /// Delete cash box
  Future<int> deleteCashBox(int id) async {
    try {
      final db = await _dbHelper.database;
      
      // Check if cash box has transactions
      final transactionCount = await getCashBoxTransactionCount(id);
      if (transactionCount > 0) {
        throw Exception('Cannot delete cash box with existing transactions');
      }
      
      final result = await db.delete(
        'cash_boxes',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (kDebugMode) {
        print('✅ Cash box deleted with ID: $id');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting cash box: $e');
      }
      rethrow;
    }
  }

  /// Add transaction to cash box
  Future<int> addTransaction(CashBoxTransaction transaction) async {
    try {
      final db = await _dbHelper.database;
      
      // Start transaction
      return await db.transaction((txn) async {
        // Insert transaction
        final transactionId = await txn.insert('cash_box_transactions', transaction.toMap());
        
        // Update cash box balance
        final cashBox = await getCashBoxById(transaction.cashBoxId);
        if (cashBox != null) {
          double newBalance = cashBox.currentBalance;
          
          if (transaction.type == CashBoxTransactionType.income) {
            newBalance += transaction.amount;
          } else if (transaction.type == CashBoxTransactionType.expense) {
            newBalance -= transaction.amount;
          }
          
          final updatedCashBox = cashBox.copyWithBalance(newBalance);
          await txn.update(
            'cash_boxes',
            updatedCashBox.toMap(),
            where: 'id = ?',
            whereArgs: [cashBox.id],
          );
        }
        
        return transactionId;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding transaction: $e');
      }
      rethrow;
    }
  }

  /// Get cash box transactions
  Future<List<CashBoxTransaction>> getCashBoxTransactions(
    int cashBoxId, {
    DateTime? startDate,
    DateTime? endDate,
    String? type,
    int? limit,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      String whereClause = 'cash_box_id = ?';
      List<dynamic> whereArgs = [cashBoxId];
      
      if (startDate != null) {
        whereClause += ' AND transaction_date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }
      
      if (endDate != null) {
        whereClause += ' AND transaction_date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }
      
      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }
      
      final List<Map<String, dynamic>> maps = await db.query(
        'cash_box_transactions',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transaction_date DESC, created_at DESC',
        limit: limit,
      );

      return List.generate(maps.length, (i) {
        return CashBoxTransaction.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting cash box transactions: $e');
      }
      rethrow;
    }
  }

  /// Get cash box transaction count
  Future<int> getCashBoxTransactionCount(int cashBoxId) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM cash_box_transactions WHERE cash_box_id = ?',
        [cashBoxId],
      );
      
      return result.first['count'] as int;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting transaction count: $e');
      }
      rethrow;
    }
  }

  /// Get cash box balance summary
  Future<Map<String, double>> getCashBoxSummary(int cashBoxId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.rawQuery('''
        SELECT 
          SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
          SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
          COUNT(*) as transaction_count
        FROM cash_box_transactions 
        WHERE cash_box_id = ?
      ''', [cashBoxId]);
      
      final data = result.first;
      return {
        'total_income': (data['total_income'] as num?)?.toDouble() ?? 0.0,
        'total_expense': (data['total_expense'] as num?)?.toDouble() ?? 0.0,
        'transaction_count': (data['transaction_count'] as num?)?.toDouble() ?? 0.0,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting cash box summary: $e');
      }
      rethrow;
    }
  }

  /// Transfer money between cash boxes
  Future<bool> transferBetweenCashBoxes({
    required int fromCashBoxId,
    required int toCashBoxId,
    required double amount,
    required String description,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.transaction((txn) async {
        // Get both cash boxes
        final fromCashBox = await getCashBoxById(fromCashBoxId);
        final toCashBox = await getCashBoxById(toCashBoxId);
        
        if (fromCashBox == null || toCashBox == null) {
          throw Exception('Cash box not found');
        }
        
        if (!fromCashBox.canWithdraw(amount)) {
          throw Exception('Insufficient balance in source cash box');
        }
        
        final now = DateTime.now();
        
        // Create withdrawal transaction
        final withdrawalTransaction = CashBoxTransaction(
          cashBoxId: fromCashBoxId,
          type: CashBoxTransactionType.transfer,
          amount: amount,
          description: 'Transfer to ${toCashBox.name}: $description',
          referenceType: CashBoxReferenceType.transfer,
          reference: toCashBoxId.toString(),
          transactionDate: now,
          createdAt: now,
        );
        
        // Create deposit transaction
        final depositTransaction = CashBoxTransaction(
          cashBoxId: toCashBoxId,
          type: CashBoxTransactionType.income,
          amount: amount,
          description: 'Transfer from ${fromCashBox.name}: $description',
          referenceType: CashBoxReferenceType.transfer,
          reference: fromCashBoxId.toString(),
          transactionDate: now,
          createdAt: now,
        );
        
        // Insert transactions
        await txn.insert('cash_box_transactions', withdrawalTransaction.toMap());
        await txn.insert('cash_box_transactions', depositTransaction.toMap());
        
        // Update balances
        final updatedFromCashBox = fromCashBox.copyWithBalance(fromCashBox.currentBalance - amount);
        final updatedToCashBox = toCashBox.copyWithBalance(toCashBox.currentBalance + amount);
        
        await txn.update(
          'cash_boxes',
          updatedFromCashBox.toMap(),
          where: 'id = ?',
          whereArgs: [fromCashBoxId],
        );
        
        await txn.update(
          'cash_boxes',
          updatedToCashBox.toMap(),
          where: 'id = ?',
          whereArgs: [toCashBoxId],
        );
        
        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error transferring between cash boxes: $e');
      }
      rethrow;
    }
  }

  /// Sync cash boxes with Firebase
  Future<void> sync() async {
    try {
      if (!await _connectivityService.shouldSync()) {
        if (kDebugMode) {
          print('⚠️ Skipping cash box sync - no connectivity');
        }
        return;
      }

      if (kDebugMode) {
        print('🔄 Starting cash box sync...');
      }

      // Sync local changes to Firebase
      await _syncLocalToFirebase();
      
      // Sync Firebase changes to local
      await _syncFirebaseToLocal();

      if (kDebugMode) {
        print('✅ Cash box sync completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing cash boxes: $e');
      }
      rethrow;
    }
  }

  /// Sync local changes to Firebase
  Future<void> _syncLocalToFirebase() async {
    try {
      final localCashBoxes = await getAllCashBoxes();
      
      for (final cashBox in localCashBoxes) {
        if (cashBox.firestoreId == null) {
          // Create new cash box in Firebase
          await _createCashBoxInFirebase(cashBox);
        } else {
          // Update existing cash box in Firebase
          await _updateCashBoxInFirebase(cashBox);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing local to Firebase: $e');
      }
      rethrow;
    }
  }

  /// Sync Firebase changes to local
  Future<void> _syncFirebaseToLocal() async {
    return await FirebaseUtils.safeFirebaseOperation<void>(
      () async {
        final firestore = _firestore;
        if (firestore == null) {
          throw Exception('Firestore not available');
        }

        final snapshot = await firestore.collection('cash_boxes').get();

        for (final doc in snapshot.docs) {
          final data = doc.data();
          data['id'] = doc.id;

          final cashBox = CashBox.fromFirestore(data, doc.id);
          await _upsertLocalCashBox(cashBox);
        }
      },
      operationName: 'Sync Firebase to local',
    );
  }

  /// Create cash box in Firebase
  Future<void> _createCashBoxInFirebase(CashBox cashBox) async {
    return await FirebaseUtils.safeFirebaseOperation<void>(
      () async {
        final firestore = _firestore;
        if (firestore == null) {
          throw Exception('Firestore not available');
        }

        final data = cashBox.toFirestoreMap();
        final docRef = await firestore.collection('cash_boxes').add(data);

        // Update local cash box with Firebase ID
        await _updateCashBoxFirestoreId(cashBox.localId!, docRef.id);

        if (kDebugMode) {
          print('✅ Created cash box in Firebase: ${docRef.id}');
        }
      },
      operationName: 'Create cash box in Firebase',
    );
  }

  /// Update cash box in Firebase
  Future<void> _updateCashBoxInFirebase(CashBox cashBox) async {
    return await FirebaseUtils.safeFirebaseOperation<void>(
      () async {
        if (cashBox.firestoreId == null) return;

        final firestore = _firestore;
        if (firestore == null) {
          throw Exception('Firestore not available');
        }

        final data = cashBox.toFirestoreMap();
        data['updated_at'] = FieldValue.serverTimestamp();

        await firestore
            .collection('cash_boxes')
            .doc(cashBox.firestoreId)
            .update(data);

        if (kDebugMode) {
          print('✅ Updated cash box in Firebase: ${cashBox.firestoreId}');
        }
      },
      operationName: 'Update cash box in Firebase',
    );
  }

  /// Upsert cash box in local database
  Future<void> _upsertLocalCashBox(CashBox cashBox) async {
    try {
      final db = await _dbHelper.database;
      
      // Check if cash box exists by Firebase ID
      final existing = await db.query(
        'cash_boxes',
        where: 'firestore_id = ?',
        whereArgs: [cashBox.firestoreId],
      );
      
      final data = cashBox.toMap();
      
      if (existing.isNotEmpty) {
        // Update existing
        await db.update(
          'cash_boxes',
          data,
          where: 'firestore_id = ?',
          whereArgs: [cashBox.firestoreId],
        );
      } else {
        // Insert new
        await db.insert('cash_boxes', data);
      }
      
      if (kDebugMode) {
        print('✅ Upserted cash box locally: ${cashBox.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error upserting cash box locally: $e');
      }
      rethrow;
    }
  }

  /// Update cash box's Firebase ID in local database
  Future<void> _updateCashBoxFirestoreId(int localId, String firestoreId) async {
    try {
      final db = await _dbHelper.database;
      await db.update(
        'cash_boxes',
        {'firestore_id': firestoreId},
        where: 'id = ?',
        whereArgs: [localId],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating cash box Firebase ID: $e');
      }
      rethrow;
    }
  }

  /// Deduct technician daily wage from cash box
  Future<bool> deductTechnicianDailyWage({
    required int cashBoxId,
    required double amount,
    required String technicianName,
    required String serviceRequestNumber,
    String? notes,
  }) async {
    try {
      final db = await _dbHelper.database;

      return await db.transaction((txn) async {
        // Check cash box exists and is active
        final cashBoxMaps = await txn.query(
          'cash_boxes',
          where: 'id = ? AND is_active = 1',
          whereArgs: [cashBoxId],
        );

        if (cashBoxMaps.isEmpty) {
          throw Exception('الصندوق المحدد غير موجود أو غير نشط');
        }

        final cashBox = CashBox.fromMap(cashBoxMaps.first);

        // Check sufficient balance
        if (cashBox.currentBalance < amount) {
          throw Exception(
            'رصيد الصندوق غير كافي.\n'
            'الرصيد الحالي: ${cashBox.currentBalance.toStringAsFixed(2)} ر.س\n'
            'المبلغ المطلوب: ${amount.toStringAsFixed(2)} ر.س'
          );
        }

        // Update cash box balance
        final newBalance = cashBox.currentBalance - amount;
        await txn.update(
          'cash_boxes',
          {
            'current_balance': newBalance,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [cashBoxId],
        );

        // Create transaction record
        final transactionId = await txn.insert(
          'cash_box_transactions',
          {
            'cash_box_id': cashBoxId,
            'type': 'expense',
            'amount': amount,
            'description': 'أجر فني يومي - $technicianName',
            'reference': serviceRequestNumber,
            'reference_type': 'service_request',
            'transaction_date': DateTime.now().toIso8601String(),
            'notes': notes,
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        if (kDebugMode) {
          print('✅ تم خصم أجر الفني بنجاح:');
          print('   - الفني: $technicianName');
          print('   - المبلغ: ${amount.toStringAsFixed(2)} ر.س');
          print('   - طلب الخدمة: $serviceRequestNumber');
          print('   - الرصيد الجديد: ${newBalance.toStringAsFixed(2)} ر.س');
          print('   - معرف المعاملة: $transactionId');
        }

        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في خصم أجر الفني: $e');
      }
      rethrow;
    }
  }

  /// Get technician expenses summary
  Future<Map<String, dynamic>> getTechnicianExpensesSummary({
    required DateTime startDate,
    required DateTime endDate,
    int? cashBoxId,
  }) async {
    try {
      final db = await _dbHelper.database;

      String whereClause = '''
        type = 'expense'
        AND reference_type = 'service_request'
        AND DATE(transaction_date) BETWEEN DATE(?) AND DATE(?)
      ''';

      List<dynamic> whereArgs = [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ];

      if (cashBoxId != null) {
        whereClause += ' AND cash_box_id = ?';
        whereArgs.add(cashBoxId);
      }

      final results = await db.query(
        'cash_box_transactions',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transaction_date DESC',
      );

      double totalAmount = 0;
      int totalTransactions = results.length;
      Map<String, double> expensesByCashBox = {};

      for (final row in results) {
        final amount = row['amount'] as double;
        final cashBoxId = row['cash_box_id'] as int;

        totalAmount += amount;

        // Get cash box name
        final cashBoxMaps = await db.query(
          'cash_boxes',
          columns: ['name'],
          where: 'id = ?',
          whereArgs: [cashBoxId],
        );

        String cashBoxName = 'صندوق غير محدد';
        if (cashBoxMaps.isNotEmpty) {
          cashBoxName = cashBoxMaps.first['name'] as String;
        }

        expensesByCashBox[cashBoxName] = (expensesByCashBox[cashBoxName] ?? 0) + amount;
      }

      return {
        'total_amount': totalAmount,
        'total_transactions': totalTransactions,
        'expenses_by_cash_box': expensesByCashBox,
        'transactions': results,
        'period': {
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب ملخص مصروفات الفنيين: $e');
      }
      rethrow;
    }
  }
}
