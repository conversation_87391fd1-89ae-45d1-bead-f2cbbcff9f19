import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:intl/intl.dart';
import 'package:icecorner/shared/utils/app_colors.dart';
import 'package:icecorner/shared/utils/app_text_styles.dart';
import 'package:icecorner/shared/models/customer.dart';
import 'package:icecorner/shared/widgets/custom_app_bar.dart';

import 'package:icecorner/features/reports/utils/paginated_pdf_export.dart';
import 'package:icecorner/features/reports/screens/pdf_preview_screen.dart';

/// شاشة معاينة بيانات العميل
class CustomerDataPreviewScreen extends StatefulWidget {
  final Customer? customer;
  final List<Map<String, dynamic>> allData;
  final String title;

  const CustomerDataPreviewScreen({
    Key? key,
    required this.allData,
    required this.title,
    this.customer,
  }) : super(key: key);

  @override
  State<CustomerDataPreviewScreen> createState() => _CustomerDataPreviewScreenState();
}

class _CustomerDataPreviewScreenState extends State<CustomerDataPreviewScreen> {
  Customer? get customer => widget.customer;
  List<Map<String, dynamic>> get allData => widget.allData;
  String get title => widget.title;

  /// تصدير البيانات إلى ملف PDF
  Future<void> _exportToPdf(BuildContext context) async {
    // حفظ مرجع للسياق للتحقق مما إذا كان لا يزال مثبتًا
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري إنشاء التقرير...'),
              ],
            ),
          );
        },
      );

      // حساب الإجماليات بنفس منطق المعاينة
      double totalInvoices = allData.where((item) => item['type'] == 'invoice').fold(0.0, (sum, item) => sum + (item['amount'] as double));
      double totalTransactions = allData.where((item) => item['type'] == 'transaction').fold(0.0, (sum, item) => sum + (item['amount'] as double));

      // حساب إجمالي مبلغ طلبات الخدمات بطريقة صحيحة
      double totalServices = 0.0;

      // إذا كان العميل لديه اشتراك شهري، نستخدم مبلغ الاشتراك الشهري
      if (customer?.paymentMethod == CustomerPaymentMethod.monthlySubscription) {
        // استخدام مبلغ الاشتراك الشهري إذا كان متاحًا
        totalServices = customer?.monthlySubscriptionAmount ?? 0.0;
      } else {
        // إذا كان العميل يدفع لكل خدمة، نجمع مبالغ الخدمات من طلبات الخدمة
        totalServices = allData
            .where((item) => item['type'] == 'service')
            .fold(0.0, (sum, item) => sum + (item['amount'] as double));
      }

      double remainingBalance = totalTransactions - (totalInvoices + totalServices);

      // إضافة معلومات الاشتراك إلى البيانات
      final List<Map<String, dynamic>> processedData = allData.map((item) {
        final newItem = Map<String, dynamic>.from(item);
        if (item['type'] == 'service') {
          newItem['customerPaymentMethod'] = customer?.paymentMethod;
        }
        return newItem;
      }).toList();

      // تمرير الإجماليات الصحيحة إلى PDF
      final summaryData = {
        'totalAmount': totalInvoices,
        'totalTransactions': totalTransactions,
        'totalServices': totalServices,
        'remainingBalance': remainingBalance,
        // إضافة مبلغ الاشتراك الشهري إذا كان العميل لديه اشتراك شهري
        'monthlySubscriptionAmount': customer?.paymentMethod == CustomerPaymentMethod.monthlySubscription
            ? (customer?.monthlySubscriptionAmount ?? 0.0)
            : 0.0,
      };

      // استخدام مولد PDF المحسن مع دعم الصفحات المتعددة وتعطيل المشاركة التلقائية
      final filePath = await PaginatedPdfExport.exportCustomerTableToPdf(
        context: context,
        title: 'تقرير البيانات',
        customer: customer,
        tableData: processedData,
        summaryData: summaryData,
        shareFile: false, // تعطيل المشاركة التلقائية
      );

      // التحقق مما إذا كان السياق لا يزال مثبتًا
      if (!mounted) return;

      // إغلاق مؤشر التحميل
      navigator.pop();

      if (filePath == null) {
        throw Exception('فشل في إنشاء ملف PDF');
      }

      // التحقق مما إذا كان السياق لا يزال مثبتًا
      if (!mounted) return;

      // عرض معاينة PDF
      navigator.push(
        MaterialPageRoute(
          builder: (context) => PdfPreviewScreen(
            filePath: filePath,
            title: 'تقرير البيانات',
          ),
        ),
      );
    } catch (e) {
      // التحقق مما إذا كان السياق لا يزال مثبتًا
      if (!mounted) return;

      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (navigator.canPop()) {
        navigator.pop();
      }

      // عرض رسالة الخطأ
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: ui.TextDirection.rtl,
      child: Scaffold(
        appBar: CustomAppBar(
          title: title,
          actions: [
            // زر الطباعة
            IconButton(
              icon: const Icon(Icons.print),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة الطباعة غير متوفرة حالياً'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              tooltip: 'طباعة',
            ),
            // زر تصدير PDF (الخيار الأول للمستخدم)
            IconButton(
              icon: const Icon(Icons.picture_as_pdf),
              onPressed: () => _exportToPdf(context),
              tooltip: 'تصدير PDF',
            ),
          ],
        ),
        body: Column(
          children: [
            // عنوان الجدول
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: const Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      'النوع',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'الرقم المرجعي',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'التاريخ',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'الوصف',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'المبلغ',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      'العملة',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'الحالة',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),

            // محتوى الجدول
            Expanded(
              child: _buildDataTable(),
            ),

            // ملخص الإحصائيات في نهاية الشاشة
            _buildStatisticsSummary(),
          ],
        ),
      ),
    );
  }

  /// بناء جدول البيانات
  Widget _buildDataTable() {
    if (allData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات لهذا العميل',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return ListView.builder(
      itemCount: allData.length,
      itemBuilder: (context, index) {
        final item = allData[index];
        final bool isEvenRow = index % 2 == 0;

        // منطق العملة والمبلغ
        String amountText = _getAmountText(item);
        String currencyText = '';
        if (item['type'] == 'service' && customer?.paymentMethod == CustomerPaymentMethod.monthlySubscription) {
          amountText = 'ضمن الاشتراك';
          currencyText = '';
        } else if (item['type'] == 'service') {
          amountText = item['amount'].toStringAsFixed(2);
          currencyText = 'ر.س';
        } else {
          amountText = item['amount'].toStringAsFixed(2);
          currencyText = 'ر.س';
        }

        return Container(
          color: isEvenRow ? Colors.grey.shade50 : Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Row(
              children: [
                // نوع العنصر
                Expanded(
                  flex: 1,
                  child: CircleAvatar(
                    backgroundColor: item['iconColor'].withAlpha(50),
                    child: Icon(
                      item['icon'],
                      color: item['iconColor'],
                      size: 20,
                    ),
                  ),
                ),

                // الرقم المرجعي
                Expanded(
                  flex: 2,
                  child: Text(
                    item['reference'],
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),

                // التاريخ
                Expanded(
                  flex: 2,
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(item['date']),
                    textAlign: TextAlign.center,
                  ),
                ),

                // الوصف
                Expanded(
                  flex: 2,
                  child: Text(
                    item['description'],
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),

                // المبلغ
                Expanded(
                  flex: 2,
                  child: Text(
                    amountText,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: item['type'] == 'transaction' ? item['iconColor'] : Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // العملة
                Expanded(
                  flex: 1,
                  child: Text(
                    currencyText,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),

                // الحالة
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: item['statusColor'].withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item['status'],
                      style: TextStyle(
                        color: item['statusColor'],
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء ملخص الإحصائيات في نهاية الشاشة
  Widget _buildStatisticsSummary() {
    // حساب الإجماليات حسب النوع
    double totalInvoices = allData.where((item) => item['type'] == 'invoice').fold(0.0, (sum, item) => sum + (item['amount'] as double));
    double totalTransactions = allData.where((item) => item['type'] == 'transaction').fold(0.0, (sum, item) => sum + (item['amount'] as double));

    // حساب إجمالي مبلغ طلبات الخدمات بطريقة صحيحة
    double totalServices = 0.0;

    // إذا كان العميل لديه اشتراك شهري، نستخدم مبلغ الاشتراك الشهري
    if (customer?.paymentMethod == CustomerPaymentMethod.monthlySubscription) {
      // استخدام مبلغ الاشتراك الشهري إذا كان متاحًا
      totalServices = customer?.monthlySubscriptionAmount ?? 0.0;
    } else {
      // إذا كان العميل يدفع لكل خدمة، نجمع مبالغ الخدمات من طلبات الخدمة
      totalServices = allData
          .where((item) => item['type'] == 'service')
          .fold(0.0, (sum, item) => sum + (item['amount'] as double));
    }

    // حساب الرصيد المتبقي: إجمالي الإيرادات - (إجمالي الفواتير + إجمالي مبلغ طلبات الخدمات)
    double remainingBalance = totalTransactions - (totalInvoices + totalServices);

    TextStyle valueStyleBlue = const TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold, fontSize: 13, color: Color(0xFF1565C0));
    TextStyle valueStyleGreen = const TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold, fontSize: 13, color: Color(0xFF2E7D32));
    TextStyle valueStyleRed = const TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold, fontSize: 13, color: Color(0xFFC62828));
    TextStyle valueStyleOrange = const TextStyle(fontFamily: 'Cairo', fontWeight: FontWeight.bold, fontSize: 13, color: Color(0xFFF9A825));

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF90CAF9)),
      ),
      child: Wrap(
        alignment: WrapAlignment.spaceEvenly,
        spacing: 8, // المسافة الأفقية بين العناصر
        runSpacing: 8, // المسافة الرأسية بين الصفوف
        children: [
          // ترتيب العناصر حسب المعادلة: الرصيد المتبقي = إجمالي الإيرادات - (إجمالي الفواتير + إجمالي مبلغ طلبات الخدمات)
          // إظهار الإيرادات كقيمة موجبة
          _buildSummaryItem('إجمالي الإيرادات', '+${totalTransactions.toStringAsFixed(2)} ر.س', valueStyleGreen),
          // إظهار الفواتير كقيمة سالبة
          _buildSummaryItem('إجمالي الفواتير', '-${totalInvoices.toStringAsFixed(2)} ر.س', valueStyleBlue),
          // إظهار مبلغ طلبات الخدمات كقيمة سالبة
          _buildSummaryItem('إجمالي مبلغ طلبات الخدمات', '-${totalServices.toStringAsFixed(2)} ر.س', valueStyleOrange),
          // إظهار الرصيد المتبقي كناتج المعادلة مع إضافة علامة + أو - حسب القيمة
          _buildSummaryItem(
            'الرصيد المتبقي',
            '${remainingBalance >= 0 ? '+' : ''}${remainingBalance.toStringAsFixed(2)} ر.س',
            remainingBalance >= 0 ? valueStyleGreen : valueStyleRed
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, TextStyle valueStyle) {
    // حساب العرض المناسب للعنصر (تقريباً ربع عرض الشاشة مع هوامش)
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = (screenWidth - 48) / 2.2; // تقسيم العرض على 2.2 لضمان ملاءمة عنصرين في كل صف

    return Container(
      width: itemWidth,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: const Color(0xFFBDBDBD)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.bold,
              fontSize: 11,
              color: Color(0xFF424242)
            ),
            textDirection: ui.TextDirection.rtl,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: valueStyle.copyWith(fontSize: 13),
            textDirection: ui.TextDirection.rtl,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// الحصول على نص المبلغ
  String _getAmountText(Map<String, dynamic> item) {
    // 1. إذا كان نوع العنصر هو "service" (خدمة) والعميل لديه اشتراك شهري
    if (item['type'] == 'service' && customer?.paymentMethod == CustomerPaymentMethod.monthlySubscription) {
      return 'ضمن الاشتراك';
    }

    // 2. إذا كان نوع العنصر هو "service" (خدمة) والعميل يدفع لكل خدمة
    if (item['type'] == 'service' && customer?.paymentMethod == CustomerPaymentMethod.perService) {
      return '${item['amount'].toStringAsFixed(2)} ر.س';
    }

    // 3. لجميع أنواع العناصر الأخرى (مثل الفواتير والمعاملات)
    return '${item['amount'].toStringAsFixed(2)} ر.س';
  }
}
