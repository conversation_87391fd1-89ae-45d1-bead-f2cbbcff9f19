import 'package:flutter/material.dart';
import '../../config/constants.dart';

class IconPickerDialog extends StatefulWidget {
  final IconData selectedIcon;
  final Function(IconData) onIconSelected;

  const IconPickerDialog({
    super.key,
    required this.selectedIcon,
    required this.onIconSelected,
  });

  @override
  State<IconPickerDialog> createState() => _IconPickerDialogState();
}

class _IconPickerDialogState extends State<IconPickerDialog> {
  late IconData _currentIcon;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _currentIcon = widget.selectedIcon;
  }

  List<IconData> _getFilteredIcons() {
    if (_searchQuery.isEmpty) {
      return _commonIcons;
    }

    return _commonIcons.where((icon) {
      final iconName = _getIconName(icon).toLowerCase();
      return iconName.contains(_searchQuery.toLowerCase());
    }).toList();
  }

  String _getIconName(IconData icon) {
    // استخراج اسم الأيقونة من كائن IconData
    final String name = icon.toString();
    final RegExp regExp = RegExp(r'IconData\(U\+[0-9A-F]+\)');
    if (regExp.hasMatch(name)) {
      return name.replaceAll(regExp, '').trim();
    }
    return name;
  }

  @override
  Widget build(BuildContext context) {
    final filteredIcons = _getFilteredIcons();

    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'اختر أيقونة',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextField(
              decoration: const InputDecoration(
                hintText: 'بحث...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5,
              ),
              child: GridView.builder(
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  childAspectRatio: 1,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemCount: filteredIcons.length,
                itemBuilder: (context, index) {
                  final icon = filteredIcons[index];
                  final isSelected = _currentIcon.codePoint == icon.codePoint;

                  return InkWell(
                    onTap: () {
                      setState(() {
                        _currentIcon = icon;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary.withAlpha(51) : Colors.transparent, // 0.2 opacity (51/255)
                        border: Border.all(
                          color: isSelected ? AppColors.primary : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        icon,
                        color: isSelected ? AppColors.primary : Colors.grey.shade700,
                        size: 24,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                ElevatedButton(
                  onPressed: () {
                    widget.onIconSelected(_currentIcon);
                    Navigator.pop(context);
                  },
                  child: const Text('اختيار'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // قائمة بالأيقونات الشائعة
  final List<IconData> _commonIcons = [
    Icons.ac_unit,
    Icons.access_time,
    Icons.account_balance,
    Icons.account_circle,
    Icons.add_shopping_cart,
    Icons.alarm,
    Icons.all_inclusive,
    Icons.android,
    Icons.announcement,
    Icons.apartment,
    Icons.api,
    Icons.archive,
    Icons.arrow_back,
    Icons.arrow_forward,
    Icons.assessment,
    Icons.assignment,
    Icons.attach_money,
    Icons.audiotrack,
    Icons.auto_awesome,
    Icons.auto_fix_high,
    Icons.auto_stories,
    Icons.backpack,
    Icons.backup,
    Icons.badge,
    Icons.bakery_dining,
    Icons.batch_prediction,
    Icons.battery_full,
    Icons.beach_access,
    Icons.bedtime,
    Icons.bike_scooter,
    Icons.biotech,
    Icons.block,
    Icons.bluetooth,
    Icons.book,
    Icons.bookmark,
    Icons.brightness_7,
    Icons.build,
    Icons.business,
    Icons.cake,
    Icons.calculate,
    Icons.calendar_today,
    Icons.call,
    Icons.camera_alt,
    Icons.campaign,
    Icons.card_giftcard,
    Icons.category,
    Icons.celebration,
    Icons.chat,
    Icons.check_circle,
    Icons.child_care,
    Icons.clean_hands,
    Icons.cleaning_services,
    Icons.close,
    Icons.cloud,
    Icons.code,
    Icons.coffee,
    Icons.computer,
    Icons.contact_mail,
    Icons.contact_phone,
    Icons.contacts,
    Icons.content_cut,
    Icons.coronavirus,
    Icons.credit_card,
    Icons.dashboard,
    Icons.data_usage,
    Icons.date_range,
    Icons.delete,
    Icons.description,
    Icons.devices,
    Icons.directions_bike,
    Icons.directions_car,
    Icons.directions_run,
    Icons.document_scanner,
    Icons.domain,
    Icons.done,
    Icons.download,
    Icons.drafts,
    Icons.eco,
    Icons.edit,
    Icons.email,
    Icons.emoji_events,
    Icons.error,
    Icons.euro,
    Icons.event,
    Icons.explore,
    Icons.extension,
    Icons.face,
    Icons.favorite,
    Icons.feedback,
    Icons.file_copy,
    Icons.filter_list,
    Icons.fingerprint,
    Icons.fireplace,
    Icons.fitness_center,
    Icons.flag,
    Icons.flash_on,
    Icons.flight,
    Icons.folder,
    Icons.format_paint,
    Icons.format_quote,
    Icons.forum,
    Icons.free_breakfast,
    Icons.fullscreen,
    Icons.games,
    Icons.gesture,
    Icons.gif,
    Icons.grade,
    Icons.group,
    Icons.headset,
    Icons.healing,
    Icons.help,
    Icons.highlight,
    Icons.history,
    Icons.home,
    Icons.hotel,
    Icons.hourglass_empty,
    Icons.http,
    Icons.https,
    Icons.image,
    Icons.info,
    Icons.insert_chart,
    Icons.insert_drive_file,
    Icons.insert_emoticon,
    Icons.insert_invitation,
    Icons.insert_link,
    Icons.insert_photo,
    Icons.inventory,
    Icons.language,
    Icons.laptop,
    Icons.layers,
    Icons.leaderboard,
    Icons.library_books,
    Icons.light,
    Icons.lightbulb,
    Icons.line_style,
    Icons.link,
    Icons.list,
    Icons.local_activity,
    Icons.local_airport,
    Icons.local_atm,
    Icons.local_bar,
    Icons.local_cafe,
    Icons.local_car_wash,
    Icons.local_dining,
    Icons.local_drink,
    Icons.local_fire_department,
    Icons.local_florist,
    Icons.local_gas_station,
    Icons.local_grocery_store,
    Icons.local_hospital,
    Icons.local_hotel,
    Icons.local_laundry_service,
    Icons.local_library,
    Icons.local_mall,
    Icons.local_movies,
    Icons.local_offer,
    Icons.local_parking,
    Icons.local_pharmacy,
    Icons.local_phone,
    Icons.local_pizza,
    Icons.local_play,
    Icons.local_police,
    Icons.local_post_office,
    Icons.local_printshop,
    Icons.local_see,
    Icons.local_shipping,
    Icons.local_taxi,
    Icons.location_city,
    Icons.location_on,
    Icons.lock,
    Icons.looks,
    Icons.loop,
    Icons.loyalty,
    Icons.mail,
    Icons.map,
    Icons.margin,
    Icons.mark_email_read,
    Icons.memory,
    Icons.menu,
    Icons.merge_type,
    Icons.message,
    Icons.mic,
    Icons.microwave,
    Icons.military_tech,
    Icons.mobile_friendly,
    Icons.money,
    Icons.monitor,
    Icons.mood,
    Icons.more,
    Icons.mouse,
    Icons.movie,
    Icons.multiline_chart,
    Icons.music_note,
    Icons.nature,
    Icons.navigation,
    Icons.near_me,
    Icons.network_cell,
    Icons.new_releases,
    Icons.next_plan,
    Icons.nightlight_round,
    Icons.note,
    Icons.note_add,
    Icons.notification_important,
    Icons.notifications,
    Icons.offline_bolt,
    Icons.offline_pin,
    Icons.ondemand_video,
    Icons.opacity,
    Icons.open_in_browser,
    Icons.open_in_new,
    Icons.open_with,
    Icons.outlet,
    Icons.pages,
    Icons.pageview,
    Icons.palette,
    Icons.panorama,
    Icons.park,
    Icons.party_mode,
    Icons.password,
    Icons.payment,
    Icons.people,
    Icons.perm_identity,
    Icons.person,
    Icons.person_add,
    Icons.pets,
    Icons.phone,
    Icons.photo,
    Icons.photo_album,
    Icons.photo_camera,
    Icons.photo_filter,
    Icons.piano,
    Icons.picture_as_pdf,
    Icons.pie_chart,
    Icons.pin_drop,
    Icons.place,
    Icons.play_arrow,
    Icons.playlist_add,
    Icons.playlist_add_check,
    Icons.playlist_play,
    Icons.plus_one,
    Icons.poll,
    Icons.polymer,
    Icons.pool,
    Icons.portable_wifi_off,
    Icons.portrait,
    Icons.power,
    Icons.power_settings_new,
    Icons.pregnant_woman,
    Icons.present_to_all,
    Icons.print,
    Icons.priority_high,
    Icons.public,
    Icons.publish,
    Icons.query_builder,
    Icons.question_answer,
    Icons.queue,
    Icons.queue_music,
    Icons.radio,
    Icons.receipt,
    Icons.recent_actors,
    Icons.record_voice_over,
    Icons.redeem,
    Icons.redo,
    Icons.refresh,
    Icons.remove,
    Icons.remove_from_queue,
    Icons.remove_red_eye,
    Icons.repeat,
    Icons.restaurant,
    Icons.restaurant_menu,
    Icons.restore,
    Icons.restore_from_trash,
    Icons.ring_volume,
    Icons.room,
    Icons.room_service,
    Icons.rotate_90_degrees_ccw,
    Icons.rotate_left,
    Icons.rotate_right,
    Icons.rounded_corner,
    Icons.router,
    Icons.rowing,
    Icons.rss_feed,
    Icons.rv_hookup,
    Icons.sanitizer,
    Icons.save,
    Icons.scanner,
    Icons.schedule,
    Icons.school,
    Icons.science,
    Icons.score,
    Icons.screen_lock_landscape,
    Icons.screen_lock_portrait,
    Icons.screen_lock_rotation,
    Icons.screen_rotation,
    Icons.screenshot,
    Icons.sd_card,
    Icons.sd_storage,
    Icons.search,
    Icons.security,
    Icons.select_all,
    Icons.send,
    Icons.sentiment_dissatisfied,
    Icons.sentiment_satisfied,
    Icons.sentiment_very_dissatisfied,
    Icons.sentiment_very_satisfied,
    Icons.settings,
    Icons.settings_applications,
    Icons.settings_backup_restore,
    Icons.settings_bluetooth,
    Icons.settings_brightness,
    Icons.settings_cell,
    Icons.settings_ethernet,
    Icons.settings_input_antenna,
    Icons.settings_input_component,
    Icons.settings_input_composite,
    Icons.settings_input_hdmi,
    Icons.settings_input_svideo,
    Icons.settings_overscan,
    Icons.settings_phone,
    Icons.settings_power,
    Icons.settings_remote,
    Icons.settings_system_daydream,
    Icons.settings_voice,
    Icons.share,
    Icons.shield,
    Icons.shop,
    Icons.shop_two,
    Icons.shopping_bag,
    Icons.shopping_basket,
    Icons.shopping_cart,
    Icons.short_text,
    Icons.show_chart,
    Icons.shuffle,
    Icons.shutter_speed,
    Icons.signal_cellular_4_bar,
    Icons.signal_cellular_alt,
    Icons.signal_cellular_connected_no_internet_4_bar,
    Icons.signal_cellular_no_sim,
    Icons.signal_cellular_null,
    Icons.signal_cellular_off,
    Icons.signal_wifi_4_bar,
    Icons.signal_wifi_4_bar_lock,
    Icons.signal_wifi_off,
    Icons.sim_card,
    Icons.single_bed,
    Icons.skip_next,
    Icons.skip_previous,
    Icons.slideshow,
    Icons.slow_motion_video,
    Icons.smartphone,
    Icons.smoke_free,
    Icons.smoking_rooms,
    Icons.sms,
    Icons.snooze,
    Icons.sort,
    Icons.sort_by_alpha,
    Icons.spa,
    Icons.space_bar,
    Icons.speaker,
    Icons.speaker_group,
    Icons.speaker_notes,
    Icons.speaker_notes_off,
    Icons.speaker_phone,
    Icons.speed,
    Icons.spellcheck,
    Icons.sports,
    Icons.sports_bar,
    Icons.sports_baseball,
    Icons.sports_basketball,
    Icons.sports_cricket,
    Icons.sports_esports,
    Icons.sports_football,
    Icons.sports_golf,
    Icons.sports_handball,
    Icons.sports_hockey,
    Icons.sports_kabaddi,
    Icons.sports_mma,
    Icons.sports_motorsports,
    Icons.sports_rugby,
    Icons.sports_soccer,
    Icons.sports_tennis,
    Icons.sports_volleyball,
    Icons.square_foot,
    Icons.stacked_line_chart,
    Icons.stairs,
    Icons.star,
    Icons.star_border,
    Icons.star_half,
    Icons.star_outline,
    Icons.star_rate,
    Icons.stars,
    Icons.stay_current_landscape,
    Icons.stay_current_portrait,
    Icons.stay_primary_landscape,
    Icons.stay_primary_portrait,
    Icons.sticky_note_2,
    Icons.stop,
    Icons.stop_circle,
    Icons.stop_screen_share,
    Icons.storage,
    Icons.store,
    Icons.store_mall_directory,
    Icons.storefront,
    Icons.straighten,
    Icons.streetview,
    Icons.strikethrough_s,
    Icons.style,
    Icons.subdirectory_arrow_left,
    Icons.subdirectory_arrow_right,
    Icons.subject,
    Icons.subscript,
    Icons.subscriptions,
    Icons.subtitles,
    Icons.subway,
    Icons.superscript,
    Icons.supervised_user_circle,
    Icons.supervisor_account,
    Icons.support,
    Icons.support_agent,
    Icons.surround_sound,
    Icons.swap_calls,
    Icons.swap_horiz,
    Icons.swap_horizontal_circle,
    Icons.swap_vert,
    Icons.swap_vertical_circle,
    Icons.switch_camera,
    Icons.switch_left,
    Icons.switch_right,
    Icons.switch_video,
    Icons.sync,
    Icons.sync_alt,
    Icons.sync_disabled,
    Icons.sync_problem,
    Icons.system_update,
    Icons.system_update_alt,
    Icons.tab,
    Icons.tab_unselected,
    Icons.table_chart,
    Icons.table_rows,
    Icons.tablet,
    Icons.tablet_android,
    Icons.tablet_mac,
    Icons.tag,
    Icons.tag_faces,
    Icons.takeout_dining,
    Icons.tap_and_play,
    Icons.tapas,
    Icons.taxi_alert,
    Icons.terrain,
    Icons.text_fields,
    Icons.text_format,
    Icons.text_rotate_up,
    Icons.text_rotate_vertical,
    Icons.text_rotation_angledown,
    Icons.text_rotation_angleup,
    Icons.text_rotation_down,
    Icons.text_rotation_none,
    Icons.text_snippet,
    Icons.textsms,
    Icons.texture,
    Icons.theaters,
    Icons.thermostat,
    Icons.thumb_down,
    Icons.thumb_up,
    Icons.thumbs_up_down,
    Icons.time_to_leave,
    Icons.timelapse,
    Icons.timeline,
    Icons.timer,
    Icons.timer_10,
    Icons.timer_3,
    Icons.timer_off,
    Icons.title,
    Icons.toc,
    Icons.today,
    Icons.toggle_off,
    Icons.toggle_on,
    Icons.toll,
    Icons.tonality,
    Icons.topic,
    Icons.touch_app,
    Icons.tour,
    Icons.toys,
    Icons.track_changes,
    Icons.traffic,
    Icons.train,
    Icons.tram,
    Icons.transfer_within_a_station,
    Icons.transform,
    Icons.transit_enterexit,
    Icons.translate,
    Icons.trending_down,
    Icons.trending_flat,
    Icons.trending_up,
    Icons.trip_origin,
    Icons.tty,
    Icons.tune,
    Icons.tv,
    Icons.tv_off,
    Icons.two_wheeler,
    Icons.umbrella,
    Icons.unarchive,
    Icons.undo,
    Icons.unfold_less,
    Icons.unfold_more,
    Icons.unpublished,
    Icons.unsubscribe,
    Icons.update,
    Icons.upgrade,
    Icons.upload_file,
    Icons.usb,
    Icons.verified,
    Icons.verified_user,
    Icons.vertical_align_bottom,
    Icons.vertical_align_center,
    Icons.vertical_align_top,
    Icons.vertical_split,
    Icons.vibration,
    Icons.video_call,
    Icons.video_label,
    Icons.video_library,
    Icons.videocam,
    Icons.videocam_off,
    Icons.videogame_asset,
    Icons.view_agenda,
    Icons.view_array,
    Icons.view_carousel,
    Icons.view_column,
    Icons.view_comfy,
    Icons.view_compact,
    Icons.view_day,
    Icons.view_headline,
    Icons.view_list,
    Icons.view_module,
    Icons.view_quilt,
    Icons.view_stream,
    Icons.view_week,
    Icons.vignette,
    Icons.visibility,
    Icons.visibility_off,
    Icons.voice_chat,
    Icons.voice_over_off,
    Icons.voicemail,
    Icons.volume_down,
    Icons.volume_mute,
    Icons.volume_off,
    Icons.volume_up,
    Icons.vpn_key,
    Icons.vpn_lock,
    Icons.wallet_giftcard,
    Icons.wallet_membership,
    Icons.wallet_travel,
    Icons.wallpaper,
    Icons.warning,
    Icons.watch,
    Icons.watch_later,
    Icons.water_damage,
    Icons.waterfall_chart,
    Icons.waves,
    Icons.wb_auto,
    Icons.wb_cloudy,
    Icons.wb_incandescent,
    Icons.wb_iridescent,
    Icons.wb_sunny,
    Icons.wc,
    Icons.web,
    Icons.web_asset,
    Icons.weekend,
    Icons.west,
    Icons.whatshot,
    Icons.wheelchair_pickup,
    Icons.where_to_vote,
    Icons.widgets,
    Icons.wifi,
    Icons.wifi_calling,
    Icons.wifi_lock,
    Icons.wifi_off,
    Icons.wifi_tethering,
    Icons.wine_bar,
    Icons.work,
    Icons.work_off,
    Icons.work_outline,
    Icons.wrap_text,
    Icons.youtube_searched_for,
    Icons.zoom_in,
    Icons.zoom_out,
    Icons.zoom_out_map,
  ];
}
