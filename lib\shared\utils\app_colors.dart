import 'package:flutter/material.dart';

/// Colores utilizados en la aplicación
class AppColors {
  /// Color primario de la aplicación
  static const Color primary = Color(0xFF1976D2);
  
  /// Color secundario de la aplicación
  static const Color secondary = Color(0xFF03A9F4);
  
  /// Color de acento
  static const Color accent = Color(0xFF00BCD4);
  
  /// Color para elementos de éxito
  static const Color success = Color(0xFF4CAF50);
  
  /// Color para elementos de advertencia
  static const Color warning = Color(0xFFFFC107);
  
  /// Color para elementos de error
  static const Color error = Color(0xFFF44336);
  
  /// Color para elementos de información
  static const Color info = Color(0xFF2196F3);
  
  /// Color para texto principal
  static const Color textPrimary = Color(0xFF212121);
  
  /// Color para texto secundario
  static const Color textSecondary = Color(0xFF757575);
  
  /// Color para fondos
  static const Color background = Color(0xFFF5F5F5);
  
  /// Color para tarjetas
  static const Color cardBackground = Color(0xFFFFFFFF);
  
  /// Color para bordes
  static const Color border = Color(0xFFE0E0E0);
  
  /// Color para elementos deshabilitados
  static const Color disabled = Color(0xFFBDBDBD);
  
  /// Obtener color para estado de solicitud de servicio
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return warning;
      case 'in_progress':
        return info;
      case 'completed':
        return success;
      case 'cancelled':
        return error;
      default:
        return primary;
    }
  }
  
  /// Obtener color para tipo de transacción
  static Color getTransactionTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'income':
        return success;
      case 'expense':
        return error;
      default:
        return primary;
    }
  }
}
