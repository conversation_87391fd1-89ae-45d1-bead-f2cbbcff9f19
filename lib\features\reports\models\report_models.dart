import 'package:flutter/material.dart';

// Simplified models for reports

/// Status of a service request
enum ServiceStatus {
  /// Waiting to be processed
  pending,

  /// Currently being worked on
  inProgress,

  /// Successfully completed
  completed,

  /// Cancelled by customer or company
  cancelled,

  /// On hold for some reason
  onHold,

  /// Requires follow-up
  followUp,
}

/// Priority level for service requests
enum ServicePriority {
  /// Low priority, can be handled when convenient
  low,

  /// Normal priority, standard handling
  normal,

  /// High priority, needs prompt attention
  high,

  /// Urgent priority, needs immediate attention
  urgent,
}

enum TransactionType {
  income,
  expense,
}

/// Status of an invoice
enum InvoiceStatus {
  /// Draft invoice that can be edited
  draft,

  /// Issued to customer but not paid
  issued,

  /// Partially paid invoice
  partiallyPaid,

  /// Fully paid invoice
  paid,

  /// Overdue invoice
  overdue,

  /// Cancelled invoice
  cancelled,
}

/// Represents an invoice in the system
class Invoice {
  /// Unique identifier for the invoice
  final int id;

  /// Human-readable invoice number (e.g., INV-2023-001)
  final String invoiceNumber;

  /// ID of the customer this invoice belongs to
  final int customerId;

  /// Name of the customer for display purposes
  final String customerName;

  /// Date when the invoice was created
  final DateTime date;

  /// Due date for payment
  final DateTime dueDate;

  /// Line items in the invoice
  final List<dynamic> items;

  /// Subtotal amount before tax and discount
  final double subtotal;

  /// Tax amount
  final double tax;

  /// Discount amount
  final double discount;

  /// Total amount after tax and discount
  final double total;

  /// Optional notes about the invoice
  final String? notes;

  /// Whether the invoice is fully paid
  final bool isPaid;

  /// Date when the invoice was paid (if applicable)
  final DateTime? paymentDate;

  /// Amount already paid
  final double amountPaid;

  /// Date when the invoice was created in the system
  final DateTime createdAt;

  /// Date when the invoice was last updated
  final DateTime? updatedAt;

  /// Current status of the invoice
  final InvoiceStatus status;

  /// Creates a new invoice instance
  Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.customerId,
    required this.customerName,
    required this.date,
    required this.dueDate,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.discount,
    required this.total,
    this.notes,
    required this.isPaid,
    this.paymentDate,
    this.amountPaid = 0.0,
    required this.createdAt,
    this.updatedAt,
    InvoiceStatus? status,
  }) : status = status ?? (isPaid ? InvoiceStatus.paid :
                          (DateTime.now().isAfter(dueDate) ? InvoiceStatus.overdue :
                          InvoiceStatus.issued));

  /// Returns the remaining balance to be paid
  double get remainingBalance => total - amountPaid;

  /// Checks if the invoice is overdue
  bool get isOverdue => !isPaid && DateTime.now().isAfter(dueDate);

  /// Returns the number of days until the due date (negative if overdue)
  int get daysUntilDue => dueDate.difference(DateTime.now()).inDays;

  /// Returns a formatted string of the invoice status in Arabic
  String get statusText => getStatusName(status);

  /// Returns the color associated with the current status
  Color get statusColor => getStatusColor(status);

  /// Creates a copy of this invoice with modified fields
  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    int? customerId,
    String? customerName,
    DateTime? date,
    DateTime? dueDate,
    List<dynamic>? items,
    double? subtotal,
    double? tax,
    double? discount,
    double? total,
    String? notes,
    bool? isPaid,
    DateTime? paymentDate,
    double? amountPaid,
    DateTime? createdAt,
    DateTime? updatedAt,
    InvoiceStatus? status,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      date: date ?? this.date,
      dueDate: dueDate ?? this.dueDate,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      notes: notes ?? this.notes,
      isPaid: isPaid ?? this.isPaid,
      paymentDate: paymentDate ?? this.paymentDate,
      amountPaid: amountPaid ?? this.amountPaid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
    );
  }

  /// Converts the invoice to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'date': date.toIso8601String(),
      'dueDate': dueDate.toIso8601String(),
      'items': items,
      'subtotal': subtotal,
      'tax': tax,
      'discount': discount,
      'total': total,
      'notes': notes,
      'isPaid': isPaid,
      'paymentDate': paymentDate?.toIso8601String(),
      'amountPaid': amountPaid,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'status': status.index,
    };
  }

  /// Creates an invoice from a JSON map
  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'],
      invoiceNumber: json['invoiceNumber'],
      customerId: json['customerId'],
      customerName: json['customerName'],
      date: DateTime.parse(json['date']),
      dueDate: DateTime.parse(json['dueDate']),
      items: json['items'],
      subtotal: json['subtotal'].toDouble(),
      tax: json['tax'].toDouble(),
      discount: json['discount'].toDouble(),
      total: json['total'].toDouble(),
      notes: json['notes'],
      isPaid: json['isPaid'],
      paymentDate: json['paymentDate'] != null ? DateTime.parse(json['paymentDate']) : null,
      amountPaid: json['amountPaid']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      status: json['status'] != null ? InvoiceStatus.values[json['status']] : null,
    );
  }

  /// Returns the Arabic name for an invoice status
  static String getStatusName(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.issued:
        return 'صادرة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.overdue:
        return 'متأخرة';
      case InvoiceStatus.cancelled:
        return 'ملغاة';
    }
  }

  /// Returns the color associated with an invoice status
  static Color getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.issued:
        return Colors.blue;
      case InvoiceStatus.partiallyPaid:
        return Colors.amber;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.black54;
    }
  }
}

/// Represents a service request in the system
class ServiceRequest {
  /// Unique identifier for the service request
  final int id;

  /// Human-readable request number (e.g., SR-2023-001)
  final String requestNumber;

  /// ID of the customer this request belongs to (optional for walk-in customers)
  final int? customerId;

  /// Name of the customer for display purposes
  final String customerName;

  /// Type of service requested (e.g., AC Installation, Maintenance)
  final String serviceType;

  /// Detailed description of the service request
  final String description;

  /// Customer's address where the service will be performed
  final String? address;

  /// Date and time when the service is scheduled
  final DateTime scheduledDate;

  /// Current status of the service request
  final ServiceStatus status;

  /// Priority level of the service request
  final ServicePriority priority;

  /// ID of the employee assigned to this request
  final int? assignedEmployeeId;

  /// Name of the employee assigned to this request
  final String? assignedTo;

  /// Date when the service was completed (if applicable)
  final DateTime? completionDate;

  /// Additional notes about the service request
  final String? notes;

  /// Estimated duration of the service in hours
  final double? estimatedHours;

  /// Actual duration of the service in hours (after completion)
  final double? actualHours;

  /// Date when the service request was created in the system
  final DateTime createdAt;

  /// Date when the service request was last updated
  final DateTime? updatedAt;

  /// Creates a new service request instance
  ServiceRequest({
    required this.id,
    required this.requestNumber,
    this.customerId,
    required this.customerName,
    required this.serviceType,
    required this.description,
    this.address,
    required this.scheduledDate,
    required this.status,
    this.priority = ServicePriority.normal,
    this.assignedEmployeeId,
    this.assignedTo,
    this.completionDate,
    this.notes,
    this.estimatedHours,
    this.actualHours,
    required this.createdAt,
    this.updatedAt,
  });

  /// Returns a formatted string of the service status in Arabic
  String get statusText => getStatusName(status);

  /// Returns the color associated with the current status
  Color get statusColor => getStatusColor(status);

  /// Returns a formatted string of the priority level in Arabic
  String get priorityText => getPriorityName(priority);

  /// Returns the color associated with the priority level
  Color get priorityColor => getPriorityColor(priority);

  /// Checks if the service request is overdue
  bool get isOverdue => status != ServiceStatus.completed &&
                        status != ServiceStatus.cancelled &&
                        DateTime.now().isAfter(scheduledDate);

  /// Returns the number of days until the scheduled date (negative if overdue)
  int get daysUntilScheduled => scheduledDate.difference(DateTime.now()).inDays;

  /// Checks if the service request is assigned to an employee
  bool get isAssigned => assignedTo != null && assignedTo!.isNotEmpty;

  /// Creates a copy of this service request with modified fields
  ServiceRequest copyWith({
    int? id,
    String? requestNumber,
    int? customerId,
    String? customerName,
    String? serviceType,
    String? description,
    String? address,
    DateTime? scheduledDate,
    ServiceStatus? status,
    ServicePriority? priority,
    int? assignedEmployeeId,
    String? assignedTo,
    DateTime? completionDate,
    String? notes,
    double? estimatedHours,
    double? actualHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceRequest(
      id: id ?? this.id,
      requestNumber: requestNumber ?? this.requestNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      serviceType: serviceType ?? this.serviceType,
      description: description ?? this.description,
      address: address ?? this.address,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      assignedEmployeeId: assignedEmployeeId ?? this.assignedEmployeeId,
      assignedTo: assignedTo ?? this.assignedTo,
      completionDate: completionDate ?? this.completionDate,
      notes: notes ?? this.notes,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Converts the service request to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requestNumber': requestNumber,
      'customerId': customerId,
      'customerName': customerName,
      'serviceType': serviceType,
      'description': description,
      'address': address,
      'scheduledDate': scheduledDate.toIso8601String(),
      'status': status.index,
      'priority': priority.index,
      'assignedEmployeeId': assignedEmployeeId,
      'assignedTo': assignedTo,
      'completionDate': completionDate?.toIso8601String(),
      'notes': notes,
      'estimatedHours': estimatedHours,
      'actualHours': actualHours,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Creates a service request from a JSON map
  factory ServiceRequest.fromJson(Map<String, dynamic> json) {
    return ServiceRequest(
      id: json['id'],
      requestNumber: json['requestNumber'],
      customerId: json['customerId'],
      customerName: json['customerName'],
      serviceType: json['serviceType'],
      description: json['description'],
      address: json['address'],
      scheduledDate: DateTime.parse(json['scheduledDate']),
      status: ServiceStatus.values[json['status']],
      priority: json['priority'] != null
          ? ServicePriority.values[json['priority']]
          : ServicePriority.normal,
      assignedEmployeeId: json['assignedEmployeeId'],
      assignedTo: json['assignedTo'],
      completionDate: json['completionDate'] != null
          ? DateTime.parse(json['completionDate'])
          : null,
      notes: json['notes'],
      estimatedHours: json['estimatedHours']?.toDouble(),
      actualHours: json['actualHours']?.toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  /// Returns the Arabic name for a service status
  static String getStatusName(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.pending:
        return 'معلق';
      case ServiceStatus.inProgress:
        return 'قيد التنفيذ';
      case ServiceStatus.completed:
        return 'مكتمل';
      case ServiceStatus.cancelled:
        return 'ملغى';
      case ServiceStatus.onHold:
        return 'متوقف مؤقتاً';
      case ServiceStatus.followUp:
        return 'متابعة';
    }
  }

  /// Returns the color associated with a service status
  static Color getStatusColor(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.pending:
        return Colors.blue;
      case ServiceStatus.inProgress:
        return Colors.orange;
      case ServiceStatus.completed:
        return Colors.green;
      case ServiceStatus.cancelled:
        return Colors.red;
      case ServiceStatus.onHold:
        return Colors.purple;
      case ServiceStatus.followUp:
        return Colors.amber;
    }
  }

  /// Returns the Arabic name for a priority level
  static String getPriorityName(ServicePriority priority) {
    switch (priority) {
      case ServicePriority.low:
        return 'منخفضة';
      case ServicePriority.normal:
        return 'عادية';
      case ServicePriority.high:
        return 'عالية';
      case ServicePriority.urgent:
        return 'عاجلة';
    }
  }

  /// Returns the color associated with a priority level
  static Color getPriorityColor(ServicePriority priority) {
    switch (priority) {
      case ServicePriority.low:
        return Colors.green;
      case ServicePriority.normal:
        return Colors.blue;
      case ServicePriority.high:
        return Colors.orange;
      case ServicePriority.urgent:
        return Colors.red;
    }
  }
}

/// Payment method for transactions
enum PaymentMethod {
  /// Cash payment
  cash,

  /// Bank transfer
  bankTransfer,

  /// Credit card payment
  creditCard,

  /// Debit card payment
  debitCard,

  /// Check payment
  check,

  /// Online payment
  online,

  /// Other payment methods
  other,
}

/// Represents a financial transaction in the system
class Transaction {
  /// Unique identifier for the transaction
  final int id;

  /// Reference number for the transaction
  final String reference;

  /// Date when the transaction occurred
  final DateTime date;

  /// Amount of the transaction
  final double amount;

  /// Type of transaction (income or expense)
  final TransactionType type;

  /// Category of the transaction (e.g., Salary, Rent, Utilities)
  final String? category;

  /// ID of the category
  final int? categoryId;

  /// Detailed description of the transaction
  final String description;

  /// Method used for payment
  final PaymentMethod paymentMethod;

  /// ID of the bank account used (if applicable)
  final int? bankAccountId;

  /// Name of the bank account used (if applicable)
  final String? bankAccountName;

  /// ID of the customer or supplier related to this transaction
  final int? relatedEntityId;

  /// Name of the customer or supplier related to this transaction
  final String? relatedEntityName;

  /// Type of the related entity (customer, supplier, employee)
  final String? relatedEntityType;

  /// ID of the employee who handled the transaction
  final int? employeeId;

  /// Name of the employee who handled the transaction
  final String? employeeName;

  /// Additional notes about the transaction
  final String? notes;

  /// Whether the transaction has been reconciled
  final bool isReconciled;

  /// Date when the transaction was created in the system
  final DateTime createdAt;

  /// Date when the transaction was last updated
  final DateTime? updatedAt;

  /// Creates a new transaction instance
  Transaction({
    required this.id,
    required this.reference,
    required this.date,
    required this.amount,
    required this.type,
    this.category,
    this.categoryId,
    required this.description,
    required this.paymentMethod,
    this.bankAccountId,
    this.bankAccountName,
    this.relatedEntityId,
    this.relatedEntityName,
    this.relatedEntityType,
    this.employeeId,
    this.employeeName,
    this.notes,
    this.isReconciled = false,
    required this.createdAt,
    this.updatedAt,
  });

  /// Returns a formatted string of the transaction type in Arabic
  String get typeName => getTypeName(type);

  /// Returns the color associated with the transaction type
  Color get typeColor => getTypeColor(type);

  /// Returns a formatted string of the payment method in Arabic
  String get paymentMethodName => getPaymentMethodName(paymentMethod);

  /// Creates a copy of this transaction with modified fields
  Transaction copyWith({
    int? id,
    String? reference,
    DateTime? date,
    double? amount,
    TransactionType? type,
    String? category,
    int? categoryId,
    String? description,
    PaymentMethod? paymentMethod,
    int? bankAccountId,
    String? bankAccountName,
    int? relatedEntityId,
    String? relatedEntityName,
    String? relatedEntityType,
    int? employeeId,
    String? employeeName,
    String? notes,
    bool? isReconciled,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      reference: reference ?? this.reference,
      date: date ?? this.date,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      category: category ?? this.category,
      categoryId: categoryId ?? this.categoryId,
      description: description ?? this.description,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      bankAccountName: bankAccountName ?? this.bankAccountName,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityName: relatedEntityName ?? this.relatedEntityName,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      notes: notes ?? this.notes,
      isReconciled: isReconciled ?? this.isReconciled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Converts the transaction to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reference': reference,
      'date': date.toIso8601String(),
      'amount': amount,
      'type': type.index,
      'category': category,
      'categoryId': categoryId,
      'description': description,
      'paymentMethod': paymentMethod.index,
      'bankAccountId': bankAccountId,
      'bankAccountName': bankAccountName,
      'relatedEntityId': relatedEntityId,
      'relatedEntityName': relatedEntityName,
      'relatedEntityType': relatedEntityType,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'notes': notes,
      'isReconciled': isReconciled,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Creates a transaction from a JSON map
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      reference: json['reference'],
      date: DateTime.parse(json['date']),
      amount: json['amount'].toDouble(),
      type: TransactionType.values[json['type']],
      category: json['category'],
      categoryId: json['categoryId'],
      description: json['description'],
      paymentMethod: PaymentMethod.values[json['paymentMethod'] ?? 0],
      bankAccountId: json['bankAccountId'],
      bankAccountName: json['bankAccountName'],
      relatedEntityId: json['relatedEntityId'],
      relatedEntityName: json['relatedEntityName'],
      relatedEntityType: json['relatedEntityType'],
      employeeId: json['employeeId'],
      employeeName: json['employeeName'],
      notes: json['notes'],
      isReconciled: json['isReconciled'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  get partyName => null;

  /// Returns the Arabic name for a transaction type
  static String getTypeName(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return 'إيراد';
      case TransactionType.expense:
        return 'مصروف';
    }
  }

  /// Returns the color associated with a transaction type
  static Color getTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
    }
  }

  /// Returns the Arabic name for a payment method
  static String getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethod.debitCard:
        return 'بطاقة خصم';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.online:
        return 'دفع إلكتروني';
      case PaymentMethod.other:
        return 'أخرى';
    }
  }
}
