import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/bank_account.dart';

class BankAccountRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all bank accounts
  Future<List<BankAccount>> getAllBankAccounts() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('bank_accounts');

      return List.generate(maps.length, (i) {
        return BankAccount(
          id: maps[i]['id'] as int,
          bankName: maps[i]['bank_name'] as String,
          accountNumber: maps[i]['account_number'] as String,
          accountName: maps[i]['account_name'] as String,
          type: _parseAccountType(maps[i]['type'] as String),
          iban: maps[i]['iban'] as String?,
          swiftCode: maps[i]['swift_code'] as String?,
          branchName: maps[i]['branch_name'] as String?,
          isActive: maps[i]['status'] == 'active',
          balance: maps[i]['balance'] as double,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting bank accounts: $e');
      }
      return [];
    }
  }

  // Get bank account by ID
  Future<BankAccount?> getBankAccountById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'bank_accounts',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return BankAccount(
          id: maps[0]['id'] as int,
          bankName: maps[0]['bank_name'] as String,
          accountNumber: maps[0]['account_number'] as String,
          accountName: maps[0]['account_name'] as String,
          type: _parseAccountType(maps[0]['type'] as String),
          iban: maps[0]['iban'] as String?,
          swiftCode: maps[0]['swift_code'] as String?,
          branchName: maps[0]['branch_name'] as String?,
          isActive: maps[0]['status'] == 'active',
          balance: maps[0]['balance'] as double,
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting bank account by ID: $e');
      }
      return null;
    }
  }

  // Insert a new bank account
  Future<int> insertBankAccount(BankAccount account) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'bank_accounts',
        {
          'bank_name': account.bankName,
          'account_number': account.accountNumber,
          'account_name': account.accountName,
          'type': account.type.toString().split('.').last,
          'iban': account.iban,
          'swift_code': account.swiftCode,
          'branch_name': account.branchName,
          'status': account.isActive ? 'active' : 'inactive',
          'balance': account.balance,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting bank account: $e');
      }
      return -1;
    }
  }

  // Update an existing bank account
  Future<int> updateBankAccount(BankAccount account) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'bank_accounts',
        {
          'bank_name': account.bankName,
          'account_number': account.accountNumber,
          'account_name': account.accountName,
          'type': account.type.toString().split('.').last,
          'iban': account.iban,
          'swift_code': account.swiftCode,
          'branch_name': account.branchName,
          'status': account.isActive ? 'active' : 'inactive',
          'balance': account.balance,
        },
        where: 'id = ?',
        whereArgs: [account.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating bank account: $e');
      }
      return 0;
    }
  }

  // Delete a bank account
  Future<int> deleteBankAccount(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'bank_accounts',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting bank account: $e');
      }
      return 0;
    }
  }

  // Update bank account balance
  Future<int> updateBankAccountBalance(int id, double newBalance) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'bank_accounts',
        {'balance': newBalance},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating bank account balance: $e');
      }
      return 0;
    }
  }

  // Helper method to parse account type string to enum
  BankAccountType _parseAccountType(String type) {
    switch (type.toLowerCase()) {
      case 'checking':
        return BankAccountType.checking;
      case 'savings':
        return BankAccountType.savings;
      case 'business':
        return BankAccountType.business;
      case 'other':
      default:
        return BankAccountType.other;
    }
  }
}
