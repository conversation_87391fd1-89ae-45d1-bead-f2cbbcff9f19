import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A custom widget that renders a snowflake icon similar to the provided image
class SnowflakeIcon extends StatelessWidget {
  final double size;
  final Color color;
  final Color backgroundColor;
  final bool showBackground;

  const SnowflakeIcon({
    super.key,
    this.size = 48.0,
    this.color = Colors.white,
    this.backgroundColor = const Color(0xFF3498DB),
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: showBackground
          ? BoxDecoration(
              color: backgroundColor,
              shape: BoxShape.circle,
            )
          : null,
      child: CustomPaint(
        size: <PERSON>ze(size, size),
        painter: <PERSON><PERSON><PERSON><PERSON>ain<PERSON>(
          color: color,
        ),
      ),
    );
  }
}

/// Custom painter for drawing the snowflake
class SnowflakePainter extends CustomPainter {
  final Color color;

  SnowflakePainter({
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 * 0.8; // Slightly smaller to fit in circle
    
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.08
      ..strokeCap = StrokeCap.round;
    
    // Draw the six main arms of the snowflake
    for (int i = 0; i < 6; i++) {
      final angle = (i * 60) * (math.pi / 180); // Convert degrees to radians
      
      // Draw the main arm
      final startPoint = center;
      final endPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle)
      );
      
      canvas.drawLine(startPoint, endPoint, paint);
      
      // Draw the two branches on each arm
      final branchLength = radius * 0.4;
      final branchAngle1 = angle + (45 * math.pi / 180);
      final branchAngle2 = angle - (45 * math.pi / 180);
      
      // Calculate the point where the branch starts (about 60% along the main arm)
      final branchStartPoint = Offset(
        center.dx + (radius * 0.6) * math.cos(angle),
        center.dy + (radius * 0.6) * math.sin(angle)
      );
      
      // Draw the two branches
      final branch1EndPoint = Offset(
        branchStartPoint.dx + branchLength * math.cos(branchAngle1),
        branchStartPoint.dy + branchLength * math.sin(branchAngle1)
      );
      
      final branch2EndPoint = Offset(
        branchStartPoint.dx + branchLength * math.cos(branchAngle2),
        branchStartPoint.dy + branchLength * math.sin(branchAngle2)
      );
      
      canvas.drawLine(branchStartPoint, branch1EndPoint, paint);
      canvas.drawLine(branchStartPoint, branch2EndPoint, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
