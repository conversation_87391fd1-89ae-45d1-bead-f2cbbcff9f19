import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/ui_components.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/salary_repository.dart';
import '../../../shared/models/transaction.dart' as transaction_models;
import '../utils/paginated_pdf_export.dart';

/// شاشة تقرير الأرباح والخسائر
class ProfitLossReportScreen extends StatefulWidget {
  const ProfitLossReportScreen({super.key});

  @override
  State<ProfitLossReportScreen> createState() => _ProfitLossReportScreenState();
}

class _ProfitLossReportScreenState extends State<ProfitLossReportScreen> {
  final TransactionRepository _transactionRepository = TransactionRepository();
  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final SalaryRepository _salaryRepository = SalaryRepository();

  bool _isLoading = true;
  String _selectedPeriod = 'monthly'; // monthly, quarterly, yearly
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  
  // بيانات التقرير
  double _totalRevenue = 0;
  double _totalExpenses = 0;
  double _netProfit = 0;
  double _grossProfit = 0;
  double _operatingProfit = 0;
  
  // تفاصيل الإيرادات
  double _salesRevenue = 0;
  double _serviceRevenue = 0;
  double _otherRevenue = 0;
  
  // تفاصيل المصروفات
  double _costOfGoodsSold = 0;
  double _salaries = 0;
  double _rent = 0;
  double _utilities = 0;
  double _marketing = 0;
  double _otherExpenses = 0;
  
  // بيانات الرسم البياني
  List<FlSpot> _revenueSpots = [];
  List<FlSpot> _expenseSpots = [];
  List<FlSpot> _profitSpots = [];
  List<String> _periodLabels = [];
  
  @override
  void initState() {
    super.initState();
    _loadReportData();
  }
  
  /// تحميل بيانات التقرير
  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // تحديث نطاق التاريخ بناءً على الفترة المحددة
      _updateDateRange();
      
      // تحميل المعاملات المالية
      final transactions = await _transactionRepository.getTransactionsByDateRange(
        _startDate,
        _endDate,
      );
      
      // تحميل الفواتير
      final invoices = await _invoiceRepository.getInvoicesByDateRange(
        _startDate,
        _endDate,
      );
      
      // تحميل طلبات الخدمة
      final serviceRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(
        _startDate,
        _endDate,
      );
      
      // تحميل الرواتب
      final salaries = await _salaryRepository.getSalariesByDateRange(
        _startDate,
        _endDate,
      );

      // تحميل مصروفات الأجور اليومية للفنيين
      final technicianExpenses = await _serviceRequestRepository.getTechnicianExpenses(
        startDate: _startDate,
        endDate: _endDate,
      );

      // حساب الإيرادات
      _calculateRevenue(transactions, invoices, serviceRequests);

      // حساب المصروفات
      _calculateExpenses(transactions, salaries, technicianExpenses);
      
      // حساب الأرباح
      _calculateProfits();
      
      // إعداد بيانات الرسم البياني
      _prepareChartData();
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
  
  /// تحديث نطاق التاريخ بناءً على الفترة المحددة
  void _updateDateRange() {
    final now = DateTime.now();
    
    switch (_selectedPeriod) {
      case 'monthly':
        _startDate = DateTime(now.year, now.month - 1, 1);
        _endDate = DateTime(now.year, now.month, 0);
        break;
      case 'quarterly':
        final currentQuarter = (now.month - 1) ~/ 3;
        _startDate = DateTime(now.year, currentQuarter * 3 + 1, 1);
        _endDate = DateTime(now.year, (currentQuarter + 1) * 3 + 1, 0);
        break;
      case 'yearly':
        _startDate = DateTime(now.year, 1, 1);
        _endDate = DateTime(now.year, 12, 31);
        break;
    }
  }
  
  /// حساب الإيرادات
  void _calculateRevenue(
    List<transaction_models.Transaction> transactions,
    List<dynamic> invoices,
    List<dynamic> serviceRequests,
  ) {
    // إعادة تعيين القيم
    _totalRevenue = 0;
    _salesRevenue = 0;
    _serviceRevenue = 0;
    _otherRevenue = 0;
    
    // حساب الإيرادات من المعاملات
    for (final transaction in transactions) {
      if (transaction.type == transaction_models.TransactionType.income) {
        _totalRevenue += transaction.amount;
        
        // تصنيف الإيرادات
        if (transaction.category == 'sales') {
          _salesRevenue += transaction.amount;
        } else if (transaction.category == 'services') {
          _serviceRevenue += transaction.amount;
        } else {
          _otherRevenue += transaction.amount;
        }
      }
    }
    
    // إضافة إيرادات الفواتير المدفوعة
    for (final invoice in invoices) {
      if (invoice.status == 'paid') {
        _totalRevenue += invoice.total;
        _salesRevenue += invoice.total;
      }
    }
    
    // إضافة إيرادات طلبات الخدمة المكتملة
    for (final serviceRequest in serviceRequests) {
      if (serviceRequest.status == 'completed') {
        _totalRevenue += serviceRequest.amount ?? 0;
        _serviceRevenue += serviceRequest.amount ?? 0;
      }
    }
  }
  
  /// حساب المصروفات
  void _calculateExpenses(
    List<transaction_models.Transaction> transactions,
    List<dynamic> salaries,
    Map<String, dynamic> technicianExpenses,
  ) {
    // إعادة تعيين القيم
    _totalExpenses = 0;
    _costOfGoodsSold = 0;
    _salaries = 0;
    _rent = 0;
    _utilities = 0;
    _marketing = 0;
    _otherExpenses = 0;
    
    // حساب المصروفات من المعاملات
    for (final transaction in transactions) {
      if (transaction.type == transaction_models.TransactionType.expense) {
        _totalExpenses += transaction.amount;
        
        // تصنيف المصروفات
        switch (transaction.category) {
          case 'purchases':
            _costOfGoodsSold += transaction.amount;
            break;
          case 'rent':
            _rent += transaction.amount;
            break;
          case 'utilities':
            _utilities += transaction.amount;
            break;
          case 'marketing':
            _marketing += transaction.amount;
            break;
          default:
            _otherExpenses += transaction.amount;
            break;
        }
      }
    }
    
    // إضافة مصروفات الرواتب
    for (final salary in salaries) {
      _totalExpenses += salary.netSalary;
      _salaries += salary.netSalary;
    }

    // إضافة مصروفات الأجور اليومية للفنيين
    final technicianDailyWages = technicianExpenses['total_expenses'] as double? ?? 0.0;
    _totalExpenses += technicianDailyWages;
    _salaries += technicianDailyWages; // تصنيف الأجور اليومية ضمن الرواتب
  }
  
  /// حساب الأرباح
  void _calculateProfits() {
    _grossProfit = _totalRevenue - _costOfGoodsSold;
    _operatingProfit = _grossProfit - (_salaries + _rent + _utilities + _marketing);
    _netProfit = _totalRevenue - _totalExpenses;
  }
  
  /// إعداد بيانات الرسم البياني
  void _prepareChartData() {
    _revenueSpots = [];
    _expenseSpots = [];
    _profitSpots = [];
    _periodLabels = [];
    
    // تحديد عدد الفترات بناءً على الفترة المحددة
    int periods;
    switch (_selectedPeriod) {
      case 'monthly':
        periods = 30; // يوم
        break;
      case 'quarterly':
        periods = 3; // شهر
        break;
      case 'yearly':
        periods = 12; // شهر
        break;
      default:
        periods = 30;
    }
    
    // إنشاء بيانات وهمية للرسم البياني (يجب استبدالها ببيانات حقيقية)
    for (int i = 0; i < periods; i++) {
      final revenue = _totalRevenue / periods * (0.8 + 0.4 * i / periods);
      final expense = _totalExpenses / periods * (0.9 + 0.2 * i / periods);
      final profit = revenue - expense;
      
      _revenueSpots.add(FlSpot(i.toDouble(), revenue));
      _expenseSpots.add(FlSpot(i.toDouble(), expense));
      _profitSpots.add(FlSpot(i.toDouble(), profit));
      
      // إضافة تسميات الفترات
      if (_selectedPeriod == 'monthly') {
        final date = _startDate.add(Duration(days: i));
        _periodLabels.add(DateFormat('d').format(date));
      } else if (_selectedPeriod == 'quarterly') {
        final date = DateTime(_startDate.year, _startDate.month + i, 1);
        _periodLabels.add(DateFormat('MMM').format(date));
      } else {
        final date = DateTime(_startDate.year, i + 1, 1);
        _periodLabels.add(DateFormat('MMM').format(date));
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير الأرباح والخسائر'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _exportToPdf,
            tooltip: 'تصدير إلى PDF',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }
  
  Widget _buildReportContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPeriodSelector(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildSummaryCards(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildProfitLossChart(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildRevenueBreakdown(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildExpenseBreakdown(),
        ],
      ),
    );
  }
  
  Widget _buildPeriodSelector() {
    return StyledCard(
      title: 'الفترة الزمنية',
      icon: Icons.date_range,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('شهري'),
                  value: 'monthly',
                  groupValue: _selectedPeriod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                    _loadReportData();
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('ربع سنوي'),
                  value: 'quarterly',
                  groupValue: _selectedPeriod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                    _loadReportData();
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('سنوي'),
                  value: 'yearly',
                  groupValue: _selectedPeriod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                    _loadReportData();
                  },
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'من: ${DateFormat('yyyy/MM/dd').format(_startDate)}',
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
                Expanded(
                  child: Text(
                    'إلى: ${DateFormat('yyyy/MM/dd').format(_endDate)}',
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSummaryCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص الأرباح والخسائر',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'إجمالي الإيرادات',
                _totalRevenue,
                Icons.arrow_upward,
                AppColors.success,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildSummaryCard(
                'إجمالي المصروفات',
                _totalExpenses,
                Icons.arrow_downward,
                AppColors.error,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'إجمالي الربح',
                _grossProfit,
                Icons.trending_up,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildSummaryCard(
                'صافي الربح',
                _netProfit,
                Icons.account_balance,
                _netProfit >= 0 ? AppColors.success : AppColors.error,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return StyledCard(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: AppTextStyles.labelMedium,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          MoneyCounterAnimation(
            endValue: amount,
            currency: 'ر.س',
            duration: const Duration(milliseconds: 1500),
            style: AppTextStyles.titleLarge.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProfitLossChart() {
    return StyledCard(
      title: 'رسم بياني للأرباح والخسائر',
      icon: Icons.insert_chart,
      child: SizedBox(
        height: 300,
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: LineChart(
            LineChartData(
              gridData: FlGridData(show: true),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40,
                  ),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value.toInt() % 5 == 0 && value.toInt() < _periodLabels.length) {
                        return Text(
                          _periodLabels[value.toInt()],
                          style: AppTextStyles.labelSmall,
                        );
                      }
                      return const Text('');
                    },
                    reservedSize: 30,
                  ),
                ),
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
              ),
              borderData: FlBorderData(show: true),
              lineBarsData: [
                // خط الإيرادات
                LineChartBarData(
                  spots: _revenueSpots,
                  isCurved: true,
                  color: AppColors.success,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: false),
                  belowBarData: BarAreaData(
                    show: true,
                    color: AppColors.success.withOpacity(0.1),
                  ),
                ),
                // خط المصروفات
                LineChartBarData(
                  spots: _expenseSpots,
                  isCurved: true,
                  color: AppColors.error,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: false),
                  belowBarData: BarAreaData(
                    show: true,
                    color: AppColors.error.withOpacity(0.1),
                  ),
                ),
                // خط الأرباح
                LineChartBarData(
                  spots: _profitSpots,
                  isCurved: true,
                  color: AppColors.primary,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: false),
                  belowBarData: BarAreaData(
                    show: true,
                    color: AppColors.primary.withOpacity(0.1),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildRevenueBreakdown() {
    return StyledCard(
      title: 'تفاصيل الإيرادات',
      icon: Icons.monetization_on,
      child: Column(
        children: [
          _buildBreakdownItem('مبيعات', _salesRevenue, _totalRevenue, AppColors.success),
          const Divider(),
          _buildBreakdownItem('خدمات', _serviceRevenue, _totalRevenue, AppColors.primary),
          const Divider(),
          _buildBreakdownItem('إيرادات أخرى', _otherRevenue, _totalRevenue, AppColors.info),
        ],
      ),
    );
  }
  
  Widget _buildExpenseBreakdown() {
    return StyledCard(
      title: 'تفاصيل المصروفات',
      icon: Icons.money_off,
      child: Column(
        children: [
          _buildBreakdownItem('تكلفة البضاعة المباعة', _costOfGoodsSold, _totalExpenses, AppColors.error),
          const Divider(),
          _buildBreakdownItem('رواتب', _salaries, _totalExpenses, AppColors.warning),
          const Divider(),
          _buildBreakdownItem('إيجار', _rent, _totalExpenses, AppColors.info),
          const Divider(),
          _buildBreakdownItem('مرافق', _utilities, _totalExpenses, AppColors.secondary),
          const Divider(),
          _buildBreakdownItem('تسويق', _marketing, _totalExpenses, AppColors.primary),
          const Divider(),
          _buildBreakdownItem('مصروفات أخرى', _otherExpenses, _totalExpenses, AppColors.textSecondary),
        ],
      ),
    );
  }
  
  Widget _buildBreakdownItem(
    String title,
    double amount,
    double total,
    Color color,
  ) {
    final percentage = total > 0 ? (amount / total * 100) : 0;
    
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppDimensions.paddingS,
        horizontal: AppDimensions.paddingM,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium,
              ),
              const Spacer(),
              Text(
                '${amount.toStringAsFixed(2)} ر.س',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: AppDimensions.spacingM),
              Text(
                '(${percentage.toStringAsFixed(1)}%)',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: color.withOpacity(0.1),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }
  
  Future<void> _exportToPdf() async {
    try {
      // إنشاء بيانات الجدول
      final tableData = [
        {
          'category': 'الإيرادات',
          'amount': _totalRevenue,
          'type': 'header',
        },
        {
          'category': 'مبيعات',
          'amount': _salesRevenue,
          'type': 'revenue',
        },
        {
          'category': 'خدمات',
          'amount': _serviceRevenue,
          'type': 'revenue',
        },
        {
          'category': 'إيرادات أخرى',
          'amount': _otherRevenue,
          'type': 'revenue',
        },
        {
          'category': 'المصروفات',
          'amount': _totalExpenses,
          'type': 'header',
        },
        {
          'category': 'تكلفة البضاعة المباعة',
          'amount': _costOfGoodsSold,
          'type': 'expense',
        },
        {
          'category': 'رواتب',
          'amount': _salaries,
          'type': 'expense',
        },
        {
          'category': 'إيجار',
          'amount': _rent,
          'type': 'expense',
        },
        {
          'category': 'مرافق',
          'amount': _utilities,
          'type': 'expense',
        },
        {
          'category': 'تسويق',
          'amount': _marketing,
          'type': 'expense',
        },
        {
          'category': 'مصروفات أخرى',
          'amount': _otherExpenses,
          'type': 'expense',
        },
        {
          'category': 'الأرباح',
          'amount': 0,
          'type': 'header',
        },
        {
          'category': 'إجمالي الربح',
          'amount': _grossProfit,
          'type': 'profit',
        },
        {
          'category': 'صافي الربح',
          'amount': _netProfit,
          'type': 'profit',
        },
      ];
      
      // إنشاء بيانات الملخص
      final summaryData = {
        'totalRevenue': _totalRevenue,
        'totalExpenses': _totalExpenses,
        'grossProfit': _grossProfit,
        'netProfit': _netProfit,
      };
      
      // تصدير التقرير إلى PDF
      final pdfPath = await PaginatedPdfExport.exportProfitLossReport(
        context: context,
        title: 'تقرير الأرباح والخسائر',
        startDate: _startDate,
        endDate: _endDate,
        tableData: tableData,
        summaryData: summaryData,
      );
      
      if (pdfPath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير بنجاح إلى: $pdfPath'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
