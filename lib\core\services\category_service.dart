import 'package:flutter/foundation.dart';
import '../../shared/models/category.dart';
import '../database/database_helper.dart';

class CategoryService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all categories
  Future<List<CategoryModel>> getCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> categoryMaps = await db.query('categories');

      return categoryMaps.map((map) => CategoryModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories: $e');
      }
      return [];
    }
  }

  // Get categories by type
  Future<List<CategoryModel>> getCategoriesByType(String type) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> categoryMaps = await db.query(
        'categories',
        where: 'type = ?',
        whereArgs: [type],
      );

      return categoryMaps.map((map) => CategoryModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories by type: $e');
      }
      return [];
    }
  }

  // Get category by ID
  Future<CategoryModel?> getCategoryById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> categoryMaps = await db.query(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (categoryMaps.isNotEmpty) {
        return CategoryModel.fromMap(categoryMaps.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting category by ID: $e');
      }
      return null;
    }
  }

  // Add a new category
  Future<CategoryModel?> addCategory(CategoryModel category) async {
    try {
      final db = await _dbHelper.database;

      // Prepare data for insertion
      final Map<String, dynamic> categoryData = {
        'name': category.name,
        'type': category.type,
        'description': category.description,
        'status': category.isActive ? 'active' : 'inactive',
        'created_at': DateTime.now().toIso8601String(),
      };

      // Insert the category
      final id = await db.insert('categories', categoryData);

      if (id > 0) {
        // Return the new category with the generated ID
        return category.copyWith(
          id: id,
          createdAt: DateTime.now(),
        );
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding category: $e');
      }
      return null;
    }
  }

  // Update an existing category
  Future<CategoryModel?> updateCategory(CategoryModel category) async {
    try {
      final db = await _dbHelper.database;

      // Prepare data for update
      final Map<String, dynamic> categoryData = {
        'name': category.name,
        'type': category.type,
        'description': category.description,
        'status': category.isActive ? 'active' : 'inactive',
      };

      // Update the category
      final rowsAffected = await db.update(
        'categories',
        categoryData,
        where: 'id = ?',
        whereArgs: [category.id],
      );

      if (rowsAffected > 0) {
        // Return the updated category
        return category;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating category: $e');
      }
      return null;
    }
  }

  // Delete a category
  Future<bool> deleteCategory(int id) async {
    try {
      final db = await _dbHelper.database;

      // Delete the category
      final rowsAffected = await db.delete(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );

      return rowsAffected > 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting category: $e');
      }
      return false;
    }
  }
}
