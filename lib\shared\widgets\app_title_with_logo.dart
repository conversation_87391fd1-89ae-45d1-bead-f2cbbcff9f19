import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// عنصر يعرض عنوان التطبيق مع شعار الثلج
class AppTitleWithLogo extends StatelessWidget {
  /// عنوان التطبيق
  final String title;

  /// حجم الأيقونة
  final double iconSize;

  /// إظهار الأيقونة
  final bool showIcon;

  /// لون النص
  final Color? textColor;

  /// نمط النص
  final TextStyle? textStyle;

  const AppTitleWithLogo({
    super.key,
    required this.title,
    this.iconSize = 24.0,
    this.showIcon = true,
    this.textColor,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final defaultTextStyle = Theme.of(context).appBarTheme.titleTextStyle ??
                            AppTextStyles.headingSmall;

    final effectiveTextStyle = textStyle ??
                              defaultTextStyle.copyWith(
                                color: textColor ?? AppColors.textOnPrimary,
                              );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showIcon) ...[
          Image.asset(
            'assets/images/snowflake_logo.png',
            width: iconSize,
            height: iconSize,
            color: textColor ?? AppColors.textOnPrimary,
          ),
          const SizedBox(width: 8),
        ],
        Flexible(
          child: Text(
            title,
            style: effectiveTextStyle,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
