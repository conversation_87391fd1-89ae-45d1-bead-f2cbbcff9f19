import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../database/database_helper.dart';
import '../../shared/models/service_request.dart';
import './cash_box_repository.dart';
import '../../shared/models/cash_box.dart';

class ServiceRequestRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();

  // Get all service requests
  Future<List<ServiceRequest>> getAllServiceRequests() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('service_requests');

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service requests: $e');
      }
      return [];
    }
  }

  // Get service request by ID
  Future<ServiceRequest?> getServiceRequestById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        final requests = await _mapServiceRequests(maps);
        return requests.isNotEmpty ? requests.first : null;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service request by ID: $e');
      }
      return null;
    }
  }

  // Get service requests by customer ID
  Future<List<ServiceRequest>> getServiceRequestsByCustomerId(int customerId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'customer_id = ?',
        whereArgs: [customerId],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service requests by customer ID: $e');
      }
      return [];
    }
  }

  // Get service requests by status
  Future<List<ServiceRequest>> getServiceRequestsByStatus(ServiceRequestStatus status) async {
    try {
      final db = await _dbHelper.database;
      final statusStr = status.toString().split('.').last;

      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'status = ?',
        whereArgs: [statusStr],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service requests by status: $e');
      }
      return [];
    }
  }

  // Get service requests assigned to employee
  Future<List<ServiceRequest>> getServiceRequestsByAssignedTo(int employeeId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'assigned_to = ?',
        whereArgs: [employeeId],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service requests by assigned employee: $e');
      }
      return [];
    }
  }

  // Get service requests by category ID
  Future<List<ServiceRequest>> getServiceRequestsByCategoryId(int categoryId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'category_id = ?',
        whereArgs: [categoryId],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service requests by category ID: $e');
      }
      return [];
    }
  }

  // Insert a new service request
  Future<int> insertServiceRequest(ServiceRequest request) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'service_requests',
        {
          'request_number': request.reference,
          'customer_id': request.customerId,
          'service_type': request.requestType.toString().split('.').last,
          'description': request.description,
          'address': request.location,
          'scheduled_date': request.scheduledDate.toIso8601String(),
          'status': request.status.toString().split('.').last,
          'assigned_to': request.assignedTo,
          'technician_daily_rate': request.technicianDailyRate,
          'completion_date': request.completedDate?.toIso8601String(),
          'solution': request.solution,
          'used_parts': request.usedParts != null ? json.encode(request.usedParts) : null,
          'reminder_minutes': request.reminderMinutes,
          'service_amount': request.serviceAmount,
          'used_inventory_ids': request.usedInventoryIds != null ? json.encode(request.usedInventoryIds) : null,
          'used_inventory_quantities': request.usedInventoryQuantities != null ? json.encode(request.usedInventoryQuantities) : null,
          'problem_images': request.problemImages != null ? json.encode(request.problemImages) : null,
          'solution_images': request.solutionImages != null ? json.encode(request.solutionImages) : null,
          'device_type': request.deviceType,
          'device_brand': request.deviceBrand,
          'device_model': request.deviceModel,
          'serial_number': request.serialNumber,
          'installation_date': request.installationDate,
          'warranty_info': request.warrantyInfo,
          'technical_notes': request.technicalNotes,
          'category_id': request.categoryId,
          'category_name': request.categoryName,
          'notes': request.notes,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting service request: $e');
      }
      return -1;
    }
  }

  // Update an existing service request
  Future<int> updateServiceRequest(ServiceRequest request) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'service_requests',
        {
          'request_number': request.reference,
          'customer_id': request.customerId,
          'service_type': request.requestType.toString().split('.').last,
          'description': request.description,
          'address': request.location,
          'scheduled_date': request.scheduledDate.toIso8601String(),
          'status': request.status.toString().split('.').last,
          'assigned_to': request.assignedTo,
          'technician_daily_rate': request.technicianDailyRate,
          'completion_date': request.completedDate?.toIso8601String(),
          'solution': request.solution,
          'used_parts': request.usedParts != null ? json.encode(request.usedParts) : null,
          'reminder_minutes': request.reminderMinutes,
          'service_amount': request.serviceAmount,
          'used_inventory_ids': request.usedInventoryIds != null ? json.encode(request.usedInventoryIds) : null,
          'used_inventory_quantities': request.usedInventoryQuantities != null ? json.encode(request.usedInventoryQuantities) : null,
          'problem_images': request.problemImages != null ? json.encode(request.problemImages) : null,
          'solution_images': request.solutionImages != null ? json.encode(request.solutionImages) : null,
          'device_type': request.deviceType,
          'device_brand': request.deviceBrand,
          'device_model': request.deviceModel,
          'serial_number': request.serialNumber,
          'installation_date': request.installationDate,
          'warranty_info': request.warrantyInfo,
          'technical_notes': request.technicalNotes,
          'category_id': request.categoryId,
          'category_name': request.categoryName,
          'notes': request.notes,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [request.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating service request: $e');
      }
      return 0;
    }
  }

  // Delete a service request
  Future<int> deleteServiceRequest(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'service_requests',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting service request: $e');
      }
      return 0;
    }
  }

  // Get service requests by date range
  Future<List<ServiceRequest>> getServiceRequestsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'scheduled_date BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service requests by date range: $e');
      }
      return [];
    }
  }

  // Get upcoming service requests for the next week
  Future<List<ServiceRequest>> getUpcomingServiceRequestsWeek() async {
    try {
      final db = await _dbHelper.database;
      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));

      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'scheduled_date BETWEEN ? AND ? AND status != ?',
        whereArgs: [
          now.toIso8601String(),
          nextWeek.toIso8601String(),
          'completed',
        ],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting upcoming service requests for week: $e');
      }
      return [];
    }
  }

  // Get upcoming service requests (scheduled for the next 24 hours)
  Future<List<ServiceRequest>> getUpcomingServiceRequests() async {
    try {
      final db = await _dbHelper.database;
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));

      // Get service requests scheduled between now and tomorrow
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        where: 'scheduled_date BETWEEN ? AND ? AND status != ? AND status != ?',
        whereArgs: [
          now.toIso8601String(),
          tomorrow.toIso8601String(),
          'completed',
          'cancelled',
        ],
      );

      return await _mapServiceRequests(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting upcoming service requests: $e');
      }
      return [];
    }
  }

  // Update service request status
  Future<int> updateServiceRequestStatus(int id, ServiceRequestStatus status) async {
    try {
      final db = await _dbHelper.database;
      final statusStr = status.toString().split('.').last;

      // If status is completed, also set completion date
      if (status == ServiceRequestStatus.completed) {
        return await db.update(
          'service_requests',
          {
            'status': statusStr,
            'completion_date': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [id],
        );
      } else {
        return await db.update(
          'service_requests',
          {'status': statusStr},
          where: 'id = ?',
          whereArgs: [id],
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating service request status: $e');
      }
      return 0;
    }
  }

  // Update service request category
  Future<int> updateServiceRequestCategory(int id, int categoryId, String categoryName) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'service_requests',
        {
          'category_id': categoryId,
          'category_name': categoryName,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating service request category: $e');
      }
      return 0;
    }
  }

  // Assign service request to employee
  Future<int> assignServiceRequest(int id, int employeeId) async {
    try {
      final db = await _dbHelper.database;

      // Get employee name
      final employeeMaps = await db.query(
        'employees',
        columns: ['name'],
        where: 'id = ?',
        whereArgs: [employeeId],
      );

      String? employeeName;
      if (employeeMaps.isNotEmpty) {
        employeeName = employeeMaps.first['name'] as String;
      }

      return await db.update(
        'service_requests',
        {
          'assigned_to': employeeId,
          'assigned_to_name': employeeName,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error assigning service request: $e');
      }
      return 0;
    }
  }



  // Save problem images for a service request
  Future<bool> saveProblemImages(String requestNumber, List<File> imageFiles) async {
    try {
      // Get service request ID
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_requests',
        columns: ['id', 'problem_images'],
        where: 'request_number = ?',
        whereArgs: [requestNumber],
      );

      if (maps.isEmpty) {
        return false;
      }

      final int requestId = maps.first['id'] as int;
      List<String> existingImages = [];

      // Get existing images if any
      if (maps.first['problem_images'] != null) {
        existingImages = List<String>.from(json.decode(maps.first['problem_images'] as String));
      }

      // Save images to app directory
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/service_request_images');
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // Save each image and add path to list
      List<String> newImagePaths = [];
      for (var imageFile in imageFiles) {
        final fileName = '${requestNumber}_problem_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final savedImagePath = '${imagesDir.path}/$fileName';
        await imageFile.copy(savedImagePath);
        newImagePaths.add(savedImagePath);
      }

      // Add new images
      existingImages.addAll(newImagePaths);

      // Update service request with new images
      await db.update(
        'service_requests',
        {
          'problem_images': json.encode(existingImages),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [requestId],
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving problem images: $e');
      }
      return false;
    }
  }

  // Helper method to parse service request status
  ServiceRequestStatus _parseServiceRequestStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ServiceRequestStatus.pending;
      case 'in_progress':
      case 'inprogress':
        return ServiceRequestStatus.inProgress;
      case 'completed':
        return ServiceRequestStatus.completed;
      case 'cancelled':
        return ServiceRequestStatus.cancelled;
      default:
        return ServiceRequestStatus.pending;
    }
  }

  // Helper method to parse service request type
  ServiceRequestType _parseRequestType(String type) {
    switch (type.toLowerCase()) {
      case 'installation':
        return ServiceRequestType.installation;
      case 'maintenance':
        return ServiceRequestType.maintenance;
      case 'repair':
        return ServiceRequestType.repair;
      case 'inspection':
        return ServiceRequestType.inspection;
      default:
        return ServiceRequestType.other;
    }
  }

  // Helper method to map database results to ServiceRequest objects
  Future<List<ServiceRequest>> _mapServiceRequests(List<Map<String, dynamic>> maps) async {
    final db = await _dbHelper.database;
    final List<ServiceRequest> requests = [];

    for (final map in maps) {
      // Get customer name
      String customerName = 'عميل غير معروف';
      String? customerPhone;

      if (map['customer_id'] != null) {
        final customerMaps = await db.query(
          'customers',
          columns: ['name', 'phone'],
          where: 'id = ?',
          whereArgs: [map['customer_id']],
        );

        if (customerMaps.isNotEmpty) {
          customerName = customerMaps.first['name'] as String;
          customerPhone = customerMaps.first['phone'] as String?;
        }
      }

      // Get assigned employee name
      String? assignedToName;

      if (map['assigned_to'] != null) {
        final employeeMaps = await db.query(
          'employees',
          columns: ['name'],
          where: 'id = ?',
          whereArgs: [map['assigned_to']],
        );

        if (employeeMaps.isNotEmpty) {
          assignedToName = employeeMaps.first['name'] as String;
        }
      }

      // Get category name if not already in the record
      int? categoryId = map['category_id'] as int?;
      String? categoryName = map['category_name'] as String?;

      if (categoryId != null && categoryName == null) {
        final categoryMaps = await db.query(
          'service_categories',
          columns: ['name'],
          where: 'id = ?',
          whereArgs: [categoryId],
        );

        if (categoryMaps.isNotEmpty) {
          categoryName = categoryMaps.first['name'] as String;
        }
      }

      requests.add(ServiceRequest(
        id: map['id'] as int,
        reference: map['request_number'] as String,
        customerId: map['customer_id'] as int?,
        customerName: customerName,
        customerPhone: customerPhone,
        requestType: _parseRequestType(map['service_type'] as String),
        description: map['description'] as String,
        priority: ServiceRequestPriority.medium, // Default priority
        status: _parseServiceRequestStatus(map['status'] as String),
        location: map['address'] as String?,
        assignedTo: map['assigned_to'] as int?,
        assignedToName: assignedToName,
        technicianDailyRate: map['technician_daily_rate'] != null
            ? (map['technician_daily_rate'] as num).toDouble()
            : null,
        scheduledDate: DateTime.parse(map['scheduled_date'] as String),
        completedDate: map['completion_date'] != null
            ? DateTime.parse(map['completion_date'] as String)
            : null,
        solution: map['solution'] as String?,
        reminderMinutes: map['reminder_minutes'] != null
            ? map['reminder_minutes'] as int
            : 60, // القيمة الافتراضية هي 60 دقيقة (ساعة واحدة)
        serviceAmount: map['service_amount'] != null
            ? (map['service_amount'] as num).toDouble()
            : 0, // مبلغ الخدمة
        usedParts: map['used_parts'] != null
            ? List<String>.from(json.decode(map['used_parts'] as String))
            : null,
        usedInventoryIds: map['used_inventory_ids'] != null
            ? List<int>.from(json.decode(map['used_inventory_ids'] as String))
            : null,
        usedInventoryQuantities: map['used_inventory_quantities'] != null
            ? List<int>.from(json.decode(map['used_inventory_quantities'] as String))
            : null,
        problemImages: map['problem_images'] != null
            ? List<String>.from(json.decode(map['problem_images'] as String))
            : null,
        solutionImages: map['solution_images'] != null
            ? List<String>.from(json.decode(map['solution_images'] as String))
            : null,
        deviceType: map['device_type'] as String?,
        deviceBrand: map['device_brand'] as String?,
        deviceModel: map['device_model'] as String?,
        serialNumber: map['serial_number'] as String?,
        installationDate: map['installation_date'] as String?,
        warrantyInfo: map['warranty_info'] as String?,
        technicalNotes: map['technical_notes'] as String?,
        categoryId: categoryId,
        categoryName: categoryName,
        invoiceId: map['invoice_id'] as int?,
        notes: map['notes'] as String?,
        createdAt: DateTime.parse(map['created_at'] as String),
        updatedAt: map['updated_at'] != null
            ? DateTime.parse(map['updated_at'] as String)
            : null,
      ));
    }

    return requests;
  }

  // Record service payment through cash box
  Future<bool> recordServicePayment({
    required int serviceRequestId,
    required int cashBoxId,
    required double amount,
    String? paymentMethod,
    String? notes,
  }) async {
    try {
      final db = await _dbHelper.database;

      return await db.transaction((txn) async {
        // Get service request details
        final serviceData = await txn.query(
          'service_requests',
          where: 'id = ?',
          whereArgs: [serviceRequestId],
        );

        if (serviceData.isEmpty) {
          throw Exception('Service request not found');
        }

        final service = serviceData.first;
        final serviceAmount = (service['service_amount'] as double?) ?? 0.0;
        final paidAmount = (service['paid_amount'] as double?) ?? 0.0;
        final remainingAmount = serviceAmount - paidAmount;

        if (amount > remainingAmount) {
          throw Exception('Payment amount exceeds remaining balance');
        }

        // Update service request paid amount
        final newPaidAmount = paidAmount + amount;
        final newStatus = newPaidAmount >= serviceAmount ? 'paid' : 'partially_paid';

        await txn.update(
          'service_requests',
          {
            'paid_amount': newPaidAmount,
            'payment_status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [serviceRequestId],
        );

        // Create cash box transaction
        final cashBoxTransaction = CashBoxTransaction(
          cashBoxId: cashBoxId,
          type: CashBoxTransactionType.income,
          amount: amount,
          description: notes ?? 'Payment for Service #${service['request_number']}',
          reference: serviceRequestId.toString(),
          referenceType: CashBoxReferenceType.service,
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
        );

        // Add transaction to cash box (this will update the balance)
        await _cashBoxRepository.addTransaction(cashBoxTransaction);

        if (kDebugMode) {
          print('Service payment recorded: Service $serviceRequestId, Amount: $amount');
        }

        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error recording service payment: $e');
      }
      rethrow;
    }
  }

  /// Complete service request with automatic technician payment
  Future<bool> completeServiceRequestWithPayment({
    required int serviceRequestId,
    required String solution,
    required int cashBoxId,
    String? notes,
  }) async {
    try {
      final db = await _dbHelper.database;

      // Start transaction
      return await db.transaction((txn) async {
        // Get service request details
        final serviceRequestMaps = await txn.query(
          'service_requests',
          where: 'id = ?',
          whereArgs: [serviceRequestId],
        );

        if (serviceRequestMaps.isEmpty) {
          throw Exception('طلب الخدمة غير موجود');
        }

        final serviceRequestMap = serviceRequestMaps.first;
        final technicianDailyRate = serviceRequestMap['technician_daily_rate'] as double?;
        final assignedTo = serviceRequestMap['assigned_to'] as int?;
        final requestNumber = serviceRequestMap['request_number'] as String;

        // Update service request status to completed
        await txn.update(
          'service_requests',
          {
            'status': 'completed',
            'completion_date': DateTime.now().toIso8601String(),
            'solution': solution,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [serviceRequestId],
        );

        // If technician has daily rate, deduct payment from cash box
        if (technicianDailyRate != null && technicianDailyRate > 0 && assignedTo != null) {
          // Get technician name
          final employeeMaps = await txn.query(
            'employees',
            columns: ['name'],
            where: 'id = ?',
            whereArgs: [assignedTo],
          );

          String technicianName = 'فني غير محدد';
          if (employeeMaps.isNotEmpty) {
            technicianName = employeeMaps.first['name'] as String;
          }

          // Check cash box balance
          final cashBoxMaps = await txn.query(
            'cash_boxes',
            columns: ['current_balance', 'name'],
            where: 'id = ? AND is_active = 1',
            whereArgs: [cashBoxId],
          );

          if (cashBoxMaps.isEmpty) {
            throw Exception('الصندوق المحدد غير موجود أو غير نشط');
          }

          final currentBalance = cashBoxMaps.first['current_balance'] as double;
          final cashBoxName = cashBoxMaps.first['name'] as String;

          if (currentBalance < technicianDailyRate) {
            throw Exception('رصيد الصندوق غير كافي. الرصيد الحالي: ${currentBalance.toStringAsFixed(2)} ر.س، المطلوب: ${technicianDailyRate.toStringAsFixed(2)} ر.س');
          }

          // Deduct amount from cash box
          await txn.update(
            'cash_boxes',
            {
              'current_balance': currentBalance - technicianDailyRate,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [cashBoxId],
          );

          // Record cash box transaction
          await txn.insert(
            'cash_box_transactions',
            {
              'cash_box_id': cashBoxId,
              'type': 'expense',
              'amount': technicianDailyRate,
              'description': 'أجر فني يومي - $technicianName',
              'reference': requestNumber,
              'reference_type': 'service_request',
              'transaction_date': DateTime.now().toIso8601String(),
              'created_at': DateTime.now().toIso8601String(),
            },
          );

          if (kDebugMode) {
            print('✅ تم خصم أجر الفني: ${technicianDailyRate.toStringAsFixed(2)} ر.س من $cashBoxName');
            print('📊 الرصيد الجديد: ${(currentBalance - technicianDailyRate).toStringAsFixed(2)} ر.س');
          }
        }

        if (kDebugMode) {
          print('✅ تم إكمال طلب الخدمة رقم: $requestNumber');
        }

        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إكمال طلب الخدمة مع الدفع: $e');
      }
      rethrow;
    }
  }

  /// Get technician expenses for a specific period
  Future<Map<String, dynamic>> getTechnicianExpenses({
    required DateTime startDate,
    required DateTime endDate,
    int? technicianId,
  }) async {
    try {
      final db = await _dbHelper.database;

      String whereClause = '''
        cbt.type = 'expense'
        AND cbt.reference_type = 'service_request'
        AND DATE(cbt.transaction_date) BETWEEN DATE(?) AND DATE(?)
      ''';

      List<dynamic> whereArgs = [
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ];

      if (technicianId != null) {
        whereClause += ' AND sr.assigned_to = ?';
        whereArgs.add(technicianId);
      }

      final query = '''
        SELECT
          cbt.*,
          sr.assigned_to,
          e.name as technician_name,
          e.position as technician_position,
          cb.name as cash_box_name
        FROM cash_box_transactions cbt
        LEFT JOIN service_requests sr ON cbt.reference = sr.request_number
        LEFT JOIN employees e ON sr.assigned_to = e.id
        LEFT JOIN cash_boxes cb ON cbt.cash_box_id = cb.id
        WHERE $whereClause
        ORDER BY cbt.transaction_date DESC
      ''';

      final results = await db.rawQuery(query, whereArgs);

      double totalExpenses = 0;
      int totalServices = results.length;
      Map<String, double> expensesByTechnician = {};
      Map<String, int> servicesByTechnician = {};

      for (final row in results) {
        final amount = row['amount'] as double;
        final technicianName = row['technician_name'] as String? ?? 'فني غير محدد';

        totalExpenses += amount;
        expensesByTechnician[technicianName] = (expensesByTechnician[technicianName] ?? 0) + amount;
        servicesByTechnician[technicianName] = (servicesByTechnician[technicianName] ?? 0) + 1;
      }

      return {
        'total_expenses': totalExpenses,
        'total_services': totalServices,
        'expenses_by_technician': expensesByTechnician,
        'services_by_technician': servicesByTechnician,
        'transactions': results,
        'period': {
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب مصروفات الفنيين: $e');
      }
      rethrow;
    }
  }
}
