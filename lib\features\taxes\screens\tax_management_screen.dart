import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/ui_components.dart';
import '../../../core/repositories/tax_repository.dart';
import '../../../shared/models/tax.dart';

/// شاشة إدارة الضرائب
class TaxManagementScreen extends StatefulWidget {
  const TaxManagementScreen({super.key});

  @override
  State<TaxManagementScreen> createState() => _TaxManagementScreenState();
}

class _TaxManagementScreenState extends State<TaxManagementScreen> {
  final TaxRepository _taxRepository = TaxRepository();

  bool _isLoading = true;
  List<Tax> _taxes = [];

  @override
  void initState() {
    super.initState();
    _loadTaxes();
  }

  /// تحميل الضرائب
  Future<void> _loadTaxes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _taxes = await _taxRepository.getAllTaxes();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل الضرائب: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الضرائب'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTaxes,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showTaxDialog(context),
        tooltip: 'إضافة ضريبة جديدة',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent() {
    return _taxes.isEmpty
        ? _buildEmptyState()
        : _buildTaxesList();
  }

  Widget _buildEmptyState() {
    return EmptyState(
      icon: Icons.receipt_long,
      title: 'لا توجد ضرائب',
      message: 'لم يتم إضافة أي ضرائب بعد. اضغط على زر الإضافة لإضافة ضريبة جديدة.',
      action: StyledButton(
        label: 'إضافة ضريبة',
        icon: Icons.add,
        onPressed: () => _showTaxDialog(context),
      ),
    );
  }

  Widget _buildTaxesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: _taxes.length,
      itemBuilder: (context, index) {
        final tax = _taxes[index];
        return _buildTaxCard(tax);
      },
    );
  }

  Widget _buildTaxCard(Tax tax) {
    return StyledCard(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTaxTypeColor(tax.type),
          child: Icon(
            _getTaxTypeIcon(tax.type),
            color: Colors.white,
          ),
        ),
        title: Row(
          children: [
            Text(
              tax.name,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (tax.isDefault) ...[
              const SizedBox(width: AppDimensions.spacingS),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingXS,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'افتراضي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppDimensions.spacingXS),
            Row(
              children: [
                Text(
                  'النوع: ${Tax.getTaxTypeName(tax.type)}',
                  style: AppTextStyles.labelMedium,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Text(
                  'النسبة: ${tax.rate}%',
                  style: AppTextStyles.labelMedium,
                ),
              ],
            ),
            if (tax.description != null && tax.description!.isNotEmpty) ...[
              const SizedBox(height: AppDimensions.spacingXS),
              Text(
                tax.description!,
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: tax.isActive,
              onChanged: (value) => _toggleTaxStatus(tax),
              activeColor: AppColors.primary,
            ),
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showTaxDialog(context, tax),
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteConfirmation(context, tax),
              tooltip: 'حذف',
              color: AppColors.error,
            ),
          ],
        ),
        onTap: () => _showTaxDialog(context, tax),
      ),
    );
  }

  Color _getTaxTypeColor(TaxType type) {
    switch (type) {
      case TaxType.vat:
        return AppColors.primary;
      case TaxType.incomeTax:
        return AppColors.success;
      case TaxType.salesTax:
        return AppColors.warning;
      case TaxType.other:
        return AppColors.secondary;
    }
  }

  IconData _getTaxTypeIcon(TaxType type) {
    switch (type) {
      case TaxType.vat:
        return Icons.receipt;
      case TaxType.incomeTax:
        return Icons.account_balance;
      case TaxType.salesTax:
        return Icons.shopping_cart;
      case TaxType.other:
        return Icons.category;
    }
  }

  /// تبديل حالة الضريبة (نشطة/غير نشطة)
  Future<void> _toggleTaxStatus(Tax tax) async {
    try {
      final newStatus = !tax.isActive;
      await _taxRepository.toggleTaxStatus(tax.id!, newStatus);

      // تحديث القائمة
      _loadTaxes();

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            newStatus
                ? 'تم تفعيل الضريبة بنجاح'
                : 'تم تعطيل الضريبة بنجاح',
          ),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحديث حالة الضريبة: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// عرض حوار تأكيد الحذف
  void _showDeleteConfirmation(BuildContext context, Tax tax) {
    showDialog(
      context: context,
      builder: (context) {
        return StyledDialog(
          title: 'تأكيد الحذف',
          icon: Icons.delete_forever,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.warning,
                color: AppColors.error,
                size: 48,
              ),
              const SizedBox(height: AppDimensions.spacingM),
              Text(
                'هل أنت متأكد من رغبتك في حذف الضريبة "${tax.name}"؟',
                style: AppTextStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.spacingS),
              const Text(
                'لا يمكن التراجع عن هذا الإجراء.',
                style: TextStyle(
                  color: AppColors.error,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            StyledButton(
              label: 'إلغاء',
              icon: Icons.cancel,
              type: StyledButtonType.secondary,
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            StyledButton(
              label: 'حذف',
              icon: Icons.delete,
              type: StyledButtonType.danger,
              onPressed: () async {
                Navigator.pop(context);
                await _deleteTax(tax);
              },
            ),
          ],
        );
      },
    );
  }

  /// حذف ضريبة
  Future<void> _deleteTax(Tax tax) async {
    try {
      await _taxRepository.deleteTax(tax.id!);

      // تحديث القائمة
      _loadTaxes();

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حذف الضريبة "${tax.name}" بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء حذف الضريبة: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// عرض حوار إضافة/تعديل ضريبة
  void _showTaxDialog(BuildContext context, [Tax? tax]) {
    final isEditing = tax != null;
    final formKey = GlobalKey<FormState>();

    String name = isEditing ? tax.name : '';
    TaxType type = isEditing ? tax.type : TaxType.vat;
    double rate = isEditing ? tax.rate : 15.0;
    bool isActive = isEditing ? tax.isActive : true;
    bool isDefault = isEditing ? tax.isDefault : false;
    String? description = isEditing ? tax.description : null;

    showDialog(
      context: context,
      builder: (context) {
        return StyledDialog(
          title: isEditing ? 'تعديل ضريبة' : 'إضافة ضريبة جديدة',
          icon: isEditing ? Icons.edit : Icons.add,
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // اسم الضريبة
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم الضريبة',
                      prefixIcon: Icon(Icons.label),
                      border: OutlineInputBorder(),
                    ),
                    initialValue: name,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم الضريبة';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      name = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacingM),

                  // نوع الضريبة
                  DropdownButtonFormField<TaxType>(
                    decoration: const InputDecoration(
                      labelText: 'نوع الضريبة',
                      prefixIcon: Icon(Icons.category),
                      border: OutlineInputBorder(),
                    ),
                    value: type,
                    items: TaxType.values.map((taxType) {
                      return DropdownMenuItem<TaxType>(
                        value: taxType,
                        child: Text(Tax.getTaxTypeName(taxType)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      type = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacingM),

                  // نسبة الضريبة
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'نسبة الضريبة (%)',
                      prefixIcon: Icon(Icons.percent),
                      border: OutlineInputBorder(),
                    ),
                    initialValue: rate.toString(),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال نسبة الضريبة';
                      }
                      final doubleValue = double.tryParse(value);
                      if (doubleValue == null || doubleValue < 0 || doubleValue > 100) {
                        return 'يرجى إدخال نسبة صحيحة بين 0 و 100';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      rate = double.parse(value!);
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacingM),

                  // الوصف
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الوصف (اختياري)',
                      prefixIcon: Icon(Icons.description),
                      border: OutlineInputBorder(),
                    ),
                    initialValue: description,
                    maxLines: 3,
                    onSaved: (value) {
                      description = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.spacingM),

                  // الحالة والافتراضي
                  Row(
                    children: [
                      Expanded(
                        child: SwitchListTile(
                          title: const Text('نشطة'),
                          value: isActive,
                          onChanged: (value) {
                            setState(() {
                              isActive = value;
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: SwitchListTile(
                          title: const Text('افتراضية'),
                          value: isDefault,
                          onChanged: (value) {
                            setState(() {
                              isDefault = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            StyledButton(
              label: 'إلغاء',
              icon: Icons.cancel,
              type: StyledButtonType.secondary,
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            StyledButton(
              label: isEditing ? 'تحديث' : 'إضافة',
              icon: isEditing ? Icons.save : Icons.add,
              type: StyledButtonType.primary,
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();

                  Navigator.pop(context);

                  if (isEditing) {
                    await _updateTax(
                      tax.copyWith(
                        name: name,
                        type: type,
                        rate: rate,
                        isActive: isActive,
                        isDefault: isDefault,
                        description: description,
                        updatedAt: DateTime.now(),
                      ),
                    );
                  } else {
                    await _addTax(
                      Tax(
                        name: name,
                        type: type,
                        rate: rate,
                        isActive: isActive,
                        isDefault: isDefault,
                        description: description,
                        createdAt: DateTime.now(),
                      ),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  /// إضافة ضريبة جديدة
  Future<void> _addTax(Tax tax) async {
    try {
      await _taxRepository.insertTax(tax);

      // تحديث القائمة
      _loadTaxes();

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة الضريبة "${tax.name}" بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إضافة الضريبة: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  /// تحديث ضريبة
  Future<void> _updateTax(Tax tax) async {
    try {
      await _taxRepository.updateTax(tax);

      // تحديث القائمة
      _loadTaxes();

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحديث الضريبة "${tax.name}" بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحديث الضريبة: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
