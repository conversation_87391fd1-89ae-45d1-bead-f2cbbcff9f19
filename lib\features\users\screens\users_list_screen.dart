import 'package:flutter/material.dart';
import 'package:icecorner/config/constants.dart';
import 'package:provider/provider.dart';
import '../../../config/routes.dart';
import '../../../shared/models/user.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/empty_state.dart';
import '../../../state/user_state.dart';
import '../widgets/user_list_item.dart';

class UsersListScreen extends StatefulWidget {
  const UsersListScreen({super.key});

  @override
  State<UsersListScreen> createState() => _UsersListScreenState();
}

class _UsersListScreenState extends State<UsersListScreen> {
  bool _isLoading = false;
  String? _searchQuery;

  @override
  void initState() {
    super.initState();
    // Use a post-frame callback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadUsers();
      }
    });
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    await Provider.of<UserState>(context, listen: false).loadUsers();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<User> _getFilteredUsers() {
    final users = Provider.of<UserState>(context, listen: false).users;

    if (_searchQuery == null || _searchQuery!.isEmpty) {
      return users;
    }

    final query = _searchQuery!.toLowerCase();
    return users.where((user) {
      return user.name.toLowerCase().contains(query) ||
             (user.email?.toLowerCase().contains(query) ?? false) ||
             (user.phone?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  void _showDeleteConfirmation(User user) {
    // Get the BuildContext before any async operations
    final currentContext = context;

    showDialog(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستخدم "${user.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // Close the dialog first
              Navigator.pop(dialogContext);

              // Then perform the delete operation
              _deleteUser(user);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // Separate method to handle the delete operation
  Future<void> _deleteUser(User user) async {
    if (!mounted) return;

    // Get references before any async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await Provider.of<UserState>(context, listen: false)
        .deleteUser(user.id.toString()); // Convert id to String

    if (!mounted) return;

    // Show the snackbar
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(
          success
              ? 'تم حذف المستخدم بنجاح'
              : 'فشل حذف المستخدم',
        ),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المستخدمين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsers,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRoutes.userEdit).then((_) {
            _loadUsers();
          });
        },
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن مستخدم...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Users list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Consumer<UserState>(
                    builder: (context, userState, child) {
                      final filteredUsers = _getFilteredUsers();

                      if (filteredUsers.isEmpty) {
                        return const EmptyState(
                          icon: Icons.people,
                          title: 'لا يوجد مستخدمين',
                          message: 'لم يتم العثور على أي مستخدمين. يمكنك إضافة مستخدم جديد بالضغط على زر الإضافة.',
                        );
                      }

                      return RefreshIndicator(
                        onRefresh: _loadUsers,
                        child: ListView.builder(
                          itemCount: filteredUsers.length,
                          itemBuilder: (context, index) {
                            final user = filteredUsers[index];
                            return UserListItem(
                              user: user,
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.userDetails,
                                  arguments: user,
                                ).then((_) {
                                  _loadUsers();
                                });
                              },
                              onEdit: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.userEdit,
                                  arguments: user,
                                ).then((_) {
                                  _loadUsers();
                                });
                              },
                              onDelete: () => _showDeleteConfirmation(user),
                              onPermissions: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.userPermissions,
                                  arguments: user,
                                ).then((_) {
                                  _loadUsers();
                                });
                              },
                            );
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
