import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';

class ReportFilterDialog extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final String filterType;
  final Function(DateTime, DateTime, String) onApplyFilter;

  const ReportFilterDialog({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.filterType,
    required this.onApplyFilter,
  });

  @override
  State<ReportFilterDialog> createState() => _ReportFilterDialogState();
}

class _ReportFilterDialogState extends State<ReportFilterDialog> {
  late DateTime _startDate;
  late DateTime _endDate;
  late String _filterType;

  @override
  void initState() {
    super.initState();
    _startDate = widget.startDate;
    _endDate = widget.endDate;
    _filterType = widget.filterType;
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // Ensure end date is not before start date
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = picked;
          // Ensure start date is not after end date
          if (_startDate.isAfter(_endDate)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }

  void _applyPredefinedFilter(String filterType) {
    final now = DateTime.now();
    setState(() {
      _filterType = filterType;
      switch (filterType) {
        case 'today':
          _startDate = DateTime(now.year, now.month, now.day);
          _endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case 'yesterday':
          final yesterday = now.subtract(const Duration(days: 1));
          _startDate = DateTime(yesterday.year, yesterday.month, yesterday.day);
          _endDate = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59);
          break;
        case 'thisWeek':
          // Find the first day of the week (Sunday)
          final firstDayOfWeek = now.subtract(Duration(days: now.weekday % 7));
          _startDate = DateTime(firstDayOfWeek.year, firstDayOfWeek.month, firstDayOfWeek.day);
          _endDate = now;
          break;
        case 'thisMonth':
          _startDate = DateTime(now.year, now.month, 1);
          _endDate = now;
          break;
        case 'lastMonth':
          final lastMonth = DateTime(now.year, now.month - 1, 1);
          _startDate = lastMonth;
          _endDate = DateTime(now.year, now.month, 0, 23, 59, 59);
          break;
        case 'thisYear':
          _startDate = DateTime(now.year, 1, 1);
          _endDate = now;
          break;
        case 'custom':
          // Keep current dates
          break;
        default: // 'all'
          _startDate = DateTime(2020, 1, 1);
          _endDate = now;
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تصفية التقرير'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الفترة الزمنية',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Wrap(
              spacing: 8,
              children: [
                _buildFilterChip('الكل', 'all'),
                _buildFilterChip('اليوم', 'today'),
                _buildFilterChip('أمس', 'yesterday'),
                _buildFilterChip('هذا الأسبوع', 'thisWeek'),
                _buildFilterChip('هذا الشهر', 'thisMonth'),
                _buildFilterChip('الشهر الماضي', 'lastMonth'),
                _buildFilterChip('هذا العام', 'thisYear'),
                _buildFilterChip('مخصص', 'custom'),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            if (_filterType == 'custom') ...[
              const Text(
                'تاريخ البداية',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: AppDimensions.paddingS),
              InkWell(
                onTap: () => _selectDate(context, true),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(DateFormat('dd/MM/yyyy').format(_startDate)),
                      const Icon(Icons.calendar_today, size: 20),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Text(
                'تاريخ النهاية',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: AppDimensions.paddingS),
              InkWell(
                onTap: () => _selectDate(context, false),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(DateFormat('dd/MM/yyyy').format(_endDate)),
                      const Icon(Icons.calendar_today, size: 20),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApplyFilter(_startDate, _endDate, _filterType);
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('تطبيق'),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, String value) {
    return FilterChip(
      label: Text(label),
      selected: _filterType == value,
      onSelected: (selected) {
        if (selected) {
          _applyPredefinedFilter(value);
        }
      },
      selectedColor: AppColors.primary.withAlpha(51),
      checkmarkColor: AppColors.primary,
    );
  }
}
