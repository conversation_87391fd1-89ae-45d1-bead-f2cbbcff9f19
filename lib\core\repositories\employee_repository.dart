import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/employee.dart';

class EmployeeRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all employees
  Future<List<Employee>> getAllEmployees() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('employees');
      
      return List.generate(maps.length, (i) {
        return Employee(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          email: maps[i]['email'] as String?,
          phone: maps[i]['phone'] as String?,
          address: maps[i]['address'] as String?,
          position: maps[i]['position'] as String,
          joinDate: DateTime.parse(maps[i]['join_date'] as String),
          salary: maps[i]['salary'] as double,
          paymentType: _parsePaymentType(maps[i]['payment_type'] as String?),
          status: _parseStatus(maps[i]['status'] as String),
          nationalId: maps[i]['national_id'] as String?,
          bankAccount: maps[i]['bank_account'] as String?,
          notes: maps[i]['notes'] as String?,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          updatedAt: maps[i]['updated_at'] != null
              ? DateTime.parse(maps[i]['updated_at'] as String)
              : null,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting employees: $e');
      }
      return [];
    }
  }

  // Get employee by ID
  Future<Employee?> getEmployeeById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'employees',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Employee(
          id: maps[0]['id'] as int,
          name: maps[0]['name'] as String,
          email: maps[0]['email'] as String?,
          phone: maps[0]['phone'] as String?,
          address: maps[0]['address'] as String?,
          position: maps[0]['position'] as String,
          joinDate: DateTime.parse(maps[0]['join_date'] as String),
          salary: maps[0]['salary'] as double,
          paymentType: _parsePaymentType(maps[0]['payment_type'] as String?),
          status: _parseStatus(maps[0]['status'] as String),
          nationalId: maps[0]['national_id'] as String?,
          bankAccount: maps[0]['bank_account'] as String?,
          notes: maps[0]['notes'] as String?,
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
          updatedAt: maps[0]['updated_at'] != null 
              ? DateTime.parse(maps[0]['updated_at'] as String) 
              : null,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting employee by ID: $e');
      }
      return null;
    }
  }

  // Insert a new employee
  Future<int> insertEmployee(Employee employee) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'employees',
        {
          'name': employee.name,
          'email': employee.email,
          'phone': employee.phone,
          'address': employee.address,
          'position': employee.position,
          'join_date': employee.joinDate.toIso8601String(),
          'salary': employee.salary,
          'payment_type': employee.paymentType.toString().split('.').last,
          'status': employee.status.toString().split('.').last,
          'national_id': employee.nationalId,
          'bank_account': employee.bankAccount,
          'notes': employee.notes,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error inserting employee: $e');
      }
      return -1;
    }
  }

  // Update an existing employee
  Future<int> updateEmployee(Employee employee) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'employees',
        {
          'name': employee.name,
          'email': employee.email,
          'phone': employee.phone,
          'address': employee.address,
          'position': employee.position,
          'join_date': employee.joinDate.toIso8601String(),
          'salary': employee.salary,
          'payment_type': employee.paymentType.toString().split('.').last,
          'status': employee.status.toString().split('.').last,
          'national_id': employee.nationalId,
          'bank_account': employee.bankAccount,
          'notes': employee.notes,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [employee.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating employee: $e');
      }
      return 0;
    }
  }

  // Delete an employee
  Future<int> deleteEmployee(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'employees',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting employee: $e');
      }
      return 0;
    }
  }

  // Helper method to parse status string to enum
  EmployeeStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return EmployeeStatus.active;
      case 'inactive':
        return EmployeeStatus.inactive;
      case 'onleave':
      case 'on_leave':
        return EmployeeStatus.onLeave;
      case 'terminated':
        return EmployeeStatus.terminated;
      default:
        return EmployeeStatus.active;
    }
  }

  PaymentType _parsePaymentType(String? paymentType) {
    if (paymentType == null) return PaymentType.monthly;

    switch (paymentType.toLowerCase()) {
      case 'monthly':
        return PaymentType.monthly;
      case 'daily':
        return PaymentType.daily;
      default:
        return PaymentType.monthly;
    }
  }
}
