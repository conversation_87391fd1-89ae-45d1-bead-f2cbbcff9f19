import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../database/database_helper.dart';

/// Comprehensive error handling service for HVAC Service Manager
class AppErrorHandler {
  static final AppErrorHandler _instance = AppErrorHandler._internal();
  factory AppErrorHandler() => _instance;
  AppErrorHandler._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// Handle and log errors
  static Future<void> handleError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalData,
    bool showToUser = true,
    BuildContext? buildContext,
  }) async {
    try {
      final errorInfo = _analyzeError(error);
      
      // Log error
      await _logError(
        error: error,
        stackTrace: stackTrace,
        context: context,
        errorInfo: errorInfo,
        additionalData: additionalData,
      );

      // Show user-friendly message if requested
      if (showToUser && buildContext != null) {
        _showErrorToUser(buildContext, errorInfo);
      }

      // Debug logging
      if (kDebugMode) {
        print('🔥 Error in $context: ${errorInfo.userMessage}');
        print('Technical details: ${errorInfo.technicalMessage}');
        if (stackTrace != null) {
          print('Stack trace: $stackTrace');
        }
      }
    } catch (e) {
      // Fallback error handling
      if (kDebugMode) {
        print('❌ Error in error handler: $e');
        print('Original error: $error');
      }
    }
  }

  /// Analyze error and provide user-friendly messages
  static ErrorInfo _analyzeError(dynamic error) {
    if (error is FirebaseAuthException) {
      return _handleFirebaseAuthError(error);
    } else if (error is FirebaseException) {
      return _handleFirebaseError(error);
    } else if (error is FormatException) {
      return ErrorInfo(
        type: ErrorType.validation,
        userMessage: 'البيانات المدخلة غير صحيحة',
        technicalMessage: error.message,
        severity: ErrorSeverity.medium,
      );
    } else if (error is ArgumentError) {
      return ErrorInfo(
        type: ErrorType.validation,
        userMessage: 'خطأ في البيانات المدخلة',
        technicalMessage: error.message ?? error.toString(),
        severity: ErrorSeverity.medium,
      );
    } else if (error is StateError) {
      return ErrorInfo(
        type: ErrorType.application,
        userMessage: 'خطأ في حالة التطبيق',
        technicalMessage: error.message,
        severity: ErrorSeverity.high,
      );
    } else if (error is TypeError) {
      return ErrorInfo(
        type: ErrorType.application,
        userMessage: 'خطأ تقني في التطبيق',
        technicalMessage: error.toString(),
        severity: ErrorSeverity.high,
      );
    } else if (error is Exception) {
      return ErrorInfo(
        type: ErrorType.unknown,
        userMessage: 'حدث خطأ غير متوقع',
        technicalMessage: error.toString(),
        severity: ErrorSeverity.medium,
      );
    } else {
      return ErrorInfo(
        type: ErrorType.unknown,
        userMessage: 'حدث خطأ غير متوقع',
        technicalMessage: error.toString(),
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Handle Firebase Auth errors
  static ErrorInfo _handleFirebaseAuthError(FirebaseAuthException error) {
    String userMessage;
    ErrorSeverity severity = ErrorSeverity.medium;

    switch (error.code) {
      case 'user-not-found':
        userMessage = 'المستخدم غير موجود';
        break;
      case 'wrong-password':
        userMessage = 'كلمة المرور غير صحيحة';
        break;
      case 'email-already-in-use':
        userMessage = 'البريد الإلكتروني مستخدم بالفعل';
        break;
      case 'weak-password':
        userMessage = 'كلمة المرور ضعيفة';
        break;
      case 'invalid-email':
        userMessage = 'البريد الإلكتروني غير صحيح';
        break;
      case 'user-disabled':
        userMessage = 'تم تعطيل هذا الحساب';
        severity = ErrorSeverity.high;
        break;
      case 'too-many-requests':
        userMessage = 'محاولات كثيرة جداً، يرجى المحاولة لاحقاً';
        severity = ErrorSeverity.high;
        break;
      case 'network-request-failed':
        userMessage = 'خطأ في الاتصال بالإنترنت';
        severity = ErrorSeverity.high;
        break;
      default:
        userMessage = 'خطأ في المصادقة: ${error.message ?? 'غير معروف'}';
    }

    return ErrorInfo(
      type: ErrorType.authentication,
      userMessage: userMessage,
      technicalMessage: '${error.code}: ${error.message}',
      severity: severity,
    );
  }

  /// Handle Firebase errors
  static ErrorInfo _handleFirebaseError(FirebaseException error) {
    String userMessage;
    ErrorSeverity severity = ErrorSeverity.medium;

    switch (error.code) {
      case 'permission-denied':
        userMessage = 'ليس لديك صلاحية للوصول لهذه البيانات';
        severity = ErrorSeverity.high;
        break;
      case 'not-found':
        userMessage = 'البيانات المطلوبة غير موجودة';
        break;
      case 'already-exists':
        userMessage = 'البيانات موجودة بالفعل';
        break;
      case 'resource-exhausted':
        userMessage = 'تم تجاوز الحد المسموح، يرجى المحاولة لاحقاً';
        severity = ErrorSeverity.high;
        break;
      case 'failed-precondition':
        userMessage = 'لا يمكن تنفيذ العملية في الوقت الحالي';
        break;
      case 'aborted':
        userMessage = 'تم إلغاء العملية';
        break;
      case 'out-of-range':
        userMessage = 'البيانات خارج النطاق المسموح';
        break;
      case 'unimplemented':
        userMessage = 'هذه الميزة غير متوفرة حالياً';
        break;
      case 'internal':
        userMessage = 'خطأ داخلي في الخادم';
        severity = ErrorSeverity.critical;
        break;
      case 'unavailable':
        userMessage = 'الخدمة غير متوفرة حالياً';
        severity = ErrorSeverity.high;
        break;
      case 'data-loss':
        userMessage = 'حدث فقدان في البيانات';
        severity = ErrorSeverity.critical;
        break;
      default:
        userMessage = 'خطأ في الخادم: ${error.message ?? 'غير معروف'}';
    }

    return ErrorInfo(
      type: ErrorType.network,
      userMessage: userMessage,
      technicalMessage: '${error.code}: ${error.message}',
      severity: severity,
    );
  }

  /// Log error to database
  static Future<void> _logError({
    required dynamic error,
    StackTrace? stackTrace,
    String? context,
    required ErrorInfo errorInfo,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final instance = AppErrorHandler._instance;
      final db = await instance._dbHelper.database;

      await db.insert('error_logs', {
        'error_type': errorInfo.type.toString().split('.').last,
        'severity': errorInfo.severity.toString().split('.').last,
        'user_message': errorInfo.userMessage,
        'technical_message': errorInfo.technicalMessage,
        'context': context,
        'stack_trace': stackTrace?.toString(),
        'additional_data': additionalData != null ? additionalData.toString() : null,
        'timestamp': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to log error to database: $e');
      }
    }
  }

  /// Show error to user
  static void _showErrorToUser(BuildContext context, ErrorInfo errorInfo) {
    final color = _getErrorColor(errorInfo.severity);
    final icon = _getErrorIcon(errorInfo.severity);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                errorInfo.userMessage,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: color,
        duration: Duration(
          seconds: errorInfo.severity == ErrorSeverity.critical ? 10 : 5,
        ),
        action: errorInfo.severity == ErrorSeverity.critical
            ? SnackBarAction(
                label: 'تفاصيل',
                textColor: Colors.white,
                onPressed: () => _showErrorDialog(context, errorInfo),
              )
            : null,
      ),
    );
  }

  /// Show detailed error dialog
  static void _showErrorDialog(BuildContext context, ErrorInfo errorInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(_getErrorIcon(errorInfo.severity)),
            const SizedBox(width: 8),
            const Text('تفاصيل الخطأ'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرسالة: ${errorInfo.userMessage}'),
            const SizedBox(height: 8),
            if (kDebugMode) ...[
              const Text('التفاصيل التقنية:'),
              const SizedBox(height: 4),
              Text(
                errorInfo.technicalMessage,
                style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// Get error color based on severity
  static Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Colors.blue;
      case ErrorSeverity.medium:
        return Colors.orange;
      case ErrorSeverity.high:
        return Colors.red;
      case ErrorSeverity.critical:
        return Colors.red.shade900;
    }
  }

  /// Get error icon based on severity
  static IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Icons.info;
      case ErrorSeverity.medium:
        return Icons.warning;
      case ErrorSeverity.high:
        return Icons.error;
      case ErrorSeverity.critical:
        return Icons.dangerous;
    }
  }

  /// Get error logs from database
  static Future<List<Map<String, dynamic>>> getErrorLogs({
    int limit = 100,
    ErrorSeverity? severity,
    DateTime? since,
  }) async {
    try {
      final instance = AppErrorHandler._instance;
      final db = await instance._dbHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (severity != null) {
        whereClause += 'severity = ?';
        whereArgs.add(severity.toString().split('.').last);
      }

      if (since != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'timestamp >= ?';
        whereArgs.add(since.toIso8601String());
      }

      return await db.query(
        'error_logs',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'timestamp DESC',
        limit: limit,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get error logs: $e');
      }
      return [];
    }
  }

  /// Clear old error logs
  static Future<void> clearOldErrorLogs({int daysToKeep = 30}) async {
    try {
      final instance = AppErrorHandler._instance;
      final db = await instance._dbHelper.database;

      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      await db.delete(
        'error_logs',
        where: 'timestamp < ?',
        whereArgs: [cutoffDate.toIso8601String()],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear old error logs: $e');
      }
    }
  }
}

/// Error information class
class ErrorInfo {
  final ErrorType type;
  final String userMessage;
  final String technicalMessage;
  final ErrorSeverity severity;

  const ErrorInfo({
    required this.type,
    required this.userMessage,
    required this.technicalMessage,
    required this.severity,
  });
}

/// Error types
enum ErrorType {
  authentication,
  network,
  database,
  validation,
  application,
  unknown,
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}
