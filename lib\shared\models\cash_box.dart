import 'dart:convert';

/// Cash Box model for managing financial transactions
class CashBox {
  final int? id;
  final String name;
  final String? description;
  final double openingBalance;
  final double currentBalance;
  final bool isActive;
  final String? currency;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? firestoreId;
  final DateTime? lastSyncTime;
  final SyncStatus syncStatus;
  final int version;

  const CashBox({
    this.id,
    required this.name,
    this.description,
    required this.openingBalance,
    required this.currentBalance,
    this.isActive = true,
    this.currency = 'SAR',
    required this.createdAt,
    this.updatedAt,
    this.firestoreId,
    this.lastSyncTime,
    this.syncStatus = SyncStatus.pending,
    this.version = 1,
  });

  /// Get local database ID
  int? get localId => id;

  /// Calculate total transactions amount
  double get totalTransactions => currentBalance - openingBalance;

  /// Check if cash box has sufficient balance for withdrawal
  bool canWithdraw(double amount) => currentBalance >= amount;

  /// Create a copy with updated balance
  CashBox copyWithBalance(double newBalance) {
    return copyWith(
      currentBalance: newBalance,
      updatedAt: DateTime.now(),
      version: version + 1,
    );
  }

  CashBox copyWith({
    int? id,
    String? name,
    String? description,
    double? openingBalance,
    double? currentBalance,
    bool? isActive,
    String? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? firestoreId,
    DateTime? lastSyncTime,
    SyncStatus? syncStatus,
    int? version,
  }) {
    return CashBox(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      openingBalance: openingBalance ?? this.openingBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      isActive: isActive ?? this.isActive,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      firestoreId: firestoreId ?? this.firestoreId,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      syncStatus: syncStatus ?? this.syncStatus,
      version: version ?? this.version,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
      'opening_balance': openingBalance,
      'current_balance': currentBalance,
      'is_active': isActive ? 1 : 0,
      'currency': currency,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'firestore_id': firestoreId,
      'last_sync_time': lastSyncTime?.toIso8601String(),
      'sync_status': syncStatus.toString(),
      'version': version,
    };
  }

  Map<String, dynamic> toFirestoreMap() {
    return <String, dynamic>{
      'name': name,
      'description': description,
      'opening_balance': openingBalance,
      'current_balance': currentBalance,
      'is_active': isActive,
      'currency': currency,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'version': version,
    };
  }

  factory CashBox.fromMap(Map<String, dynamic> map) {
    return CashBox(
      id: map['id'] != null ? map['id'] as int : null,
      name: map['name'] as String,
      description: map['description'] != null ? map['description'] as String : null,
      openingBalance: (map['opening_balance'] as num).toDouble(),
      currentBalance: (map['current_balance'] as num).toDouble(),
      isActive: map['is_active'] == 1,
      currency: map['currency'] != null ? map['currency'] as String : 'SAR',
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String) 
          : null,
      firestoreId: map['firestore_id'] != null ? map['firestore_id'] as String : null,
      lastSyncTime: map['last_sync_time'] != null 
          ? DateTime.parse(map['last_sync_time'] as String) 
          : null,
      syncStatus: map['sync_status'] != null 
          ? SyncStatus.values.firstWhere(
              (e) => e.toString() == map['sync_status'],
              orElse: () => SyncStatus.pending,
            )
          : SyncStatus.pending,
      version: map['version'] as int? ?? 1,
    );
  }

  factory CashBox.fromFirestore(Map<String, dynamic> data, String firestoreId) {
    return CashBox(
      firestoreId: firestoreId,
      name: data['name'] as String,
      description: data['description'] as String?,
      openingBalance: (data['opening_balance'] as num).toDouble(),
      currentBalance: (data['current_balance'] as num).toDouble(),
      isActive: data['is_active'] as bool? ?? true,
      currency: data['currency'] as String? ?? 'SAR',
      createdAt: data['created_at'] != null
          ? DateTime.parse(data['created_at'] as String)
          : DateTime.now(),
      updatedAt: data['updated_at'] != null
          ? DateTime.parse(data['updated_at'] as String)
          : null,
      lastSyncTime: DateTime.now(),
      syncStatus: SyncStatus.synced,
      version: data['version'] as int? ?? 1,
    );
  }

  String toJson() => json.encode(toMap());

  factory CashBox.fromJson(String source) =>
      CashBox.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'CashBox(id: $id, name: $name, openingBalance: $openingBalance, currentBalance: $currentBalance, isActive: $isActive)';
  }

  @override
  bool operator ==(covariant CashBox other) {
    if (identical(this, other)) return true;
  
    return 
      other.id == id &&
      other.name == name &&
      other.description == description &&
      other.openingBalance == openingBalance &&
      other.currentBalance == currentBalance &&
      other.isActive == isActive &&
      other.currency == currency &&
      other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      description.hashCode ^
      openingBalance.hashCode ^
      currentBalance.hashCode ^
      isActive.hashCode ^
      currency.hashCode ^
      createdAt.hashCode;
  }
}

/// Cash Box Transaction model for tracking individual transactions
class CashBoxTransaction {
  final int? id;
  final int cashBoxId;
  final String type; // 'income', 'expense', 'transfer'
  final double amount;
  final String description;
  final String? reference; // Invoice ID, Service ID, etc.
  final String? referenceType; // 'invoice', 'service', 'manual', etc.
  final DateTime transactionDate;
  final DateTime createdAt;
  final String? firestoreId;

  const CashBoxTransaction({
    this.id,
    required this.cashBoxId,
    required this.type,
    required this.amount,
    required this.description,
    this.reference,
    this.referenceType,
    required this.transactionDate,
    required this.createdAt,
    this.firestoreId,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'cash_box_id': cashBoxId,
      'type': type,
      'amount': amount,
      'description': description,
      'reference': reference,
      'reference_type': referenceType,
      'transaction_date': transactionDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'firestore_id': firestoreId,
    };
  }

  Map<String, dynamic> toFirestoreMap() {
    return <String, dynamic>{
      'cash_box_id': cashBoxId,
      'type': type,
      'amount': amount,
      'description': description,
      'reference': reference,
      'reference_type': referenceType,
      'transaction_date': transactionDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory CashBoxTransaction.fromMap(Map<String, dynamic> map) {
    return CashBoxTransaction(
      id: map['id'] != null ? map['id'] as int : null,
      cashBoxId: map['cash_box_id'] as int,
      type: map['type'] as String,
      amount: (map['amount'] as num).toDouble(),
      description: map['description'] as String,
      reference: map['reference'] != null ? map['reference'] as String : null,
      referenceType: map['reference_type'] != null ? map['reference_type'] as String : null,
      transactionDate: DateTime.parse(map['transaction_date'] as String),
      createdAt: DateTime.parse(map['created_at'] as String),
      firestoreId: map['firestore_id'] != null ? map['firestore_id'] as String : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory CashBoxTransaction.fromJson(String source) =>
      CashBoxTransaction.fromMap(json.decode(source) as Map<String, dynamic>);
}

/// Sync status enum
enum SyncStatus {
  pending,
  syncing,
  synced,
  error,
}

/// Cash Box Transaction Types
class CashBoxTransactionType {
  static const String income = 'income';
  static const String expense = 'expense';
  static const String transfer = 'transfer';
  static const String opening = 'opening';
  static const String adjustment = 'adjustment';
}

/// Cash Box Transaction Reference Types
class CashBoxReferenceType {
  static const String invoice = 'invoice';
  static const String service = 'service';
  static const String salary = 'salary';
  static const String advance = 'advance';
  static const String manual = 'manual';
  static const String transfer = 'transfer';
  static const String opening = 'opening';
}
