import 'dart:convert';
import 'package:flutter/material.dart';

/// أنواع الضرائب
enum TaxType {
  /// ضريبة القيمة المضافة
  vat,
  
  /// ضريبة الدخل
  incomeTax,
  
  /// ضريبة المبيعات
  salesTax,
  
  /// ضريبة أخرى
  other,
}

/// نموذج الضريبة
class Tax {
  /// المعرف الفريد للضريبة
  final int? id;
  
  /// اسم الضريبة
  final String name;
  
  /// نوع الضريبة
  final TaxType type;
  
  /// نسبة الضريبة (%)
  final double rate;
  
  /// هل الضريبة نشطة
  final bool isActive;
  
  /// هل الضريبة افتراضية
  final bool isDefault;
  
  /// وصف الضريبة
  final String? description;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime? updatedAt;

  /// إنشاء نموذج ضريبة جديد
  Tax({
    this.id,
    required this.name,
    required this.type,
    required this.rate,
    required this.isActive,
    this.isDefault = false,
    this.description,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نموذج من خريطة بيانات
  factory Tax.fromMap(Map<String, dynamic> map) {
    return Tax(
      id: map['id'] as int?,
      name: map['name'] as String,
      type: _parseTaxType(map['type'] as String),
      rate: map['rate'] as double,
      isActive: map['is_active'] == 1 || map['is_active'] == true,
      isDefault: map['is_default'] == 1 || map['is_default'] == true,
      description: map['description'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  /// تحويل النموذج إلى خريطة بيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.toString().split('.').last,
      'rate': rate,
      'is_active': isActive ? 1 : 0,
      'is_default': isDefault ? 1 : 0,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// تحويل النموذج إلى سلسلة JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من سلسلة JSON
  factory Tax.fromJson(String source) =>
      Tax.fromMap(json.decode(source) as Map<String, dynamic>);

  /// إنشاء نسخة معدلة من النموذج
  Tax copyWith({
    int? id,
    String? name,
    TaxType? type,
    double? rate,
    bool? isActive,
    bool? isDefault,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tax(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      rate: rate ?? this.rate,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل نوع الضريبة من نص
  static TaxType _parseTaxType(String type) {
    switch (type) {
      case 'vat':
        return TaxType.vat;
      case 'incomeTax':
        return TaxType.incomeTax;
      case 'salesTax':
        return TaxType.salesTax;
      case 'other':
        return TaxType.other;
      default:
        return TaxType.vat;
    }
  }

  /// الحصول على اسم نوع الضريبة
  static String getTaxTypeName(TaxType type) {
    switch (type) {
      case TaxType.vat:
        return 'ضريبة القيمة المضافة';
      case TaxType.incomeTax:
        return 'ضريبة الدخل';
      case TaxType.salesTax:
        return 'ضريبة المبيعات';
      case TaxType.other:
        return 'ضريبة أخرى';
    }
  }

  /// إنشاء قائمة من نماذج الضرائب للاختبار
  static List<Tax> getMockTaxes() {
    return [
      Tax(
        id: 1,
        name: 'ضريبة القيمة المضافة 15%',
        type: TaxType.vat,
        rate: 15.0,
        isActive: true,
        isDefault: true,
        description: 'ضريبة القيمة المضافة الأساسية في المملكة العربية السعودية',
        createdAt: DateTime(2023, 1, 1),
      ),
      Tax(
        id: 2,
        name: 'ضريبة مبيعات 5%',
        type: TaxType.salesTax,
        rate: 5.0,
        isActive: true,
        isDefault: false,
        description: 'ضريبة مبيعات خاصة لبعض المنتجات',
        createdAt: DateTime(2023, 1, 1),
      ),
      Tax(
        id: 3,
        name: 'ضريبة دخل 20%',
        type: TaxType.incomeTax,
        rate: 20.0,
        isActive: true,
        isDefault: false,
        description: 'ضريبة الدخل للشركات',
        createdAt: DateTime(2023, 1, 1),
      ),
    ];
  }
}
