import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// جدول بيانات منسق بتصميم موحد
class StyledDataTable extends StatelessWidget {
  /// عناوين الأعمدة
  final List<String> columns;
  
  /// بيانات الصفوف
  final List<List<Widget>> rows;
  
  /// عرض الأعمدة (اختياري)
  final List<double>? columnWidths;
  
  /// تمكين التمرير الأفقي
  final bool horizontalScrollEnabled;
  
  /// ارتفاع الصف
  final double rowHeight;
  
  /// دالة تنفذ عند النقر على صف
  final Function(int)? onRowTap;
  
  /// مؤشر الصف المحدد
  final int? selectedRowIndex;
  
  /// إظهار حدود الجدول
  final bool showBorders;
  
  /// إظهار صفوف متناوبة
  final bool alternatingRowColors;
  
  /// لون خلفية رأس الجدول
  final Color headerBackgroundColor;
  
  /// لون نص رأس الجدول
  final Color headerTextColor;
  
  /// لون خلفية الصف الزوجي
  final Color evenRowColor;
  
  /// لون خلفية الصف الفردي
  final Color oddRowColor;
  
  /// لون خلفية الصف المحدد
  final Color selectedRowColor;
  
  /// لون حدود الجدول
  final Color borderColor;
  
  /// سماكة حدود الجدول
  final double borderWidth;
  
  /// نصف قطر زوايا الجدول
  final double borderRadius;
  
  /// إنشاء جدول بيانات منسق
  const StyledDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.columnWidths,
    this.horizontalScrollEnabled = true,
    this.rowHeight = 50.0,
    this.onRowTap,
    this.selectedRowIndex,
    this.showBorders = true,
    this.alternatingRowColors = true,
    this.headerBackgroundColor = AppColors.tableHeader,
    this.headerTextColor = AppColors.tableHeaderText,
    this.evenRowColor = AppColors.tableRowEven,
    this.oddRowColor = AppColors.tableRowOdd,
    this.selectedRowColor = AppColors.tableRowSelected,
    this.borderColor = AppColors.tableBorder,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
  }) : assert(columns.length == (columnWidths?.length ?? columns.length),
            'عدد الأعمدة يجب أن يساوي عدد عروض الأعمدة');

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: evenRowColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: showBorders
            ? Border.all(
                color: borderColor,
                width: borderWidth,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // رأس الجدول
          _buildTableHeader(),
          
          // محتوى الجدول
          Flexible(
            child: horizontalScrollEnabled
                ? SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: _buildTableContent(),
                  )
                : _buildTableContent(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الجدول
  Widget _buildTableHeader() {
    return Container(
      height: rowHeight,
      decoration: BoxDecoration(
        color: headerBackgroundColor,
        border: showBorders && rows.isNotEmpty
            ? const Border(
                bottom: BorderSide(
                  color: AppColors.tableBorder,
                  width: 1.0,
                ),
              )
            : null,
      ),
      child: Row(
        children: List.generate(
          columns.length,
          (index) => Container(
            width: columnWidths != null ? columnWidths![index] : null,
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacingM),
            alignment: Alignment.centerRight,
            child: Text(
              columns[index],
              style: AppTextStyles.tableHeader.copyWith(color: headerTextColor),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء محتوى الجدول
  Widget _buildTableContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rows.isEmpty
          ? [_buildEmptyState()]
          : List.generate(
              rows.length,
              (rowIndex) => _buildTableRow(rowIndex),
            ),
    );
  }

  /// بناء صف في الجدول
  Widget _buildTableRow(int rowIndex) {
    final isSelected = selectedRowIndex == rowIndex;
    final isEven = rowIndex % 2 == 0;
    final rowColor = isSelected
        ? selectedRowColor
        : (alternatingRowColors ? (isEven ? evenRowColor : oddRowColor) : evenRowColor);

    return InkWell(
      onTap: onRowTap != null ? () => onRowTap!(rowIndex) : null,
      hoverColor: AppColors.tableRowHover,
      splashColor: AppColors.tableRowHover.withOpacity(0.5),
      child: Container(
        height: rowHeight,
        decoration: BoxDecoration(
          color: rowColor,
          border: showBorders && rowIndex < rows.length - 1
              ? const Border(
                  bottom: BorderSide(
                    color: AppColors.tableBorder,
                    width: 0.5,
                  ),
                )
              : null,
        ),
        child: Row(
          children: List.generate(
            columns.length,
            (colIndex) => Container(
              width: columnWidths != null ? columnWidths![colIndex] : null,
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacingM),
              alignment: Alignment.centerRight,
              child: colIndex < rows[rowIndex].length
                  ? rows[rowIndex][colIndex]
                  : const Text('-'),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء حالة الجدول الفارغ
  Widget _buildEmptyState() {
    return Container(
      height: rowHeight * 3,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 32,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'لا توجد بيانات',
            style: AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }
}
