import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// Migration to add service_amount column to service_requests table
class AddServiceAmountToServiceRequests {
  /// Apply the migration
  static Future<void> migrate(Database db) async {
    try {
      // Check if the column already exists
      final List<Map<String, dynamic>> columns = await db.rawQuery(
        "PRAGMA table_info(service_requests)"
      );
      
      final bool columnExists = columns.any((column) => column['name'] == 'service_amount');
      
      if (!columnExists) {
        if (kDebugMode) {
          print('Adding service_amount column to service_requests table');
        }
        
        // Add the service_amount column with default value of 0
        await db.execute(
          "ALTER TABLE service_requests ADD COLUMN service_amount REAL DEFAULT 0"
        );
        
        if (kDebugMode) {
          print('Successfully added service_amount column to service_requests table');
        }
      } else {
        if (kDebugMode) {
          print('service_amount column already exists in service_requests table');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding service_amount column to service_requests table: $e');
      }
    }
  }
}
