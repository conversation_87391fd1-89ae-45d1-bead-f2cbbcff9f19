import 'package:flutter/material.dart';
import '../../config/constants.dart';

class ColorPickerDialog extends StatefulWidget {
  final Color selectedColor;
  final Function(Color) onColorSelected;

  const ColorPickerDialog({
    super.key,
    required this.selectedColor,
    required this.onColorSelected,
  });

  @override
  State<ColorPickerDialog> createState() => _ColorPickerDialogState();
}

class _ColorPickerDialogState extends State<ColorPickerDialog> {
  late Color _currentColor;

  @override
  void initState() {
    super.initState();
    _currentColor = widget.selectedColor;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'اختر لون',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Container(
              height: 50,
              width: double.infinity,
              decoration: BoxDecoration(
                color: _currentColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.5,
              ),
              child: GridView.builder(
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  childAspectRatio: 1,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemCount: _materialColors.length,
                itemBuilder: (context, index) {
                  final color = _materialColors[index];
                  // Compare colors by their ARGB values
                  final isSelected = _currentColor.toString() == color.toString();

                  return InkWell(
                    onTap: () {
                      setState(() {
                        _currentColor = color;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: color,
                        border: Border.all(
                          color: isSelected ? Colors.white : Colors.transparent,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: Colors.black.withAlpha(77), // 0.3 opacity (77/255)
                                  blurRadius: 5,
                                  spreadRadius: 1,
                                ),
                              ]
                            : null,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                ElevatedButton(
                  onPressed: () {
                    widget.onColorSelected(_currentColor);
                    Navigator.pop(context);
                  },
                  child: const Text('اختيار'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // قائمة بالألوان الشائعة
  final List<Color> _materialColors = [
    Colors.red,
    Colors.red.shade300,
    Colors.red.shade700,
    Colors.pink,
    Colors.pink.shade300,
    Colors.pink.shade700,
    Colors.purple,
    Colors.purple.shade300,
    Colors.purple.shade700,
    Colors.deepPurple,
    Colors.deepPurple.shade300,
    Colors.deepPurple.shade700,
    Colors.indigo,
    Colors.indigo.shade300,
    Colors.indigo.shade700,
    Colors.blue,
    Colors.blue.shade300,
    Colors.blue.shade700,
    Colors.lightBlue,
    Colors.lightBlue.shade300,
    Colors.lightBlue.shade700,
    Colors.cyan,
    Colors.cyan.shade300,
    Colors.cyan.shade700,
    Colors.teal,
    Colors.teal.shade300,
    Colors.teal.shade700,
    Colors.green,
    Colors.green.shade300,
    Colors.green.shade700,
    Colors.lightGreen,
    Colors.lightGreen.shade300,
    Colors.lightGreen.shade700,
    Colors.lime,
    Colors.lime.shade300,
    Colors.lime.shade700,
    Colors.yellow,
    Colors.yellow.shade300,
    Colors.yellow.shade700,
    Colors.amber,
    Colors.amber.shade300,
    Colors.amber.shade700,
    Colors.orange,
    Colors.orange.shade300,
    Colors.orange.shade700,
    Colors.deepOrange,
    Colors.deepOrange.shade300,
    Colors.deepOrange.shade700,
    Colors.brown,
    Colors.brown.shade300,
    Colors.brown.shade700,
    Colors.grey,
    Colors.grey.shade300,
    Colors.grey.shade700,
    Colors.blueGrey,
    Colors.blueGrey.shade300,
    Colors.blueGrey.shade700,
    Colors.black,
  ];
}
