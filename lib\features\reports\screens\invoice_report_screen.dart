import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/invoice.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/customer_repository.dart';
import '../utils/report_generator.dart';
import '../widgets/report_filter_dialog.dart';
import '../widgets/customer_filter_dialog.dart';

class InvoiceReportScreen extends StatefulWidget {
  const InvoiceReportScreen({super.key});

  @override
  State<InvoiceReportScreen> createState() => _InvoiceReportScreenState();
}

class _InvoiceReportScreenState extends State<InvoiceReportScreen> {
  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final CustomerRepository _customerRepository = CustomerRepository();

  bool _isLoading = false;
  List<Invoice> _invoices = [];
  List<Customer> _customers = [];

  // فلاتر التقرير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _filterType = 'thisMonth';
  int? _selectedCustomerId;
  String _statusFilter = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الفواتير
      final invoices = await _invoiceRepository.getInvoicesByDateRange(_startDate, _endDate);

      // تحميل العملاء
      final customers = await _customerRepository.getAllCustomers();

      if (mounted) {
        setState(() {
          _invoices = invoices;
          _customers = customers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading invoice report data: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // الفواتير المفلترة
  List<Invoice> get _filteredInvoices {
    var filtered = _invoices;

    // تطبيق فلتر العميل
    if (_selectedCustomerId != null) {
      filtered = filtered.where((invoice) => invoice.customerId == _selectedCustomerId).toList();
    }

    // تطبيق فلتر الحالة
    if (_statusFilter != 'all') {
      filtered = filtered.where((invoice) {
        final status = invoice.status.toString().split('.').last;
        return status == _statusFilter;
      }).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((invoice) {
        return invoice.invoiceNumber.toLowerCase().contains(query) ||
            invoice.customerName.toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }

  // إجمالي المبالغ
  double get _totalAmount {
    return _filteredInvoices.fold(0, (sum, invoice) => sum + invoice.total);
  }

  // إجمالي المبالغ المدفوعة
  double get _totalPaid {
    return _filteredInvoices
        .where((invoice) => invoice.status == InvoiceStatus.paid)
        .fold(0, (sum, invoice) => sum + invoice.total);
  }

  // إجمالي المبالغ غير المدفوعة
  double get _totalUnpaid {
    return _filteredInvoices
        .where((invoice) => invoice.status != InvoiceStatus.paid)
        .fold(0, (sum, invoice) => sum + invoice.total);
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => ReportFilterDialog(
        startDate: _startDate,
        endDate: _endDate,
        filterType: _filterType,
        onApplyFilter: (startDate, endDate, filterType) {
          setState(() {
            _startDate = startDate;
            _endDate = endDate;
            _filterType = filterType;
          });
          _loadData();
        },
      ),
    );
  }

  void _showCustomerFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => CustomerFilterDialog(
        selectedCustomerId: _selectedCustomerId,
        onApplyFilter: (customerId) {
          setState(() {
            _selectedCustomerId = customerId;
          });
        },
      ),
    );
  }

  void _exportToPdf() async {
    try {
      // استخدام الشاشة الموحدة لعرض التقرير
      Navigator.pushNamed(context, AppRoutes.invoiceReport);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء فتح تقرير الفواتير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _printReport() async {
    try {
      // استخدام الشاشة الموحدة لعرض التقرير
      Navigator.pushNamed(context, AppRoutes.invoiceReport);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء فتح تقرير الفواتير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير الفواتير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_alt),
            tooltip: 'تصفية حسب التاريخ',
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.person),
            tooltip: 'تصفية حسب العميل',
            onPressed: _showCustomerFilterDialog,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'export_pdf') {
                _exportToPdf();
              } else if (value == 'print') {
                _printReport();
              } else if (value == 'refresh') {
                _loadData();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'export_pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red),
                    SizedBox(width: 8),
                    Text('تصدير PDF'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh, color: Colors.green),
                    SizedBox(width: 8),
                    Text('تحديث'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // عرض الفترة الزمنية والفلاتر المطبقة
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                        style: AppTextStyles.heading3,
                      ),
                      if (_selectedCustomerId != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            'العميل: ${_customers.firstWhere((c) => c.localId == _selectedCustomerId, orElse: () => Customer(name: 'غير معروف', email: '', phone: '', type: CustomerType.individual, createdAt: DateTime.now())).name}',
                            style: AppTextStyles.heading3,
                          ),
                        ),
                    ],
                  ),
                ),

                // فلاتر الحالة
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                  child: Row(
                    children: [
                      const Text('الحالة:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(width: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildStatusFilterChip('الكل', 'all'),
                              _buildStatusFilterChip('مدفوعة', 'paid'),
                              _buildStatusFilterChip('غير مدفوعة', 'issued'),
                              _buildStatusFilterChip('مدفوعة جزئياً', 'partiallyPaid'),
                              _buildStatusFilterChip('متأخرة', 'overdue'),
                              _buildStatusFilterChip('ملغاة', 'cancelled'),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // حقل البحث
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'بحث عن فاتورة...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ),

                // جدول الفواتير
                Expanded(
                  child: _filteredInvoices.isEmpty
                      ? const Center(
                          child: Text(
                            'لا توجد فواتير للعرض',
                            style: AppTextStyles.heading3,
                          ),
                        )
                      : SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              columns: const [
                                DataColumn(
                                  label: Text(
                                    'رقم الفاتورة',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                DataColumn(
                                  label: Text(
                                    'العميل',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                DataColumn(
                                  label: Text(
                                    'التاريخ',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                DataColumn(
                                  label: Text(
                                    'المبلغ',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  numeric: true,
                                ),
                                DataColumn(
                                  label: Text(
                                    'العملة',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                DataColumn(
                                  label: Text(
                                    'الحالة',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ],
                              rows: _filteredInvoices.map((invoice) {
                                return DataRow(
                                  cells: [
                                    DataCell(Text(invoice.invoiceNumber)),
                                    DataCell(Text(invoice.customerName)),
                                    DataCell(Text(DateFormat('dd/MM/yyyy').format(invoice.date))),
                                    DataCell(Text(invoice.total.toStringAsFixed(2))),
                                    DataCell(const Text('ر.س')), // العملة ثابتة (ريال سعودي)
                                    DataCell(
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: _getStatusColor(invoice.status),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          _getStatusName(invoice.status),
                                          style: const TextStyle(color: Colors.white),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                ),

                // ملخص الإجماليات
                Container(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  color: Colors.grey.shade100,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'إجمالي الفواتير:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${_filteredInvoices.length} فاتورة',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'إجمالي المبالغ:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${_totalAmount.toStringAsFixed(2)} ر.س',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبالغ المدفوعة:'),
                          Text(
                            '${_totalPaid.toStringAsFixed(2)} ر.س',
                            style: const TextStyle(color: Colors.green),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبالغ غير المدفوعة:'),
                          Text(
                            '${_totalUnpaid.toStringAsFixed(2)} ر.س',
                            style: const TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildStatusFilterChip(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: FilterChip(
        label: Text(label),
        selected: _statusFilter == value,
        onSelected: (selected) {
          setState(() {
            _statusFilter = selected ? value : 'all';
          });
        },
        selectedColor: AppColors.primary.withAlpha(51),
        checkmarkColor: AppColors.primary,
      ),
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.issued:
        return Colors.red;
      case InvoiceStatus.partiallyPaid:
        return Colors.orange;
      case InvoiceStatus.cancelled:
        return Colors.grey;
      case InvoiceStatus.draft:
        return Colors.grey.shade400;
      case InvoiceStatus.overdue:
        return Colors.deepOrange;
    }
  }

  String _getStatusName(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.issued:
        return 'غير مدفوعة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.cancelled:
        return 'ملغاة';
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.overdue:
        return 'متأخرة';
    }
  }
}
