import 'package:flutter/material.dart';
import '../../../shared/models/service_request.dart';

class PriorityBadge extends StatelessWidget {
  final ServiceRequestPriority priority;

  const PriorityBadge({
    super.key,
    required this.priority,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getPriorityColor().withAlpha(26),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getPriorityColor(),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.flag,
            size: 16,
            color: _getPriorityColor(),
          ),
          const SizedBox(width: 4),
          Text(
            ServiceRequest.getPriorityName(priority),
            style: TextStyle(
              color: _getPriorityColor(),
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor() {
    return ServiceRequest.getPriorityColor(priority);
  }
}
