import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/salary.dart' as salary_models;
import '../../shared/models/withdrawal.dart';

class PayrollTransactionRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Crear una transacción financiera para el pago de un salario
  Future<int> createSalaryPaymentTransaction(
    salary_models.Salary salary,
    salary_models.PaymentMethod paymentMethod,
    int? employeeId,
    int? bankAccountId,
  ) async {
    try {
      final db = await _dbHelper.database;

      // Generar una referencia única para la transacción
      final now = DateTime.now();
      final reference = 'SAL-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch % 10000}';

      // Crear la transacción como un gasto (salida de dinero)
      final transaction = {
        'reference': reference,
        'date': now.toIso8601String(),
        'amount': salary.netSalary,
        'type': 'expense', // Es un gasto porque sale dinero
        'category_id': null, // Podría ser una categoría específica para salarios
        'description': 'دفع راتب ${salary.employeeName} لشهر ${salary.month}/${salary.year}',
        'payment_method': paymentMethod.toString().split('.').last,
        'bank_account_id': paymentMethod == salary_models.PaymentMethod.bankTransfer ? bankAccountId : null,
        'employee_id': paymentMethod == salary_models.PaymentMethod.cash ? employeeId : null,
        'created_at': now.toIso8601String(),
      };

      // Iniciar una transacción en la base de datos
      int transactionId = -1;
      await db.transaction((txn) async {
        // Insertar la transacción
        transactionId = await txn.insert('transactions', transaction);

        // Actualizar el balance según el método de pago
        if (paymentMethod == salary_models.PaymentMethod.bankTransfer && bankAccountId != null) {
          // Reducir el balance de la cuenta bancaria
          await txn.rawUpdate('''
            UPDATE bank_accounts
            SET balance = balance - ?
            WHERE id = ?
          ''', [salary.netSalary, bankAccountId]);
        }
      });

      return transactionId;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating salary payment transaction: $e');
      }
      return -1;
    }
  }

  // Crear una transacción financiera para el pago de un retiro
  Future<int> createWithdrawalPaymentTransaction(
    Withdrawal withdrawal,
    salary_models.PaymentMethod paymentMethod,
    int? employeeId,
    int? bankAccountId,
  ) async {
    try {
      final db = await _dbHelper.database;

      // Generar una referencia única para la transacción
      final now = DateTime.now();
      final reference = 'WDR-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch % 10000}';

      // Crear la transacción como un gasto (salida de dinero)
      final transaction = {
        'reference': reference,
        'date': now.toIso8601String(),
        'amount': withdrawal.amount,
        'type': 'expense', // Es un gasto porque sale dinero
        'category_id': null, // Podría ser una categoría específica para retiros
        'description': 'دفع سحب نقدي للموظف ${withdrawal.employeeName}',
        'payment_method': paymentMethod.toString().split('.').last,
        'bank_account_id': paymentMethod == salary_models.PaymentMethod.bankTransfer ? bankAccountId : null,
        'employee_id': paymentMethod == salary_models.PaymentMethod.cash ? employeeId : null,
        'created_at': now.toIso8601String(),
      };

      // Iniciar una transacción en la base de datos
      int transactionId = -1;
      await db.transaction((txn) async {
        // Insertar la transacción
        transactionId = await txn.insert('transactions', transaction);

        // Actualizar el balance según el método de pago
        if (paymentMethod == salary_models.PaymentMethod.bankTransfer && bankAccountId != null) {
          // Reducir el balance de la cuenta bancaria
          await txn.rawUpdate('''
            UPDATE bank_accounts
            SET balance = balance - ?
            WHERE id = ?
          ''', [withdrawal.amount, bankAccountId]);
        }
      });

      return transactionId;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating withdrawal payment transaction: $e');
      }
      return -1;
    }
  }

  // Verificar si hay suficiente balance para realizar un pago
  Future<bool> checkSufficientBalance(
    double amount,
    salary_models.PaymentMethod paymentMethod,
    int? employeeId,
    int? bankAccountId,
  ) async {
    try {
      if (paymentMethod == salary_models.PaymentMethod.cash && employeeId != null) {
        // Verificar el balance del empleado (custodia)
        final employeeBalance = await getEmployeeBalance(employeeId);
        return employeeBalance >= amount;
      } else if (paymentMethod == salary_models.PaymentMethod.bankTransfer && bankAccountId != null) {
        // Verificar el balance de la cuenta bancaria
        final bankBalance = await getBankAccountBalance(bankAccountId);
        return bankBalance >= amount;
      }

      // Si no se proporciona información válida, asumimos que no hay suficiente balance
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking sufficient balance: $e');
      }
      return false;
    }
  }

  // Obtener el balance de un empleado (custodia)
  Future<double> getEmployeeBalance(int employeeId) async {
    try {
      final db = await _dbHelper.database;

      // Obtener las transacciones donde el empleado es el responsable del pago en efectivo
      final cashInResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_cash_in
        FROM transactions
        WHERE type = 'income' AND employee_id = ?
      ''', [employeeId]);

      final totalCashIn = cashInResult.isNotEmpty
          ? (cashInResult.first['total_cash_in'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Obtener las transacciones donde el empleado es el responsable del pago en efectivo
      final cashOutResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_cash_out
        FROM transactions
        WHERE type = 'expense' AND employee_id = ?
      ''', [employeeId]);

      final totalCashOut = cashOutResult.isNotEmpty
          ? (cashOutResult.first['total_cash_out'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Obtener las transferencias donde el empleado es el destino
      final transfersInResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_transfers_in
        FROM account_transfers
        WHERE destination_type = 'employee' AND destination_id = ?
      ''', [employeeId]);

      final totalTransfersIn = transfersInResult.isNotEmpty
          ? (transfersInResult.first['total_transfers_in'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Obtener las transferencias donde el empleado es el origen
      final transfersOutResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_transfers_out
        FROM account_transfers
        WHERE source_type = 'employee' AND source_id = ?
      ''', [employeeId]);

      final totalTransfersOut = transfersOutResult.isNotEmpty
          ? (transfersOutResult.first['total_transfers_out'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Calcular el balance
      return totalCashIn - totalCashOut + totalTransfersIn - totalTransfersOut;
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating employee balance: $e');
      }
      return 0.0;
    }
  }

  // Obtener el balance de una cuenta bancaria
  Future<double> getBankAccountBalance(int bankAccountId) async {
    try {
      final db = await _dbHelper.database;

      final result = await db.query(
        'bank_accounts',
        columns: ['balance'],
        where: 'id = ?',
        whereArgs: [bankAccountId],
      );

      if (result.isNotEmpty) {
        return (result.first['balance'] as num).toDouble();
      }

      return 0.0;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting bank account balance: $e');
      }
      return 0.0;
    }
  }
}
