import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';

import '../../../shared/models/service_request.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/inventory_transaction_repository.dart';
import '../widgets/status_badge.dart';
import '../widgets/priority_badge.dart';
import '../widgets/image_capture_widget.dart';
import '../widgets/inventory_selection_widget.dart';
import 'service_inventory_transactions_screen.dart';

class ServiceRequestDetailsScreen extends StatefulWidget {
  final ServiceRequest serviceRequest;

  const ServiceRequestDetailsScreen({
    super.key,
    required this.serviceRequest,
  });

  @override
  State<ServiceRequestDetailsScreen> createState() => _ServiceRequestDetailsScreenState();
}

class _ServiceRequestDetailsScreenState extends State<ServiceRequestDetailsScreen> {
  late ServiceRequest _serviceRequest;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _serviceRequest = widget.serviceRequest;
  }

  Future<void> _updateServiceRequestStatus(ServiceRequestStatus newStatus, {String? solution}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث حالة طلب الخدمة في قاعدة البيانات
      final serviceRequestRepository = ServiceRequestRepository();

      // إذا كانت الحالة الجديدة هي "مكتمل" وتم تقديم حل، قم بتحديث الطلب بالكامل
      if (newStatus == ServiceRequestStatus.completed && solution != null) {
        final updatedRequest = _serviceRequest.copyWith(
          status: newStatus,
          solution: solution,
          completedDate: DateTime.now(),
        );

        final result = await serviceRequestRepository.updateServiceRequest(updatedRequest);

        if (result > 0) {
          setState(() {
            _serviceRequest = updatedRequest;
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إكمال طلب الخدمة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          setState(() {
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حدث خطأ أثناء تحديث طلب الخدمة'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // تحديث حالة الطلب فقط
        final result = await serviceRequestRepository.updateServiceRequestStatus(
          _serviceRequest.id!,
          newStatus,
        );

        if (result > 0) {
          setState(() {
            _serviceRequest = _serviceRequest.copyWith(
              status: newStatus,
              completedDate: newStatus == ServiceRequestStatus.completed
                  ? DateTime.now()
                  : _serviceRequest.completedDate,
            );
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تحديث حالة الطلب إلى ${ServiceRequest.getStatusName(newStatus)}'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          setState(() {
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حدث خطأ أثناء تحديث حالة طلب الخدمة'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCompleteServiceRequestDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String solution = '';
    String? technicalNotes;
    List<String> usedParts = [];
    String currentPart = '';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('إكمال الطلب ${_serviceRequest.reference}'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الحل / الإجراء المتخذ',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الحل أو الإجراء المتخذ';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      solution = value!;
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'قطع الغيار المستخدمة:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  // Lista de piezas usadas
                  if (usedParts.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        children: [
                          ...usedParts.map((part) => Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4),
                                child: Row(
                                  children: [
                                    const Icon(Icons.build, size: 16),
                                    const SizedBox(width: 8),
                                    Expanded(child: Text(part)),
                                    IconButton(
                                      icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                                      onPressed: () {
                                        usedParts.remove(part);
                                        (context as Element).markNeedsBuild();
                                      },
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    ),
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ),
                  const SizedBox(height: 8),
                  // Campo para agregar nueva pieza
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'إضافة قطعة غيار',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          onChanged: (value) {
                            currentPart = value;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          if (currentPart.isNotEmpty) {
                            usedParts.add(currentPart);
                            currentPart = '';
                            (context as Element).markNeedsBuild();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.all(12),
                          minimumSize: const Size(0, 0),
                        ),
                        child: const Icon(Icons.add, size: 20),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات فنية (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                    onSaved: (value) {
                      technicalNotes = value;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // تحديث طلب الخدمة في قاعدة البيانات
                  _completeServiceRequest(
                    solution: solution,
                    usedParts: usedParts.isNotEmpty ? usedParts : null,
                    technicalNotes: technicalNotes,
                  );
                }
              },
              child: const Text('إكمال'),
            ),
          ],
        );
      },
    );
  }

  void _showCancelConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الإلغاء'),
          content: Text(
            'هل أنت متأكد من رغبتك في إلغاء الطلب ${_serviceRequest.reference}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('لا'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _updateServiceRequestStatus(ServiceRequestStatus.cancelled);
              },
              child: const Text(
                'نعم، إلغاء الطلب',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _completeServiceRequest({
    required String solution,
    List<String>? usedParts,
    String? technicalNotes,
  }) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث طلب الخدمة في قاعدة البيانات
      final serviceRequestRepository = ServiceRequestRepository();

      // إنشاء نسخة محدثة من طلب الخدمة
      final updatedRequest = _serviceRequest.copyWith(
        status: ServiceRequestStatus.completed,
        solution: solution,
        usedParts: usedParts,
        technicalNotes: technicalNotes,
        completedDate: DateTime.now(),
        // الحفاظ على الصور وعناصر المخزون الحالية
        solutionImages: _serviceRequest.solutionImages,
        usedInventoryIds: _serviceRequest.usedInventoryIds,
        usedInventoryQuantities: _serviceRequest.usedInventoryQuantities,
      );

      // تحديث طلب الخدمة في قاعدة البيانات
      final result = await serviceRequestRepository.updateServiceRequest(updatedRequest);

      if (result > 0) {
        // إذا كان هناك عناصر مخزون مستخدمة، قم بتحديث المخزون
        if (updatedRequest.usedInventoryIds != null &&
            updatedRequest.usedInventoryQuantities != null &&
            updatedRequest.usedInventoryIds!.isNotEmpty) {

          final inventoryTransactionRepository = InventoryTransactionRepository();

          // التحقق من وجود معاملات مخزون سابقة لهذا الطلب
          final existingTransactions = await inventoryTransactionRepository.getInventoryTransactionsByServiceRequestId(
            updatedRequest.id!,
          );

          // إذا لم تكن هناك معاملات سابقة، قم بإنشاء معاملات جديدة
          if (existingTransactions.isEmpty) {
            // إنشاء معاملات المخزون لطلب الخدمة
            final success = await inventoryTransactionRepository.createInventoryTransactionsForServiceRequest(
              updatedRequest.id!,
              updatedRequest.reference,
              updatedRequest.usedInventoryIds!,
              updatedRequest.usedInventoryQuantities!,
              1, // استخدم معرف المستخدم الحالي (يمكن تعديله لاحقًا)
              'admin', // استخدم اسم المستخدم الحالي (يمكن تعديله لاحقًا)
            );

            if (!success) {
              setState(() {
                _isLoading = false;
              });

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إكمال الطلب ولكن حدث خطأ أثناء تحديث المخزون'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
              return;
            }
          }
        }

        setState(() {
          _serviceRequest = updatedRequest;
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إكمال طلب الخدمة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث طلب الخدمة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAssignTechnicianDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String technicianName = '';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تعيين فني للطلب ${_serviceRequest.reference}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'اسم الموظف الفني',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسم الموظف الفني';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    technicianName = value!;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  _assignTechnician(technicianName);
                }
              },
              child: const Text('تعيين'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _assignTechnician(String technicianName) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث طلب الخدمة في قاعدة البيانات
      final serviceRequestRepository = ServiceRequestRepository();

      // إنشاء نسخة محدثة من طلب الخدمة
      final updatedRequest = _serviceRequest.copyWith(
        assignedToName: technicianName,
      );

      // تحديث طلب الخدمة في قاعدة البيانات
      final result = await serviceRequestRepository.updateServiceRequest(updatedRequest);

      if (result > 0) {
        setState(() {
          _serviceRequest = updatedRequest;
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تعيين $technicianName للطلب'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تعيين الموظف الفني للطلب'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('طلب خدمة ${_serviceRequest.reference}'),
        actions: [
          if (_serviceRequest.status != ServiceRequestStatus.completed &&
              _serviceRequest.status != ServiceRequestStatus.cancelled)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  Navigator.pushNamed(
                    context,
                    '/service-request-edit',
                    arguments: _serviceRequest,
                  );
                } else if (value == 'start') {
                  _updateServiceRequestStatus(ServiceRequestStatus.inProgress);
                } else if (value == 'complete') {
                  _showCompleteServiceRequestDialog(context);
                } else if (value == 'cancel') {
                  _showCancelConfirmation(context);
                } else if (value == 'assign') {
                  _showAssignTechnicianDialog(context);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 20),
                      SizedBox(width: 8),
                      Text('تعديل الطلب'),
                    ],
                  ),
                ),
                if (_serviceRequest.status == ServiceRequestStatus.pending)
                  const PopupMenuItem(
                    value: 'start',
                    child: Row(
                      children: [
                        Icon(Icons.play_arrow, size: 20),
                        SizedBox(width: 8),
                        Text('بدء العمل'),
                      ],
                    ),
                  ),
                if (_serviceRequest.status == ServiceRequestStatus.inProgress)
                  const PopupMenuItem(
                    value: 'complete',
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, size: 20),
                        SizedBox(width: 8),
                        Text('إكمال الطلب'),
                      ],
                    ),
                  ),
                if (_serviceRequest.assignedToName == null)
                  const PopupMenuItem(
                    value: 'assign',
                    child: Row(
                      children: [
                        Icon(Icons.person_add, size: 20),
                        SizedBox(width: 8),
                        Text('تعيين فني'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'cancel',
                  child: Row(
                    children: [
                      Icon(Icons.cancel, size: 20, color: Colors.red),
                      SizedBox(width: 8),
                      Text('إلغاء الطلب', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeaderCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildDetailsCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildCustomerCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildDeviceInfoCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  if (_serviceRequest.solution != null) _buildSolutionCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  if (_serviceRequest.usedParts != null && _serviceRequest.usedParts!.isNotEmpty)
                    _buildUsedPartsCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildInventoryItemsCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  if (_serviceRequest.technicalNotes != null) _buildTechnicalNotesCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildProblemImagesCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  if (_serviceRequest.status == ServiceRequestStatus.completed)
                    _buildSolutionImagesCard(),
                  const SizedBox(height: AppDimensions.paddingL),
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'طلب خدمة ${_serviceRequest.reference}',
                        style: AppTextStyles.heading2,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        ServiceRequest.getRequestTypeName(_serviceRequest.requestType),
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    StatusBadge(status: _serviceRequest.status),
                    const SizedBox(height: 4),
                    PriorityBadge(priority: _serviceRequest.priority),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الطلب',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الوصف',
              _serviceRequest.description,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الموقع',
              _serviceRequest.location ?? 'غير محدد',
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'تاريخ الجدولة',
              DateFormat('dd/MM/yyyy').format(_serviceRequest.scheduledDate),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الموظف الفني المسؤول',
              _serviceRequest.assignedToName ?? 'غير معين',
            ),
            if (_serviceRequest.completedDate != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow(
                'تاريخ الإكمال',
                DateFormat('dd/MM/yyyy').format(_serviceRequest.completedDate!),
              ),
            ],
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'تاريخ الإنشاء',
              DateFormat('dd/MM/yyyy').format(_serviceRequest.createdAt),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات العميل',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'اسم العميل',
              _serviceRequest.customerName,
            ),
            if (_serviceRequest.customerPhone != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow(
                'رقم الهاتف',
                _serviceRequest.customerPhone!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionCard() {
    return Card(
      elevation: 2,
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الحل / الإجراء المتخذ',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              _serviceRequest.solution!,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (_serviceRequest.status == ServiceRequestStatus.pending)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                _updateServiceRequestStatus(ServiceRequestStatus.inProgress);
              },
              icon: const Icon(Icons.play_arrow),
              label: const Text('بدء العمل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        if (_serviceRequest.status == ServiceRequestStatus.inProgress) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                _showCompleteServiceRequestDialog(context);
              },
              icon: const Icon(Icons.check_circle),
              label: const Text('إكمال الطلب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
        if (_serviceRequest.status != ServiceRequestStatus.completed &&
            _serviceRequest.status != ServiceRequestStatus.cancelled) ...[
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                _showCancelConfirmation(context);
              },
              icon: const Icon(Icons.cancel, color: Colors.red),
              label: const Text('إلغاء الطلب'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceInfoCard() {
    // Si no hay información del dispositivo, no mostrar la tarjeta
    if (_serviceRequest.deviceType == null &&
        _serviceRequest.deviceBrand == null &&
        _serviceRequest.deviceModel == null &&
        _serviceRequest.serialNumber == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الجهاز',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            if (_serviceRequest.deviceType != null)
              _buildDetailRow('نوع الجهاز', _serviceRequest.deviceType!),
            if (_serviceRequest.deviceBrand != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow('الماركة', _serviceRequest.deviceBrand!),
            ],
            if (_serviceRequest.deviceModel != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow('الموديل', _serviceRequest.deviceModel!),
            ],
            if (_serviceRequest.serialNumber != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow('الرقم التسلسلي', _serviceRequest.serialNumber!),
            ],
            if (_serviceRequest.installationDate != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow('تاريخ التركيب', _serviceRequest.installationDate!),
            ],
            if (_serviceRequest.warrantyInfo != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow('معلومات الضمان', _serviceRequest.warrantyInfo!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUsedPartsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'قطع الغيار المستخدمة',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _serviceRequest.usedParts!.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.build, size: 16, color: AppColors.textSecondary),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _serviceRequest.usedParts![index],
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalNotesCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات فنية',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              _serviceRequest.technicalNotes!,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProblemImagesCard() {
    return ImageCaptureWidget(
      title: 'صور المشكلة',
      icon: Icons.error_outline,
      color: Colors.orange,
      initialImages: _serviceRequest.problemImages,
      onImagesChanged: (images) {
        _updateProblemImages(images);
      },
    );
  }

  Widget _buildSolutionImagesCard() {
    return ImageCaptureWidget(
      title: 'صور الإصلاح',
      icon: Icons.check_circle_outline,
      color: Colors.green,
      initialImages: _serviceRequest.solutionImages,
      onImagesChanged: (images) {
        _updateSolutionImages(images);
      },
    );
  }

  Future<void> _updateProblemImages(List<String> images) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث طلب الخدمة في قاعدة البيانات
      final serviceRequestRepository = ServiceRequestRepository();

      // إنشاء نسخة محدثة من طلب الخدمة
      final updatedRequest = _serviceRequest.copyWith(
        problemImages: images,
      );

      // تحديث طلب الخدمة في قاعدة البيانات
      final result = await serviceRequestRepository.updateServiceRequest(updatedRequest);

      if (result > 0) {
        setState(() {
          _serviceRequest = updatedRequest;
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث صور المشكلة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث صور المشكلة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateSolutionImages(List<String> images) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث طلب الخدمة في قاعدة البيانات
      final serviceRequestRepository = ServiceRequestRepository();

      // إنشاء نسخة محدثة من طلب الخدمة
      final updatedRequest = _serviceRequest.copyWith(
        solutionImages: images,
      );

      // تحديث طلب الخدمة في قاعدة البيانات
      final result = await serviceRequestRepository.updateServiceRequest(updatedRequest);

      if (result > 0) {
        setState(() {
          _serviceRequest = updatedRequest;
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث صور الإصلاح بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث صور الإصلاح'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildInventoryItemsCard() {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'قطع الغيار المستخدمة من المخزون',
                  style: AppTextStyles.heading3,
                ),
                if (_serviceRequest.status == ServiceRequestStatus.completed &&
                    _serviceRequest.usedInventoryIds != null &&
                    _serviceRequest.usedInventoryIds!.isNotEmpty)
                  TextButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ServiceInventoryTransactionsScreen(
                            serviceRequest: _serviceRequest,
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.history, size: 16),
                    label: const Text('عرض المعاملات'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                    ),
                  ),
              ],
            ),
          ),
          InventorySelectionWidget(
            title: '',
            initialSelectedIds: _serviceRequest.usedInventoryIds,
            initialQuantities: _serviceRequest.usedInventoryQuantities,
            onSelectionChanged: (selectedIds, quantities) {
              _updateInventoryItems(selectedIds, quantities);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _updateInventoryItems(List<int> selectedIds, List<int> quantities) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث طلب الخدمة في قاعدة البيانات
      final serviceRequestRepository = ServiceRequestRepository();

      // إنشاء نسخة محدثة من طلب الخدمة
      final updatedRequest = _serviceRequest.copyWith(
        usedInventoryIds: selectedIds.isNotEmpty ? selectedIds : null,
        usedInventoryQuantities: quantities.isNotEmpty ? quantities : null,
      );

      // تحديث طلب الخدمة في قاعدة البيانات
      final result = await serviceRequestRepository.updateServiceRequest(updatedRequest);

      if (result > 0) {
        setState(() {
          _serviceRequest = updatedRequest;
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث قطع الغيار المستخدمة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث قطع الغيار المستخدمة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
