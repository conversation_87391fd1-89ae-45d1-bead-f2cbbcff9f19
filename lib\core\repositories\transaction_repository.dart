import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/transaction.dart';
import '../../shared/models/customer.dart';
import '../../shared/models/supplier.dart';
import '../../shared/models/employee.dart';
import '../../shared/models/bank_account.dart';
import '../../shared/models/category.dart';
import './customer_repository.dart';
import './supplier_repository.dart';
import './employee_repository.dart';
import './bank_account_repository.dart';
import './transaction_category_repository.dart';
import './cash_box_repository.dart';
import '../../shared/models/cash_box.dart';

class TransactionRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CustomerRepository _customerRepository = CustomerRepository();
  final SupplierRepository _supplierRepository = SupplierRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final BankAccountRepository _bankAccountRepository = BankAccountRepository();
  final TransactionCategoryRepository _categoryRepository = TransactionCategoryRepository();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();

  // Get all transactions
  Future<List<Transaction>> getAllTransactions() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('transactions');

      return await _mapTransactions(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions: $e');
      }
      return [];
    }
  }

  // Get transactions by type (income, expense)
  Future<List<Transaction>> getTransactionsByType(String type) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'type = ?',
        whereArgs: [type],
      );

      return await _mapTransactions(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions by type: $e');
      }
      return [];
    }
  }

  // Get transactions by date range
  Future<List<Transaction>> getTransactionsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'date BETWEEN ? AND ?',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      );

      return await _mapTransactions(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions by date range: $e');
      }
      return [];
    }
  }

  // Get transactions by customer
  Future<List<Transaction>> getTransactionsByCustomer(int customerId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'customer_id = ?',
        whereArgs: [customerId],
      );

      return await _mapTransactions(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions by customer: $e');
      }
      return [];
    }
  }

  // Get transactions by supplier
  Future<List<Transaction>> getTransactionsBySupplier(int supplierId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'supplier_id = ?',
        whereArgs: [supplierId],
      );

      return await _mapTransactions(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions by supplier: $e');
      }
      return [];
    }
  }

  // Get transactions by invoice ID
  Future<List<Transaction>> getTransactionsByInvoiceId(int invoiceId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'invoice_id = ?',
        whereArgs: [invoiceId],
      );

      return await _mapTransactions(maps);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions by invoice ID: $e');
      }
      return [];
    }
  }

  // Get transaction by ID
  Future<Transaction?> getTransactionById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'transactions',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        final transactions = await _mapTransactions(maps);
        return transactions.isNotEmpty ? transactions.first : null;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transaction by ID: $e');
      }
      return null;
    }
  }

  // Insert a new transaction
  Future<int> insertTransaction(Transaction transaction) async {
    try {
      final db = await _dbHelper.database;

      // Get category ID if category name is provided
      int? categoryId;
      if (transaction.category != null) {
        final List<Map<String, dynamic>> categoryMaps = await db.query(
          'categories',
          columns: ['id'],
          where: 'name = ? AND status = ?',
          whereArgs: [transaction.category, 'active'],
        );

        if (categoryMaps.isNotEmpty) {
          categoryId = categoryMaps.first['id'] as int;
        }
      }

      // Begin transaction
      int transactionId = -1;
      await db.transaction((txn) async {
        // Insert the transaction
        transactionId = await txn.insert(
          'transactions',
          {
            'reference': transaction.reference,
            'date': transaction.date.toIso8601String(),
            'amount': transaction.amount,
            'type': transaction.type.toString().split('.').last,
            'category_id': categoryId,
            'description': transaction.description,
            'payment_method': transaction.paymentMethod.toString().split('.').last,
            'bank_account_id': transaction.bankAccountId,
            'supplier_id': transaction.supplierId,
            'employee_id': transaction.employeeId,
            'customer_id': transaction.customerId,
            'invoice_id': transaction.invoiceId,
            'created_by': transaction.createdBy,
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        // Update related balances

        // Update bank account balance if specified
        if (transaction.bankAccountId != null) {
          await txn.rawUpdate('''
            UPDATE bank_accounts
            SET balance = balance + ?
            WHERE id = ?
          ''', [
            transaction.type == TransactionType.income ? transaction.amount : -transaction.amount,
            transaction.bankAccountId
          ]);
        }

        // Update supplier balance if specified
        if (transaction.supplierId != null) {
          // Update supplier balance
          await txn.rawUpdate('''
            UPDATE suppliers
            SET balance = balance + ?
            WHERE id = ?
          ''', [
            transaction.type == TransactionType.expense ? transaction.amount : -transaction.amount,
            transaction.supplierId
          ]);
        }

        // Update customer balance if implemented in the future
        // Currently, customers don't have a balance field in the database
      });

      return transactionId; // Return the actual transaction ID
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting transaction: $e');
      }
      return -1;
    }
  }

  // Insert transaction with cash box integration
  Future<int> insertTransactionWithCashBox({
    required Transaction transaction,
    required int cashBoxId,
    String? description,
  }) async {
    try {
      final db = await _dbHelper.database;

      return await db.transaction((txn) async {
        // Get category ID if category name is provided
        int? categoryId;
        if (transaction.category != null) {
          final List<Map<String, dynamic>> categoryMaps = await txn.query(
            'categories',
            columns: ['id'],
            where: 'name = ? AND status = ?',
            whereArgs: [transaction.category, 'active'],
          );

          if (categoryMaps.isNotEmpty) {
            categoryId = categoryMaps.first['id'] as int;
          }
        }

        // Insert the main transaction with cash box reference
        final transactionId = await txn.insert(
          'transactions',
          {
            'reference': transaction.reference,
            'date': transaction.date.toIso8601String(),
            'amount': transaction.amount,
            'type': transaction.type.toString().split('.').last,
            'category_id': categoryId,
            'description': transaction.description,
            'payment_method': transaction.paymentMethod.toString().split('.').last,
            'bank_account_id': transaction.bankAccountId,
            'cash_box_id': cashBoxId, // Link to cash box
            'supplier_id': transaction.supplierId,
            'employee_id': transaction.employeeId,
            'customer_id': transaction.customerId,
            'invoice_id': transaction.invoiceId,
            'created_by': transaction.createdBy,
            'created_at': DateTime.now().toIso8601String(),
          },
        );

        // Create corresponding cash box transaction
        final cashBoxTransaction = CashBoxTransaction(
          cashBoxId: cashBoxId,
          type: transaction.type == TransactionType.income
              ? CashBoxTransactionType.income
              : CashBoxTransactionType.expense,
          amount: transaction.amount,
          description: description ?? transaction.description ?? 'Transaction #$transactionId',
          reference: transactionId.toString(),
          referenceType: 'transaction',
          transactionDate: DateTime.parse(transaction.date.toIso8601String()),
          createdAt: DateTime.now(),
        );

        // Insert cash box transaction and update balance
        await txn.insert('cash_box_transactions', cashBoxTransaction.toMap());

        // Update cash box balance
        final balanceChange = transaction.type == TransactionType.income
            ? transaction.amount
            : -transaction.amount;

        await txn.rawUpdate('''
          UPDATE cash_boxes
          SET current_balance = current_balance + ?,
              updated_at = ?,
              version = version + 1
          WHERE id = ?
        ''', [balanceChange, DateTime.now().toIso8601String(), cashBoxId]);

        if (kDebugMode) {
          print('Transaction with cash box integration inserted with ID: $transactionId');
        }

        return transactionId;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting transaction with cash box: $e');
      }
      rethrow;
    }
  }

  // Update an existing transaction
  Future<int> updateTransaction(Transaction transaction) async {
    try {
      final db = await _dbHelper.database;

      // Get the original transaction to calculate balance adjustments
      final originalTransaction = await getTransactionById(transaction.id!);
      if (originalTransaction == null) {
        return 0;
      }

      // Get category ID if category name is provided
      int? categoryId;
      if (transaction.category != null) {
        final List<Map<String, dynamic>> categoryMaps = await db.query(
          'categories',
          columns: ['id'],
          where: 'name = ? AND status = ?',
          whereArgs: [transaction.category, 'active'],
        );

        if (categoryMaps.isNotEmpty) {
          categoryId = categoryMaps.first['id'] as int;
        }
      }

      // Begin transaction
      await db.transaction((txn) async {
        // Update the transaction
        await txn.update(
          'transactions',
          {
            'reference': transaction.reference,
            'date': transaction.date.toIso8601String(),
            'amount': transaction.amount,
            'type': transaction.type.toString().split('.').last,
            'category_id': categoryId,
            'description': transaction.description,
            'payment_method': transaction.paymentMethod.toString().split('.').last,
            'bank_account_id': transaction.bankAccountId,
            'supplier_id': transaction.supplierId,
            'employee_id': transaction.employeeId,
            'customer_id': transaction.customerId,
            'invoice_id': transaction.invoiceId,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [transaction.id],
        );

        // Update bank account balances if needed
        if (originalTransaction.bankAccountId != transaction.bankAccountId ||
            originalTransaction.amount != transaction.amount ||
            originalTransaction.type != transaction.type) {

          // Revert original transaction effect on bank account
          if (originalTransaction.bankAccountId != null) {
            await txn.rawUpdate('''
              UPDATE bank_accounts
              SET balance = balance - ?
              WHERE id = ?
            ''', [
              originalTransaction.type == TransactionType.income ? originalTransaction.amount : -originalTransaction.amount,
              originalTransaction.bankAccountId
            ]);
          }

          // Apply new transaction effect on bank account
          if (transaction.bankAccountId != null) {
            await txn.rawUpdate('''
              UPDATE bank_accounts
              SET balance = balance + ?
              WHERE id = ?
            ''', [
              transaction.type == TransactionType.income ? transaction.amount : -transaction.amount,
              transaction.bankAccountId
            ]);
          }
        }

        // Update supplier balances if needed
        if (originalTransaction.supplierId != transaction.supplierId ||
            originalTransaction.amount != transaction.amount ||
            originalTransaction.type != transaction.type) {

          // Revert original transaction effect on supplier
          if (originalTransaction.supplierId != null) {
            await txn.rawUpdate('''
              UPDATE suppliers
              SET balance = balance - ?
              WHERE id = ?
            ''', [
              originalTransaction.type == TransactionType.expense ? originalTransaction.amount : -originalTransaction.amount,
              originalTransaction.supplierId
            ]);
          }

          // Apply new transaction effect on supplier
          if (transaction.supplierId != null) {
            await txn.rawUpdate('''
              UPDATE suppliers
              SET balance = balance + ?
              WHERE id = ?
            ''', [
              transaction.type == TransactionType.expense ? transaction.amount : -transaction.amount,
              transaction.supplierId
            ]);
          }
        }
      });

      return 1; // Success
    } catch (e) {
      if (kDebugMode) {
        print('Error updating transaction: $e');
      }
      return 0;
    }
  }

  // Delete a transaction
  Future<int> deleteTransaction(int id) async {
    try {
      final db = await _dbHelper.database;

      // Get the transaction to revert balance adjustments
      final transaction = await getTransactionById(id);
      if (transaction == null) {
        return 0;
      }

      // Begin transaction
      await db.transaction((txn) async {
        // Delete the transaction
        final deletedRows = await txn.delete(
          'transactions',
          where: 'id = ?',
          whereArgs: [id],
        );

        if (deletedRows == 0) {
          return 0; // No transaction was deleted
        }

        // Revert bank account balance adjustment
        if (transaction.bankAccountId != null) {
          await txn.rawUpdate('''
            UPDATE bank_accounts
            SET balance = balance - ?
            WHERE id = ?
          ''', [
            transaction.type == TransactionType.income ? transaction.amount : -transaction.amount,
            transaction.bankAccountId
          ]);
        }

        // Revert supplier balance adjustment
        if (transaction.supplierId != null) {
          await txn.rawUpdate('''
            UPDATE suppliers
            SET balance = balance - ?
            WHERE id = ?
          ''', [
            transaction.type == TransactionType.expense ? transaction.amount : -transaction.amount,
            transaction.supplierId
          ]);
        }
      });

      return 1; // Success
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting transaction: $e');
      }
      return 0;
    }
  }

  // Helper method to parse transaction type
  TransactionType _parseTransactionType(String type) {
    switch (type.toLowerCase()) {
      case 'income':
        return TransactionType.income;
      case 'expense':
      default:
        return TransactionType.expense;
    }
  }

  // Helper method to parse payment method
  PaymentMethod _parsePaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'bank_transfer':
      case 'banktransfer':
        return PaymentMethod.bankTransfer;
      case 'check':
        return PaymentMethod.check;
      case 'credit_card':
      case 'creditcard':
        return PaymentMethod.creditCard;
      case 'other':
      default:
        return PaymentMethod.other;
    }
  }

  // Obtener categorías de ingresos
  Future<List<CategoryModel>> getIncomeCategories() async {
    return await _categoryRepository.getIncomeCategories();
  }

  // Obtener categorías de gastos
  Future<List<CategoryModel>> getExpenseCategories() async {
    return await _categoryRepository.getExpenseCategories();
  }

  // Obtener categoría por ID
  Future<CategoryModel?> getCategoryById(int id) async {
    return await _categoryRepository.getCategoryById(id);
  }

  // Obtener clientes para transacciones
  Future<List<Customer>> getCustomersForTransactions() async {
    try {
      return await _customerRepository.getActiveCustomers();
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo clientes para transacciones: $e');
      }
      return [];
    }
  }

  // Obtener proveedores para transacciones
  Future<List<Supplier>> getSuppliersForTransactions() async {
    try {
      return await _supplierRepository.getAllSuppliers();
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo proveedores para transacciones: $e');
      }
      return [];
    }
  }

  // Obtener empleados para transacciones
  Future<List<Employee>> getEmployeesForTransactions() async {
    try {
      return await _employeeRepository.getAllEmployees();
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo empleados para transacciones: $e');
      }
      return [];
    }
  }

  // Obtener cuentas bancarias para transacciones
  Future<List<BankAccount>> getBankAccountsForTransactions() async {
    try {
      return await _bankAccountRepository.getAllBankAccounts();
    } catch (e) {
      if (kDebugMode) {
        print('Error obteniendo cuentas bancarias para transacciones: $e');
      }
      return [];
    }
  }

  // Helper method to map database results to Transaction objects
  Future<List<Transaction>> _mapTransactions(List<Map<String, dynamic>> maps) async {
    final List<Transaction> transactions = [];
    final db = await _dbHelper.database;

    for (final map in maps) {
      // Get related entity information
      String? customerName;
      String? supplierName;
      String? employeeName;
      String? bankAccountName;
      String? categoryName;

      try {
        // Get category name if category_id exists
        if (map['category_id'] != null) {
          final List<Map<String, dynamic>> categoryMaps = await db.query(
            'categories',
            columns: ['name'],
            where: 'id = ?',
            whereArgs: [map['category_id']],
          );

          if (categoryMaps.isNotEmpty) {
            categoryName = categoryMaps.first['name'] as String;
          }
        }

        // Get customer name if customer_id exists
        if (map['customer_id'] != null) {
          final customer = await _customerRepository.getCustomerById(map['customer_id'] as int);
          if (customer != null) {
            customerName = customer.name;
          }
        }

        // Get supplier name if supplier_id exists
        if (map['supplier_id'] != null) {
          final supplier = await _supplierRepository.getSupplierById(map['supplier_id'] as int);
          if (supplier != null) {
            supplierName = supplier.name;
          }
        }

        // Get employee name if employee_id exists
        if (map['employee_id'] != null) {
          final employee = await _employeeRepository.getEmployeeById(map['employee_id'] as int);
          if (employee != null) {
            employeeName = employee.name;
          }
        }

        // Get bank account name if bank_account_id exists
        if (map['bank_account_id'] != null) {
          final bankAccount = await _bankAccountRepository.getBankAccountById(map['bank_account_id'] as int);
          if (bankAccount != null) {
            bankAccountName = '${bankAccount.bankName} - ${bankAccount.accountNumber}';
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error getting related information for transaction: $e');
        }
      }

      transactions.add(Transaction(
        id: map['id'] as int,
        reference: map['reference'] as String,
        date: DateTime.parse(map['date'] as String),
        amount: map['amount'] as double,
        type: _parseTransactionType(map['type'] as String),
        category: categoryName, // Use the category name from the categories table
        description: map['description'] as String?,
        paymentMethod: _parsePaymentMethod(map['payment_method'] as String),
        invoiceId: map['invoice_id'] as int?,
        customerId: map['customer_id'] as int?,
        supplierId: map['supplier_id'] as int?,
        employeeId: map['employee_id'] as int?,
        createdBy: map['created_by'] as int?,
        createdAt: DateTime.parse(map['created_at'] as String),
        updatedAt: map['updated_at'] != null
            ? DateTime.parse(map['updated_at'] as String)
            : null,
        bankAccountId: map['bank_account_id'] as int?,
        // Add additional information
        customerName: customerName,
        supplierName: supplierName,
        employeeName: employeeName,
        bankAccountName: bankAccountName,
      ));
    }

    return transactions;
  }
}
