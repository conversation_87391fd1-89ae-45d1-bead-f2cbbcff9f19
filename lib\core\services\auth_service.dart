import 'package:flutter/foundation.dart';
import '../../shared/models/user.dart';
import '../database/database_helper.dart'; // Import DatabaseHelper

class AuthResult {
  final bool success;
  final String? token;
  final User? user;
  final String? message;

  AuthResult({
    required this.success,
    this.token,
    this.user,
    this.message,
  });
}

class AuthService {
  final DatabaseHelper _dbHelper = DatabaseHelper(); // Get DatabaseHelper instance

  // Login with username and password
  Future<AuthResult> login(String username, String password) async {
    try {
      if (kDebugMode) {
        print('Attempting login with username: $username, password: $password');
      }

      final db = await _dbHelper.database;

      // First, check if the database has any users
      final List<Map<String, dynamic>> allUsers = await db.query('users');
      if (kDebugMode) {
        print('Total users in database: ${allUsers.length}');
        for (var user in allUsers) {
          print('User: ${user['name']}, Password: ${user['password']}');
        }
      }

      // Check if the admin user exists
      final List<Map<String, dynamic>> adminUsers = await db.query(
        'users',
        where: 'name = ?',
        whereArgs: ['admin'],
      );

      if (kDebugMode) {
        print('Admin users found: ${adminUsers.length}');
      }

      // Query for the user with the provided credentials
      // First try exact match
      List<Map<String, dynamic>> userMaps = await db.query(
        'users',
        where: 'name = ? AND password = ?',
        whereArgs: [username, password],
      );

      // If no match, try case-insensitive match for username
      if (userMaps.isEmpty) {
        if (kDebugMode) {
          print('No exact match found, trying case-insensitive match');
        }

        // Get all users and filter manually for case-insensitive match
        final allUsersList = await db.query('users');
        userMaps = allUsersList.where((user) =>
          user['name'].toString().toLowerCase() == username.toLowerCase() &&
          user['password'] == password
        ).toList();

        if (kDebugMode) {
          print('Case-insensitive match found: ${userMaps.length} users');
        }
      }

      // Special case for admin user
      if (userMaps.isEmpty && username.toLowerCase() == 'admin' && password == '1') {
        if (kDebugMode) {
          print('Special case: admin user login attempt');
        }

        // Create admin user if it doesn't exist
        await _dbHelper.ensureAdminUserExists();

        // Try again
        userMaps = await db.query(
          'users',
          where: 'name = ? AND password = ?',
          whereArgs: ['admin', '1'],
        );

        if (kDebugMode) {
          print('After ensuring admin exists: ${userMaps.length} users found');
        }
      }

      if (kDebugMode) {
        print('Users matching credentials: ${userMaps.length}');
      }

      if (userMaps.isNotEmpty) {
        final userMap = userMaps.first;

        if (kDebugMode) {
          print('User found: ${userMap['name']}');
        }

        // Fetch roles for the user
        final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
          SELECT r.name FROM roles r
          JOIN user_roles ur ON r.id = ur.role_id
          WHERE ur.user_id = ?
        ''', [userMap['id']]);

        if (kDebugMode) {
          print('Roles found: ${roleMaps.length}');
          for (var role in roleMaps) {
            print('Role: ${role['name']}');
          }
        }

        final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

        // Update last login time
        await db.update(
          'users',
          {'last_login': DateTime.now().toIso8601String()},
          where: 'id = ?',
          whereArgs: [userMap['id']],
        );

        // Create User object using fromMap constructor
        // Pass the fetched roles list to the fromMap constructor
        final user = User.fromMap({...userMap, 'roles': roles});

        if (kDebugMode) {
          print('User object created: ${user.name}, Roles: ${user.roles}');
        }

        // Generate a simple mock token for now
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';

        return AuthResult(
          success: true,
          token: token,
          user: user,
          message: 'Login successful',
        );
      } else {
        if (kDebugMode) {
          print('No user found with the provided credentials');
        }

        return AuthResult(
          success: false,
          message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      return AuthResult(
        success: false,
        message: 'حدث خطأ أثناء تسجيل الدخول',
      );
    }
  }

  // Register a new user
  Future<AuthResult> register(String name, String email, String password) async {
    try {
      final db = await _dbHelper.database;

      // Check if username already exists
      final existingUsers = await db.query(
        'users',
        where: 'name = ?',
        whereArgs: [name],
      );

      if (existingUsers.isNotEmpty) {
        return AuthResult(
          success: false,
          message: 'اسم المستخدم موجود بالفعل',
        );
      }

      // Insert new user into the users table
      final newUserMap = {
        'name': name,
        'email': email,
        'password': password,
        'status': 'active',
        'created_at': DateTime.now().toIso8601String(),
      };

      final userId = await db.insert('users', newUserMap);

      if (userId > 0) {
        // Assign default role (employee) to the new user in user_roles table
        // First, find the role_id for 'employee'
        final List<Map<String, dynamic>> roleMaps = await db.query(
          'roles',
          where: 'name = ?',
          whereArgs: ['employee'],
        );

        if (roleMaps.isNotEmpty) {
          final roleId = roleMaps.first['id'];
          await db.insert('user_roles', {
            'user_id': userId,
            'role_id': roleId,
          });
        } else {
          // Handle case where 'employee' role does not exist in roles table
          if (kDebugMode) {
            print('Warning: "employee" role not found in roles table.');
          }
          // Optionally, you might want to fail registration or log this issue
        }

        // Fetch the newly created user with their roles
        final List<Map<String, dynamic>> userMaps = await db.query(
          'users',
          where: 'id = ?',
          whereArgs: [userId],
        );

        final userMap = userMaps.first;

        // Fetch roles for the user
        final List<Map<String, dynamic>> userRoleMaps = await db.rawQuery('''
          SELECT r.name FROM roles r
          JOIN user_roles ur ON r.id = ur.role_id
          WHERE ur.user_id = ?
        ''', [userId]);

        final List<String> roles = userRoleMaps.map((roleMap) => roleMap['name'] as String).toList();


        final user = User.fromMap({...userMap, 'roles': roles});

        // Generate a simple mock token for now
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';


        return AuthResult(
          success: true,
          token: token,
          user: user,
          message: 'Registration successful',
        );
      } else {
        return AuthResult(
          success: false,
          message: 'Failed to register user',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Registration error: $e');
      }
      return AuthResult(
        success: false,
        message: 'An error occurred during registration',
      );
    }
  }

  // Forgot password (placeholder - needs implementation)
  Future<AuthResult> forgotPassword(String email) async {
    // This would typically involve sending a reset link via email
    // For now, it's a placeholder
    return AuthResult(
      success: false,
      message: 'Forgot password functionality not implemented yet.',
    );
  }

  // Logout (placeholder - token invalidation not needed for local DB)
  Future<void> logout(String? token) async {
    // For a local database, token invalidation is not strictly necessary
    // This method can remain as a placeholder or be removed if not needed
    if (kDebugMode) {
      print('Logout called. Token: $token');
    }
  }
}
