import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_request.dart';
import '../../../shared/models/inventory_transaction.dart';
import '../../../core/repositories/inventory_transaction_repository.dart';

class ServiceInventoryTransactionsScreen extends StatefulWidget {
  final ServiceRequest serviceRequest;

  const ServiceInventoryTransactionsScreen({
    super.key,
    required this.serviceRequest,
  });

  @override
  State<ServiceInventoryTransactionsScreen> createState() => _ServiceInventoryTransactionsScreenState();
}

class _ServiceInventoryTransactionsScreenState extends State<ServiceInventoryTransactionsScreen> {
  final InventoryTransactionRepository _transactionRepository = InventoryTransactionRepository();
  // Nota: Podríamos necesitar un repositorio de inventario en el futuro
  // para mostrar información adicional sobre los elementos

  List<InventoryTransaction> _transactions = [];
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      if (widget.serviceRequest.id == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'لا يمكن تحميل معاملات المخزون لطلب خدمة غير محفوظ';
        });
        return;
      }

      final transactions = await _transactionRepository.getInventoryTransactionsByServiceRequestId(
        widget.serviceRequest.id!,
      );

      setState(() {
        _transactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل معاملات المخزون: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معاملات المخزون - ${widget.serviceRequest.reference}'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage,
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadTransactions,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : _transactions.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد معاملات مخزون لهذا الطلب',
                        style: TextStyle(fontSize: 16),
                      ),
                    )
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSummaryCard(),
                          const SizedBox(height: AppDimensions.paddingM),
                          _buildTransactionsTable(),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildSummaryCard() {
    // حساب إجمالي العناصر المستخدمة
    int totalItems = _transactions.length;
    double totalQuantity = _transactions.fold(0.0, (sum, transaction) => sum + transaction.quantity);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص استخدام المخزون',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'عدد العناصر',
                    '$totalItems',
                    Icons.category,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الكمية',
                    '$totalQuantity',
                    Icons.inventory,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل معاملات المخزون',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: 20,
                headingTextStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
                columns: const [
                  DataColumn(label: Text('العنصر')),
                  DataColumn(label: Text('الكود')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('الوحدة')),
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('السبب')),
                ],
                rows: _transactions.map((transaction) {
                  return DataRow(
                    cells: [
                      DataCell(Text(transaction.inventoryItemName)),
                      DataCell(Text(transaction.inventoryItemCode ?? '-')),
                      DataCell(Text('${transaction.quantity}')),
                      DataCell(Text(transaction.unit ?? 'قطعة')),
                      DataCell(Text(DateFormat('yyyy/MM/dd').format(transaction.transactionDate))),
                      DataCell(Text(transaction.reason ?? '-')),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
