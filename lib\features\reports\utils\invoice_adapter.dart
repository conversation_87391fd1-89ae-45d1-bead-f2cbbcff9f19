import 'package:icecorner/shared/models/invoice.dart' as app_model;
import 'package:icecorner/features/reports/models/report_models.dart' as report_model;

/// Adaptador para convertir entre los modelos de factura de la aplicación y los modelos de factura de los reportes
class InvoiceAdapter {
  /// Convierte una factura del modelo de la aplicación al modelo de reporte
  static report_model.Invoice toReportModel(app_model.Invoice invoice) {
    // Calcular el monto pagado según el estado de la factura
    double amountPaid = 0.0;

    if (invoice.status == app_model.InvoiceStatus.paid) {
      // Si está completamente pagada, el monto pagado es igual al total
      amountPaid = invoice.total;
    } else if (invoice.status == app_model.InvoiceStatus.partiallyPaid) {
      // Si está parcialmente pagada, asumimos un 50% de pago por defecto
      // Esto es una aproximación, idealmente deberíamos obtener el monto real de las transacciones
      amountPaid = invoice.total * 0.5;
    }

    return report_model.Invoice(
      id: invoice.id ?? 0,
      invoiceNumber: invoice.invoiceNumber,
      customerId: invoice.customerId,
      customerName: invoice.customerName,
      date: invoice.date,
      dueDate: invoice.dueDate,
      items: invoice.items.map((item) => {
        'name': item.name,
        'quantity': item.quantity,
        'price': item.unitPrice,
        'total': item.total,
      }).toList(),
      subtotal: invoice.subtotal,
      tax: invoice.taxAmount,
      discount: invoice.discount,
      total: invoice.total,
      notes: invoice.notes,
      isPaid: invoice.isPaid,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt,
      status: _convertStatus(invoice.status),
      // Agregar información de pago
      amountPaid: amountPaid,
      paymentDate: invoice.isPaid ? DateTime.now() : null, // Fecha aproximada si está pagada
    );
  }

  /// Convierte una lista de facturas del modelo de la aplicación al modelo de reporte
  static List<report_model.Invoice> toReportModelList(List<app_model.Invoice> invoices) {
    return invoices.map((invoice) => toReportModel(invoice)).toList();
  }

  /// Convierte el estado de la factura del modelo de la aplicación al modelo de reporte
  static report_model.InvoiceStatus _convertStatus(app_model.InvoiceStatus status) {
    switch (status) {
      case app_model.InvoiceStatus.draft:
        return report_model.InvoiceStatus.draft;
      case app_model.InvoiceStatus.issued:
        return report_model.InvoiceStatus.issued;
      case app_model.InvoiceStatus.paid:
        return report_model.InvoiceStatus.paid;
      case app_model.InvoiceStatus.partiallyPaid:
        return report_model.InvoiceStatus.partiallyPaid;
      case app_model.InvoiceStatus.overdue:
        return report_model.InvoiceStatus.overdue;
      case app_model.InvoiceStatus.cancelled:
        return report_model.InvoiceStatus.cancelled;
    }
  }
}
