import 'dart:convert';
import 'package:flutter/material.dart';

enum InventoryTransactionType {
  addition,
  subtraction,
  adjustment,
  transfer,
}

class InventoryTransaction {
  final int? id;
  final int inventoryItemId;
  final String inventoryItemName;
  final String? inventoryItemCode;
  final InventoryTransactionType type;
  final double quantity;
  final String? unit;
  final String? reference;
  final String? reason;
  final int? serviceRequestId;
  final String? serviceRequestReference;
  final int? userId;
  final String? userName;
  final DateTime transactionDate;
  final String? notes;

  InventoryTransaction({
    this.id,
    required this.inventoryItemId,
    required this.inventoryItemName,
    this.inventoryItemCode,
    required this.type,
    required this.quantity,
    this.unit,
    this.reference,
    this.reason,
    this.serviceRequestId,
    this.serviceRequestReference,
    this.userId,
    this.userName,
    required this.transactionDate,
    this.notes,
  });

  factory InventoryTransaction.fromMap(Map<String, dynamic> map) {
    return InventoryTransaction(
      id: map['id'] as int?,
      inventoryItemId: map['inventory_item_id'] as int,
      inventoryItemName: map['inventory_item_name'] as String,
      inventoryItemCode: map['inventory_item_code'] as String?,
      type: InventoryTransactionType.values[map['type'] as int],
      quantity: (map['quantity'] as num).toDouble(),
      unit: map['unit'] as String?,
      reference: map['reference'] as String?,
      reason: map['reason'] as String?,
      serviceRequestId: map['service_request_id'] as int?,
      serviceRequestReference: map['service_request_reference'] as String?,
      userId: map['user_id'] as int?,
      userName: map['user_name'] as String?,
      transactionDate: DateTime.parse(map['transaction_date'] as String),
      notes: map['notes'] as String?,
    );
  }

  factory InventoryTransaction.fromJson(String source) =>
      InventoryTransaction.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'inventory_item_id': inventoryItemId,
      'inventory_item_name': inventoryItemName,
      'inventory_item_code': inventoryItemCode,
      'type': type.index,
      'quantity': quantity,
      'unit': unit,
      'reference': reference,
      'reason': reason,
      'service_request_id': serviceRequestId,
      'service_request_reference': serviceRequestReference,
      'user_id': userId,
      'user_name': userName,
      'transaction_date': transactionDate.toIso8601String(),
      'notes': notes,
    };
  }

  String toJson() => json.encode(toMap());

  InventoryTransaction copyWith({
    int? id,
    int? inventoryItemId,
    String? inventoryItemName,
    String? inventoryItemCode,
    InventoryTransactionType? type,
    double? quantity,
    String? unit,
    String? reference,
    String? reason,
    int? serviceRequestId,
    String? serviceRequestReference,
    int? userId,
    String? userName,
    DateTime? transactionDate,
    String? notes,
  }) {
    return InventoryTransaction(
      id: id ?? this.id,
      inventoryItemId: inventoryItemId ?? this.inventoryItemId,
      inventoryItemName: inventoryItemName ?? this.inventoryItemName,
      inventoryItemCode: inventoryItemCode ?? this.inventoryItemCode,
      type: type ?? this.type,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      reference: reference ?? this.reference,
      reason: reason ?? this.reason,
      serviceRequestId: serviceRequestId ?? this.serviceRequestId,
      serviceRequestReference: serviceRequestReference ?? this.serviceRequestReference,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      transactionDate: transactionDate ?? this.transactionDate,
      notes: notes ?? this.notes,
    );
  }

  String getTypeText() {
    switch (type) {
      case InventoryTransactionType.addition:
        return 'إضافة';
      case InventoryTransactionType.subtraction:
        return 'سحب';
      case InventoryTransactionType.adjustment:
        return 'تعديل';
      case InventoryTransactionType.transfer:
        return 'نقل';
    }
  }

  Color getTypeColor() {
    switch (type) {
      case InventoryTransactionType.addition:
        return Colors.green;
      case InventoryTransactionType.subtraction:
        return Colors.red;
      case InventoryTransactionType.adjustment:
        return Colors.amber;
      case InventoryTransactionType.transfer:
        return Colors.blue;
    }
  }

  IconData getTypeIcon() {
    switch (type) {
      case InventoryTransactionType.addition:
        return Icons.add_circle;
      case InventoryTransactionType.subtraction:
        return Icons.remove_circle;
      case InventoryTransactionType.adjustment:
        return Icons.edit;
      case InventoryTransactionType.transfer:
        return Icons.swap_horiz;
    }
  }
}
