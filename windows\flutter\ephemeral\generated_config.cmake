# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter_windows_3.29.1-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\food\\HVAC Service Managers" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter_windows_3.29.1-stable\\flutter"
  "PROJECT_DIR=D:\\food\\HVAC Service Managers"
  "FLUTTER_ROOT=C:\\flutter_windows_3.29.1-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\food\\HVAC Service Managers\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\food\\HVAC Service Managers"
  "FLUTTER_TARGET=D:\\food\\HVAC Service Managers\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\food\\HVAC Service Managers\\.dart_tool\\package_config.json"
)
