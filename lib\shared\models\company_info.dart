import 'dart:convert';

/// Model class for company information
class CompanyInfo {
  final int? id;
  final String nameAr;
  final String? nameEn;
  final String? addressAr;
  final String? addressEn;
  final String? phoneAr;
  final String? phoneEn;
  final String? email;
  final String? website;
  final String? taxNumber;
  final String? commercialRegister;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CompanyInfo({
    this.id,
    required this.nameAr,
    this.nameEn,
    this.addressAr,
    this.addressEn,
    this.phoneAr,
    this.phoneEn,
    this.email,
    this.website,
    this.taxNumber,
    this.commercialRegister,
    required this.createdAt,
    this.updatedAt,
  });

  CompanyInfo copyWith({
    int? id,
    String? nameAr,
    String? nameEn,
    String? addressAr,
    String? addressEn,
    String? phoneAr,
    String? phoneEn,
    String? email,
    String? website,
    String? taxNumber,
    String? commercialRegister,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyInfo(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      addressAr: addressAr ?? this.addressAr,
      addressEn: addressEn ?? this.addressEn,
      phoneAr: phoneAr ?? this.phoneAr,
      phoneEn: phoneEn ?? this.phoneEn,
      email: email ?? this.email,
      website: website ?? this.website,
      taxNumber: taxNumber ?? this.taxNumber,
      commercialRegister: commercialRegister ?? this.commercialRegister,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_en': nameEn,
      'address_ar': addressAr,
      'address_en': addressEn,
      'phone_ar': phoneAr,
      'phone_en': phoneEn,
      'email': email,
      'website': website,
      'tax_number': taxNumber,
      'commercial_register': commercialRegister,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory CompanyInfo.fromMap(Map<String, dynamic> map) {
    return CompanyInfo(
      id: map['id'],
      nameAr: map['name_ar'] ?? '',
      nameEn: map['name_en'],
      addressAr: map['address_ar'],
      addressEn: map['address_en'],
      phoneAr: map['phone_ar'],
      phoneEn: map['phone_en'],
      email: map['email'],
      website: map['website'],
      taxNumber: map['tax_number'],
      commercialRegister: map['commercial_register'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory CompanyInfo.fromJson(String source) => CompanyInfo.fromMap(json.decode(source));

  @override
  String toString() {
    return 'CompanyInfo(id: $id, nameAr: $nameAr, nameEn: $nameEn, addressAr: $addressAr, addressEn: $addressEn, phoneAr: $phoneAr, phoneEn: $phoneEn, email: $email, website: $website, taxNumber: $taxNumber, commercialRegister: $commercialRegister, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
