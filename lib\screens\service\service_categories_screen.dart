import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../core/repositories/service_category_repository.dart';
import '../../shared/models/service_category.dart';
import '../../shared/widgets/app_drawer.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/simple_loading_indicator.dart';
import '../../shared/widgets/simple_empty_state.dart';
import '../../shared/utils/app_colors.dart';

class ServiceCategoriesScreen extends StatefulWidget {
  const ServiceCategoriesScreen({super.key});

  @override
  State<ServiceCategoriesScreen> createState() => _ServiceCategoriesScreenState();
}

class _ServiceCategoriesScreenState extends State<ServiceCategoriesScreen> {
  final ServiceCategoryRepository _repository = ServiceCategoryRepository();
  List<ServiceCategory> _categories = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final categories = await _repository.getAllServiceCategories();
      setState(() {
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تحميل فئات الخدمة');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  // دالة لعرض مربع حوار اختيار الأيقونة
  Future<IconData?> _showIconPickerDialog(BuildContext context) async {
    final List<IconData> commonIcons = [
      Icons.ac_unit,
      Icons.settings,
      Icons.build,
      Icons.home_repair_service,
      Icons.engineering,
      Icons.handyman,
      Icons.plumbing,
      Icons.electrical_services,
      Icons.construction,
      Icons.architecture,
      Icons.cleaning_services,
      Icons.hvac,
      Icons.thermostat,
      Icons.water_damage,
      Icons.air,
      Icons.device_thermostat,
      Icons.heat_pump,
      Icons.water,
      Icons.power,
      Icons.bolt,
      Icons.home,
      Icons.apartment,
      Icons.business,
      Icons.store,
      Icons.factory,
      Icons.house,
    ];

    IconData? selectedIcon;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر أيقونة'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
            ),
            itemCount: commonIcons.length,
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () {
                  selectedIcon = commonIcons[index];
                  Navigator.of(context).pop();
                },
                child: CircleAvatar(
                  child: Icon(commonIcons[index]),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    return selectedIcon;
  }

  Future<void> _showCategoryDialog({ServiceCategory? category}) async {
    final isEditing = category != null;
    final nameController = TextEditingController(text: isEditing ? category.name : '');
    final descriptionController = TextEditingController(text: isEditing ? category.description : '');

    IconData selectedIcon = isEditing ? category.icon : Icons.category;
    Color selectedColor = isEditing ? category.color : AppColors.primary;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isEditing ? 'تعديل فئة الخدمة' : 'إضافة فئة خدمة جديدة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الفئة',
                    hintText: 'أدخل اسم الفئة',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'وصف الفئة',
                    hintText: 'أدخل وصف الفئة (اختياري)',
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        // استخدام قائمة أيقونات مخصصة
                        final IconData? icon = await _showIconPickerDialog(context);
                        if (icon != null) {
                          setState(() {
                            selectedIcon = icon;
                          });
                        }
                      },
                      child: Row(
                        children: [
                          Icon(selectedIcon),
                          const SizedBox(width: 8),
                          const Text('اختر أيقونة'),
                        ],
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('اختر لون الفئة'),
                            content: SingleChildScrollView(
                              child: ColorPicker(
                                pickerColor: selectedColor,
                                onColorChanged: (color) {
                                  setState(() {
                                    selectedColor = color;
                                  });
                                },
                                pickerAreaHeightPercent: 0.8,
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text('تم'),
                              ),
                            ],
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: selectedColor,
                      ),
                      child: const Text('اختر لون'),
                    ),
                  ],
                ),

              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  _showErrorSnackBar('يرجى إدخال اسم الفئة');
                  return;
                }

                Navigator.of(context).pop();

                final now = DateTime.now();

                if (isEditing) {
                  final updatedCategory = category.copyWith(
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim(),
                    icon: selectedIcon,
                    color: selectedColor,
                    updatedAt: now,
                  );

                  final result = await _repository.updateServiceCategory(updatedCategory);
                  if (result > 0) {
                    _showSuccessSnackBar('تم تحديث الفئة بنجاح');
                    _loadCategories();
                  } else {
                    _showErrorSnackBar('حدث خطأ أثناء تحديث الفئة');
                  }
                } else {
                  final newCategory = ServiceCategory(
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim(),
                    icon: selectedIcon,
                    color: selectedColor,
                    isActive: true,
                    createdAt: now,
                  );

                  final result = await _repository.insertServiceCategory(newCategory);
                  if (result > 0) {
                    _showSuccessSnackBar('تم إضافة الفئة بنجاح');
                    _loadCategories();
                  } else {
                    _showErrorSnackBar('حدث خطأ أثناء إضافة الفئة');
                  }
                }
              },
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _confirmDeleteCategory(ServiceCategory category) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فئة "${category.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    ) ?? false;

    if (confirmed) {
      final result = await _repository.deleteServiceCategory(category.id!);
      if (result > 0) {
        _showSuccessSnackBar('تم حذف الفئة بنجاح');
        _loadCategories();
      } else {
        _showErrorSnackBar('حدث خطأ أثناء حذف الفئة');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'فئات الخدمة'),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const SimpleLoadingIndicator()
          : _categories.isEmpty
              ? SimpleEmptyState(
                  message: 'لا توجد فئات خدمة',
                  onRefresh: _loadCategories,
                )
              : RefreshIndicator(
                  onRefresh: _loadCategories,
                  child: ListView.builder(
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: category.color,
                            child: Icon(category.icon, color: Colors.white),
                          ),
                          title: Text(category.name),
                          subtitle: category.description.isNotEmpty
                              ? Text(category.description)
                              : null,
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.edit),
                                onPressed: () => _showCategoryDialog(category: category),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red),
                                onPressed: () => _confirmDeleteCategory(category),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }
}
