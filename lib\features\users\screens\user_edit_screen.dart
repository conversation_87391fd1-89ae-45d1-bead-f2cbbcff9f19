import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:icecorner/config/constants.dart';
import 'package:provider/provider.dart';
import '../../../shared/models/user.dart';
import '../../../state/user_state.dart';

class UserEditScreen extends StatefulWidget {
  final User? user;

  const UserEditScreen({
    super.key,
    this.user,
  });

  @override
  State<UserEditScreen> createState() => _UserEditScreenState();
}

class _UserEditScreenState extends State<UserEditScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _isNewUser = false;
  bool _showPassword = false;

  @override
  void initState() {
    super.initState();
    _isNewUser = widget.user == null;
  }

  Future<void> _saveUser() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      if (!mounted) return;

      // Get references before any async operations
      final currentContext = context;
      final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
      final navigator = Navigator.of(currentContext);

      setState(() {
        _isLoading = true;
      });

      final formData = _formKey.currentState!.value;

      try {
        bool success;
        final selectedRole = formData['role'] as String;
        final List<String> roles = [selectedRole]; // Assuming single role selection for now

        if (_isNewUser) {
          // Create new user
          final newUser = User(
            id: 0, // Will be assigned by the service
            name: formData['name'] as String,
            email: formData['email'] as String,
            phone: formData['phone'] as String?,
            roles: roles, // Use the list of roles
            isActive: formData['is_active'] as bool,
            createdAt: DateTime.now(),
            password: formData['password'] as String, // Include password for new user
          );

          success = await Provider.of<UserState>(currentContext, listen: false)
              .addUser(newUser, newUser.password!); // Pass user object and password
        } else {
          // Update existing user
          final updatedUser = widget.user!.copyWith(
            name: formData['name'] as String,
            email: formData['email'] as String,
            phone: formData['phone'] as String?,
            roles: roles, // Use the list of roles
            isActive: formData['is_active'] as bool,
            // Password is not updated here, assuming a separate flow for password change
          );

          success = await Provider.of<UserState>(currentContext, listen: false)
              .updateUser(updatedUser); // Pass only the user object
        }

        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        if (success) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                _isNewUser
                    ? 'تم إضافة المستخدم بنجاح'
                    : 'تم تحديث بيانات المستخدم بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );

          navigator.pop();
        } else {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                _isNewUser
                    ? 'فشل إضافة المستخدم'
                    : 'فشل تحديث بيانات المستخدم',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Removed _parseRole and _getRoleValue methods

  String _getRoleName(String role) {
    switch (role) {
      case 'admin':
        return 'مدير النظام';
      case 'manager':
        return 'مدير';
      case 'accountant':
        return 'محاسب';
      case 'technician':
        return 'فني';
      case 'receptionist':
        return 'موظف استقبال';
      case 'employee':
        return 'موظف';
      default:
        return 'موظف';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isNewUser ? 'إضافة مستخدم جديد' : 'تعديل بيانات المستخدم'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveUser,
            tooltip: 'حفظ',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: FormBuilder(
                key: _formKey,
                initialValue: {
                  'name': widget.user?.name ?? '',
                  'email': widget.user?.email ?? '',
                  'phone': widget.user?.phone ?? '',
                  'role': widget.user != null && widget.user!.roles.isNotEmpty
                      ? widget.user!.roles.first // Use the first role from the list
                      : 'employee',
                  'is_active': widget.user?.isActive ?? true,
                  'password': '',
                  'confirm_password': '',
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name
                    FormBuilderTextField(
                      name: 'name',
                      decoration: const InputDecoration(
                        labelText: 'اسم المستخدم',
                        hintText: 'أدخل اسم المستخدم',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(errorText: 'يرجى إدخال اسم المستخدم'),
                        FormBuilderValidators.minLength(3, errorText: 'يجب أن يكون الاسم 3 أحرف على الأقل'),
                      ]),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Email
                    FormBuilderTextField(
                      name: 'email',
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        hintText: 'أدخل البريد الإلكتروني',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.email),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(errorText: 'يرجى إدخال البريد الإلكتروني'),
                        FormBuilderValidators.email(errorText: 'يرجى إدخال بريد إلكتروني صحيح'),
                      ]),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Phone
                    FormBuilderTextField(
                      name: 'phone',
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        hintText: 'أدخل رقم الهاتف',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.phone),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Role
                    FormBuilderDropdown<String>(
                      name: 'role',
                      decoration: const InputDecoration(
                        labelText: 'الدور الوظيفي',
                        hintText: 'اختر الدور الوظيفي',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.work),
                      ),
                      items: [
                        'admin',
                        'manager',
                        'accountant',
                        'technician',
                        'receptionist',
                        'employee',
                      ].map((role) {
                        return DropdownMenuItem(
                          value: role,
                          child: Text(_getRoleName(role)),
                        );
                      }).toList(),
                      validator: FormBuilderValidators.required(errorText: 'يرجى اختيار الدور الوظيفي'),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Is Active
                    FormBuilderSwitch(
                      name: 'is_active',
                      title: const Text('مستخدم نشط'),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                      ),
                      activeColor: Colors.green,
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Password (only for new users)
                    if (_isNewUser) ...[
                      FormBuilderTextField(
                        name: 'password',
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور',
                          hintText: 'أدخل كلمة المرور',
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.lock),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _showPassword ? Icons.visibility_off : Icons.visibility,
                            ),
                            onPressed: () {
                              setState(() {
                                _showPassword = !_showPassword;
                              });
                            },
                          ),
                        ),
                        obscureText: !_showPassword,
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(errorText: 'يرجى إدخال كلمة المرور'),
                          FormBuilderValidators.minLength(6, errorText: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل'),
                        ]),
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // Confirm Password
                      FormBuilderTextField(
                        name: 'confirm_password',
                        decoration: InputDecoration(
                          labelText: 'تأكيد كلمة المرور',
                          hintText: 'أعد إدخال كلمة المرور',
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.lock_outline),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _showPassword ? Icons.visibility_off : Icons.visibility,
                            ),
                            onPressed: () {
                              setState(() {
                                _showPassword = !_showPassword;
                              });
                            },
                          ),
                        ),
                        obscureText: !_showPassword,
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(errorText: 'يرجى تأكيد كلمة المرور'),
                          (value) {
                            if (value != _formKey.currentState?.fields['password']?.value) {
                              return 'كلمة المرور غير متطابقة';
                            }
                            return null;
                          },
                        ]),
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                    ],

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveUser,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : Text(_isNewUser ? 'إضافة المستخدم' : 'حفظ التغييرات'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
