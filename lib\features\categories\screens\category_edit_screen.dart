
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:provider/provider.dart';
import '../../../config/constants.dart';
import '../../../shared/models/category.dart';
import '../../../state/category_state.dart';

class CategoryEditScreen extends StatefulWidget {
  final CategoryModel? category;
  final String type; // 'income' or 'expense'

  const CategoryEditScreen({
    super.key,
    this.category,
    required this.type,
  });

  @override
  State<CategoryEditScreen> createState() => _CategoryEditScreenState();
}

class _CategoryEditScreenState extends State<CategoryEditScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _isNewCategory = false;

  // Available icons for selection
  final List<IconData> _availableIcons = [
    Icons.shopping_cart,
    Icons.shopping_bag,
    Icons.store,
    Icons.attach_money,
    Icons.money_off,
    Icons.account_balance,
    Icons.account_balance_wallet,
    Icons.credit_card,
    Icons.receipt,
    Icons.receipt_long,
    Icons.home,
    Icons.business,
    Icons.apartment,
    Icons.house,
    Icons.local_gas_station,
    Icons.directions_car,
    Icons.commute,
    Icons.delivery_dining,
    Icons.local_shipping,
    Icons.flight,
    Icons.hotel,
    Icons.restaurant,
    Icons.fastfood,
    Icons.local_cafe,
    Icons.local_bar,
    Icons.local_grocery_store,
    Icons.local_mall,
    Icons.local_offer,
    Icons.local_pharmacy,
    Icons.local_hospital,
    Icons.medical_services,
    Icons.school,
    Icons.book,
    Icons.sports,
    Icons.fitness_center,
    Icons.spa,
    Icons.movie,
    Icons.theaters,
    Icons.music_note,
    Icons.games,
    Icons.sports_esports,
    Icons.phone,
    Icons.computer,
    Icons.tv,
    Icons.camera_alt,
    Icons.videocam,
    Icons.headset,
    Icons.watch,
    Icons.smartphone,
    Icons.tablet,
    Icons.print,
    Icons.build,
    Icons.handyman,
    Icons.construction,
    Icons.electrical_services,
    Icons.plumbing,
    Icons.power,
    Icons.wifi,
    Icons.network_cell,
    Icons.security,
    Icons.lock,
    Icons.people,
    Icons.person,
    Icons.family_restroom,
    Icons.child_care,
    Icons.pets,
    Icons.favorite,
    Icons.favorite_border,
    Icons.volunteer_activism,
    Icons.card_giftcard,
    Icons.redeem,
    Icons.cake,
    Icons.celebration,
    Icons.emoji_events,
    Icons.military_tech,
    Icons.workspace_premium,
    Icons.campaign,
    Icons.trending_up,
    Icons.trending_down,
    Icons.assignment_return,
    Icons.miscellaneous_services,
  ];

  // Available colors for selection
  final List<Color> _availableColors = [
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
    Colors.blueGrey,
  ];

  // Selected icon and color
  IconData? _selectedIcon;
  Color? _selectedColor;

  @override
  void initState() {
    super.initState();
    _isNewCategory = widget.category == null;
    _selectedIcon = widget.category?.icon ?? _availableIcons.first;
    _selectedColor = widget.category?.color ?? _availableColors.first;
  }

  Future<void> _saveCategory() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      if (!mounted) return;

      // Get references before any async operations
      final currentContext = context;
      final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
      final navigator = Navigator.of(currentContext);

      setState(() {
        _isLoading = true;
      });

      final formData = _formKey.currentState!.value;

      try {
        bool success;

        if (_isNewCategory) {
          // Create new category
          final newCategory = CategoryModel(
            id: 0, // Will be assigned by the service
            name: formData['name'] as String,
            type: widget.type,
            icon: _selectedIcon!,
            color: _selectedColor!,
            isActive: formData['is_active'] as bool,
            createdAt: DateTime.now(),
          );

          success = await Provider.of<CategoryState>(currentContext, listen: false)
              .addCategory(newCategory);
        } else {
          // Update existing category
          final updatedCategory = widget.category!.copyWith(
            name: formData['name'] as String,
            icon: _selectedIcon,
            color: _selectedColor,
            isActive: formData['is_active'] as bool,
          );

          success = await Provider.of<CategoryState>(currentContext, listen: false)
              .updateCategory(updatedCategory);
        }

        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        if (success) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                _isNewCategory
                    ? 'تم إضافة الفئة بنجاح'
                    : 'تم تحديث الفئة بنجاح',
              ),
              backgroundColor: Colors.green,
            ),
          );

          navigator.pop();
        } else {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                _isNewCategory
                    ? 'فشل إضافة الفئة'
                    : 'فشل تحديث الفئة',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final String title = _isNewCategory
        ? widget.type == 'income'
            ? 'إضافة فئة إيرادات جديدة'
            : 'إضافة فئة مصروفات جديدة'
        : 'تعديل الفئة';

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveCategory,
            tooltip: 'حفظ',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: FormBuilder(
                key: _formKey,
                initialValue: {
                  'name': widget.category?.name ?? '',
                  'is_active': widget.category?.isActive ?? true,
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Category name
                    FormBuilderTextField(
                      name: 'name',
                      decoration: const InputDecoration(
                        labelText: 'اسم الفئة',
                        hintText: 'أدخل اسم الفئة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      validator: FormBuilderValidators.compose([
                        FormBuilderValidators.required(errorText: 'يرجى إدخال اسم الفئة'),
                        FormBuilderValidators.minLength(2, errorText: 'يجب أن يكون الاسم حرفين على الأقل'),
                      ]),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Category type (display only)
                    FormBuilderTextField(
                      name: 'type',
                      initialValue: widget.type == 'income' ? 'إيرادات' : 'مصروفات',
                      decoration: const InputDecoration(
                        labelText: 'نوع الفئة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.type_specimen),
                      ),
                      enabled: false,
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Icon selection
                    const Text(
                      'اختر أيقونة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Container(
                      height: 150,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: GridView.builder(
                        padding: const EdgeInsets.all(8),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 8,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: _availableIcons.length,
                        itemBuilder: (context, index) {
                          final icon = _availableIcons[index];
                          final isSelected = icon == _selectedIcon;

                          return InkWell(
                            onTap: () {
                              setState(() {
                                _selectedIcon = icon;
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.grey.withAlpha(76) : null,
                                borderRadius: BorderRadius.circular(4),
                                border: isSelected
                                    ? Border.all(color: Colors.blue, width: 2)
                                    : null,
                              ),
                              child: Icon(icon),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Color selection
                    const Text(
                      'اختر لون',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Container(
                      height: 70,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: GridView.builder(
                        padding: const EdgeInsets.all(8),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 10,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                          childAspectRatio: 1,
                        ),
                        itemCount: _availableColors.length,
                        itemBuilder: (context, index) {
                          final color = _availableColors[index];
                          final isSelected = color == _selectedColor;

                          return InkWell(
                            onTap: () {
                              setState(() {
                                _selectedColor = color;
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                                border: isSelected
                                    ? Border.all(color: Colors.white, width: 2)
                                    : null,
                                boxShadow: isSelected
                                    ? [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(76),
                                          blurRadius: 4,
                                          spreadRadius: 1,
                                        ),
                                      ]
                                    : null,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Preview
                    const Text(
                      'معاينة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: _selectedColor?.withAlpha(51),
                            child: Icon(
                              _selectedIcon,
                              color: _selectedColor,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: Text(
                              _formKey.currentState?.fields['name']?.value as String? ?? 'اسم الفئة',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Is Active
                    FormBuilderSwitch(
                      name: 'is_active',
                      title: const Text('فئة نشطة'),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                      ),
                      activeColor: Colors.green,
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveCategory,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : Text(_isNewCategory ? 'إضافة الفئة' : 'حفظ التغييرات'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
