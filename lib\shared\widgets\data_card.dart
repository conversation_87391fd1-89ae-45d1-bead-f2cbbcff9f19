import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// A customizable card widget for displaying data with a consistent style
class DataCard extends StatelessWidget {
  /// Title of the card
  final String title;

  /// Optional subtitle for additional context
  final String? subtitle;

  /// Optional icon to display in the header
  final IconData? icon;

  /// Color of the icon and accent elements
  final Color? accentColor;

  /// Content to display in the card body
  final Widget content;

  /// Optional footer widget
  final Widget? footer;

  /// Whether to show a divider between header and content
  final bool showDivider;

  /// Whether the card is expandable
  final bool isExpandable;

  /// Whether the card is initially expanded (if expandable)
  final bool initiallyExpanded;

  /// Optional actions to display in the header
  final List<Widget>? actions;

  /// Padding for the card content
  final EdgeInsets contentPadding;

  /// Creates a data card with the given parameters
  const DataCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.accentColor,
    required this.content,
    this.footer,
    this.showDivider = true,
    this.isExpandable = false,
    this.initiallyExpanded = true,
    this.actions,
    this.contentPadding = const EdgeInsets.all(AppDimensions.paddingM),
  });

  @override
  Widget build(BuildContext context) {
    final Color effectiveAccentColor = accentColor ?? AppColors.primary;

    final Widget cardContent = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context, effectiveAccentColor),
        if (showDivider) const Divider(),
        Padding(
          padding: contentPadding,
          child: content,
        ),
        if (footer != null) ...[
          if (showDivider) const Divider(),
          footer!,
        ],
      ],
    );

    return Card(
      margin: const EdgeInsets.all(AppDimensions.paddingM),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: isExpandable
          ? _buildExpandableCard(cardContent)
          : cardContent,
    );
  }

  Widget _buildHeader(BuildContext context, Color accentColor) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: accentColor,
              size: 24,
            ),
            const SizedBox(width: AppDimensions.paddingM),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }

  Widget _buildExpandableCard(Widget cardContent) {
    return ExpansionTile(
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: accentColor ?? AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: AppDimensions.paddingM),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
      initiallyExpanded: initiallyExpanded,
      childrenPadding: EdgeInsets.zero,
      tilePadding: const EdgeInsets.all(AppDimensions.paddingM),
      expandedCrossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showDivider) const Divider(),
        Padding(
          padding: contentPadding,
          child: content,
        ),
        if (footer != null) ...[
          if (showDivider) const Divider(),
          footer!,
        ],
      ],
    );
  }
}

/// A specialized data card for displaying key-value pairs
class KeyValueCard extends StatelessWidget {
  /// Title of the card
  final String title;

  /// Optional subtitle for additional context
  final String? subtitle;

  /// Optional icon to display in the header
  final IconData? icon;

  /// Color of the icon and accent elements
  final Color? accentColor;

  /// Map of key-value pairs to display
  final Map<String, String> data;

  /// Optional footer widget
  final Widget? footer;

  /// Whether to show a divider between header and content
  final bool showDivider;

  /// Whether the card is expandable
  final bool isExpandable;

  /// Whether the card is initially expanded (if expandable)
  final bool initiallyExpanded;

  /// Optional actions to display in the header
  final List<Widget>? actions;

  /// Creates a key-value card with the given parameters
  const KeyValueCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.accentColor,
    required this.data,
    this.footer,
    this.showDivider = true,
    this.isExpandable = false,
    this.initiallyExpanded = true,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return DataCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      accentColor: accentColor,
      showDivider: showDivider,
      isExpandable: isExpandable,
      initiallyExpanded: initiallyExpanded,
      actions: actions,
      footer: footer,
      content: Column(
        children: data.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    entry.key,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    entry.value,
                    style: const TextStyle(
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
