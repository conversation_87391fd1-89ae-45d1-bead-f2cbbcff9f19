import 'package:flutter/foundation.dart';
import '../shared/models/category.dart';
import '../core/services/category_service.dart';

enum CategoryStateStatus {
  initial,
  loading,
  loaded,
  error,
}

class CategoryState extends ChangeNotifier {
  final CategoryService _categoryService = CategoryService();

  CategoryStateStatus _status = CategoryStateStatus.initial;
  List<CategoryModel> _categories = [];
  CategoryModel? _selectedCategory;
  String? _errorMessage;

  // Getters
  CategoryStateStatus get status => _status;
  List<CategoryModel> get categories => _categories;
  List<CategoryModel> get incomeCategories => _categories.where((c) => c.isIncome).toList();
  List<CategoryModel> get expenseCategories => _categories.where((c) => c.isExpense).toList();
  CategoryModel? get selectedCategory => _selectedCategory;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == CategoryStateStatus.loading;

  // Load all categories
  Future<void> loadCategories() async {
    // Only notify if not already loading
    if (_status != CategoryStateStatus.loading) {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final categories = await _categoryService.getCategories();
      _categories = categories;
      _status = CategoryStateStatus.loaded;
    } catch (e) {
      _errorMessage = e.toString();
      _status = CategoryStateStatus.error;
    }

    // Use Future.microtask to avoid calling notifyListeners during build
    await Future.microtask(() => notifyListeners());
  }

  // Load categories by type
  Future<void> loadCategoriesByType(String type) async {
    // Only notify if not already loading
    if (_status != CategoryStateStatus.loading) {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final categories = await _categoryService.getCategoriesByType(type);
      _categories = categories;
      _status = CategoryStateStatus.loaded;
    } catch (e) {
      _errorMessage = e.toString();
      _status = CategoryStateStatus.error;
    }

    // Use Future.microtask to avoid calling notifyListeners during build
    await Future.microtask(() => notifyListeners());
  }

  // Load category by ID
  Future<void> loadCategoryById(int id) async {
    // Only notify if not already loading
    if (_status != CategoryStateStatus.loading) {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final category = await _categoryService.getCategoryById(id);
      _selectedCategory = category;
      _status = CategoryStateStatus.loaded;
    } catch (e) {
      _errorMessage = e.toString();
      _status = CategoryStateStatus.error;
    }

    // Use Future.microtask to avoid calling notifyListeners during build
    await Future.microtask(() => notifyListeners());
  }

  // Add a new category
  Future<bool> addCategory(CategoryModel category) async {
    // Only notify if not already loading
    if (_status != CategoryStateStatus.loading) {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final newCategory = await _categoryService.addCategory(category);

      if (newCategory != null) {
        _categories.add(newCategory);
        _status = CategoryStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل إضافة الفئة';
        _status = CategoryStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = CategoryStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Update an existing category
  Future<bool> updateCategory(CategoryModel category) async {
    // Only notify if not already loading
    if (_status != CategoryStateStatus.loading) {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final updatedCategory = await _categoryService.updateCategory(category);

      if (updatedCategory != null) {
        // Update in the list
        final index = _categories.indexWhere((c) => c.id == category.id);
        if (index >= 0) {
          _categories[index] = updatedCategory;
        }

        // Update selected category if it's the same
        if (_selectedCategory?.id == category.id) {
          _selectedCategory = updatedCategory;
        }

        _status = CategoryStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل تحديث الفئة';
        _status = CategoryStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = CategoryStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Delete a category
  Future<bool> deleteCategory(int id) async {
    // Only notify if not already loading
    if (_status != CategoryStateStatus.loading) {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = CategoryStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final success = await _categoryService.deleteCategory(id);

      if (success) {
        // Remove from the list
        _categories.removeWhere((category) => category.id == id);

        // Clear selected category if it's the same
        if (_selectedCategory?.id == id) {
          _selectedCategory = null;
        }

        _status = CategoryStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل حذف الفئة';
        _status = CategoryStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = CategoryStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Set selected category
  Future<void> setSelectedCategory(CategoryModel? category) async {
    _selectedCategory = category;
    await Future.microtask(() => notifyListeners());
  }

  // Clear error
  Future<void> clearError() async {
    _errorMessage = null;
    await Future.microtask(() => notifyListeners());
  }
}
