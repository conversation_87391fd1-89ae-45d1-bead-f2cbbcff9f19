import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/transaction.dart';

class TransactionCard extends StatelessWidget {
  final Transaction transaction;
  final VoidCallback? onTap;

  const TransactionCard({
    super.key,
    required this.transaction,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Transaction.getTypeColor(transaction.type).withAlpha(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                    child: Center(
                      child: Icon(
                        _getTransactionIcon(),
                        color: Transaction.getTypeColor(transaction.type),
                        size: 28,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          transaction.reference,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          transaction.category ?? 'غير مصنف',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${transaction.amount} ر.س',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Transaction.getTypeColor(transaction.type),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DateFormat('dd/MM/yyyy').format(transaction.date),
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(height: 1),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                children: [
                  _buildInfoItem(
                    Transaction.getPaymentMethodIcon(transaction.paymentMethod),
                    Transaction.getPaymentMethodName(transaction.paymentMethod),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  if (transaction.description != null)
                    Expanded(
                      child: _buildInfoItem(
                        Icons.description,
                        transaction.description!,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTransactionIcon() {
    if (transaction.type == TransactionType.income) {
      if (transaction.invoiceId != null) {
        return Icons.receipt_long;
      }
      return Icons.arrow_upward;
    } else {
      if (transaction.category == 'رواتب') {
        return Icons.payments;
      } else if (transaction.category == 'مشتريات') {
        return Icons.shopping_cart;
      }
      return Icons.arrow_downward;
    }
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
