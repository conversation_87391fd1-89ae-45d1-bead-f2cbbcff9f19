/// Represents the possible roles a user can have.
enum UserRole {
  admin,
  manager,
  accountant,
  technician,
  receptionist,
  employee,
}

/// Extension to provide string values for UserRole enum.
extension UserRoleExtension on UserRole {
  String get value {
    switch (this) {
      case UserRole.admin:
        return 'admin';
      case UserRole.manager:
        return 'manager';
      case UserRole.accountant:
        return 'accountant';
      case UserRole.technician:
        return 'technician';
      case UserRole.receptionist:
        return 'receptionist';
      case UserRole.employee:
        return 'employee';
    }
  }
}

/// Helper function to parse a string value to a UserRole enum.
UserRole? parseUserRole(String? value) {
  if (value == null) return null;
  switch (value.toLowerCase()) {
    case 'admin':
      return UserRole.admin;
    case 'manager':
      return UserRole.manager;
    case 'accountant':
      return UserRole.accountant;
    case 'technician':
      return UserRole.technician;
    case 'receptionist':
      return UserRole.receptionist;
    case 'employee':
      return UserRole.employee;
    default:
      return null; // Or throw an exception, depending on desired behavior
  }
}
