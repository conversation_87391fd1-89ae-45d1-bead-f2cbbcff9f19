import 'package:sqflite/sqflite.dart';
import '../../shared/models/commission.dart';
import '../database/database_helper.dart';

/// مستودع العمولات
class CommissionRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع العمولات
  Future<List<Commission>> getAllCommissions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commissions',
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Commission.fromMap(maps[i]);
    });
  }

  /// الحصول على العمولات حسب الموظف
  Future<List<Commission>> getCommissionsByEmployee(int employeeId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commissions',
      where: 'employee_id = ?',
      whereArgs: [employeeId],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Commission.fromMap(maps[i]);
    });
  }

  /// الحصول على العمولات حسب الحالة
  Future<List<Commission>> getCommissionsByStatus(CommissionStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commissions',
      where: 'status = ?',
      whereArgs: [status.toString().split('.').last],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Commission.fromMap(maps[i]);
    });
  }

  /// الحصول على العمولات حسب نطاق التاريخ
  Future<List<Commission>> getCommissionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commissions',
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Commission.fromMap(maps[i]);
    });
  }

  /// الحصول على العمولات حسب الكيان المرتبط
  Future<List<Commission>> getCommissionsByRelatedEntity(
    String relatedEntityType,
    int relatedEntityId,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commissions',
      where: 'related_entity_type = ? AND related_entity_id = ?',
      whereArgs: [relatedEntityType, relatedEntityId],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Commission.fromMap(maps[i]);
    });
  }

  /// الحصول على عمولة بواسطة المعرف
  Future<Commission?> getCommissionById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commissions',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return Commission.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة عمولة جديدة
  Future<int> insertCommission(Commission commission) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      'commissions',
      commission.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث عمولة
  Future<int> updateCommission(Commission commission) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'commissions',
      commission.toMap(),
      where: 'id = ?',
      whereArgs: [commission.id],
    );
  }

  /// حذف عمولة
  Future<int> deleteCommission(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'commissions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تحديث حالة العمولة
  Future<int> updateCommissionStatus(int id, CommissionStatus status) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'commissions',
      {
        'status': status.toString().split('.').last,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تسجيل دفع العمولة
  Future<int> markCommissionAsPaid(
    int id,
    DateTime paymentDate,
    int? paymentTransactionId,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'commissions',
      {
        'status': CommissionStatus.paid.toString().split('.').last,
        'payment_date': paymentDate.toIso8601String(),
        'payment_transaction_id': paymentTransactionId,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على إجمالي العمولات المستحقة للموظف
  Future<double> getTotalPendingCommissionsForEmployee(int employeeId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM commissions
      WHERE employee_id = ? AND status = ?
    ''', [
      employeeId,
      CommissionStatus.pending.toString().split('.').last,
    ]);

    return result.isNotEmpty && result.first['total'] != null
        ? (result.first['total'] as num).toDouble()
        : 0.0;
  }

  /// الحصول على إجمالي العمولات المدفوعة للموظف
  Future<double> getTotalPaidCommissionsForEmployee(
    int employeeId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM commissions
      WHERE employee_id = ? AND status = ? AND payment_date >= ? AND payment_date <= ?
    ''', [
      employeeId,
      CommissionStatus.paid.toString().split('.').last,
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);

    return result.isNotEmpty && result.first['total'] != null
        ? (result.first['total'] as num).toDouble()
        : 0.0;
  }
}
