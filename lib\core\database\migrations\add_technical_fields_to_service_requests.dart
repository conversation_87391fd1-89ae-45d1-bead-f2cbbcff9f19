import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

class AddTechnicalFieldsToServiceRequests {
  static Future<void> migrate(Database db) async {
    try {
      // Check if the columns already exist
      final List<Map<String, dynamic>> columns = await db.rawQuery(
        "PRAGMA table_info(service_requests)"
      );
      
      final columnNames = columns.map((c) => c['name'] as String).toList();
      
      // Add solution column if it doesn't exist
      if (!columnNames.contains('solution')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN solution TEXT'
        );
        if (kDebugMode) {
          print('Added solution column to service_requests table');
        }
      }
      
      // Add used_parts column if it doesn't exist
      if (!columnNames.contains('used_parts')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN used_parts TEXT'
        );
        if (kDebugMode) {
          print('Added used_parts column to service_requests table');
        }
      }
      
      // Add used_inventory_ids column if it doesn't exist
      if (!columnNames.contains('used_inventory_ids')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN used_inventory_ids TEXT'
        );
        if (kDebugMode) {
          print('Added used_inventory_ids column to service_requests table');
        }
      }
      
      // Add used_inventory_quantities column if it doesn't exist
      if (!columnNames.contains('used_inventory_quantities')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN used_inventory_quantities TEXT'
        );
        if (kDebugMode) {
          print('Added used_inventory_quantities column to service_requests table');
        }
      }
      
      // Add problem_images column if it doesn't exist
      if (!columnNames.contains('problem_images')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN problem_images TEXT'
        );
        if (kDebugMode) {
          print('Added problem_images column to service_requests table');
        }
      }
      
      // Add solution_images column if it doesn't exist
      if (!columnNames.contains('solution_images')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN solution_images TEXT'
        );
        if (kDebugMode) {
          print('Added solution_images column to service_requests table');
        }
      }
      
      // Add device_type column if it doesn't exist
      if (!columnNames.contains('device_type')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN device_type TEXT'
        );
        if (kDebugMode) {
          print('Added device_type column to service_requests table');
        }
      }
      
      // Add device_brand column if it doesn't exist
      if (!columnNames.contains('device_brand')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN device_brand TEXT'
        );
        if (kDebugMode) {
          print('Added device_brand column to service_requests table');
        }
      }
      
      // Add device_model column if it doesn't exist
      if (!columnNames.contains('device_model')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN device_model TEXT'
        );
        if (kDebugMode) {
          print('Added device_model column to service_requests table');
        }
      }
      
      // Add serial_number column if it doesn't exist
      if (!columnNames.contains('serial_number')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN serial_number TEXT'
        );
        if (kDebugMode) {
          print('Added serial_number column to service_requests table');
        }
      }
      
      // Add installation_date column if it doesn't exist
      if (!columnNames.contains('installation_date')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN installation_date TEXT'
        );
        if (kDebugMode) {
          print('Added installation_date column to service_requests table');
        }
      }
      
      // Add warranty_info column if it doesn't exist
      if (!columnNames.contains('warranty_info')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN warranty_info TEXT'
        );
        if (kDebugMode) {
          print('Added warranty_info column to service_requests table');
        }
      }
      
      // Add technical_notes column if it doesn't exist
      if (!columnNames.contains('technical_notes')) {
        await db.execute(
          'ALTER TABLE service_requests ADD COLUMN technical_notes TEXT'
        );
        if (kDebugMode) {
          print('Added technical_notes column to service_requests table');
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Error adding technical fields to service_requests table: $e');
      }
    }
  }
}
