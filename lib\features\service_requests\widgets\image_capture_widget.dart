import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../../config/constants.dart';

class ImageCaptureWidget extends StatefulWidget {
  final List<String>? initialImages;
  final Function(List<String>) onImagesChanged;
  final String title;
  final IconData icon;
  final Color color;

  const ImageCaptureWidget({
    super.key,
    this.initialImages,
    required this.onImagesChanged,
    required this.title,
    required this.icon,
    this.color = AppColors.primary,
  });

  @override
  State<ImageCaptureWidget> createState() => _ImageCaptureWidgetState();
}

class _ImageCaptureWidgetState extends State<ImageCaptureWidget> {
  late List<String> _images;
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _images = widget.initialImages?.toList() ?? [];
  }

  Future<void> _takePicture() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 80,
      );

      if (photo != null) {
        // Save the image to app documents directory with a unique name
        final appDir = await getApplicationDocumentsDirectory();
        final fileName = 'service_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final savedImage = File(path.join(appDir.path, fileName));
        
        await savedImage.writeAsBytes(await photo.readAsBytes());
        
        setState(() {
          _images.add(savedImage.path);
          _isLoading = false;
        });
        
        widget.onImagesChanged(_images);
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء التقاط الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 80,
      );

      if (image != null) {
        // Save the image to app documents directory with a unique name
        final appDir = await getApplicationDocumentsDirectory();
        final fileName = 'service_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final savedImage = File(path.join(appDir.path, fileName));
        
        await savedImage.writeAsBytes(await image.readAsBytes());
        
        setState(() {
          _images.add(savedImage.path);
          _isLoading = false;
        });
        
        widget.onImagesChanged(_images);
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _images.removeAt(index);
    });
    
    widget.onImagesChanged(_images);
  }

  void _viewImage(String imagePath) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text(widget.title),
          ),
          body: Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4,
              child: Image.file(
                File(imagePath),
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(widget.icon, color: widget.color),
                    const SizedBox(width: 8),
                    Text(
                      widget.title,
                      style: AppTextStyles.heading3.copyWith(color: widget.color),
                    ),
                  ],
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.photo_library),
                      onPressed: _isLoading ? null : _pickImage,
                      tooltip: 'اختيار من المعرض',
                      color: widget.color,
                    ),
                    IconButton(
                      icon: const Icon(Icons.camera_alt),
                      onPressed: _isLoading ? null : _takePicture,
                      tooltip: 'التقاط صورة',
                      color: widget.color,
                    ),
                  ],
                ),
              ],
            ),
            const Divider(),
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_images.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  child: Text('لا توجد صور'),
                ),
              )
            else
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _images.length,
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      GestureDetector(
                        onTap: () => _viewImage(_images[index]),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(_images[index]),
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () => _removeImage(index),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
