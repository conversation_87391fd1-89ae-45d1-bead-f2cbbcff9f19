import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../../shared/models/category.dart';

class CategoryRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all categories
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('categories');

      return List.generate(maps.length, (i) {
        // Default icon and color based on type
        final IconData defaultIcon = maps[i]['type'] == 'income' ? Icons.arrow_upward : Icons.arrow_downward;
        final Color defaultColor = maps[i]['type'] == 'income' ? Colors.green : Colors.red;

        return CategoryModel(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          type: maps[i]['type'] as String,
          icon: maps[i]['icon_code'] != null
              ? IconData(maps[i]['icon_code'] as int, fontFamily: 'MaterialIcons')
              : defaultIcon,
          color: maps[i]['color'] != null
              ? Color(maps[i]['color'] as int)
              : defaultColor,
          description: maps[i]['description'] as String?,
          isActive: maps[i]['status'] == 'active',
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories: $e');
      }
      return [];
    }
  }

  // Get categories by type (income, expense, etc.)
  Future<List<CategoryModel>> getCategoriesByType(String type) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'categories',
        where: 'type = ?',
        whereArgs: [type],
      );

      return List.generate(maps.length, (i) {
        // Default icon and color based on type
        final IconData defaultIcon = maps[i]['type'] == 'income' ? Icons.arrow_upward : Icons.arrow_downward;
        final Color defaultColor = maps[i]['type'] == 'income' ? Colors.green : Colors.red;

        return CategoryModel(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          type: maps[i]['type'] as String,
          icon: maps[i]['icon_code'] != null
              ? IconData(maps[i]['icon_code'] as int, fontFamily: 'MaterialIcons')
              : defaultIcon,
          color: maps[i]['color'] != null
              ? Color(maps[i]['color'] as int)
              : defaultColor,
          description: maps[i]['description'] as String?,
          isActive: maps[i]['status'] == 'active',
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories by type: $e');
      }
      return [];
    }
  }

  // Get category by ID
  Future<CategoryModel?> getCategoryById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        // Default icon and color based on type
        final IconData defaultIcon = maps[0]['type'] == 'income' ? Icons.arrow_upward : Icons.arrow_downward;
        final Color defaultColor = maps[0]['type'] == 'income' ? Colors.green : Colors.red;

        return CategoryModel(
          id: maps[0]['id'] as int,
          name: maps[0]['name'] as String,
          type: maps[0]['type'] as String,
          icon: maps[0]['icon_code'] != null
              ? IconData(maps[0]['icon_code'] as int, fontFamily: 'MaterialIcons')
              : defaultIcon,
          color: maps[0]['color'] != null
              ? Color(maps[0]['color'] as int)
              : defaultColor,
          description: maps[0]['description'] as String?,
          isActive: maps[0]['status'] == 'active',
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting category by ID: $e');
      }
      return null;
    }
  }

  // Insert a new category
  Future<int> insertCategory(CategoryModel category) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'categories',
        {
          'name': category.name,
          'type': category.type,
          'description': category.description,
          'icon_code': category.icon.codePoint,
          'color': category.color.value,
          'status': category.isActive ? 'active' : 'inactive',
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting category: $e');
      }
      return -1;
    }
  }

  // Update an existing category
  Future<int> updateCategory(CategoryModel category) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'categories',
        {
          'name': category.name,
          'type': category.type,
          'description': category.description,
          'icon_code': category.icon.codePoint,
          'color': category.color.value,
          'status': category.isActive ? 'active' : 'inactive',
        },
        where: 'id = ?',
        whereArgs: [category.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating category: $e');
      }
      return 0;
    }
  }

  // Delete a category
  Future<int> deleteCategory(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting category: $e');
      }
      return 0;
    }
  }
}
