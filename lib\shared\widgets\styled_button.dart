import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// أنواع الأزرار المنسقة
enum StyledButtonType {
  /// زر رئيسي (خلفية ملونة)
  primary,
  
  /// زر ثانوي (حدود ملونة)
  secondary,
  
  /// زر نصي (بدون خلفية أو حدود)
  text,
  
  /// زر خطر (أحمر)
  danger,
  
  /// زر نجاح (أخضر)
  success,
}

/// أحجام الأزرار المنسقة
enum StyledButtonSize {
  /// زر صغير
  small,
  
  /// زر متوسط
  medium,
  
  /// زر كبير
  large,
}

/// زر منسق بتصميم موحد
class StyledButton extends StatelessWidget {
  /// نص الزر
  final String label;
  
  /// أيقونة الزر (اختياري)
  final IconData? icon;
  
  /// دالة تنفذ عند النقر على الزر
  final VoidCallback? onPressed;
  
  /// نوع الزر
  final StyledButtonType type;
  
  /// حجم الزر
  final StyledButtonSize size;
  
  /// عرض الزر (اختياري)
  final double? width;
  
  /// ارتفاع الزر (اختياري)
  final double? height;
  
  /// تعبئة الزر للعرض المتاح
  final bool expanded;
  
  /// موضع الأيقونة (يمين أو يسار)
  final bool iconOnRight;
  
  /// تأثير رفع الزر
  final double elevation;
  
  /// نصف قطر زوايا الزر
  final double borderRadius;
  
  /// إنشاء زر منسق
  const StyledButton({
    super.key,
    required this.label,
    this.icon,
    this.onPressed,
    this.type = StyledButtonType.primary,
    this.size = StyledButtonSize.medium,
    this.width,
    this.height,
    this.expanded = false,
    this.iconOnRight = true,
    this.elevation = 2.0,
    this.borderRadius = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان حسب نوع الزر
    final (backgroundColor, foregroundColor, borderColor) = _getButtonColors();
    
    // تحديد الأبعاد حسب حجم الزر
    final (buttonHeight, fontSize, iconSize, padding) = _getButtonDimensions();
    
    // بناء محتوى الزر
    final buttonContent = Row(
      mainAxisSize: expanded ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (icon != null && !iconOnRight) ...[
          Icon(icon, size: iconSize),
          SizedBox(width: padding / 2),
        ],
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: fontSize,
            fontWeight: FontWeight.w700,
          ),
        ),
        if (icon != null && iconOnRight) ...[
          SizedBox(width: padding / 2),
          Icon(icon, size: iconSize),
        ],
      ],
    );
    
    // تحديد نوع الزر وإنشائه
    Widget button;
    switch (type) {
      case StyledButtonType.primary:
      case StyledButtonType.danger:
      case StyledButtonType.success:
        button = ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            elevation: elevation,
            padding: EdgeInsets.symmetric(
              horizontal: padding,
              vertical: padding / 2,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            minimumSize: Size(0, buttonHeight),
          ),
          child: buttonContent,
        );
        break;
        
      case StyledButtonType.secondary:
        button = OutlinedButton(
          onPressed: onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: foregroundColor,
            side: BorderSide(color: borderColor, width: 1.5),
            padding: EdgeInsets.symmetric(
              horizontal: padding,
              vertical: padding / 2,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            minimumSize: Size(0, buttonHeight),
          ),
          child: buttonContent,
        );
        break;
        
      case StyledButtonType.text:
        button = TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            foregroundColor: foregroundColor,
            padding: EdgeInsets.symmetric(
              horizontal: padding,
              vertical: padding / 2,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            minimumSize: Size(0, buttonHeight),
          ),
          child: buttonContent,
        );
        break;
    }
    
    // تطبيق العرض المخصص إذا تم تحديده
    if (width != null || height != null || expanded) {
      return Container(
        width: expanded ? double.infinity : width,
        height: height,
        child: button,
      );
    }
    
    return button;
  }
  
  /// الحصول على ألوان الزر حسب النوع
  (Color, Color, Color) _getButtonColors() {
    return switch (type) {
      StyledButtonType.primary => (
        AppColors.primary,
        AppColors.textOnPrimary,
        AppColors.primary,
      ),
      StyledButtonType.secondary => (
        Colors.transparent,
        AppColors.primary,
        AppColors.primary,
      ),
      StyledButtonType.text => (
        Colors.transparent,
        AppColors.primary,
        Colors.transparent,
      ),
      StyledButtonType.danger => (
        AppColors.error,
        AppColors.textOnPrimary,
        AppColors.error,
      ),
      StyledButtonType.success => (
        AppColors.success,
        AppColors.textOnPrimary,
        AppColors.success,
      ),
    };
  }
  
  /// الحصول على أبعاد الزر حسب الحجم
  (double, double, double, double) _getButtonDimensions() {
    return switch (size) {
      StyledButtonSize.small => (
        AppDimensions.buttonHeightS,
        14.0,
        16.0,
        AppDimensions.spacingM,
      ),
      StyledButtonSize.medium => (
        AppDimensions.buttonHeightM,
        16.0,
        20.0,
        AppDimensions.spacingL,
      ),
      StyledButtonSize.large => (
        AppDimensions.buttonHeightL,
        18.0,
        24.0,
        AppDimensions.spacingXL,
      ),
    };
  }
}
