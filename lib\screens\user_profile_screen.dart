import 'package:flutter/material.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({Key? key}) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Profile'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // User Information
            const Text(
              'User Information',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            // Placeholder for user information
            Container(
              height: 100,
              color: Colors.grey[200],
              child: const Center(
                child: Text('User information will be displayed here'),
              ),
            ),
            const SizedBox(height: 20),

            // Booking History
            const Text(
              'Booking History',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            // Placeholder for booking history
            Container(
              height: 100,
              color: Colors.grey[200],
              child: const Center(
                child: Text('Booking history will be displayed here'),
              ),
            ),
            const SizedBox(height: 20),

            // Account Settings
            const Text(
              'Account Settings',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            // Placeholder for account settings
            Container(
              height: 100,
              color: Colors.grey[200],
              child: const Center(
                child: Text('Account settings will be displayed here'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
