import 'package:flutter/material.dart';
import 'dart:math';
import '../../../config/constants.dart';

class StatisticsChart extends StatelessWidget {
  final bool isServiceRequests;

  const StatisticsChart({
    Key? key,
    this.isServiceRequests = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  isServiceRequests ? 'طلبات الخدمة' : 'المبيعات',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                _buildLegend(),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Expanded(
              child: _buildChart(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend() {
    if (isServiceRequests) {
      return Row(
        children: [
          _buildLegendItem('مكتملة', AppColors.success),
          const SizedBox(width: AppDimensions.paddingS),
          _buildLegendItem('قيد التنفيذ', AppColors.warning),
          const SizedBox(width: AppDimensions.paddingS),
          _buildLegendItem('في الانتظار', AppColors.info),
        ],
      );
    } else {
      return Row(
        children: [
          _buildLegendItem('هذا الشهر', AppColors.primary),
          const SizedBox(width: AppDimensions.paddingS),
          _buildLegendItem('الشهر الماضي', AppColors.primary.withAlpha(128)),
        ],
      );
    }
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildChart() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return CustomPaint(
          size: Size(constraints.maxWidth, constraints.maxHeight),
          painter: isServiceRequests
              ? ServiceRequestsChartPainter()
              : SalesChartPainter(),
        );
      },
    );
  }
}

class SalesChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final random = Random();

    // Draw grid lines
    final paint = Paint()
      ..color = AppColors.border
      ..strokeWidth = 1;

    // Horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = height - (height / 4 * i);
      canvas.drawLine(Offset(0, y), Offset(width, y), paint);
    }

    // Draw current month data
    final currentMonthPaint = Paint()
      ..color = AppColors.primary
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final currentMonthPath = Path();
    final barWidth = width / 30;
    final spacing = (width - (barWidth * 30)) / 29;

    for (int i = 0; i < 30; i++) {
      final value = random.nextDouble() * 0.8 + 0.2; // Random value between 0.2 and 1.0
      final x = i * (barWidth + spacing);
      final y = height - (height * value);

      if (i == 0) {
        currentMonthPath.moveTo(x, y);
      } else {
        currentMonthPath.lineTo(x, y);
      }
    }

    canvas.drawPath(currentMonthPath, currentMonthPaint);

    // Draw previous month data
    final prevMonthPaint = Paint()
      ..color = AppColors.primary.withAlpha(128)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final prevMonthPath = Path();

    for (int i = 0; i < 30; i++) {
      final value = random.nextDouble() * 0.7 + 0.1; // Random value between 0.1 and 0.8
      final x = i * (barWidth + spacing);
      final y = height - (height * value);

      if (i == 0) {
        prevMonthPath.moveTo(x, y);
      } else {
        prevMonthPath.lineTo(x, y);
      }
    }

    canvas.drawPath(prevMonthPath, prevMonthPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ServiceRequestsChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final random = Random();

    // Draw grid lines
    final paint = Paint()
      ..color = AppColors.border
      ..strokeWidth = 1;

    // Horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = height - (height / 4 * i);
      canvas.drawLine(Offset(0, y), Offset(width, y), paint);
    }

    // Draw bars
    final barWidth = width / 12;
    final spacing = (width - (barWidth * 12)) / 11;

    for (int i = 0; i < 12; i++) {
      final completedValue = random.nextDouble() * 0.3 + 0.1; // 10-40%
      final inProgressValue = random.nextDouble() * 0.3 + 0.1; // 10-40%
      final pendingValue = random.nextDouble() * 0.3 + 0.1; // 10-40%

      final totalHeight = height * (completedValue + inProgressValue + pendingValue);
      final completedHeight = height * completedValue;
      final inProgressHeight = height * inProgressValue;
      final pendingHeight = height * pendingValue;

      final x = i * (barWidth + spacing);

      // Draw pending (blue)
      final pendingRect = Rect.fromLTWH(
        x,
        height - totalHeight,
        barWidth,
        pendingHeight
      );
      canvas.drawRect(pendingRect, Paint()..color = AppColors.info);

      // Draw in progress (orange)
      final inProgressRect = Rect.fromLTWH(
        x,
        height - totalHeight + pendingHeight,
        barWidth,
        inProgressHeight
      );
      canvas.drawRect(inProgressRect, Paint()..color = AppColors.warning);

      // Draw completed (green)
      final completedRect = Rect.fromLTWH(
        x,
        height - totalHeight + pendingHeight + inProgressHeight,
        barWidth,
        completedHeight
      );
      canvas.drawRect(completedRect, Paint()..color = AppColors.success);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
