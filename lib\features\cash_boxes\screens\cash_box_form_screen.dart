import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../core/repositories/cash_box_repository.dart';
import '../../../shared/models/cash_box.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../../../shared/widgets/rtl_aware_widget.dart';
import '../../../core/error_handling/error_handler.dart';
import '../../../core/validation/validation_service.dart';

/// Cash Box form screen for creating and editing cash boxes
class CashBoxFormScreen extends StatefulWidget {
  final CashBox? cashBox;

  const CashBoxFormScreen({super.key, this.cashBox});

  @override
  State<CashBoxFormScreen> createState() => _CashBoxFormScreenState();
}

class _CashBoxFormScreenState extends State<CashBoxFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();
  
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _openingBalanceController;
  
  bool _isActive = true;
  String _currency = 'SAR';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.cashBox?.name ?? '');
    _descriptionController = TextEditingController(text: widget.cashBox?.description ?? '');
    _openingBalanceController = TextEditingController(
      text: widget.cashBox?.openingBalance.toString() ?? '0',
    );
    
    if (widget.cashBox != null) {
      _isActive = widget.cashBox!.isActive;
      _currency = widget.cashBox!.currency ?? 'SAR';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _openingBalanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final isEditing = widget.cashBox != null;

    return RTLAwareWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            isEditing 
                ? localizations.translate('edit_cash_box')
                : localizations.translate('add_cash_box'),
          ),
          actions: [
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              TextButton(
                onPressed: _saveCashBox,
                child: Text(localizations.translate('save')),
              ),
          ],
        ),
        body: ResponsiveContainer(
          child: Form(
            key: _formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Name Field
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: localizations.translate('cash_box_name'),
                    hintText: localizations.translate('enter_cash_box_name'),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.account_balance_wallet),
                  ),
                  validator: (value) => ValidationService.validateRequired(
                    value,
                    localizations,
                    localizations.translate('cash_box_name'),
                  ),
                  textInputAction: TextInputAction.next,
                ),
                
                const SizedBox(height: 16),
                
                // Description Field
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: localizations.translate('description'),
                    hintText: localizations.translate('enter_description'),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.description),
                  ),
                  maxLines: 3,
                  textInputAction: TextInputAction.next,
                ),
                
                const SizedBox(height: 16),
                
                // Opening Balance Field
                TextFormField(
                  controller: _openingBalanceController,
                  decoration: InputDecoration(
                    labelText: localizations.translate('opening_balance'),
                    hintText: localizations.translate('enter_opening_balance'),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.account_balance),
                    suffixText: _currency,
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) => ValidationService.validateAmount(
                    value,
                    localizations,
                    required: true,
                  ),
                  textInputAction: TextInputAction.next,
                ),
                
                const SizedBox(height: 16),
                
                // Currency Selection
                DropdownButtonFormField<String>(
                  value: _currency,
                  decoration: InputDecoration(
                    labelText: localizations.translate('currency'),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.monetization_on),
                  ),
                  items: [
                    DropdownMenuItem(
                      value: 'SAR',
                      child: Text('${localizations.translate('saudi_riyal')} (SAR)'),
                    ),
                    DropdownMenuItem(
                      value: 'USD',
                      child: Text('${localizations.translate('us_dollar')} (USD)'),
                    ),
                    DropdownMenuItem(
                      value: 'EUR',
                      child: Text('${localizations.translate('euro')} (EUR)'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _currency = value);
                    }
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Active Status
                Card(
                  child: SwitchListTile(
                    title: Text(localizations.translate('active_status')),
                    subtitle: Text(
                      _isActive 
                          ? localizations.translate('cash_box_active_desc')
                          : localizations.translate('cash_box_inactive_desc'),
                    ),
                    value: _isActive,
                    onChanged: (value) {
                      setState(() => _isActive = value);
                    },
                    secondary: Icon(
                      _isActive ? Icons.check_circle : Icons.cancel,
                      color: _isActive ? Colors.green : Colors.orange,
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _saveCashBox,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label: Text(
                      isEditing 
                          ? localizations.translate('update_cash_box')
                          : localizations.translate('create_cash_box'),
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                
                if (isEditing) ...[
                  const SizedBox(height: 16),
                  
                  // Delete Button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: _isLoading ? null : _deleteCashBox,
                      icon: const Icon(Icons.delete),
                      label: Text(localizations.translate('delete_cash_box')),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveCashBox() async {
    if (!_formKey.currentState!.validate()) return;
    
    final localizations = AppLocalizations.of(context)!;
    
    setState(() => _isLoading = true);
    
    try {
      final openingBalance = double.parse(_openingBalanceController.text);
      
      if (widget.cashBox == null) {
        // Create new cash box
        final cashBox = CashBox(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty 
              ? null 
              : _descriptionController.text.trim(),
          openingBalance: openingBalance,
          currentBalance: openingBalance, // Start with opening balance
          isActive: _isActive,
          currency: _currency,
          createdAt: DateTime.now(),
        );
        
        await _cashBoxRepository.insertCashBox(cashBox);
        
        if (mounted) {
          ErrorHandler.showSuccess(
            context,
            localizations.translate('cash_box_created_successfully'),
          );
          Navigator.pop(context, true);
        }
      } else {
        // Update existing cash box
        final updatedCashBox = widget.cashBox!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty 
              ? null 
              : _descriptionController.text.trim(),
          openingBalance: openingBalance,
          isActive: _isActive,
          currency: _currency,
          updatedAt: DateTime.now(),
        );
        
        await _cashBoxRepository.updateCashBox(updatedCashBox);
        
        if (mounted) {
          ErrorHandler.showSuccess(
            context,
            localizations.translate('cash_box_updated_successfully'),
          );
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.handleError(context, e);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _deleteCashBox() async {
    if (widget.cashBox == null) return;
    
    final localizations = AppLocalizations.of(context)!;
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.translate('confirm_delete')),
        content: Text(
          localizations.translateWithParams(
            'confirm_delete_cash_box',
            {'name': widget.cashBox!.name},
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(localizations.translate('cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(localizations.translate('delete')),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    setState(() => _isLoading = true);
    
    try {
      await _cashBoxRepository.deleteCashBox(widget.cashBox!.localId!);
      
      if (mounted) {
        ErrorHandler.showSuccess(
          context,
          localizations.translate('cash_box_deleted_successfully'),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.handleError(context, e);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
