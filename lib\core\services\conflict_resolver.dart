import 'dart:async';
import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../repositories/customer_repository.dart';
import '../../shared/models/sync_model.dart';
import '../../shared/models/sync_conflict.dart';
import '../../shared/models/customer.dart';

/// Service for handling sync conflicts and resolution strategies
class ConflictResolver {
  static final ConflictResolver _instance = ConflictResolver._internal();
  factory ConflictResolver() => _instance;
  ConflictResolver._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CustomerRepository _customerRepository = CustomerRepository();

  // Stream controllers
  final StreamController<List<SyncConflict>> _conflictsController = 
      StreamController<List<SyncConflict>>.broadcast();

  /// Stream of unresolved conflicts
  Stream<List<SyncConflict>> get conflictsStream => _conflictsController.stream;

  /// Detect and create a conflict between local and remote models
  Future<SyncConflict?> detectConflict<T extends SyncModel>(
    T localModel,
    T remoteModel,
    String entityType,
  ) async {
    try {
      // Check if both models have been modified since last sync
      if (!localModel.isModifiedSinceSync() || !remoteModel.isModifiedSinceSync()) {
        return null; // No conflict
      }

      // Check if data hashes are different
      final localHash = localModel.generateDataHash();
      final remoteHash = remoteModel.generateDataHash();
      
      if (localHash == remoteHash) {
        return null; // Same data, no conflict
      }

      // Create conflict
      final conflict = SyncConflict.fromModels(
        entityType: entityType,
        entityId: localModel.firestoreId ?? localModel.localId.toString(),
        localModel: localModel,
        remoteModel: remoteModel,
        metadata: {
          'local_hash': localHash,
          'remote_hash': remoteHash,
          'local_version': localModel.version,
          'remote_version': remoteModel.version,
        },
      );

      // Store conflict in database
      await _dbHelper.addSyncConflict(conflict.toMap());

      // Notify listeners
      await _refreshConflicts();

      if (kDebugMode) {
        print('🔥 Conflict detected: ${conflict.id}');
      }

      return conflict;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error detecting conflict: $e');
      }
      rethrow;
    }
  }

  /// Get all unresolved conflicts
  Future<List<SyncConflict>> getUnresolvedConflicts() async {
    try {
      final conflictMaps = await _dbHelper.getUnresolvedConflicts();
      return conflictMaps.map((map) => SyncConflict.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unresolved conflicts: $e');
      }
      rethrow;
    }
  }

  /// Get conflicts for a specific entity
  Future<List<SyncConflict>> getConflictsForEntity(String entityType, String entityId) async {
    try {
      final db = await _dbHelper.database;
      final results = await db.query(
        'sync_conflicts',
        where: 'entity_type = ? AND entity_id = ? AND is_resolved = ?',
        whereArgs: [entityType, entityId, 0],
      );

      return results.map((map) => SyncConflict.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting conflicts for entity: $e');
      }
      rethrow;
    }
  }

  /// Resolve a conflict using the specified resolution strategy
  Future<void> resolveConflict(String conflictId, ConflictResolution resolution) async {
    try {
      if (kDebugMode) {
        print('🔧 Resolving conflict $conflictId with strategy: $resolution');
      }

      // Get the conflict
      final conflicts = await getUnresolvedConflicts();
      final conflict = conflicts.firstWhere(
        (c) => c.id == conflictId,
        orElse: () => throw Exception('Conflict not found: $conflictId'),
      );

      // Apply resolution strategy
      await _applyResolution(conflict, resolution);

      // Mark conflict as resolved
      await _dbHelper.resolveSyncConflict(conflictId, resolution.toString().split('.').last);

      // Refresh conflicts stream
      await _refreshConflicts();

      if (kDebugMode) {
        print('✅ Conflict resolved: $conflictId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resolving conflict: $e');
      }
      rethrow;
    }
  }

  /// Auto-resolve conflicts where possible using timestamp-based strategy
  Future<int> autoResolveConflicts() async {
    try {
      final conflicts = await getUnresolvedConflicts();
      int resolvedCount = 0;

      for (final conflict in conflicts) {
        if (conflict.canAutoResolve()) {
          final resolution = conflict.getAutoResolution();
          if (resolution != null) {
            await resolveConflict(conflict.id, resolution);
            resolvedCount++;
          }
        }
      }

      if (kDebugMode) {
        print('✅ Auto-resolved $resolvedCount conflicts');
      }

      return resolvedCount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error auto-resolving conflicts: $e');
      }
      rethrow;
    }
  }

  /// Resolve all conflicts for a specific entity using a strategy
  Future<int> resolveAllConflictsForEntity(
    String entityType,
    String entityId,
    ConflictResolution resolution,
  ) async {
    try {
      final conflicts = await getConflictsForEntity(entityType, entityId);
      
      for (final conflict in conflicts) {
        await resolveConflict(conflict.id, resolution);
      }

      if (kDebugMode) {
        print('✅ Resolved ${conflicts.length} conflicts for $entityType:$entityId');
      }

      return conflicts.length;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resolving conflicts for entity: $e');
      }
      rethrow;
    }
  }

  /// Get conflict resolution suggestions
  ConflictResolutionSuggestion getResolutionSuggestion(SyncConflict conflict) {
    try {
      // Check if auto-resolution is possible
      if (conflict.canAutoResolve()) {
        final autoResolution = conflict.getAutoResolution();
        if (autoResolution != null) {
          return ConflictResolutionSuggestion(
            suggestedResolution: autoResolution,
            confidence: ResolutionConfidence.high,
            reason: 'Only timestamp differences detected',
          );
        }
      }

      // Analyze differences
      final differences = conflict.getDifferences();
      
      // If only a few fields differ, suggest manual review
      if (differences.length <= 3) {
        return ConflictResolutionSuggestion(
          suggestedResolution: ConflictResolution.manual,
          confidence: ResolutionConfidence.medium,
          reason: 'Few field differences - manual review recommended',
        );
      }

      // Many differences - suggest using most recent
      final localUpdated = conflict.localData['updated_at'] as String?;
      final remoteUpdated = conflict.remoteData['updated_at'] as String?;

      if (localUpdated != null && remoteUpdated != null) {
        final localTime = DateTime.parse(localUpdated);
        final remoteTime = DateTime.parse(remoteUpdated);
        
        final suggestedResolution = localTime.isAfter(remoteTime)
            ? ConflictResolution.useLocal
            : ConflictResolution.useRemote;

        return ConflictResolutionSuggestion(
          suggestedResolution: suggestedResolution,
          confidence: ResolutionConfidence.medium,
          reason: 'Using most recent version based on timestamp',
        );
      }

      // Default to manual resolution
      return ConflictResolutionSuggestion(
        suggestedResolution: ConflictResolution.manual,
        confidence: ResolutionConfidence.low,
        reason: 'Complex conflict requires manual review',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting resolution suggestion: $e');
      }
      return ConflictResolutionSuggestion(
        suggestedResolution: ConflictResolution.manual,
        confidence: ResolutionConfidence.low,
        reason: 'Error analyzing conflict',
      );
    }
  }

  /// Clear all resolved conflicts older than specified duration
  Future<int> clearResolvedConflicts({Duration olderThan = const Duration(days: 30)}) async {
    try {
      final cutoffDate = DateTime.now().subtract(olderThan);
      final db = await _dbHelper.database;
      
      final deletedCount = await db.delete(
        'sync_conflicts',
        where: 'is_resolved = ? AND resolved_at < ?',
        whereArgs: [1, cutoffDate.toIso8601String()],
      );

      if (kDebugMode) {
        print('🧹 Cleared $deletedCount resolved conflicts');
      }

      return deletedCount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing resolved conflicts: $e');
      }
      rethrow;
    }
  }

  /// Dispose the conflict resolver
  void dispose() {
    _conflictsController.close();
  }

  // Private methods

  /// Apply the resolution strategy to the conflict
  Future<void> _applyResolution(SyncConflict conflict, ConflictResolution resolution) async {
    try {
      switch (conflict.entityType.toLowerCase()) {
        case 'customer':
          await _applyCustomerResolution(conflict, resolution);
          break;
        // TODO: Add other entity types
        default:
          throw Exception('Unknown entity type: ${conflict.entityType}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error applying resolution: $e');
      }
      rethrow;
    }
  }

  /// Apply resolution for customer conflicts
  Future<void> _applyCustomerResolution(SyncConflict conflict, ConflictResolution resolution) async {
    try {
      switch (resolution) {
        case ConflictResolution.useLocal:
          // Keep local version, mark for upload
          await _markCustomerForUpload(conflict.entityId);
          break;
          
        case ConflictResolution.useRemote:
          // Use remote version, update local
          final remoteCustomer = Customer.fromMap(conflict.remoteData);
          await _updateLocalCustomer(remoteCustomer);
          break;
          
        case ConflictResolution.merge:
          // Attempt to merge both versions
          final mergedCustomer = await _mergeCustomers(conflict);
          await _updateLocalCustomer(mergedCustomer);
          break;
          
        case ConflictResolution.manual:
          // Manual resolution - no automatic action
          break;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error applying customer resolution: $e');
      }
      rethrow;
    }
  }

  /// Mark customer for upload
  Future<void> _markCustomerForUpload(String customerId) async {
    final customer = await _customerRepository.getById(customerId);
    if (customer != null) {
      await _customerRepository.update(customer.copyWithSyncData(
        syncStatus: SyncStatus.pendingUpload,
      ));
    }
  }

  /// Update local customer with remote data
  Future<void> _updateLocalCustomer(Customer customer) async {
    await _customerRepository.update(customer.copyWithSyncData(
      syncStatus: SyncStatus.synced,
      lastSyncTime: DateTime.now(),
    ));
  }

  /// Merge two customer versions (simple merge strategy)
  Future<Customer> _mergeCustomers(SyncConflict conflict) async {
    final localData = conflict.localData;
    final remoteData = conflict.remoteData;
    
    // Simple merge: use remote for most fields, but keep local notes if they exist
    final mergedData = Map<String, dynamic>.from(remoteData);
    
    // Keep local notes if they exist and remote doesn't have them
    if (localData['notes'] != null && 
        (remoteData['notes'] == null || remoteData['notes'].toString().isEmpty)) {
      mergedData['notes'] = localData['notes'];
    }
    
    // Use the most recent timestamp
    final localUpdated = localData['updated_at'] as String?;
    final remoteUpdated = remoteData['updated_at'] as String?;
    
    if (localUpdated != null && remoteUpdated != null) {
      final localTime = DateTime.parse(localUpdated);
      final remoteTime = DateTime.parse(remoteUpdated);
      
      mergedData['updated_at'] = localTime.isAfter(remoteTime) 
          ? localUpdated 
          : remoteUpdated;
    }
    
    return Customer.fromMap(mergedData);
  }

  /// Refresh conflicts stream
  Future<void> _refreshConflicts() async {
    try {
      final conflicts = await getUnresolvedConflicts();
      _conflictsController.add(conflicts);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error refreshing conflicts: $e');
      }
    }
  }
}

/// Enum for resolution confidence levels
enum ResolutionConfidence {
  low,
  medium,
  high,
}

/// Class representing a conflict resolution suggestion
class ConflictResolutionSuggestion {
  final ConflictResolution suggestedResolution;
  final ResolutionConfidence confidence;
  final String reason;

  const ConflictResolutionSuggestion({
    required this.suggestedResolution,
    required this.confidence,
    required this.reason,
  });

  @override
  String toString() {
    return 'ConflictResolutionSuggestion(resolution: $suggestedResolution, confidence: $confidence, reason: $reason)';
  }
}
