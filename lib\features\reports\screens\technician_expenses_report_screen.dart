import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../shared/widgets/responsive_layout.dart';

class TechnicianExpensesReportScreen extends StatefulWidget {
  const TechnicianExpensesReportScreen({super.key});

  @override
  State<TechnicianExpensesReportScreen> createState() => _TechnicianExpensesReportScreenState();
}

class _TechnicianExpensesReportScreenState extends State<TechnicianExpensesReportScreen> {
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int? _selectedTechnicianId;
  
  bool _isLoading = false;
  Map<String, dynamic>? _reportData;
  List<Map<String, dynamic>> _technicians = [];

  @override
  void initState() {
    super.initState();
    _loadTechnicians();
    _generateReport();
  }

  Future<void> _loadTechnicians() async {
    try {
      final employees = await _employeeRepository.getAllEmployees();
      final technicians = employees.where((emp) => 
        emp.status.toString().split('.').last == 'active' && (
          emp.position.toLowerCase().contains('فني') ||
          emp.position.toLowerCase().contains('technician') ||
          emp.position.toLowerCase().contains('maintenance') ||
          emp.position.toLowerCase().contains('صيانة')
        )
      ).map((emp) => {
        'id': emp.id,
        'name': emp.name,
        'position': emp.position,
        'payment_type': emp.paymentType.toString().split('.').last,
      }).toList();

      setState(() {
        _technicians = technicians;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الفنيين: $e');
      }
    }
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportData = await _serviceRequestRepository.getTechnicianExpenses(
        startDate: _startDate,
        endDate: _endDate,
        technicianId: _selectedTechnicianId,
      );

      setState(() {
        _reportData = reportData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _generateReport();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير مصروفات الفنيين'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateReport,
          ),
        ],
      ),
      body: ResponsiveContainer(
        child: Column(
          children: [
            // Filters
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'فلاتر التقرير',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: 16),
                    
                    // Date range
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _selectDateRange,
                            icon: const Icon(Icons.date_range),
                            label: Text(
                              '${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Technician filter
                    DropdownButtonFormField<int?>(
                      decoration: const InputDecoration(
                        labelText: 'فلترة حسب الفني',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                      value: _selectedTechnicianId,
                      items: [
                        const DropdownMenuItem<int?>(
                          value: null,
                          child: Text('جميع الفنيين'),
                        ),
                        ..._technicians.map((tech) => DropdownMenuItem<int?>(
                          value: tech['id'] as int,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(tech['name'] as String),
                              Text(
                                '${tech['position']} - ${tech['payment_type'] == 'daily' ? 'أجر يومي' : 'راتب شهري'}',
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        )),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedTechnicianId = value;
                        });
                        _generateReport();
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            // Report content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _reportData == null
                      ? const Center(
                          child: Text(
                            'لا توجد بيانات للعرض',
                            style: AppTextStyles.bodyLarge,
                          ),
                        )
                      : _buildReportContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportContent() {
    final totalExpenses = _reportData!['total_expenses'] as double;
    final totalServices = _reportData!['total_services'] as int;
    final expensesByTechnician = _reportData!['expenses_by_technician'] as Map<String, double>;
    final servicesByTechnician = _reportData!['services_by_technician'] as Map<String, int>;
    final transactions = _reportData!['transactions'] as List<dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي المصروفات',
                  '${totalExpenses.toStringAsFixed(2)} ر.س',
                  Icons.money_off,
                  Colors.red,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'عدد الخدمات',
                  totalServices.toString(),
                  Icons.build,
                  Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Average per service
          if (totalServices > 0)
            _buildSummaryCard(
              'متوسط التكلفة لكل خدمة',
              '${(totalExpenses / totalServices).toStringAsFixed(2)} ر.س',
              Icons.analytics,
              Colors.orange,
            ),
          
          const SizedBox(height: 24),
          
          // Expenses by technician
          if (expensesByTechnician.isNotEmpty) ...[
            const Text(
              'المصروفات حسب الفني',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: 16),
            ...expensesByTechnician.entries.map((entry) => Card(
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: Text(
                    entry.key.substring(0, 1),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(entry.key),
                subtitle: Text('${servicesByTechnician[entry.key] ?? 0} خدمة'),
                trailing: Text(
                  '${entry.value.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            )),
          ],
          
          const SizedBox(height: 24),
          
          // Recent transactions
          if (transactions.isNotEmpty) ...[
            const Text(
              'المعاملات الأخيرة',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: 16),
            ...transactions.take(10).map((transaction) => Card(
              child: ListTile(
                leading: const Icon(Icons.payment, color: Colors.red),
                title: Text(transaction['description'] as String? ?? 'مصروف فني'),
                subtitle: Text(
                  '${transaction['reference'] ?? 'غير محدد'} - ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(transaction['transaction_date'] as String))}',
                ),
                trailing: Text(
                  '${(transaction['amount'] as double).toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.heading2.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
