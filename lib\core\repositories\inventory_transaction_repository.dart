import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/inventory_transaction.dart';
import 'inventory_repository.dart';

class InventoryTransactionRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final InventoryRepository _inventoryRepository = InventoryRepository();

  // Get all inventory transactions
  Future<List<InventoryTransaction>> getAllInventoryTransactions() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_transactions',
        orderBy: 'transaction_date DESC',
      );

      return List.generate(maps.length, (i) {
        return InventoryTransaction.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory transactions: $e');
      }
      return [];
    }
  }

  // Get inventory transactions by item ID
  Future<List<InventoryTransaction>> getInventoryTransactionsByItemId(int itemId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_transactions',
        where: 'inventory_item_id = ?',
        whereArgs: [itemId],
        orderBy: 'transaction_date DESC',
      );

      return List.generate(maps.length, (i) {
        return InventoryTransaction.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory transactions by item ID: $e');
      }
      return [];
    }
  }

  // Insert a new inventory transaction and update inventory item quantity
  Future<bool> insertInventoryTransaction(InventoryTransaction transaction) async {
    final db = await _dbHelper.database;

    try {
      await db.transaction((txn) async {
        // Insert the transaction
        await txn.insert(
          'inventory_transactions',
          transaction.toMap(),
        );

        // Get the current inventory item
        final inventoryItem = await _inventoryRepository.getInventoryItemById(transaction.inventoryItemId);

        if (inventoryItem == null) {
          throw Exception('Inventory item not found');
        }

        // Calculate the new quantity based on transaction type
        double newQuantity = inventoryItem.quantity;

        switch (transaction.type) {
          case InventoryTransactionType.addition:
            newQuantity += transaction.quantity;
            break;
          case InventoryTransactionType.subtraction:
            newQuantity -= transaction.quantity;
            if (newQuantity < 0) {
              throw Exception('Insufficient quantity');
            }
            break;
          case InventoryTransactionType.adjustment:
            newQuantity = transaction.quantity;
            break;
          case InventoryTransactionType.transfer:
            // Transfer is handled separately
            break;
        }

        // Update the inventory item quantity
        await txn.update(
          'inventory_items',
          {'quantity': newQuantity},
          where: 'id = ?',
          whereArgs: [transaction.inventoryItemId],
        );
      });

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting inventory transaction: $e');
      }
      return false;
    }
  }

  // Get inventory transactions by service request ID
  Future<List<InventoryTransaction>> getInventoryTransactionsByServiceRequestId(int serviceRequestId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_transactions',
        where: 'service_request_id = ?',
        whereArgs: [serviceRequestId],
        orderBy: 'transaction_date DESC',
      );

      return List.generate(maps.length, (i) {
        return InventoryTransaction.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory transactions by service request ID: $e');
      }
      return [];
    }
  }

  // Create inventory transactions for service request
  Future<bool> createInventoryTransactionsForServiceRequest(
    int serviceRequestId,
    String serviceRequestReference,
    List<int> inventoryItemIds,
    List<int> quantities,
    int userId,
    String userName,
  ) async {
    try {
      for (int i = 0; i < inventoryItemIds.length; i++) {
        final inventoryItem = await _inventoryRepository.getInventoryItemById(inventoryItemIds[i]);

        if (inventoryItem == null) {
          continue;
        }

        final transaction = InventoryTransaction(
          inventoryItemId: inventoryItemIds[i],
          inventoryItemName: inventoryItem.name,
          inventoryItemCode: inventoryItem.code,
          type: InventoryTransactionType.subtraction,
          quantity: quantities[i].toDouble(),
          unit: inventoryItem.unit,
          reference: 'SR-$serviceRequestReference',
          reason: 'استخدام في طلب خدمة',
          serviceRequestId: serviceRequestId,
          serviceRequestReference: serviceRequestReference,
          userId: userId,
          userName: userName,
          transactionDate: DateTime.now(),
        );

        final success = await insertInventoryTransaction(transaction);

        if (!success) {
          return false;
        }
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating inventory transactions for service request: $e');
      }
      return false;
    }
  }
}
