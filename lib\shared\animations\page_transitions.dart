import 'package:flutter/material.dart';

/// أنواع تأثيرات الانتقال بين الصفحات
enum PageTransitionType {
  /// تلاشي
  fade,

  /// انزلاق من اليمين
  slideRight,

  /// انزلاق من اليسار
  slideLeft,

  /// انزلاق من الأعلى
  slideDown,

  /// انزلاق من الأسفل
  slideUp,

  /// تكبير
  scale,

  /// تلاشي مع تكبير
  fadeScale,

  /// تلاشي مع انزلاق
  fadeSlide,

  /// دوران
  rotation,
}

/// تأثير انتقال مخصص بين الصفحات
class PageTransition<T> extends PageRouteBuilder<T> {
  /// الصفحة التي سيتم الانتقال إليها
  final Widget page;

  /// نوع تأثير الانتقال
  final PageTransitionType type;

  /// مدة الانتقال
  final Duration duration;

  /// منحنى الحركة
  final Curve curve;

  /// اتجاه الانتقال (للانزلاق)
  final Alignment alignment;

  /// إنشاء تأثير انتقال مخصص
  PageTransition({
    required this.page,
    this.type = PageTransitionType.fade,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.alignment = Alignment.center,
    RouteSettings? settings,
  }) : super(
          settings: settings,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: curve,
            );

            switch (type) {
              case PageTransitionType.fade:
                return FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(curvedAnimation),
                  child: child,
                );

              case PageTransitionType.slideRight:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-1, 0),
                    end: Offset.zero,
                  ).animate(curvedAnimation),
                  child: child,
                );

              case PageTransitionType.slideLeft:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(1, 0),
                    end: Offset.zero,
                  ).animate(curvedAnimation),
                  child: child,
                );

              case PageTransitionType.slideDown:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, -1),
                    end: Offset.zero,
                  ).animate(curvedAnimation),
                  child: child,
                );

              case PageTransitionType.slideUp:
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(curvedAnimation),
                  child: child,
                );

              case PageTransitionType.scale:
                return ScaleTransition(
                  alignment: alignment,
                  scale: Tween<double>(
                    begin: 0.0,
                    end: 1.0,
                  ).animate(curvedAnimation),
                  child: child,
                );

              case PageTransitionType.fadeScale:
                return FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
                    ),
                  ),
                  child: ScaleTransition(
                    alignment: alignment,
                    scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
                      ),
                    ),
                    child: child,
                  ),
                );

              case PageTransitionType.fadeSlide:
                return FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: const Interval(0.0, 0.7, curve: Curves.easeIn),
                    ),
                  ),
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.2),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
                      ),
                    ),
                    child: child,
                  ),
                );

              case PageTransitionType.rotation:
                return RotationTransition(
                  turns: Tween<double>(begin: 0.5, end: 1.0).animate(curvedAnimation),
                  child: FadeTransition(
                    opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: const Interval(0.5, 1.0),
                      ),
                    ),
                    child: child,
                  ),
                );
            }
          },
        );
}

/// مولد تأثيرات الانتقال بين الصفحات
class AppPageTransitions {
  /// إنشاء تأثير انتقال للصفحة
  static Route<T> createRoute<T>(
    Widget page, {
    PageTransitionType type = PageTransitionType.fadeSlide,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    RouteSettings? settings,
  }) {
    return PageTransition<T>(
      page: page,
      type: type,
      duration: duration,
      curve: curve,
      settings: settings,
    );
  }
}
