import 'dart:convert';
import 'package:flutter/material.dart';

enum ReportType {
  financial,
  employee,
  customer,
  supplier,
  inventory,
  sales,
  invoice,
  custom,
}

class Report {
  final int? id;
  final String title;
  final String? description;
  final ReportType type;
  final Map<String, dynamic>? parameters;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Report({
    this.id,
    required this.title,
    this.description,
    required this.type,
    this.parameters,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory Report.fromMap(Map<String, dynamic> map) {
    return Report(
      id: map['id'] as int?,
      title: map['title'] as String,
      description: map['description'] as String?,
      type: _parseReportType(map['type'] as String),
      parameters: map['parameters'] != null
          ? jsonDecode(map['parameters'] as String) as Map<String, dynamic>
          : null,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory Report.fromJson(String source) =>
      Report.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'parameters': parameters != null ? jsonEncode(parameters) : null,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  Report copyWith({
    int? id,
    String? title,
    String? description,
    ReportType? type,
    Map<String, dynamic>? parameters,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Report(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      parameters: parameters ?? this.parameters,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static ReportType _parseReportType(String type) {
    switch (type.toLowerCase()) {
      case 'financial':
        return ReportType.financial;
      case 'employee':
        return ReportType.employee;
      case 'customer':
        return ReportType.customer;
      case 'supplier':
        return ReportType.supplier;
      case 'inventory':
        return ReportType.inventory;
      case 'sales':
        return ReportType.sales;
      case 'invoice':
        return ReportType.invoice;
      case 'custom':
        return ReportType.custom;
      default:
        return ReportType.custom;
    }
  }

  static String getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.financial:
        return 'تقرير مالي';
      case ReportType.employee:
        return 'تقرير الموظفين';
      case ReportType.customer:
        return 'تقرير العملاء';
      case ReportType.supplier:
        return 'تقرير الموردين';
      case ReportType.inventory:
        return 'تقرير المخزون';
      case ReportType.sales:
        return 'تقرير المبيعات';
      case ReportType.invoice:
        return 'تقرير الفواتير';
      case ReportType.custom:
        return 'تقرير مخصص';
    }
  }

  static Color getReportTypeColor(ReportType type) {
    switch (type) {
      case ReportType.financial:
        return Colors.green;
      case ReportType.employee:
        return Colors.blue;
      case ReportType.customer:
        return Colors.purple;
      case ReportType.supplier:
        return Colors.orange;
      case ReportType.inventory:
        return Colors.teal;
      case ReportType.sales:
        return Colors.indigo;
      case ReportType.invoice:
        return Colors.deepOrange;
      case ReportType.custom:
        return Colors.grey;
    }
  }

  static IconData getReportTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.financial:
        return Icons.attach_money;
      case ReportType.employee:
        return Icons.people;
      case ReportType.customer:
        return Icons.person;
      case ReportType.supplier:
        return Icons.local_shipping;
      case ReportType.inventory:
        return Icons.inventory;
      case ReportType.sales:
        return Icons.shopping_cart;
      case ReportType.invoice:
        return Icons.receipt;
      case ReportType.custom:
        return Icons.description;
    }
  }
}
