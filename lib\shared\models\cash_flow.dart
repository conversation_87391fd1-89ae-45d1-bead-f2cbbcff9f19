import 'dart:convert';
import 'package:flutter/material.dart';

/// أنواع التدفق النقدي
enum CashFlowType {
  /// تدفق نقدي داخل
  inflow,
  
  /// تدفق نقدي خارج
  outflow,
}

/// فئات التدفق النقدي
enum CashFlowCategory {
  /// إيرادات تشغيلية
  operatingRevenue,
  
  /// مصروفات تشغيلية
  operatingExpense,
  
  /// إيرادات استثمارية
  investmentRevenue,
  
  /// مصروفات استثمارية
  investmentExpense,
  
  /// إيرادات تمويلية
  financingRevenue,
  
  /// مصروفات تمويلية
  financingExpense,
}

/// نموذج التدفق النقدي
class CashFlow {
  /// المعرف الفريد للتدفق النقدي
  final int? id;
  
  /// نوع التدفق النقدي
  final CashFlowType type;
  
  /// فئة التدفق النقدي
  final CashFlowCategory category;
  
  /// تاريخ التدفق النقدي
  final DateTime date;
  
  /// المبلغ
  final double amount;
  
  /// الوصف
  final String description;
  
  /// نوع الكيان المرتبط (فاتورة، معاملة، راتب، إلخ)
  final String? relatedEntityType;
  
  /// معرف الكيان المرتبط
  final int? relatedEntityId;
  
  /// اسم الكيان المرتبط
  final String? relatedEntityName;
  
  /// معرف الحساب البنكي
  final int? bankAccountId;
  
  /// اسم الحساب البنكي
  final String? bankAccountName;
  
  /// ملاحظات
  final String? notes;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime? updatedAt;

  /// إنشاء نموذج تدفق نقدي جديد
  CashFlow({
    this.id,
    required this.type,
    required this.category,
    required this.date,
    required this.amount,
    required this.description,
    this.relatedEntityType,
    this.relatedEntityId,
    this.relatedEntityName,
    this.bankAccountId,
    this.bankAccountName,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نموذج من خريطة بيانات
  factory CashFlow.fromMap(Map<String, dynamic> map) {
    return CashFlow(
      id: map['id'] as int?,
      type: _parseCashFlowType(map['type'] as String),
      category: _parseCashFlowCategory(map['category'] as String),
      date: DateTime.parse(map['date'] as String),
      amount: map['amount'] as double,
      description: map['description'] as String,
      relatedEntityType: map['related_entity_type'] as String?,
      relatedEntityId: map['related_entity_id'] as int?,
      relatedEntityName: map['related_entity_name'] as String?,
      bankAccountId: map['bank_account_id'] as int?,
      bankAccountName: map['bank_account_name'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  /// تحويل النموذج إلى خريطة بيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'category': category.toString().split('.').last,
      'date': date.toIso8601String(),
      'amount': amount,
      'description': description,
      'related_entity_type': relatedEntityType,
      'related_entity_id': relatedEntityId,
      'related_entity_name': relatedEntityName,
      'bank_account_id': bankAccountId,
      'bank_account_name': bankAccountName,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// تحويل النموذج إلى سلسلة JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من سلسلة JSON
  factory CashFlow.fromJson(String source) =>
      CashFlow.fromMap(json.decode(source) as Map<String, dynamic>);

  /// إنشاء نسخة معدلة من النموذج
  CashFlow copyWith({
    int? id,
    CashFlowType? type,
    CashFlowCategory? category,
    DateTime? date,
    double? amount,
    String? description,
    String? relatedEntityType,
    int? relatedEntityId,
    String? relatedEntityName,
    int? bankAccountId,
    String? bankAccountName,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashFlow(
      id: id ?? this.id,
      type: type ?? this.type,
      category: category ?? this.category,
      date: date ?? this.date,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityName: relatedEntityName ?? this.relatedEntityName,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      bankAccountName: bankAccountName ?? this.bankAccountName,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل نوع التدفق النقدي من نص
  static CashFlowType _parseCashFlowType(String type) {
    switch (type) {
      case 'inflow':
        return CashFlowType.inflow;
      case 'outflow':
        return CashFlowType.outflow;
      default:
        return CashFlowType.inflow;
    }
  }

  /// تحويل فئة التدفق النقدي من نص
  static CashFlowCategory _parseCashFlowCategory(String category) {
    switch (category) {
      case 'operatingRevenue':
        return CashFlowCategory.operatingRevenue;
      case 'operatingExpense':
        return CashFlowCategory.operatingExpense;
      case 'investmentRevenue':
        return CashFlowCategory.investmentRevenue;
      case 'investmentExpense':
        return CashFlowCategory.investmentExpense;
      case 'financingRevenue':
        return CashFlowCategory.financingRevenue;
      case 'financingExpense':
        return CashFlowCategory.financingExpense;
      default:
        return CashFlowCategory.operatingRevenue;
    }
  }

  /// الحصول على اسم نوع التدفق النقدي
  static String getCashFlowTypeName(CashFlowType type) {
    switch (type) {
      case CashFlowType.inflow:
        return 'تدفق داخل';
      case CashFlowType.outflow:
        return 'تدفق خارج';
    }
  }

  /// الحصول على اسم فئة التدفق النقدي
  static String getCashFlowCategoryName(CashFlowCategory category) {
    switch (category) {
      case CashFlowCategory.operatingRevenue:
        return 'إيرادات تشغيلية';
      case CashFlowCategory.operatingExpense:
        return 'مصروفات تشغيلية';
      case CashFlowCategory.investmentRevenue:
        return 'إيرادات استثمارية';
      case CashFlowCategory.investmentExpense:
        return 'مصروفات استثمارية';
      case CashFlowCategory.financingRevenue:
        return 'إيرادات تمويلية';
      case CashFlowCategory.financingExpense:
        return 'مصروفات تمويلية';
    }
  }
}
