import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';

class EmployeeBalanceRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Calcular el balance (custodia) de un empleado
  Future<double> getEmployeeBalance(int employeeId) async {
    try {
      final db = await _dbHelper.database;

      // Obtener las transacciones donde el empleado es el responsable del pago en efectivo
      final cashInResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_cash_in
        FROM transactions
        WHERE type = 'income' AND employee_id = ?
      ''', [employeeId]);

      final totalCashIn = cashInResult.isNotEmpty
          ? (cashInResult.first['total_cash_in'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Obtener las transacciones donde el empleado es el responsable del pago en efectivo
      final cashOutResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_cash_out
        FROM transactions
        WHERE type = 'expense' AND employee_id = ?
      ''', [employeeId]);

      final totalCashOut = cashOutResult.isNotEmpty
          ? (cashOutResult.first['total_cash_out'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Obtener las transferencias donde el empleado es el destino
      final transfersInResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_transfers_in
        FROM account_transfers
        WHERE destination_type = 'employee' AND destination_id = ?
      ''', [employeeId]);

      final totalTransfersIn = transfersInResult.isNotEmpty
          ? (transfersInResult.first['total_transfers_in'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Obtener las transferencias donde el empleado es el origen
      final transfersOutResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_transfers_out
        FROM account_transfers
        WHERE source_type = 'employee' AND source_id = ?
      ''', [employeeId]);

      final totalTransfersOut = transfersOutResult.isNotEmpty
          ? (transfersOutResult.first['total_transfers_out'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Calcular el balance
      return totalCashIn - totalCashOut + totalTransfersIn - totalTransfersOut;
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating employee balance: $e');
      }
      return 0.0;
    }
  }
}
