import 'dart:convert';

class InvoiceItem {
  final int? id;
  final String name;
  final String? description;
  final double quantity;
  final String? unit;
  final double unitPrice;
  final double? taxRate;
  final double? discount;
  final int? inventoryItemId;

  InvoiceItem({
    this.id,
    required this.name,
    this.description,
    required this.quantity,
    this.unit,
    required this.unitPrice,
    this.taxRate,
    this.discount = 0,
    this.inventoryItemId,
  });

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      quantity: map['quantity'] as double,
      unit: map['unit'] as String?,
      unitPrice: map['unit_price'] as double,
      taxRate: map['tax_rate'] as double?,
      discount: map['discount'] as double? ?? 0,
      inventoryItemId: map['inventory_item_id'] as int?,
    );
  }

  factory InvoiceItem.fromJson(String source) =>
      InvoiceItem.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'quantity': quantity,
      'unit': unit,
      'unit_price': unitPrice,
      'tax_rate': taxRate,
      'discount': discount,
      'inventory_item_id': inventoryItemId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'total': total,
    };
  }

  String toJson() => json.encode(toMap());

  InvoiceItem copyWith({
    int? id,
    String? name,
    String? description,
    double? quantity,
    String? unit,
    double? unitPrice,
    double? taxRate,
    double? discount,
    int? inventoryItemId,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      unitPrice: unitPrice ?? this.unitPrice,
      taxRate: taxRate ?? this.taxRate,
      discount: discount ?? this.discount,
      inventoryItemId: inventoryItemId ?? this.inventoryItemId,
    );
  }

  double get subtotal => quantity * unitPrice;
  double get taxAmount => taxRate != null ? subtotal * taxRate! : 0;
  double get total => subtotal + taxAmount - (discount ?? 0);
}
