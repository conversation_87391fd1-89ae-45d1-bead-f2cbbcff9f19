import 'dart:convert';
import 'package:crypto/crypto.dart';

/// Enum representing the synchronization status of a model
enum SyncStatus {
  synced,
  pendingUpload,
  pendingDownload,
  conflict,
  failed,
}

/// Extension methods for SyncStatus enum
extension SyncStatusExtension on SyncStatus {
  String get value {
    switch (this) {
      case SyncStatus.synced:
        return 'synced';
      case SyncStatus.pendingUpload:
        return 'pendingUpload';
      case SyncStatus.pendingDownload:
        return 'pendingDownload';
      case SyncStatus.conflict:
        return 'conflict';
      case SyncStatus.failed:
        return 'failed';
    }
  }

  static SyncStatus fromString(String value) {
    switch (value) {
      case 'synced':
        return SyncStatus.synced;
      case 'pendingUpload':
        return SyncStatus.pendingUpload;
      case 'pendingDownload':
        return SyncStatus.pendingDownload;
      case 'conflict':
        return SyncStatus.conflict;
      case 'failed':
        return SyncStatus.failed;
      default:
        return SyncStatus.pendingUpload;
    }
  }
}

/// Metadata class for sync operations
class SyncMetadata {
  final DateTime? lastSyncTime;
  final String? lastSyncOperation;
  final Map<String, dynamic>? syncContext;
  final int retryCount;
  final String? lastError;

  const SyncMetadata({
    this.lastSyncTime,
    this.lastSyncOperation,
    this.syncContext,
    this.retryCount = 0,
    this.lastError,
  });

  Map<String, dynamic> toMap() {
    return {
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'lastSyncOperation': lastSyncOperation,
      'syncContext': syncContext != null ? json.encode(syncContext) : null,
      'retryCount': retryCount,
      'lastError': lastError,
    };
  }

  factory SyncMetadata.fromMap(Map<String, dynamic> map) {
    return SyncMetadata(
      lastSyncTime: map['lastSyncTime'] != null 
          ? DateTime.parse(map['lastSyncTime'] as String)
          : null,
      lastSyncOperation: map['lastSyncOperation'] as String?,
      syncContext: map['syncContext'] != null 
          ? json.decode(map['syncContext'] as String) as Map<String, dynamic>
          : null,
      retryCount: map['retryCount'] as int? ?? 0,
      lastError: map['lastError'] as String?,
    );
  }

  SyncMetadata copyWith({
    DateTime? lastSyncTime,
    String? lastSyncOperation,
    Map<String, dynamic>? syncContext,
    int? retryCount,
    String? lastError,
  }) {
    return SyncMetadata(
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      lastSyncOperation: lastSyncOperation ?? this.lastSyncOperation,
      syncContext: syncContext ?? this.syncContext,
      retryCount: retryCount ?? this.retryCount,
      lastError: lastError ?? this.lastError,
    );
  }
}

/// Mixin for models that support soft deletion
mixin SoftDeletable {
  bool get isDeleted;
  DateTime? get deletedAt;
  
  bool get isActive => !isDeleted;
}

/// Mixin for models that support offline operations
mixin OfflineCapable {
  bool get isOfflineOnly;
  DateTime? get offlineCreatedAt;
  
  bool get needsOnlineSync => isOfflineOnly;
}

/// Abstract base class for all synchronizable models
abstract class SyncModel with SoftDeletable, OfflineCapable {
  // Core identification
  String? get id;
  String? get firestoreId;
  int? get localId;
  
  // Timestamps
  DateTime get createdAt;
  DateTime? get updatedAt;
  DateTime? get lastSyncTime;
  
  // Sync properties
  SyncStatus get syncStatus;
  bool get isDeleted;
  int get version;
  String? get dataHash;
  
  // Offline properties
  @override
  bool get isOfflineOnly => firestoreId == null && localId != null;
  
  @override
  DateTime? get offlineCreatedAt => isOfflineOnly ? createdAt : null;
  
  @override
  DateTime? get deletedAt => isDeleted ? updatedAt : null;

  /// Convert model to SQLite-compatible map
  Map<String, dynamic> toSqliteMap();
  
  /// Convert model to Firestore-compatible map
  Map<String, dynamic> toFirestoreMap();
  
  /// Create a copy of the model with updated sync data
  SyncModel copyWithSyncData({
    String? firestoreId,
    int? localId,
    DateTime? lastSyncTime,
    SyncStatus? syncStatus,
    bool? isDeleted,
    int? version,
    String? dataHash,
  });
  
  /// Generate a hash of the model's data for conflict detection
  String generateDataHash() {
    final dataMap = toFirestoreMap();
    // Remove sync-specific fields from hash calculation
    dataMap.remove('firestoreId');
    dataMap.remove('localId');
    dataMap.remove('lastSyncTime');
    dataMap.remove('syncStatus');
    dataMap.remove('version');
    dataMap.remove('dataHash');
    
    final dataString = json.encode(dataMap);
    final bytes = utf8.encode(dataString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// Check if the model needs synchronization
  bool needsSync() {
    return syncStatus == SyncStatus.pendingUpload ||
           syncStatus == SyncStatus.pendingDownload ||
           syncStatus == SyncStatus.failed;
  }
  
  /// Check if the model has been modified since last sync
  bool isModifiedSinceSync() {
    if (lastSyncTime == null || updatedAt == null) {
      return true;
    }
    return updatedAt!.isAfter(lastSyncTime!);
  }
  
  /// Get sync metadata
  SyncMetadata getSyncMetadata() {
    return SyncMetadata(
      lastSyncTime: lastSyncTime,
      lastSyncOperation: syncStatus.value,
      retryCount: 0, // This should be tracked separately if needed
    );
  }
  
  /// Check if model can be safely deleted
  bool canDelete() {
    return !needsSync() || syncStatus == SyncStatus.failed;
  }
  
  /// Check if model has conflicts
  bool hasConflicts() {
    return syncStatus == SyncStatus.conflict;
  }
  
  /// Get the primary identifier (prefers firestoreId over localId)
  String? get primaryId => firestoreId ?? localId?.toString();
  
  /// Check if this is a new record (not yet synced to Firestore)
  bool get isNewRecord => firestoreId == null;
  
  /// Check if this record exists in Firestore
  bool get existsInFirestore => firestoreId != null;
  
  /// Get display name for the model type
  String get modelType => runtimeType.toString();
}
