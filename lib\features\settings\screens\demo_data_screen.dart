import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../core/database/demo_data_generator.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/custom_button.dart';

class DemoDataScreen extends StatefulWidget {
  const DemoDataScreen({Key? key}) : super(key: key);

  @override
  State<DemoDataScreen> createState() => _DemoDataScreenState();
}

class _DemoDataScreenState extends State<DemoDataScreen> {
  final DemoDataGenerator _demoDataGenerator = DemoDataGenerator();
  bool _isGeneratingData = false;
  String? _successMessage;
  String? _errorMessage;
  
  // قائمة بالجداول التي سيتم توليد بيانات لها
  final List<Map<String, dynamic>> _tables = [
    {'name': 'معلومات الشركة', 'icon': Icons.business, 'color': Colors.blue, 'isSelected': true},
    {'name': 'الفئات', 'icon': Icons.category, 'color': Colors.green, 'isSelected': true},
    {'name': 'الحسابات البنكية', 'icon': Icons.account_balance, 'color': Colors.purple, 'isSelected': true},
    {'name': 'العملاء', 'icon': Icons.people, 'color': Colors.orange, 'isSelected': true},
    {'name': 'الموردين', 'icon': Icons.local_shipping, 'color': Colors.brown, 'isSelected': true},
    {'name': 'الموظفين', 'icon': Icons.badge, 'color': Colors.teal, 'isSelected': true},
    {'name': 'طلبات الخدمة', 'icon': Icons.build, 'color': Colors.indigo, 'isSelected': true},
    {'name': 'الفواتير', 'icon': Icons.receipt_long, 'color': Colors.red, 'isSelected': true},
    {'name': 'المعاملات المالية', 'icon': Icons.account_balance_wallet, 'color': Colors.amber, 'isSelected': true},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('توليد البيانات التعليمية'),
      ),
      drawer: const AppDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildInfoCard(),
            const SizedBox(height: AppDimensions.paddingM),
            _buildTableSelectionCard(),
            const SizedBox(height: AppDimensions.paddingM),
            _buildGenerateButton(),
            if (_successMessage != null || _errorMessage != null)
              const SizedBox(height: AppDimensions.paddingM),
            if (_successMessage != null)
              _buildMessageCard(_successMessage!, Colors.green),
            if (_errorMessage != null)
              _buildMessageCard(_errorMessage!, Colors.red),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary),
                const SizedBox(width: AppDimensions.paddingS),
                const Text(
                  'معلومات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'هذه الأداة تقوم بتوليد بيانات تعليمية في قاعدة البيانات لمساعدتك على اختبار التطبيق. '
              'سيتم إنشاء بيانات في الجداول المحددة أدناه.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'ملاحظة: لن يتم إنشاء بيانات جديدة إذا كانت هناك بيانات موجودة بالفعل في الجدول.',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableSelectionCard() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'الجداول',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingS),
              const Text(
                'حدد الجداول التي تريد توليد بيانات لها:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Expanded(
                child: ListView.builder(
                  itemCount: _tables.length,
                  itemBuilder: (context, index) {
                    final table = _tables[index];
                    return CheckboxListTile(
                      title: Text(table['name']),
                      secondary: Icon(table['icon'], color: table['color']),
                      value: table['isSelected'],
                      onChanged: (value) {
                        setState(() {
                          table['isSelected'] = value;
                        });
                      },
                    );
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        for (var table in _tables) {
                          table['isSelected'] = true;
                        }
                      });
                    },
                    icon: const Icon(Icons.select_all),
                    label: const Text('تحديد الكل'),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        for (var table in _tables) {
                          table['isSelected'] = false;
                        }
                      });
                    },
                    icon: const Icon(Icons.deselect),
                    label: const Text('إلغاء تحديد الكل'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenerateButton() {
    return CustomButton(
      text: 'توليد البيانات التعليمية',
      icon: Icons.data_array,
      isLoading: _isGeneratingData,
      onPressed: _isGeneratingData ? null : _generateDemoData,
    );
  }

  Widget _buildMessageCard(String message, Color color) {
    return Card(
      color: color.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          children: [
            Icon(
              color == Colors.green ? Icons.check_circle : Icons.error,
              color: color,
            ),
            const SizedBox(width: AppDimensions.paddingS),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateDemoData() async {
    // التحقق من وجود جداول محددة
    final hasSelectedTables = _tables.any((table) => table['isSelected'] == true);
    if (!hasSelectedTables) {
      setState(() {
        _errorMessage = 'يرجى تحديد جدول واحد على الأقل لتوليد البيانات.';
        _successMessage = null;
      });
      return;
    }

    // عرض مربع حوار للتأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد'),
        content: const Text(
          'هل أنت متأكد من رغبتك في توليد بيانات تعليمية؟ هذه العملية لا يمكن التراجع عنها.',
          textAlign: TextAlign.right,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('موافق'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirmed) return;

    setState(() {
      _isGeneratingData = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      final success = await _demoDataGenerator.generateDemoData();

      if (mounted) {
        setState(() {
          _isGeneratingData = false;
          if (success) {
            _successMessage = 'تم توليد البيانات التعليمية بنجاح.';
            _errorMessage = null;
          } else {
            _errorMessage = 'فشل في توليد البيانات التعليمية.';
            _successMessage = null;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isGeneratingData = false;
          _errorMessage = 'حدث خطأ أثناء توليد البيانات: $e';
          _successMessage = null;
        });
      }
    }
  }
}
