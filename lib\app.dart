import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'config/routes.dart';
import 'config/enhanced_theme.dart';
import 'core/localization/app_localizations.dart';
import 'state/auth_state.dart';
import 'state/user_state.dart';
import 'state/category_state.dart';
import 'features/auth/screens/login_screen.dart';
import 'features/dashboard/screens/dashboard_screen.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthState()),
        ChangeNotifierProvider(create: (_) => UserState()),
        ChangeNotifierProvider(create: (_) => CategoryState()),
        // Add other providers here
      ],
      child: Consumer<AuthState>(
        builder: (context, authState, _) {
          return MaterialApp(
            title: 'ركن الجليد',
            debugShowCheckedModeBanner: false,
            theme: EnhancedTheme.lightTheme(),
            darkTheme: EnhancedTheme.darkTheme(),
            themeMode: ThemeMode.light, // Default to light theme
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', ''), // Arabic
              Locale('en', ''), // English
            ],
            locale: const Locale('ar', ''), // Default to Arabic
            initialRoute: authState.isAuthenticated ? AppRoutes.dashboard : AppRoutes.login,
            onGenerateRoute: AppRoutes.generateRoute,
            home: authState.isAuthenticated
                ? const DashboardScreen()
                : const LoginScreen(),
          );
        },
      ),
    );
  }
}
