import 'package:flutter/material.dart';
import '../../../shared/models/employee.dart';

class EmployeeStatusBadge extends StatelessWidget {
  final EmployeeStatus status;

  const EmployeeStatusBadge({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor().withAlpha(26),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getStatusColor(),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(),
            size: 16,
            color: _getStatusColor(),
          ),
          const SizedBox(width: 4),
          Text(
            Employee.getStatusName(status),
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case EmployeeStatus.active:
        return Colors.green;
      case EmployeeStatus.inactive:
        return Colors.red;
      case EmployeeStatus.onLeave:
        return Colors.orange;
      case EmployeeStatus.terminated:
        return Colors.grey.shade800;
    }
  }

  IconData _getStatusIcon() {
    switch (status) {
      case EmployeeStatus.active:
        return Icons.check_circle;
      case EmployeeStatus.inactive:
        return Icons.cancel;
      case EmployeeStatus.onLeave:
        return Icons.beach_access;
      case EmployeeStatus.terminated:
        return Icons.block;
    }
  }
}
