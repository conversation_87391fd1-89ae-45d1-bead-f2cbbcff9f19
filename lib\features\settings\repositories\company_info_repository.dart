import 'package:flutter/foundation.dart';
import '../../../core/database/database_helper.dart';
import '../../../shared/models/company_info.dart';

class CompanyInfoRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Ensure the company_info table exists
  Future<void> ensureTableExists() async {
    try {
      final db = await _databaseHelper.database;

      // Check if the table exists
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='company_info'"
      );

      if (tables.isEmpty) {
        debugPrint('Creating company_info table as it does not exist');

        // Create the table if it doesn't exist
        await db.execute('''
          CREATE TABLE IF NOT EXISTS company_info(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar TEXT NOT NULL,
            name_en TEXT,
            address_ar TEXT,
            address_en TEXT,
            phone_ar TEXT,
            phone_en TEXT,
            email TEXT,
            website TEXT,
            tax_number TEXT,
            commercial_register TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT
          )
        ''');

        debugPrint('company_info table created successfully');
      } else {
        debugPrint('company_info table already exists');
      }
    } catch (e) {
      debugPrint('Error ensuring company_info table exists: $e');
    }
  }

  // Get company info
  Future<CompanyInfo?> getCompanyInfo() async {
    try {
      // Ensure table exists before querying
      await ensureTableExists();

      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('company_info');

      if (maps.isNotEmpty) {
        return CompanyInfo.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting company info: $e');
      return null;
    }
  }

  // Save company info
  Future<int> saveCompanyInfo(CompanyInfo companyInfo) async {
    try {
      // Ensure table exists before saving
      await ensureTableExists();

      final db = await _databaseHelper.database;

      // Check if company info already exists
      final List<Map<String, dynamic>> existingInfo = await db.query('company_info');

      if (existingInfo.isNotEmpty) {
        // Update existing record
        return await db.update(
          'company_info',
          companyInfo.toMap(),
          where: 'id = ?',
          whereArgs: [existingInfo.first['id']],
        );
      } else {
        // Insert new record
        return await db.insert('company_info', companyInfo.toMap());
      }
    } catch (e) {
      debugPrint('Error saving company info: $e');
      return -1;
    }
  }

  // Update company info
  Future<int> updateCompanyInfo(CompanyInfo companyInfo) async {
    try {
      // Ensure table exists before updating
      await ensureTableExists();

      final db = await _databaseHelper.database;
      return await db.update(
        'company_info',
        companyInfo.toMap(),
        where: 'id = ?',
        whereArgs: [companyInfo.id],
      );
    } catch (e) {
      debugPrint('Error updating company info: $e');
      return -1;
    }
  }
}
