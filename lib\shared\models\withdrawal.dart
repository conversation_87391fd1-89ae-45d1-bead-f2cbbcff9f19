import 'dart:convert';
import 'package:flutter/material.dart';
import 'salary.dart';

enum WithdrawalStatus {
  pending,
  approved,
  rejected,
}

class Withdrawal {
  final int? id;
  final int employeeId;
  final String employeeName;
  final double amount;
  final DateTime date;
  final String? reason;
  final WithdrawalStatus status;
  final String? notes;
  final int? approvedBy;
  final DateTime? approvedAt;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isPaid;
  final int? salaryId;
  final PaymentMethod? paymentMethod;
  final int? paymentEmployeeId;
  final String? paymentEmployeeName; // This is a display-only field, not stored in the database
  final int? paymentBankAccountId;
  final String? paymentBankAccountName; // This is a display-only field, not stored in the database

  Withdrawal({
    this.id,
    required this.employeeId,
    required this.employeeName,
    required this.amount,
    required this.date,
    this.reason,
    required this.status,
    this.notes,
    this.approvedBy,
    this.approvedAt,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isPaid = false,
    this.salaryId,
    this.paymentMethod,
    this.paymentEmployeeId,
    this.paymentEmployeeName,
    this.paymentBankAccountId,
    this.paymentBankAccountName,
  });

  factory Withdrawal.fromMap(Map<String, dynamic> map) {
    return Withdrawal(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      employeeName: map['employee_name'] != null ? map['employee_name'] as String : 'غير معروف',
      amount: map['amount'] is int ? (map['amount'] as int).toDouble() : map['amount'] as double,
      date: DateTime.parse(map['date'] as String),
      reason: map['reason'] as String?,
      status: _parseStatus(map['status'] as String),
      notes: map['notes'] as String?,
      approvedBy: map['approved_by'] as int?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      isPaid: map['is_paid'] != null ? (map['is_paid'] as int) == 1 : false,
      salaryId: map['salary_id'] as int?,
      paymentMethod: map['payment_method'] != null
          ? _parsePaymentMethod(map['payment_method'] as String)
          : null,
      paymentEmployeeId: map['payment_employee_id'] as int?,
      paymentEmployeeName: map['payment_employee_name'] as String?,
      paymentBankAccountId: map['payment_bank_account_id'] as int?,
      paymentBankAccountName: map['payment_bank_account_name'] as String?,
    );
  }

  factory Withdrawal.fromJson(String source) =>
      Withdrawal.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'amount': amount,
      'date': date.toIso8601String(),
      'reason': reason,
      'status': status.toString().split('.').last,
      'notes': notes,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_paid': isPaid ? 1 : 0,
      'salary_id': salaryId,
      'payment_method': paymentMethod?.toString().split('.').last,
      'payment_employee_id': paymentEmployeeId,
      'payment_bank_account_id': paymentBankAccountId,
    };
  }

  static PaymentMethod _parsePaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'banktransfer':
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      default:
        return PaymentMethod.cash;
    }
  }

  String toJson() => json.encode(toMap());

  Withdrawal copyWith({
    int? id,
    int? employeeId,
    String? employeeName,
    double? amount,
    DateTime? date,
    String? reason,
    WithdrawalStatus? status,
    String? notes,
    int? approvedBy,
    DateTime? approvedAt,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isPaid,
    int? salaryId,
    PaymentMethod? paymentMethod,
    int? paymentEmployeeId,
    String? paymentEmployeeName,
    int? paymentBankAccountId,
    String? paymentBankAccountName,
  }) {
    return Withdrawal(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isPaid: isPaid ?? this.isPaid,
      salaryId: salaryId ?? this.salaryId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentEmployeeId: paymentEmployeeId ?? this.paymentEmployeeId,
      paymentEmployeeName: paymentEmployeeName ?? this.paymentEmployeeName,
      paymentBankAccountId: paymentBankAccountId ?? this.paymentBankAccountId,
      paymentBankAccountName: paymentBankAccountName ?? this.paymentBankAccountName,
    );
  }

  static WithdrawalStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return WithdrawalStatus.pending;
      case 'approved':
        return WithdrawalStatus.approved;
      case 'rejected':
        return WithdrawalStatus.rejected;
      default:
        return WithdrawalStatus.pending;
    }
  }

  static String getStatusName(WithdrawalStatus status) {
    switch (status) {
      case WithdrawalStatus.pending:
        return 'معلق';
      case WithdrawalStatus.approved:
        return 'موافق عليه';
      case WithdrawalStatus.rejected:
        return 'مرفوض';
    }
  }

  static Color getStatusColor(WithdrawalStatus status) {
    switch (status) {
      case WithdrawalStatus.pending:
        return Colors.amber;
      case WithdrawalStatus.approved:
        return Colors.green;
      case WithdrawalStatus.rejected:
        return Colors.red;
    }
  }
}
