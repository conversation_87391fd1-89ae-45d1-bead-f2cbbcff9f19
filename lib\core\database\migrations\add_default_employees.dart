import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';

/// Migration to add default employees (technicians)
class AddDefaultEmployees {
  static Future<void> migrate(Database db) async {
    try {
      // Check if employees table exists
      final List<Map<String, dynamic>> tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='employees'"
      );

      if (tables.isNotEmpty) {
        // Check if we already have employees
        final List<Map<String, dynamic>> existingEmployees = await db.query('employees');
        
        if (existingEmployees.isEmpty) {
          if (kDebugMode) {
            print('إضافة موظفين افتراضيين...');
          }

          // Insert default employees (technicians)
          await db.insert('employees', {
            'name': 'أحمد محمد - فني تكييف',
            'email': '<EMAIL>',
            'phone': '0501234567',
            'address': 'الرياض',
            'position': 'فني تكييف',
            'join_date': DateTime.now().subtract(const Duration(days: 365)).toIso8601String(),
            'salary': 3000.0,
            'payment_type': 'monthly',
            'status': 'active',
            'national_id': '1234567890',
            'created_at': DateTime.now().toIso8601String(),
            'notes': 'فني خبير في أنظمة التكييف المركزي',
          });

          await db.insert('employees', {
            'name': 'محمد علي - فني صيانة',
            'email': '<EMAIL>',
            'phone': '0507654321',
            'address': 'جدة',
            'position': 'فني صيانة',
            'join_date': DateTime.now().subtract(const Duration(days: 180)).toIso8601String(),
            'salary': 150.0,
            'payment_type': 'daily',
            'status': 'active',
            'national_id': '0987654321',
            'created_at': DateTime.now().toIso8601String(),
            'notes': 'فني صيانة متخصص في الإصلاحات السريعة',
          });

          await db.insert('employees', {
            'name': 'خالد أحمد - فني تركيب',
            'email': '<EMAIL>',
            'phone': '0551122334',
            'address': 'الدمام',
            'position': 'فني تركيب',
            'join_date': DateTime.now().subtract(const Duration(days: 90)).toIso8601String(),
            'salary': 120.0,
            'payment_type': 'daily',
            'status': 'active',
            'national_id': '1122334455',
            'created_at': DateTime.now().toIso8601String(),
            'notes': 'متخصص في تركيب وحدات التكييف الجديدة',
          });

          await db.insert('employees', {
            'name': 'سعد الأحمد - فني كهرباء',
            'email': '<EMAIL>',
            'phone': '0556677889',
            'address': 'مكة المكرمة',
            'position': 'فني كهرباء',
            'join_date': DateTime.now().subtract(const Duration(days: 200)).toIso8601String(),
            'salary': 2800.0,
            'payment_type': 'monthly',
            'status': 'active',
            'national_id': '5566778899',
            'created_at': DateTime.now().toIso8601String(),
            'notes': 'متخصص في الأعمال الكهربائية لأنظمة التكييف',
          });

          await db.insert('employees', {
            'name': 'عبدالله سالم - فني تبريد',
            'email': '<EMAIL>',
            'phone': '0544332211',
            'address': 'المدينة المنورة',
            'position': 'فني تبريد',
            'join_date': DateTime.now().subtract(const Duration(days: 120)).toIso8601String(),
            'salary': 100.0,
            'payment_type': 'daily',
            'status': 'active',
            'national_id': '4433221100',
            'created_at': DateTime.now().toIso8601String(),
            'notes': 'خبير في أنظمة التبريد والتجميد',
          });

          if (kDebugMode) {
            print('✅ تم إضافة 5 موظفين افتراضيين بنجاح');
          }
        } else {
          if (kDebugMode) {
            print('تم تخطي الهجرة: يوجد ${existingEmployees.length} موظف بالفعل');
          }
        }
      } else {
        if (kDebugMode) {
          print('⚠️ جدول الموظفين غير موجود، سيتم إنشاؤه أولاً');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة الموظفين الافتراضيين: $e');
      }
      rethrow;
    }
  }
}
