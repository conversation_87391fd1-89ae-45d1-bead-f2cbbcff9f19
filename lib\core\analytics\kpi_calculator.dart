import 'package:flutter/foundation.dart';
import '../repositories/service_request_repository.dart';
import '../repositories/customer_repository.dart';
import '../repositories/employee_repository.dart';
import '../repositories/cash_box_repository.dart';
import '../repositories/invoice_repository.dart';

/// حاسبة مؤشرات الأداء الرئيسية (KPIs)
class KPICalculator {
  static const String _tag = 'KPICalculator';

  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final CustomerRepository _customerRepository = CustomerRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();
  final InvoiceRepository _invoiceRepository = InvoiceRepository();

  /// حساب جميع مؤشرات الأداء للفترة المحددة
  Future<KPIReport> calculateKPIs({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      if (kDebugMode) {
        print('$_tag: حساب مؤشرات الأداء للفترة من ${startDate.toIso8601String().split('T')[0]} إلى ${endDate.toIso8601String().split('T')[0]}');
      }

      // حساب مؤشرات الخدمات
      final serviceKPIs = await _calculateServiceKPIs(startDate, endDate);
      
      // حساب مؤشرات العملاء
      final customerKPIs = await _calculateCustomerKPIs(startDate, endDate);
      
      // حساب مؤشرات الموظفين
      final employeeKPIs = await _calculateEmployeeKPIs(startDate, endDate);
      
      // حساب مؤشرات مالية
      final financialKPIs = await _calculateFinancialKPIs(startDate, endDate);
      
      // حساب مؤشرات الكفاءة
      final efficiencyKPIs = await _calculateEfficiencyKPIs(startDate, endDate);

      final report = KPIReport(
        period: KPIPeriod(startDate: startDate, endDate: endDate),
        serviceKPIs: serviceKPIs,
        customerKPIs: customerKPIs,
        employeeKPIs: employeeKPIs,
        financialKPIs: financialKPIs,
        efficiencyKPIs: efficiencyKPIs,
        generatedAt: DateTime.now(),
      );

      if (kDebugMode) {
        print('$_tag: ✅ تم حساب مؤشرات الأداء بنجاح');
      }

      return report;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في حساب مؤشرات الأداء: $e');
      }
      rethrow;
    }
  }

  /// حساب مؤشرات الخدمات
  Future<ServiceKPIs> _calculateServiceKPIs(DateTime startDate, DateTime endDate) async {
    try {
      // جلب جميع طلبات الخدمة في الفترة
      final allRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(startDate, endDate);
      
      // تصنيف الطلبات حسب الحالة
      final completedRequests = allRequests.where((r) => r.status.toString().split('.').last == 'completed').toList();
      final pendingRequests = allRequests.where((r) => r.status.toString().split('.').last == 'pending').toList();
      final inProgressRequests = allRequests.where((r) => r.status.toString().split('.').last == 'inProgress').toList();
      final cancelledRequests = allRequests.where((r) => r.status.toString().split('.').last == 'cancelled').toList();

      // حساب متوسط وقت الإنجاز
      double averageCompletionTime = 0;
      if (completedRequests.isNotEmpty) {
        final totalTime = completedRequests
            .where((r) => r.completedDate != null)
            .map((r) => r.completedDate!.difference(r.createdAt).inHours)
            .fold<int>(0, (sum, hours) => sum + hours);
        averageCompletionTime = totalTime / completedRequests.length;
      }

      // حساب معدل رضا العملاء (افتراضي بناءً على الطلبات المكتملة)
      final customerSatisfactionRate = completedRequests.isNotEmpty 
          ? (completedRequests.length / allRequests.length) * 100
          : 0.0;

      // حساب معدل الإنجاز في الوقت المحدد
      final onTimeCompletions = completedRequests
          .where((r) => r.completedDate != null && r.scheduledDate != null)
          .where((r) => r.completedDate!.isBefore(r.scheduledDate!.add(const Duration(days: 1))))
          .length;
      final onTimeCompletionRate = completedRequests.isNotEmpty 
          ? (onTimeCompletions / completedRequests.length) * 100
          : 0.0;

      return ServiceKPIs(
        totalRequests: allRequests.length,
        completedRequests: completedRequests.length,
        pendingRequests: pendingRequests.length,
        inProgressRequests: inProgressRequests.length,
        cancelledRequests: cancelledRequests.length,
        completionRate: allRequests.isNotEmpty ? (completedRequests.length / allRequests.length) * 100 : 0,
        averageCompletionTime: averageCompletionTime,
        customerSatisfactionRate: customerSatisfactionRate,
        onTimeCompletionRate: onTimeCompletionRate,
      );
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في حساب مؤشرات الخدمات: $e');
      }
      return ServiceKPIs.empty();
    }
  }

  /// حساب مؤشرات العملاء
  Future<CustomerKPIs> _calculateCustomerKPIs(DateTime startDate, DateTime endDate) async {
    try {
      // جلب جميع العملاء
      final allCustomers = await _customerRepository.getAllCustomers();
      final activeCustomers = allCustomers.where((c) => c.isActive == true).toList();
      
      // العملاء الجدد في الفترة
      final newCustomers = allCustomers
          .where((c) => c.createdAt.isAfter(startDate) && c.createdAt.isBefore(endDate))
          .toList();

      // حساب متوسط قيمة العميل (من الفواتير)
      double averageCustomerValue = 0;
      try {
        final invoices = await _invoiceRepository.getInvoicesByDateRange(startDate, endDate);
        if (invoices.isNotEmpty && activeCustomers.isNotEmpty) {
          final totalRevenue = invoices.fold<double>(0, (sum, invoice) => sum + invoice.total);
          averageCustomerValue = totalRevenue / activeCustomers.length;
        }
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب متوسط قيمة العميل: $e');
        }
      }

      // معدل الاحتفاظ بالعملاء (افتراضي)
      final customerRetentionRate = activeCustomers.isNotEmpty 
          ? ((activeCustomers.length - newCustomers.length) / activeCustomers.length) * 100
          : 0.0;

      return CustomerKPIs(
        totalCustomers: allCustomers.length,
        activeCustomers: activeCustomers.length,
        newCustomers: newCustomers.length,
        customerRetentionRate: customerRetentionRate,
        averageCustomerValue: averageCustomerValue,
      );
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في حساب مؤشرات العملاء: $e');
      }
      return CustomerKPIs.empty();
    }
  }

  /// حساب مؤشرات الموظفين
  Future<EmployeeKPIs> _calculateEmployeeKPIs(DateTime startDate, DateTime endDate) async {
    try {
      // جلب جميع الموظفين
      final allEmployees = await _employeeRepository.getAllEmployees();
      final activeEmployees = allEmployees.where((e) => e.status.toString().split('.').last == 'active').toList();
      
      // تصنيف الموظفين حسب نوع الدفع
      final monthlyEmployees = activeEmployees.where((e) => e.paymentType.toString().split('.').last == 'monthly').toList();
      final dailyEmployees = activeEmployees.where((e) => e.paymentType.toString().split('.').last == 'daily').toList();

      // حساب إنتاجية الموظفين
      double averageProductivity = 0;
      try {
        final serviceRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(startDate, endDate);
        final completedRequests = serviceRequests.where((r) => r.status.toString().split('.').last == 'completed').toList();
        
        if (completedRequests.isNotEmpty && activeEmployees.isNotEmpty) {
          averageProductivity = completedRequests.length / activeEmployees.length;
        }
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب إنتاجية الموظفين: $e');
        }
      }

      // حساب تكلفة الموظفين
      double totalEmployeeCost = 0;
      try {
        final technicianExpenses = await _serviceRequestRepository.getTechnicianExpenses(
          startDate: startDate,
          endDate: endDate,
        );
        totalEmployeeCost = technicianExpenses['total_expenses'] as double? ?? 0.0;
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب تكلفة الموظفين: $e');
        }
      }

      return EmployeeKPIs(
        totalEmployees: allEmployees.length,
        activeEmployees: activeEmployees.length,
        monthlyEmployees: monthlyEmployees.length,
        dailyEmployees: dailyEmployees.length,
        averageProductivity: averageProductivity,
        totalEmployeeCost: totalEmployeeCost,
      );
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في حساب مؤشرات الموظفين: $e');
      }
      return EmployeeKPIs.empty();
    }
  }

  /// حساب مؤشرات مالية
  Future<FinancialKPIs> _calculateFinancialKPIs(DateTime startDate, DateTime endDate) async {
    try {
      double totalRevenue = 0;
      double totalExpenses = 0;
      double totalProfit = 0;
      double cashFlow = 0;

      // حساب الإيرادات من الفواتير
      try {
        final invoices = await _invoiceRepository.getInvoicesByDateRange(startDate, endDate);
        totalRevenue = invoices.fold<double>(0, (sum, invoice) => sum + invoice.total);
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب الإيرادات: $e');
        }
      }

      // حساب المصروفات
      try {
        final technicianExpenses = await _serviceRequestRepository.getTechnicianExpenses(
          startDate: startDate,
          endDate: endDate,
        );
        totalExpenses = technicianExpenses['total_expenses'] as double? ?? 0.0;
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب المصروفات: $e');
        }
      }

      // حساب الربح
      totalProfit = totalRevenue - totalExpenses;

      // حساب التدفق النقدي
      try {
        final cashBoxes = await _cashBoxRepository.getActiveCashBoxes();
        cashFlow = cashBoxes.fold<double>(0, (sum, box) => sum + box.currentBalance);
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب التدفق النقدي: $e');
        }
      }

      // حساب هامش الربح
      final profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0.0;

      return FinancialKPIs(
        totalRevenue: totalRevenue,
        totalExpenses: totalExpenses,
        totalProfit: totalProfit,
        profitMargin: profitMargin,
        cashFlow: cashFlow,
      );
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في حساب المؤشرات المالية: $e');
      }
      return FinancialKPIs.empty();
    }
  }

  /// حساب مؤشرات الكفاءة
  Future<EfficiencyKPIs> _calculateEfficiencyKPIs(DateTime startDate, DateTime endDate) async {
    try {
      // حساب معدل استخدام الموارد
      double resourceUtilization = 0;
      double costPerService = 0;
      double revenuePerEmployee = 0;

      // جلب البيانات المطلوبة
      final serviceRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(startDate, endDate);
      final completedRequests = serviceRequests.where((r) => r.status.toString().split('.').last == 'completed').toList();
      final employees = await _employeeRepository.getAllEmployees();
      final activeEmployees = employees.where((e) => e.status.toString().split('.').last == 'active').toList();

      // حساب معدل استخدام الموارد
      if (serviceRequests.isNotEmpty && activeEmployees.isNotEmpty) {
        resourceUtilization = (completedRequests.length / serviceRequests.length) * 100;
      }

      // حساب التكلفة لكل خدمة
      try {
        final technicianExpenses = await _serviceRequestRepository.getTechnicianExpenses(
          startDate: startDate,
          endDate: endDate,
        );
        final totalExpenses = technicianExpenses['total_expenses'] as double? ?? 0.0;
        
        if (completedRequests.isNotEmpty) {
          costPerService = totalExpenses / completedRequests.length;
        }
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب التكلفة لكل خدمة: $e');
        }
      }

      // حساب الإيراد لكل موظف
      try {
        final invoices = await _invoiceRepository.getInvoicesByDateRange(startDate, endDate);
        final totalRevenue = invoices.fold<double>(0, (sum, invoice) => sum + invoice.total);
        
        if (activeEmployees.isNotEmpty) {
          revenuePerEmployee = totalRevenue / activeEmployees.length;
        }
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: خطأ في حساب الإيراد لكل موظف: $e');
        }
      }

      return EfficiencyKPIs(
        resourceUtilization: resourceUtilization,
        costPerService: costPerService,
        revenuePerEmployee: revenuePerEmployee,
        serviceCompletionRate: serviceRequests.isNotEmpty ? (completedRequests.length / serviceRequests.length) * 100 : 0,
      );
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في حساب مؤشرات الكفاءة: $e');
      }
      return EfficiencyKPIs.empty();
    }
  }
}

/// تقرير مؤشرات الأداء الرئيسية
class KPIReport {
  final KPIPeriod period;
  final ServiceKPIs serviceKPIs;
  final CustomerKPIs customerKPIs;
  final EmployeeKPIs employeeKPIs;
  final FinancialKPIs financialKPIs;
  final EfficiencyKPIs efficiencyKPIs;
  final DateTime generatedAt;

  KPIReport({
    required this.period,
    required this.serviceKPIs,
    required this.customerKPIs,
    required this.employeeKPIs,
    required this.financialKPIs,
    required this.efficiencyKPIs,
    required this.generatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'period': period.toJson(),
      'service_kpis': serviceKPIs.toJson(),
      'customer_kpis': customerKPIs.toJson(),
      'employee_kpis': employeeKPIs.toJson(),
      'financial_kpis': financialKPIs.toJson(),
      'efficiency_kpis': efficiencyKPIs.toJson(),
      'generated_at': generatedAt.toIso8601String(),
    };
  }
}

/// فترة مؤشرات الأداء
class KPIPeriod {
  final DateTime startDate;
  final DateTime endDate;

  KPIPeriod({required this.startDate, required this.endDate});

  int get daysCount => endDate.difference(startDate).inDays + 1;

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate.toIso8601String().split('T')[0],
      'days_count': daysCount,
    };
  }
}

/// مؤشرات الخدمات
class ServiceKPIs {
  final int totalRequests;
  final int completedRequests;
  final int pendingRequests;
  final int inProgressRequests;
  final int cancelledRequests;
  final double completionRate;
  final double averageCompletionTime;
  final double customerSatisfactionRate;
  final double onTimeCompletionRate;

  ServiceKPIs({
    required this.totalRequests,
    required this.completedRequests,
    required this.pendingRequests,
    required this.inProgressRequests,
    required this.cancelledRequests,
    required this.completionRate,
    required this.averageCompletionTime,
    required this.customerSatisfactionRate,
    required this.onTimeCompletionRate,
  });

  factory ServiceKPIs.empty() {
    return ServiceKPIs(
      totalRequests: 0,
      completedRequests: 0,
      pendingRequests: 0,
      inProgressRequests: 0,
      cancelledRequests: 0,
      completionRate: 0,
      averageCompletionTime: 0,
      customerSatisfactionRate: 0,
      onTimeCompletionRate: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_requests': totalRequests,
      'completed_requests': completedRequests,
      'pending_requests': pendingRequests,
      'in_progress_requests': inProgressRequests,
      'cancelled_requests': cancelledRequests,
      'completion_rate': completionRate,
      'average_completion_time': averageCompletionTime,
      'customer_satisfaction_rate': customerSatisfactionRate,
      'on_time_completion_rate': onTimeCompletionRate,
    };
  }
}

/// مؤشرات العملاء
class CustomerKPIs {
  final int totalCustomers;
  final int activeCustomers;
  final int newCustomers;
  final double customerRetentionRate;
  final double averageCustomerValue;

  CustomerKPIs({
    required this.totalCustomers,
    required this.activeCustomers,
    required this.newCustomers,
    required this.customerRetentionRate,
    required this.averageCustomerValue,
  });

  factory CustomerKPIs.empty() {
    return CustomerKPIs(
      totalCustomers: 0,
      activeCustomers: 0,
      newCustomers: 0,
      customerRetentionRate: 0,
      averageCustomerValue: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_customers': totalCustomers,
      'active_customers': activeCustomers,
      'new_customers': newCustomers,
      'customer_retention_rate': customerRetentionRate,
      'average_customer_value': averageCustomerValue,
    };
  }
}

/// مؤشرات الموظفين
class EmployeeKPIs {
  final int totalEmployees;
  final int activeEmployees;
  final int monthlyEmployees;
  final int dailyEmployees;
  final double averageProductivity;
  final double totalEmployeeCost;

  EmployeeKPIs({
    required this.totalEmployees,
    required this.activeEmployees,
    required this.monthlyEmployees,
    required this.dailyEmployees,
    required this.averageProductivity,
    required this.totalEmployeeCost,
  });

  factory EmployeeKPIs.empty() {
    return EmployeeKPIs(
      totalEmployees: 0,
      activeEmployees: 0,
      monthlyEmployees: 0,
      dailyEmployees: 0,
      averageProductivity: 0,
      totalEmployeeCost: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_employees': totalEmployees,
      'active_employees': activeEmployees,
      'monthly_employees': monthlyEmployees,
      'daily_employees': dailyEmployees,
      'average_productivity': averageProductivity,
      'total_employee_cost': totalEmployeeCost,
    };
  }
}

/// مؤشرات مالية
class FinancialKPIs {
  final double totalRevenue;
  final double totalExpenses;
  final double totalProfit;
  final double profitMargin;
  final double cashFlow;

  FinancialKPIs({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.totalProfit,
    required this.profitMargin,
    required this.cashFlow,
  });

  factory FinancialKPIs.empty() {
    return FinancialKPIs(
      totalRevenue: 0,
      totalExpenses: 0,
      totalProfit: 0,
      profitMargin: 0,
      cashFlow: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_revenue': totalRevenue,
      'total_expenses': totalExpenses,
      'total_profit': totalProfit,
      'profit_margin': profitMargin,
      'cash_flow': cashFlow,
    };
  }
}

/// مؤشرات الكفاءة
class EfficiencyKPIs {
  final double resourceUtilization;
  final double costPerService;
  final double revenuePerEmployee;
  final double serviceCompletionRate;

  EfficiencyKPIs({
    required this.resourceUtilization,
    required this.costPerService,
    required this.revenuePerEmployee,
    required this.serviceCompletionRate,
  });

  factory EfficiencyKPIs.empty() {
    return EfficiencyKPIs(
      resourceUtilization: 0,
      costPerService: 0,
      revenuePerEmployee: 0,
      serviceCompletionRate: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'resource_utilization': resourceUtilization,
      'cost_per_service': costPerService,
      'revenue_per_employee': revenuePerEmployee,
      'service_completion_rate': serviceCompletionRate,
    };
  }
}
