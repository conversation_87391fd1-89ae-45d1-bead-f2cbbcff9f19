import 'dart:async';
import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../services/firebase_service.dart';
import '../services/connectivity_service.dart';
import '../../shared/models/sync_model.dart';
import '../../shared/models/sync_conflict.dart';

/// Abstract base repository for all synchronizable entities
abstract class BaseRepository<T extends SyncModel> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final FirebaseService _firebaseService = FirebaseService();
  final ConnectivityService _connectivityService = ConnectivityService();

  /// Table name in SQLite
  String get tableName;

  /// Collection name in Firestore
  String get collectionName;

  /// Convert Map to model instance
  T fromMap(Map<String, dynamic> map);

  /// Convert model to Map for SQLite
  Map<String, dynamic> toSqliteMap(T model);

  /// Convert model to Map for Firestore
  Map<String, dynamic> toFirestoreMap(T model);

  /// Create a copy of the model with sync data
  T copyWithSyncData(T model, {
    String? firestoreId,
    int? localId,
    DateTime? lastSyncTime,
    SyncStatus? syncStatus,
    bool? isDeleted,
    int? version,
    String? dataHash,
  });

  // CRUD Operations

  /// Create a new record
  Future<T> create(T model) async {
    try {
      if (kDebugMode) {
        print('📝 Creating ${T.toString()}...');
      }

      // Always save to local database first
      final localMap = toSqliteMap(model);
      localMap.remove('id'); // Remove ID to let SQLite auto-generate
      
      final db = await _dbHelper.database;
      final localId = await db.insert(tableName, localMap);

      // Create updated model with local ID
      final localModel = copyWithSyncData(
        model,
        localId: localId,
        syncStatus: SyncStatus.pendingUpload,
        dataHash: model.generateDataHash(),
      );

      // Try to sync to Firestore if connected
      if (await _connectivityService.shouldSync()) {
        try {
          final firestoreData = toFirestoreMap(localModel);
          final firestoreId = await _firebaseService.createDocument(collectionName, firestoreData);
          
          // Update local record with Firestore ID
          await _dbHelper.updateFirestoreId(tableName, localId, firestoreId);
          await _dbHelper.updateSyncStatus(tableName, localId, 'synced', lastSyncTime: DateTime.now());
          
          return copyWithSyncData(
            localModel,
            firestoreId: firestoreId,
            syncStatus: SyncStatus.synced,
            lastSyncTime: DateTime.now(),
          );
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Failed to sync to Firestore, will retry later: $e');
          }
          // Queue for later sync
          await _dbHelper.addToSyncQueue(T.toString(), localId.toString(), 'create', toFirestoreMap(localModel));
        }
      } else {
        // Queue for later sync
        await _dbHelper.addToSyncQueue(T.toString(), localId.toString(), 'create', toFirestoreMap(localModel));
      }

      return localModel;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating ${T.toString()}: $e');
      }
      rethrow;
    }
  }

  /// Update an existing record
  Future<T> update(T model) async {
    try {
      if (kDebugMode) {
        print('📝 Updating ${T.toString()}...');
      }

      // Update local database
      final updatedModel = copyWithSyncData(
        model,
        syncStatus: SyncStatus.pendingUpload,
        dataHash: model.generateDataHash(),
      );

      final localMap = toSqliteMap(updatedModel);
      localMap['updated_at'] = DateTime.now().toIso8601String();
      
      final db = await _dbHelper.database;
      await db.update(
        tableName,
        localMap,
        where: 'id = ?',
        whereArgs: [model.localId],
      );

      // Try to sync to Firestore if connected
      if (await _connectivityService.shouldSync() && model.firestoreId != null) {
        try {
          final firestoreData = toFirestoreMap(updatedModel);
          await _firebaseService.updateDocument(collectionName, model.firestoreId!, firestoreData);
          
          await _dbHelper.updateSyncStatus(tableName, model.localId!, 'synced', lastSyncTime: DateTime.now());
          
          return copyWithSyncData(
            updatedModel,
            syncStatus: SyncStatus.synced,
            lastSyncTime: DateTime.now(),
          );
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Failed to sync update to Firestore, will retry later: $e');
          }
          // Queue for later sync
          await _dbHelper.addToSyncQueue(T.toString(), model.localId.toString(), 'update', toFirestoreMap(updatedModel));
        }
      } else {
        // Queue for later sync
        await _dbHelper.addToSyncQueue(T.toString(), model.localId.toString(), 'update', toFirestoreMap(updatedModel));
      }

      return updatedModel;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating ${T.toString()}: $e');
      }
      rethrow;
    }
  }

  /// Delete a record (soft delete)
  Future<void> delete(String id) async {
    try {
      if (kDebugMode) {
        print('🗑️ Deleting ${T.toString()} with ID: $id');
      }

      // Find the record first
      final record = await getById(id);
      if (record == null) {
        throw Exception('Record not found');
      }

      // Soft delete in local database
      final db = await _dbHelper.database;
      await db.update(
        tableName,
        {
          'is_deleted': 1,
          'sync_status': 'pendingUpload',
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [record.localId],
      );

      // Try to sync deletion to Firestore if connected
      if (await _connectivityService.shouldSync() && record.firestoreId != null) {
        try {
          await _firebaseService.deleteDocument(collectionName, record.firestoreId!);
          await _dbHelper.updateSyncStatus(tableName, record.localId!, 'synced', lastSyncTime: DateTime.now());
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Failed to sync deletion to Firestore, will retry later: $e');
          }
          // Queue for later sync
          await _dbHelper.addToSyncQueue(T.toString(), record.localId.toString(), 'delete', {});
        }
      } else {
        // Queue for later sync
        await _dbHelper.addToSyncQueue(T.toString(), record.localId.toString(), 'delete', {});
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting ${T.toString()}: $e');
      }
      rethrow;
    }
  }

  /// Get a record by ID (checks both local and Firestore ID)
  Future<T?> getById(String id) async {
    try {
      final db = await _dbHelper.database;
      
      // Try to find by local ID first
      List<Map<String, dynamic>> results = await db.query(
        tableName,
        where: 'id = ? AND is_deleted = ?',
        whereArgs: [int.tryParse(id) ?? -1, 0],
      );

      // If not found, try by Firestore ID
      if (results.isEmpty) {
        results = await db.query(
          tableName,
          where: 'firestore_id = ? AND is_deleted = ?',
          whereArgs: [id, 0],
        );
      }

      if (results.isNotEmpty) {
        return fromMap(results.first);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting ${T.toString()} by ID: $e');
      }
      rethrow;
    }
  }

  /// Get all records
  Future<List<T>> getAll({int? limit, String? orderBy}) async {
    try {
      final db = await _dbHelper.database;
      
      String query = 'SELECT * FROM $tableName WHERE is_deleted = ?';
      List<dynamic> args = [0];
      
      if (orderBy != null) {
        query += ' ORDER BY $orderBy';
      }
      
      if (limit != null) {
        query += ' LIMIT ?';
        args.add(limit);
      }

      final results = await db.rawQuery(query, args);
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all ${T.toString()}: $e');
      }
      rethrow;
    }
  }

  /// Get records that need synchronization
  Future<List<T>> getRecordsNeedingSync() async {
    try {
      final records = await _dbHelper.getRecordsNeedingSync(tableName);
      return records.map((map) => fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting records needing sync: $e');
      }
      rethrow;
    }
  }

  /// Sync with Firestore
  Future<void> sync() async {
    try {
      if (!await _connectivityService.shouldSync()) {
        if (kDebugMode) {
          print('⚠️ No connectivity, skipping sync');
        }
        return;
      }

      if (kDebugMode) {
        print('🔄 Starting sync for ${T.toString()}...');
      }

      // Get records that need to be uploaded
      final recordsToUpload = await getRecordsNeedingSync();
      
      // Upload pending records
      for (final record in recordsToUpload) {
        await _syncRecord(record);
      }

      // Download new/updated records from Firestore
      await _downloadFromFirestore();

      if (kDebugMode) {
        print('✅ Sync completed for ${T.toString()}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during sync: $e');
      }
      rethrow;
    }
  }

  // Private helper methods

  /// Sync a single record
  Future<void> _syncRecord(T record) async {
    try {
      switch (record.syncStatus) {
        case SyncStatus.pendingUpload:
          if (record.firestoreId == null) {
            // Create new document in Firestore
            final firestoreData = toFirestoreMap(record);
            final firestoreId = await _firebaseService.createDocument(collectionName, firestoreData);
            await _dbHelper.updateFirestoreId(tableName, record.localId!, firestoreId);
          } else {
            // Update existing document in Firestore
            final firestoreData = toFirestoreMap(record);
            await _firebaseService.updateDocument(collectionName, record.firestoreId!, firestoreData);
          }
          await _dbHelper.updateSyncStatus(tableName, record.localId!, 'synced', lastSyncTime: DateTime.now());
          break;
        
        case SyncStatus.failed:
          // Retry failed operations
          await _syncRecord(copyWithSyncData(record, syncStatus: SyncStatus.pendingUpload));
          break;
        
        default:
          // No action needed for other statuses
          break;
      }
    } catch (e) {
      // Mark as failed
      await _dbHelper.updateSyncStatus(tableName, record.localId!, 'failed');
      rethrow;
    }
  }

  /// Download updates from Firestore
  Future<void> _downloadFromFirestore() async {
    try {
      // Get last sync time
      final db = await _dbHelper.database;
      final lastSyncResult = await db.rawQuery(
        'SELECT MAX(last_sync_time) as last_sync FROM $tableName WHERE last_sync_time IS NOT NULL'
      );
      
      DateTime? lastSyncTime;
      if (lastSyncResult.isNotEmpty && lastSyncResult.first['last_sync'] != null) {
        lastSyncTime = DateTime.parse(lastSyncResult.first['last_sync'] as String);
      }

      // Get modified documents from Firestore
      final modifiedDocs = lastSyncTime != null
          ? await _firebaseService.getModifiedDocuments(collectionName, lastSyncTime)
          : await _firebaseService.getCollection(collectionName);

      // Process each modified document
      for (final doc in modifiedDocs) {
        await _processFirestoreDocument(doc);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error downloading from Firestore: $e');
      }
      rethrow;
    }
  }

  /// Process a document from Firestore
  Future<void> _processFirestoreDocument(Map<String, dynamic> doc) async {
    try {
      final firestoreId = doc['firestore_id'] as String;
      
      // Check if we have this record locally
      final existingRecord = await _dbHelper.getRecordByFirestoreId(tableName, firestoreId);
      
      if (existingRecord != null) {
        // Check for conflicts
        final localModel = fromMap(existingRecord);
        final remoteModel = fromMap(doc);
        
        if (localModel.isModifiedSinceSync() && remoteModel.isModifiedSinceSync()) {
          // Conflict detected
          await _handleConflict(localModel, remoteModel);
        } else {
          // No conflict, update local record
          await _updateLocalFromFirestore(doc);
        }
      } else {
        // New record from Firestore
        await _insertFromFirestore(doc);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error processing Firestore document: $e');
      }
      rethrow;
    }
  }

  /// Handle sync conflict
  Future<void> _handleConflict(T localModel, T remoteModel) async {
    try {
      final conflict = SyncConflict.fromModels(
        entityType: T.toString(),
        entityId: localModel.firestoreId ?? localModel.localId.toString(),
        localModel: localModel,
        remoteModel: remoteModel,
      );

      await _dbHelper.addSyncConflict(conflict.toMap());

      if (kDebugMode) {
        print('⚠️ Conflict detected for ${T.toString()}: ${conflict.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling conflict: $e');
      }
      rethrow;
    }
  }

  /// Update local record from Firestore data
  Future<void> _updateLocalFromFirestore(Map<String, dynamic> doc) async {
    try {
      final db = await _dbHelper.database;
      doc['sync_status'] = 'synced';
      doc['last_sync_time'] = DateTime.now().toIso8601String();
      
      await db.update(
        tableName,
        doc,
        where: 'firestore_id = ?',
        whereArgs: [doc['firestore_id']],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating local from Firestore: $e');
      }
      rethrow;
    }
  }

  /// Insert new record from Firestore
  Future<void> _insertFromFirestore(Map<String, dynamic> doc) async {
    try {
      final db = await _dbHelper.database;
      doc['sync_status'] = 'synced';
      doc['last_sync_time'] = DateTime.now().toIso8601String();
      
      await db.insert(tableName, doc);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error inserting from Firestore: $e');
      }
      rethrow;
    }
  }
}
