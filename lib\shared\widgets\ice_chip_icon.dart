import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A custom widget that renders an ice chip icon
class Ice<PERSON>hipIcon extends StatelessWidget {
  final double size;
  final Color color;
  final bool isOutlined;

  const IceChipIcon({
    super.key,
    this.size = 48.0,
    this.color = Colors.lightBlue,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(size, size),
      painter: <PERSON><PERSON>hipPain<PERSON>(
        color: color,
        isOutlined: isOutlined,
      ),
    );
  }
}

/// Custom painter for drawing the ice chip
class IceChipPainter extends CustomPainter {
  final Color color;
  final bool isOutlined;

  IceChipPainter({
    required this.color,
    required this.isOutlined,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Create a hexagonal ice chip shape
    final path = Path();

    // Calculate points for a hexagon
    for (int i = 0; i < 6; i++) {
      final angle = (i * 60) * (3.14159 / 180); // Convert degrees to radians
      final x = center.dx + radius * 0.9 * cos(angle);
      final y = center.dy + radius * 0.9 * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    // Draw the main shape
    final paint = Paint()
      ..color = isOutlined ? Colors.transparent : color
      ..style = isOutlined ? PaintingStyle.stroke : PaintingStyle.fill
      ..strokeWidth = isOutlined ? 2.0 : 0;

    canvas.drawPath(path, paint);

    // Add some details to make it look like ice
    if (!isOutlined) {
      // Add shine/reflection
      final shinePaint = Paint()
        ..color = Colors.white.withAlpha(128) // Using withAlpha instead of withOpacity
        ..style = PaintingStyle.fill;

      final shinePath = Path();
      shinePath.moveTo(center.dx - radius * 0.3, center.dy - radius * 0.3);
      shinePath.lineTo(center.dx + radius * 0.1, center.dy - radius * 0.5);
      shinePath.lineTo(center.dx + radius * 0.3, center.dy - radius * 0.1);
      shinePath.lineTo(center.dx, center.dy + radius * 0.2);
      shinePath.close();

      canvas.drawPath(shinePath, shinePaint);

      // Add some lines to represent crystal structure
      final linePaint = Paint()
        ..color = Colors.white.withAlpha(77) // Using withAlpha instead of withOpacity
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      // Horizontal line
      canvas.drawLine(
        Offset(center.dx - radius * 0.6, center.dy),
        Offset(center.dx + radius * 0.6, center.dy),
        linePaint,
      );

      // Vertical line
      canvas.drawLine(
        Offset(center.dx, center.dy - radius * 0.6),
        Offset(center.dx, center.dy + radius * 0.6),
        linePaint,
      );

      // Diagonal lines
      canvas.drawLine(
        Offset(center.dx - radius * 0.4, center.dy - radius * 0.4),
        Offset(center.dx + radius * 0.4, center.dy + radius * 0.4),
        linePaint,
      );

      canvas.drawLine(
        Offset(center.dx - radius * 0.4, center.dy + radius * 0.4),
        Offset(center.dx + radius * 0.4, center.dy - radius * 0.4),
        linePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

// Helper functions to convert degrees to radians
double cos(double angle) {
  return math.cos(angle);
}

double sin(double angle) {
  return math.sin(angle);
}
