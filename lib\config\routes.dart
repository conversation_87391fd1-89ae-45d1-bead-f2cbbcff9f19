import 'package:flutter/material.dart';
import '../shared/animations/page_transitions.dart';
import '../features/auth/screens/login_screen.dart';
import '../features/auth/screens/register_screen.dart';
import '../features/auth/screens/forgot_password_screen.dart';
import '../features/dashboard/screens/dashboard_screen.dart';
import '../features/invoices/screens/invoice_list_screen.dart';
import '../features/invoices/screens/add_invoice_screen.dart';
import '../features/invoices/screens/invoice_details_screen.dart';
import '../features/transactions/screens/transactions_screen.dart';
import '../features/transactions/screens/account_transfer_screen.dart';
import '../features/customers/screens/customers_list_screen.dart';
import '../features/customers/screens/customer_details_screen.dart';
import '../shared/models/customer.dart';
import '../features/service_requests/screens/service_list_screen.dart';
import '../features/service_requests/screens/add_service_request_screen.dart';
import '../features/service_requests/screens/edit_service_request_screen.dart';
import '../features/service_requests/screens/service_schedule_screen.dart';
import '../screens/service/service_request_details_screen.dart';
import '../screens/service/service_request_form_screen.dart';
import '../screens/service/service_requests_screen.dart';
import '../screens/service/service_categories_screen.dart';
import '../shared/models/service_request.dart';
import '../features/inventory/screens/inventory_list_screen.dart';
import '../features/inventory/screens/inventory_form_screen.dart';
import '../features/inventory/screens/inventory_transactions_screen.dart';
import '../shared/models/inventory_item.dart';
import '../features/payroll/screens/payroll_list_screen.dart';
import '../features/payroll/screens/add_salary_screen.dart';
import '../features/employees/screens/employees_list_screen.dart';
import '../features/employees/screens/employee_details_screen.dart';
import '../features/employees/screens/edit_employee_screen.dart';
import '../shared/models/employee.dart';
import '../features/suppliers/screens/suppliers_list_screen.dart';
import '../features/suppliers/screens/supplier_details_screen.dart';
import '../features/suppliers/screens/edit_supplier_screen.dart';
import '../shared/models/supplier.dart';
import '../shared/models/user.dart';
import '../features/reports/screens/monthly_report_screen.dart';
import '../features/reports/screens/detailed_report_screen.dart';
import '../features/reports/screens/detailed_report_screen_new.dart' as new_report;
import '../features/reports/screens/maintenance_reports_screen.dart';
import '../features/reports/screens/customer_report_screen.dart';
import '../features/reports/screens/transaction_report_screen.dart';
import '../features/reports/screens/service_request_report_screen.dart';
import '../features/reports/screens/invoice_report_screen.dart';
import '../features/reports/screens/unified_invoice_report_screen.dart';
import '../features/reports/screens/cash_flow_report_screen.dart';
import '../features/reports/screens/profit_loss_report_screen.dart';
import '../features/reports/screens/service_profitability_screen.dart';
import '../features/dashboard/screens/financial_dashboard_screen.dart';
import '../features/taxes/screens/tax_management_screen.dart';
import '../features/settings/screens/app_settings_screen.dart';
import '../features/settings/screens/company_info_screen.dart';
import '../features/settings/screens/database_settings_screen.dart';
import '../features/settings/screens/demo_data_screen.dart';
import '../features/notifications/screens/notifications_screen.dart';
import '../features/statistics/screens/statistics_screen.dart';
import '../features/bank_accounts/screens/bank_accounts_screen.dart';
import '../features/users/screens/users_list_screen.dart';
import '../features/users/screens/user_details_screen.dart';
import '../features/users/screens/user_edit_screen.dart';
import '../screens/users/user_permissions_screen.dart';
import '../features/categories/screens/categories_screen.dart';
import '../features/categories/screens/category_edit_screen.dart';
import '../features/cash_boxes/screens/cash_boxes_screen.dart';
import '../screens/settings/notification_settings_screen.dart';


class AppRoutes {
  // Route names
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String dashboard = '/dashboard';
  static const String invoices = '/invoices';
  static const String addInvoice = '/add-invoice';
  static const String invoiceDetails = '/invoice-details';
  static const String transactions = '/transactions';
  static const String accountTransfer = '/account-transfer';
  static const String customers = '/customers';
  static const String customerDetails = '/customer-details';
  static const String serviceRequests = '/service-requests';
  static const String serviceRequestDetails = '/service-request-details';
  static const String serviceRequestAdd = '/service-request-add';
  static const String serviceRequestEdit = '/service-request-edit';
  static const String serviceSchedule = '/service-schedule';
  static const String serviceCategories = '/service-categories';
  static const String newServiceRequests = '/new-service-requests';
  static const String newServiceRequestDetails = '/new-service-request-details';
  static const String newServiceRequestForm = '/new-service-request-form';
  static const String inventory = '/inventory';
  static const String inventoryAdd = '/inventory/add';
  static const String inventoryEdit = '/inventory/edit';
  static const String inventoryTransactions = '/inventory/transactions';
  static const String payroll = '/payroll';
  static const String addSalary = '/add-salary';
  static const String employees = '/employees';
  static const String employeeDetails = '/employee-details';
  static const String employeeEdit = '/employee-edit';
  static const String suppliers = '/suppliers';
  static const String supplierDetails = '/supplier-details';
  static const String supplierEdit = '/supplier-edit';
  static const String reports = '/reports';
  static const String detailedReport = '/detailed-report';
  static const String detailedReportNew = '/detailed-report-new';
  static const String maintenanceReports = '/maintenance-reports';
  static const String customerReport = '/customer-report';
  static const String transactionReport = '/transaction-report';
  static const String serviceRequestReport = '/service-request-report';
  static const String invoiceReport = '/invoice-report';
  static const String cashFlowReport = '/cash-flow-report';
  static const String profitLossReport = '/profit-loss-report';
  static const String serviceProfitability = '/service-profitability';
  static const String financialDashboard = '/financial-dashboard';
  static const String taxManagement = '/tax-management';
  static const String statistics = '/statistics';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String bankAccounts = '/bank-accounts';
  static const String cashBoxes = '/cash-boxes';
  static const String users = '/users';
  static const String userDetails = '/user-details';
  static const String userEdit = '/user-edit';
  static const String userPermissions = '/user-permissions';
  static const String categories = '/categories';
  static const String categoryEdit = '/category-edit';
  static const String companyInfo = '/company-info';
  static const String databaseSettings = '/database-settings';
  static const String demoData = '/demo-data';
  static const String notificationSettings = '/notification-settings';

  // مولد المسارات
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name;

    // تحديد نوع الانتقال بناءً على المسار
    PageTransitionType getTransitionType(String route) {
      // استخدام تأثيرات مختلفة لأنواع مختلفة من الشاشات
      if (route.contains('add') || route.contains('edit') || route.contains('form')) {
        return PageTransitionType.fadeSlide; // تلاشي مع انزلاق للنماذج
      } else if (route.contains('details')) {
        return PageTransitionType.fadeScale; // تلاشي مع تكبير للتفاصيل
      } else if (route.contains('list') || route.contains('report')) {
        return PageTransitionType.fadeSlide; // تلاشي مع انزلاق للقوائم والتقارير
      } else if (route == dashboard) {
        return PageTransitionType.fadeScale; // تلاشي مع تكبير للوحة التحكم
      } else {
        return PageTransitionType.fadeSlide; // الافتراضي
      }
    }

    // إنشاء مسار مع تأثير انتقال
    Route<dynamic> createPageRoute(Widget page) {
      return AppPageTransitions.createRoute(
        page,
        type: getTransitionType(routeName ?? ''),
        duration: const Duration(milliseconds: 300),
      );
    }

    if (routeName == login) {
      return createPageRoute(const LoginScreen());
    } else if (routeName == register) {
      return createPageRoute(const RegisterScreen());
    } else if (routeName == forgotPassword) {
      return createPageRoute(const ForgotPasswordScreen());
    } else if (routeName == dashboard) {
      return createPageRoute(const DashboardScreen());
    } else if (routeName == invoices) {
      return createPageRoute(const InvoiceListScreen());
    } else if (routeName == addInvoice) {
      return createPageRoute(const AddInvoiceScreen());
    } else if (routeName == invoiceDetails) {
      final invoiceId = settings.arguments;
      if (invoiceId is int) {
        return createPageRoute(InvoiceDetailsScreen(invoiceId: invoiceId));
      } else if (invoiceId is String) {
        final parsedId = int.tryParse(invoiceId);
        if (parsedId != null) {
          return createPageRoute(InvoiceDetailsScreen(invoiceId: parsedId));
        }
      }
      // Fallback to invoice list if invalid argument
      return createPageRoute(const InvoiceListScreen());
    } else if (routeName == transactions) {
      return createPageRoute(const TransactionsScreen());
    } else if (routeName == accountTransfer) {
      return createPageRoute(const AccountTransferScreen());
    } else if (routeName == customers) {
      return createPageRoute(const CustomersListScreen());
    } else if (routeName == customerDetails) {
      final customer = settings.arguments as Customer;
      return createPageRoute(CustomerDetailsScreen(customer: customer));
    } else if (routeName == serviceRequests) {
      return createPageRoute(const ServiceListScreen());
    } else if (routeName == serviceSchedule) {
      return createPageRoute(const ServiceScheduleScreen());
    } else if (routeName == serviceCategories) {
      return createPageRoute(const ServiceCategoriesScreen());
    } else if (routeName == serviceRequestDetails) {
      final serviceRequest = settings.arguments as ServiceRequest;
      return createPageRoute(ServiceRequestDetailsScreen(serviceRequestId: serviceRequest.id!));
    } else if (routeName == serviceRequestAdd) {
      return createPageRoute(const AddServiceRequestScreen());
    } else if (routeName == serviceRequestEdit) {
      final serviceRequest = settings.arguments as ServiceRequest;
      return createPageRoute(EditServiceRequestScreen(serviceRequest: serviceRequest));
    } else if (routeName == inventory) {
      return createPageRoute(const InventoryListScreen());
    } else if (routeName == inventoryAdd) {
      return createPageRoute(const InventoryFormScreen());
    } else if (routeName == inventoryEdit) {
      final inventoryItem = settings.arguments as InventoryItem;
      return createPageRoute(InventoryFormScreen(inventoryItem: inventoryItem));
    } else if (routeName == inventoryTransactions) {
      final inventoryItemId = settings.arguments as int?;
      return createPageRoute(InventoryTransactionsScreen(inventoryItemId: inventoryItemId));
    } else if (routeName == payroll) {
      return createPageRoute(const PayrollListScreen());
    } else if (routeName == addSalary) {
      final args = settings.arguments as Map<String, dynamic>?;
      final employee = args?['employee'] as Employee?;
      final month = args?['month'] as int?;
      final year = args?['year'] as int?;
      return createPageRoute(AddSalaryScreen(
        employee: employee,
        month: month,
        year: year,
      ));
    } else if (routeName == employees) {
      return createPageRoute(const EmployeesListScreen());
    } else if (routeName == employeeDetails) {
      final employee = settings.arguments as Employee;
      return createPageRoute(EmployeeDetailsScreen(employee: employee));
    } else if (routeName == employeeEdit) {
      final employee = settings.arguments as Employee?;
      return createPageRoute(EditEmployeeScreen(employee: employee));
    } else if (routeName == suppliers) {
      return createPageRoute(const SuppliersListScreen());
    } else if (routeName == supplierDetails) {
      final supplier = settings.arguments as Supplier;
      return createPageRoute(SupplierDetailsScreen(supplier: supplier));
    } else if (routeName == supplierEdit) {
      final supplier = settings.arguments as Supplier?;
      return createPageRoute(EditSupplierScreen(supplier: supplier));
    } else if (routeName == reports) {
      return createPageRoute(const MonthlyReportScreen());
    } else if (routeName == detailedReport) {
      final reportArgs = settings.arguments;
      if (reportArgs is String) {
        return createPageRoute(DetailedReportScreen(reportType: reportArgs));
      } else if (reportArgs is Map<String, dynamic>) {
        return createPageRoute(DetailedReportScreen(
          reportType: reportArgs['type'] as String,
          id: reportArgs['id'] as int?,
        ));
      }
    } else if (routeName == detailedReportNew) {
      final reportArgs = settings.arguments;
      if (reportArgs is String) {
        return createPageRoute(new_report.DetailedReportScreen(reportType: reportArgs));
      } else if (reportArgs is Map<String, dynamic>) {
        return createPageRoute(new_report.DetailedReportScreen(
          reportType: reportArgs['type'] as String,
          id: reportArgs['id'] as int?,
        ));
      }
    } else if (routeName == maintenanceReports) {
      return createPageRoute(const MaintenanceReportsScreen());
    } else if (routeName == customerReport) {
      final customerId = settings.arguments as int?;
      return createPageRoute(CustomerReportScreen(customerId: customerId));
    } else if (routeName == transactionReport) {
      final transactionType = settings.arguments as String?;
      return createPageRoute(TransactionReportScreen(transactionType: transactionType));
    } else if (routeName == serviceRequestReport) {
      final args = settings.arguments as Map<String, dynamic>?;
      final employeeId = args?['employeeId'] as int?;
      final statusFilter = args?['statusFilter'];
      return createPageRoute(ServiceRequestReportScreen(
        employeeId: employeeId,
        statusFilter: statusFilter,
      ));
    } else if (routeName == invoiceReport) {
      return createPageRoute(const UnifiedInvoiceReportScreen());
    } else if (routeName == cashFlowReport) {
      return createPageRoute(const CashFlowReportScreen());
    } else if (routeName == profitLossReport) {
      return createPageRoute(const ProfitLossReportScreen());
    } else if (routeName == serviceProfitability) {
      return createPageRoute(const ServiceProfitabilityScreen());
    } else if (routeName == financialDashboard) {
      return createPageRoute(const FinancialDashboardScreen());
    } else if (routeName == taxManagement) {
      return createPageRoute(const TaxManagementScreen());
    } else if (routeName == statistics) {
      return createPageRoute(const StatisticsScreen());
    } else if (routeName == AppRoutes.settings) {
      return createPageRoute(const AppSettingsScreen());
    } else if (routeName == notifications) {
      return createPageRoute(const NotificationsScreen());
    } else if (routeName == bankAccounts) {
      return createPageRoute(const BankAccountsScreen());
    } else if (routeName == cashBoxes) {
      return createPageRoute(const CashBoxesScreen());
    } else if (routeName == users) {
      return createPageRoute(const UsersListScreen());
    } else if (routeName == userDetails) {
      final user = settings.arguments as User;
      return createPageRoute(UserDetailsScreen(user: user));
    } else if (routeName == userEdit) {
      final user = settings.arguments as User?;
      return createPageRoute(UserEditScreen(user: user));
    } else if (routeName == userPermissions) {
      final user = settings.arguments as User;
      return createPageRoute(UserPermissionsScreen(user: user));
    } else if (routeName == categories) {
      return createPageRoute(const CategoriesScreen());
    } else if (routeName == categoryEdit) {
      final args = settings.arguments as Map<String, dynamic>?;
      final category = args?['category'];
      final type = args?['type'] as String? ?? 'expense';
      return createPageRoute(CategoryEditScreen(category: category, type: type));
    } else if (routeName == companyInfo) {
      return createPageRoute(const CompanyInfoScreen());
    } else if (routeName == databaseSettings) {
      return createPageRoute(const DatabaseSettingsScreen());
    } else if (routeName == demoData) {
      return createPageRoute(const DemoDataScreen());
    } else if (routeName == notificationSettings) {
      return createPageRoute(const NotificationSettingsScreen());
    } else if (routeName == newServiceRequests) {
      return createPageRoute(const ServiceRequestsScreen());
    } else if (routeName == newServiceRequestDetails) {
      final serviceRequestId = settings.arguments;
      if (serviceRequestId is int) {
        return createPageRoute(ServiceRequestDetailsScreen(serviceRequestId: serviceRequestId));
      } else if (serviceRequestId is String) {
        final parsedId = int.tryParse(serviceRequestId);
        if (parsedId != null) {
          return createPageRoute(ServiceRequestDetailsScreen(serviceRequestId: parsedId));
        }
      }
      // Fallback to service requests list if invalid argument
      return createPageRoute(const ServiceRequestsScreen());
    } else if (routeName == newServiceRequestForm) {
      final serviceRequest = settings.arguments as ServiceRequest?;
      return createPageRoute(ServiceRequestFormScreen(serviceRequest: serviceRequest));
    }

    // المسار الافتراضي للمسارات غير المعرفة
    return createPageRoute(Scaffold(
      body: Center(
        child: Text('لا يوجد مسار محدد لـ ${settings.name}'),
      ),
    ));
  }
}
