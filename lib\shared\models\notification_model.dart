import 'dart:convert';
import 'package:flutter/material.dart';

enum NotificationType {
  info,
  success,
  warning,
  error,
  transaction,
  invoice,
  employee,
  customer,
  supplier,
  serviceRequest,
}

class NotificationModel {
  final int? id;
  final String title;
  final String message;
  final NotificationType type;
  final bool isRead;
  final int? relatedId;
  final String? relatedType;
  final int? userId;
  final DateTime createdAt;

  NotificationModel({
    this.id,
    required this.title,
    required this.message,
    required this.type,
    this.isRead = false,
    this.relatedId,
    this.relatedType,
    this.userId,
    required this.createdAt,
  });

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] as int?,
      title: map['title'] as String,
      message: map['message'] as String,
      type: _parseNotificationType(map['type'] as String),
      isRead: map['is_read'] == 1,
      relatedId: map['related_id'] as int?,
      relatedType: map['related_type'] as String?,
      userId: map['user_id'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  factory NotificationModel.fromJson(String source) =>
      NotificationModel.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.toString().split('.').last,
      'is_read': isRead ? 1 : 0,
      'related_id': relatedId,
      'related_type': relatedType,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  NotificationModel copyWith({
    int? id,
    String? title,
    String? message,
    NotificationType? type,
    bool? isRead,
    int? relatedId,
    String? relatedType,
    int? userId,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      relatedId: relatedId ?? this.relatedId,
      relatedType: relatedType ?? this.relatedType,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  static NotificationType _parseNotificationType(String type) {
    switch (type.toLowerCase()) {
      case 'info':
        return NotificationType.info;
      case 'success':
        return NotificationType.success;
      case 'warning':
        return NotificationType.warning;
      case 'error':
        return NotificationType.error;
      case 'transaction':
        return NotificationType.transaction;
      case 'invoice':
        return NotificationType.invoice;
      case 'employee':
        return NotificationType.employee;
      case 'customer':
        return NotificationType.customer;
      case 'supplier':
        return NotificationType.supplier;
      case 'servicerequest':
        return NotificationType.serviceRequest;
      default:
        return NotificationType.info;
    }
  }

  static Color getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
      case NotificationType.transaction:
        return Colors.purple;
      case NotificationType.invoice:
        return Colors.indigo;
      case NotificationType.employee:
        return Colors.teal;
      case NotificationType.customer:
        return Colors.amber;
      case NotificationType.supplier:
        return Colors.brown;
      case NotificationType.serviceRequest:
        return Colors.cyan;
    }
  }

  static IconData getNotificationTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.info:
        return Icons.info;
      case NotificationType.success:
        return Icons.check_circle;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.error:
        return Icons.error;
      case NotificationType.transaction:
        return Icons.attach_money;
      case NotificationType.invoice:
        return Icons.receipt;
      case NotificationType.employee:
        return Icons.people;
      case NotificationType.customer:
        return Icons.person;
      case NotificationType.supplier:
        return Icons.local_shipping;
      case NotificationType.serviceRequest:
        return Icons.build;
    }
  }
}
