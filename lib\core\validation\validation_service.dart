import 'package:flutter/material.dart';
import '../localization/app_localizations.dart';

/// Comprehensive validation service with Arabic support
class ValidationService {
  static final ValidationService _instance = ValidationService._internal();
  factory ValidationService() => _instance;
  ValidationService._internal();

  /// Validate email address
  static String? validateEmail(String? value, AppLocalizations localizations) {
    if (value == null || value.isEmpty) {
      return localizations.translate('email_required');
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return localizations.translate('invalid_email');
    }
    
    return null;
  }

  /// Validate phone number (Saudi format)
  static String? validatePhone(String? value, AppLocalizations localizations, {bool required = true}) {
    if (value == null || value.isEmpty) {
      return required ? localizations.translate('phone_required') : null;
    }
    
    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Saudi phone number patterns
    final saudiMobileRegex = RegExp(r'^(05|5)[0-9]{8}$');
    final saudiLandlineRegex = RegExp(r'^(01|1)[0-9]{7}$');
    final internationalRegex = RegExp(r'^\+966[0-9]{9}$');
    
    if (!saudiMobileRegex.hasMatch(cleanPhone) && 
        !saudiLandlineRegex.hasMatch(cleanPhone) && 
        !internationalRegex.hasMatch(cleanPhone)) {
      return localizations.translate('invalid_phone');
    }
    
    return null;
  }

  /// Validate password
  static String? validatePassword(String? value, AppLocalizations localizations, {int minLength = 6}) {
    if (value == null || value.isEmpty) {
      return localizations.translate('password_required');
    }
    
    if (value.length < minLength) {
      return localizations.translateWithParams('password_min_length', {'length': minLength.toString()});
    }
    
    // Check for at least one letter and one number
    if (!RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(value)) {
      return localizations.translate('password_weak');
    }
    
    return null;
  }

  /// Validate confirm password
  static String? validateConfirmPassword(String? value, String? password, AppLocalizations localizations) {
    if (value == null || value.isEmpty) {
      return localizations.translate('confirm_password_required');
    }
    
    if (value != password) {
      return localizations.translate('passwords_do_not_match');
    }
    
    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, AppLocalizations localizations, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return localizations.translateWithParams('field_required', {'field': fieldName});
    }
    return null;
  }

  /// Validate name (Arabic and English)
  static String? validateName(String? value, AppLocalizations localizations, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('name_required') : null;
    }
    
    if (value.trim().length < 2) {
      return localizations.translate('name_too_short');
    }
    
    if (value.trim().length > 50) {
      return localizations.translate('name_too_long');
    }
    
    // Allow Arabic, English letters, and spaces
    if (!RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$').hasMatch(value.trim())) {
      return localizations.translate('name_invalid_characters');
    }
    
    return null;
  }

  /// Validate amount/price
  static String? validateAmount(String? value, AppLocalizations localizations, {bool required = true, double? minValue, double? maxValue}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('amount_required') : null;
    }
    
    final amount = double.tryParse(value);
    if (amount == null) {
      return localizations.translate('invalid_amount');
    }
    
    if (amount < 0) {
      return localizations.translate('amount_negative');
    }
    
    if (minValue != null && amount < minValue) {
      return localizations.translateWithParams('amount_min_value', {'min': minValue.toString()});
    }
    
    if (maxValue != null && amount > maxValue) {
      return localizations.translateWithParams('amount_max_value', {'max': maxValue.toString()});
    }
    
    return null;
  }

  /// Validate quantity
  static String? validateQuantity(String? value, AppLocalizations localizations, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('quantity_required') : null;
    }
    
    final quantity = int.tryParse(value);
    if (quantity == null) {
      return localizations.translate('invalid_quantity');
    }
    
    if (quantity < 0) {
      return localizations.translate('quantity_negative');
    }
    
    return null;
  }

  /// Validate date
  static String? validateDate(DateTime? value, AppLocalizations localizations, {bool required = true, DateTime? minDate, DateTime? maxDate}) {
    if (value == null) {
      return required ? localizations.translate('date_required') : null;
    }
    
    if (minDate != null && value.isBefore(minDate)) {
      return localizations.translateWithParams('date_too_early', {'date': localizations.formatDate(minDate)});
    }
    
    if (maxDate != null && value.isAfter(maxDate)) {
      return localizations.translateWithParams('date_too_late', {'date': localizations.formatDate(maxDate)});
    }
    
    return null;
  }

  /// Validate address
  static String? validateAddress(String? value, AppLocalizations localizations, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('address_required') : null;
    }
    
    if (value.trim().length < 5) {
      return localizations.translate('address_too_short');
    }
    
    if (value.trim().length > 200) {
      return localizations.translate('address_too_long');
    }
    
    return null;
  }

  /// Validate notes/description
  static String? validateNotes(String? value, AppLocalizations localizations, {int maxLength = 500}) {
    if (value != null && value.length > maxLength) {
      return localizations.translateWithParams('notes_too_long', {'max': maxLength.toString()});
    }
    
    return null;
  }

  /// Validate service type
  static String? validateServiceType(String? value, AppLocalizations localizations) {
    if (value == null || value.trim().isEmpty) {
      return localizations.translate('service_type_required');
    }
    
    final validTypes = [
      'maintenance',
      'installation',
      'repair',
      'cleaning',
      'inspection',
    ];
    
    if (!validTypes.contains(value.toLowerCase())) {
      return localizations.translate('invalid_service_type');
    }
    
    return null;
  }

  /// Validate priority
  static String? validatePriority(String? value, AppLocalizations localizations) {
    if (value == null || value.trim().isEmpty) {
      return localizations.translate('priority_required');
    }
    
    final validPriorities = ['low', 'normal', 'high', 'urgent'];
    
    if (!validPriorities.contains(value.toLowerCase())) {
      return localizations.translate('invalid_priority');
    }
    
    return null;
  }

  /// Validate status
  static String? validateStatus(String? value, AppLocalizations localizations, List<String> validStatuses) {
    if (value == null || value.trim().isEmpty) {
      return localizations.translate('status_required');
    }
    
    if (!validStatuses.contains(value.toLowerCase())) {
      return localizations.translate('invalid_status');
    }
    
    return null;
  }

  /// Validate tax number (Saudi format)
  static String? validateTaxNumber(String? value, AppLocalizations localizations, {bool required = false}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('tax_number_required') : null;
    }
    
    // Saudi tax number format: 15 digits
    if (!RegExp(r'^\d{15}$').hasMatch(value.trim())) {
      return localizations.translate('invalid_tax_number');
    }
    
    return null;
  }

  /// Validate URL
  static String? validateUrl(String? value, AppLocalizations localizations, {bool required = false}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('url_required') : null;
    }
    
    final urlRegex = RegExp(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
    
    if (!urlRegex.hasMatch(value.trim())) {
      return localizations.translate('invalid_url');
    }
    
    return null;
  }

  /// Validate file size
  static String? validateFileSize(int? sizeInBytes, AppLocalizations localizations, {int maxSizeInMB = 10}) {
    if (sizeInBytes == null) {
      return localizations.translate('file_required');
    }
    
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    
    if (sizeInBytes > maxSizeInBytes) {
      return localizations.translateWithParams('file_too_large', {'max': maxSizeInMB.toString()});
    }
    
    return null;
  }

  /// Validate file extension
  static String? validateFileExtension(String? fileName, AppLocalizations localizations, List<String> allowedExtensions) {
    if (fileName == null || fileName.trim().isEmpty) {
      return localizations.translate('file_required');
    }
    
    final extension = fileName.split('.').last.toLowerCase();
    
    if (!allowedExtensions.contains(extension)) {
      return localizations.translateWithParams('invalid_file_type', {'types': allowedExtensions.join(', ')});
    }
    
    return null;
  }

  /// Validate business hours
  static String? validateBusinessHours(TimeOfDay? startTime, TimeOfDay? endTime, AppLocalizations localizations) {
    if (startTime == null || endTime == null) {
      return localizations.translate('business_hours_required');
    }
    
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;
    
    if (startMinutes >= endMinutes) {
      return localizations.translate('invalid_business_hours');
    }
    
    return null;
  }

  /// Validate credit card number (basic Luhn algorithm)
  static String? validateCreditCard(String? value, AppLocalizations localizations, {bool required = false}) {
    if (value == null || value.trim().isEmpty) {
      return required ? localizations.translate('credit_card_required') : null;
    }
    
    final cleanNumber = value.replaceAll(RegExp(r'\D'), '');
    
    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return localizations.translate('invalid_credit_card');
    }
    
    // Luhn algorithm
    int sum = 0;
    bool alternate = false;
    
    for (int i = cleanNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cleanNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    if (sum % 10 != 0) {
      return localizations.translate('invalid_credit_card');
    }
    
    return null;
  }
}
