import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:icecorner/shared/utils/app_colors.dart';
import 'package:icecorner/shared/utils/app_dimensions.dart';
import 'package:icecorner/shared/utils/app_text_styles.dart';
import 'package:icecorner/shared/widgets/custom_app_bar.dart';
import 'package:icecorner/shared/widgets/custom_button.dart';
import 'package:icecorner/shared/widgets/app_drawer.dart';
import 'package:icecorner/features/reports/utils/report_generator.dart';

/// شاشة التقارير الموحدة
class UnifiedReportScreen extends StatefulWidget {
  final String title;
  final ReportType reportType;
  final int? entityId;
  final Widget? filterWidget;
  final Widget? summaryWidget;
  final Widget? dataWidget;
  final List<Widget>? actions; // أزرار إضافية في شريط التطبيق

  /// Optional callback to get the raw data for report generation
  /// This should return a list of data objects that will be passed to the report generator
  final List<dynamic> Function()? getReportData;

  const UnifiedReportScreen({
    Key? key,
    required this.title,
    required this.reportType,
    this.entityId,
    this.filterWidget,
    this.summaryWidget,
    this.dataWidget,
    this.getReportData,
    this.actions,
  }) : super(key: key);

  @override
  State<UnifiedReportScreen> createState() => _UnifiedReportScreenState();
}

class _UnifiedReportScreenState extends State<UnifiedReportScreen> {
  // نطاق التاريخ
  late DateTime _startDate;
  late DateTime _endDate;

  // فلاتر إضافية
  final Map<String, dynamic> _filters = {};

  @override
  void initState() {
    super.initState();

    // تعيين نطاق التاريخ الافتراضي (الشهر الحالي)
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, 1);
    _endDate = DateTime(now.year, now.month + 1, 0);
  }

  /// اختيار نطاق التاريخ
  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(
        start: _startDate,
        end: _endDate,
      ),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  /// إنشاء وعرض التقرير
  void _generateReport() {
    // If getReportData callback is provided, use it to get the data directly
    // This ensures that the same data displayed in the UI is used in the report
    final List<dynamic>? customData = widget.getReportData?.call();

    ReportGenerator.generateAndShowReport(
      context: context,
      reportType: widget.reportType,
      title: widget.title,
      startDate: _startDate,
      endDate: _endDate,
      entityId: widget.entityId,
      filters: _filters.isNotEmpty ? _filters : null,
      customData: customData, // Pass the custom data to the report generator
    );
  }
 /// تحديث الفلاتر
  void updateFilters(Map<String, dynamic> newFilters) {
    setState(() {
      _filters.addAll(newFilters);
    });
  }

  /// مسح الفلاتر
  void clearFilters() {
    setState(() {
      _filters.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    // تجميع الأزرار الافتراضية والأزرار الإضافية
    final List<Widget> appBarActions = [
      IconButton(
        icon: const Icon(Icons.date_range),
        onPressed: _selectDateRange,
        tooltip: 'اختيار نطاق التاريخ',
      ),
    
    ];

    // إضافة الأزرار الإضافية إذا كانت موجودة
    if (widget.actions != null) {
      appBarActions.addAll(widget.actions!);
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: widget.title,
        actions: appBarActions,
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          // عرض نطاق التاريخ
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            color: Colors.blue.shade50,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  const Icon(Icons.calendar_today, color: AppColors.primary),
                  const SizedBox(width: AppDimensions.paddingS),
                  Text(
                    'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                    style: AppTextStyles.heading3.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 20),
                  if (_filters.isNotEmpty)
                    TextButton.icon(
                      onPressed: clearFilters,
                      icon: const Icon(Icons.clear, size: 16),
                      label: const Text('مسح الفلاتر'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.error,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // باقي المحتوى في Expanded مع SingleChildScrollView
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // عنصر الفلاتر المخصص (إذا وجد)
                  if (widget.filterWidget != null)
                    Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: widget.filterWidget!,
                    ),

                  // عنصر الملخص المخصص (إذا وجد)
                  if (widget.summaryWidget != null)
                    Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: widget.summaryWidget!,
                    ),

                  // عنصر البيانات المخصص (إذا وجد)
                  if (widget.dataWidget != null)
                    Container(
                      constraints: BoxConstraints(
                        minHeight: 300,
                        maxHeight: MediaQuery.of(context).size.height * 0.6,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: widget.dataWidget!,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // زر  المعاينة
          ],
      ),
    );
  }
}
