import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../config/constants.dart';
import '../../../shared/models/service_request.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../core/repositories/customer_repository.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/models/customer.dart';
import '../../../core/services/notification_service.dart';
import '../../../core/services/notification_manager.dart';

class AddServiceRequestScreen extends StatefulWidget {
  const AddServiceRequestScreen({super.key});

  @override
  State<AddServiceRequestScreen> createState() => _AddServiceRequestScreenState();
}

class _AddServiceRequestScreenState extends State<AddServiceRequestScreen> {
  final _formKey = GlobalKey<FormState>();
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final CustomerRepository _customerRepository = CustomerRepository();

  bool _isLoading = false;
  bool _isLoadingData = true;

  // بيانات العميل
  Customer? _selectedCustomer;
  List<Customer> _customers = [];

  // بيانات الموظف الفني
  Employee? _selectedTechnician;
  List<Employee> _technicians = [];
  double? _technicianDailyRate;

  // بيانات طلب الخدمة
  String _description = '';
  ServiceRequestType _requestType = ServiceRequestType.maintenance;
  ServiceRequestPriority _priority = ServiceRequestPriority.medium;
  String? _location;
  DateTime _scheduledDate = DateTime.now();
  TimeOfDay _scheduledTime = TimeOfDay.now();
  int _reminderMinutes = 60; // القيمة الافتراضية هي 60 دقيقة (ساعة واحدة)
  double _serviceAmount = 0; // مبلغ الخدمة
  bool _isMonthlySubscription = false; // هل العميل مشترك شهري

  // بيانات الجهاز
  String? _deviceType;
  String? _deviceBrand;
  String? _deviceModel;
  String? _serialNumber;
  String? _installationDate;
  String? _warrantyInfo;

  // صور المشكلة
  final List<File> _problemImages = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      // تحميل بيانات العملاء
      final customers = await _customerRepository.getAllCustomers();

      // تحميل بيانات الموظف الفنيين
      final employees = await _employeeRepository.getAllEmployees();
      final technicians = employees.where((emp) =>
        emp.status == EmployeeStatus.active && (
          emp.position.toLowerCase().contains('فني') ||
          emp.position.toLowerCase().contains('technician') ||
          emp.position.toLowerCase().contains('maintenance') ||
          emp.position.toLowerCase().contains('صيانة') ||
          emp.position.toLowerCase().contains('تركيب') ||
          emp.position.toLowerCase().contains('تكييف')
        )
      ).toList();

      if (kDebugMode) {
        print('تم تحميل ${employees.length} موظف');
        print('تم فلترة ${technicians.length} فني نشط');
        for (var tech in technicians) {
          print('فني: ${tech.name} - المنصب: ${tech.position} - الحالة: ${tech.status}');
        }
      }

      setState(() {
        _customers = customers;
        _technicians = technicians;
        _isLoadingData = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingData = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickProblemImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      setState(() {
        _problemImages.add(File(image.path));
      });
    }
  }

  Future<void> _takeProblemImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);

    if (image != null) {
      setState(() {
        _problemImages.add(File(image.path));
      });
    }
  }

  void _removeProblemImage(int index) {
    setState(() {
      _problemImages.removeAt(index);
    });
  }

  Future<void> _selectScheduledDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _scheduledDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _scheduledDate) {
      setState(() {
        _scheduledDate = picked;
      });
    }
  }

  Future<void> _selectScheduledTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _scheduledTime,
    );

    if (picked != null && picked != _scheduledTime) {
      setState(() {
        _scheduledTime = picked;
      });
    }
  }

  Future<void> _saveServiceRequest() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      if (_selectedCustomer == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار عميل'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // إنشاء كائن طلب الخدمة
        final DateTime scheduledDateTime = DateTime(
          _scheduledDate.year,
          _scheduledDate.month,
          _scheduledDate.day,
          _scheduledTime.hour,
          _scheduledTime.minute,
        );

        final serviceRequest = ServiceRequest(
          reference: 'SR-${DateTime.now().year}-${DateTime.now().millisecondsSinceEpoch % 10000}',
          customerId: _selectedCustomer!.localId,
          customerName: _selectedCustomer!.name,
          customerPhone: _selectedCustomer!.phone,
          requestType: _requestType,
          description: _description,
          priority: _priority,
          status: ServiceRequestStatus.pending,
          location: _location,
          assignedTo: _selectedTechnician?.id,
          assignedToName: _selectedTechnician?.name,
          technicianDailyRate: _technicianDailyRate,
          scheduledDate: scheduledDateTime,
          deviceType: _deviceType,
          deviceBrand: _deviceBrand,
          deviceModel: _deviceModel,
          serialNumber: _serialNumber,
          installationDate: _installationDate,
          warrantyInfo: _warrantyInfo,
          createdAt: DateTime.now(),
          reminderMinutes: _reminderMinutes, // إضافة وقت التذكير
          serviceAmount: _serviceAmount, // إضافة مبلغ الخدمة
        );

        // حفظ طلب الخدمة في قاعدة البيانات
        final result = await _serviceRequestRepository.insertServiceRequest(serviceRequest);

        if (result > 0) {
          // حفظ الصور إذا كانت موجودة
          if (_problemImages.isNotEmpty) {
            await _serviceRequestRepository.saveProblemImages(serviceRequest.reference, _problemImages);
          }

          // جدولة إشعارات للتذكير بموعد الزيارة
          try {
            // الحصول على طلب الخدمة بعد الإضافة للحصول على معرف الطلب
            final serviceRequests = await _serviceRequestRepository.getAllServiceRequests();
            final addedRequest = serviceRequests.firstWhere(
              (req) => req.reference == serviceRequest.reference,
              orElse: () => serviceRequest,
            );

            // جدولة الإشعارات
            await scheduleNotificationsForServiceRequest(addedRequest);

            if (kDebugMode) {
              print('تم جدولة إشعارات لطلب الخدمة: ${addedRequest.reference}');
            }
          } catch (e) {
            if (kDebugMode) {
              print('خطأ في جدولة الإشعارات: $e');
            }
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة طلب الخدمة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );

            Navigator.pop(context, true);
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء إضافة طلب الخدمة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة طلب خدمة جديد'),
        actions: [
          TextButton.icon(
            onPressed: _isLoading || _isLoadingData ? null : _saveServiceRequest,
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          _isLoadingData
              ? const Center(child: CircularProgressIndicator())
              : Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات العميل
                      const Text(
                        'معلومات العميل',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      DropdownButtonFormField<Customer>(
                        decoration: const InputDecoration(
                          labelText: 'اختر العميل',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCustomer,
                        items: _customers.map((customer) {
                          return DropdownMenuItem<Customer>(
                            value: customer,
                            child: Text('${customer.name} - ${customer.phone}'),
                          );
                        }).toList(),
                        onChanged: (Customer? value) {
                          setState(() {
                            _selectedCustomer = value;
                            if (value != null) {
                              _location = value.address;

                              // التحقق من طريقة الدفع للعميل
                              _isMonthlySubscription = value.paymentMethod == CustomerPaymentMethod.monthlySubscription;

                              // إذا كان العميل مشترك شهري، نضع قيمة الخدمة صفر
                              if (_isMonthlySubscription) {
                                _serviceAmount = 0;
                              }
                            }
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'يرجى اختيار عميل';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // تفاصيل الطلب
                      const Text(
                        'تفاصيل الطلب',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      DropdownButtonFormField<ServiceRequestType>(
                        decoration: const InputDecoration(
                          labelText: 'نوع الطلب',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        value: _requestType,
                        items: ServiceRequestType.values.map((type) {
                          return DropdownMenuItem<ServiceRequestType>(
                            value: type,
                            child: Text(ServiceRequest.getRequestTypeName(type)),
                          );
                        }).toList(),
                        onChanged: (ServiceRequestType? value) {
                          if (value != null) {
                            setState(() {
                              _requestType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'وصف المشكلة',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال وصف للمشكلة';
                          }
                          return null;
                        },
                        onSaved: (value) {
                          _description = value!;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'العنوان',
                          prefixIcon: Icon(Icons.location_on),
                          border: OutlineInputBorder(),
                        ),
                        initialValue: _location,
                        onSaved: (value) {
                          _location = value;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // مبلغ الخدمة
                      if (!_isMonthlySubscription)
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'مبلغ الخدمة',
                            prefixIcon: Icon(Icons.attach_money),
                            suffixText: 'ر.س',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          initialValue: _serviceAmount > 0 ? _serviceAmount.toString() : '',
                          onSaved: (value) {
                            if (value != null && value.isNotEmpty) {
                              _serviceAmount = double.tryParse(value) ?? 0;
                            }
                          },
                        )
                      else
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(30),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.green.withAlpha(100)),
                          ),
                          child: Row(
                            children: const [
                              Icon(Icons.check_circle, color: Colors.green),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'هذا العميل مشترك في خدمة الصيانة الشهرية، لا يوجد رسوم إضافية لهذه الخدمة',
                                  style: TextStyle(color: Colors.green),
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // الأولوية
                      const Text(
                        'الأولوية',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      SegmentedButton<ServiceRequestPriority>(
                        segments: [
                          ButtonSegment<ServiceRequestPriority>(
                            value: ServiceRequestPriority.low,
                            label: const Text('منخفضة'),
                            icon: const Icon(Icons.arrow_downward),
                          ),
                          ButtonSegment<ServiceRequestPriority>(
                            value: ServiceRequestPriority.medium,
                            label: const Text('متوسطة'),
                            icon: const Icon(Icons.remove),
                          ),
                          ButtonSegment<ServiceRequestPriority>(
                            value: ServiceRequestPriority.high,
                            label: const Text('عالية'),
                            icon: const Icon(Icons.arrow_upward),
                          ),
                          ButtonSegment<ServiceRequestPriority>(
                            value: ServiceRequestPriority.urgent,
                            label: const Text('طارئة'),
                            icon: const Icon(Icons.priority_high),
                          ),
                        ],
                        selected: {_priority},
                        onSelectionChanged: (Set<ServiceRequestPriority> newSelection) {
                          setState(() {
                            _priority = newSelection.first;
                          });
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // تعيين فني
                      const Text(
                        'تعيين فني',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      _technicians.isEmpty
                        ? Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.orange),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.orange.withOpacity(0.1),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.warning, color: Colors.orange),
                                const SizedBox(width: 8),
                                const Expanded(
                                  child: Text(
                                    'لا يوجد فنيين متاحين. يرجى إضافة موظفين من قسم الموظفين أولاً.',
                                    style: TextStyle(color: Colors.orange),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : DropdownButtonFormField<Employee>(
                            decoration: const InputDecoration(
                              labelText: 'اختر الموظف الفني',
                              prefixIcon: Icon(Icons.engineering),
                              border: OutlineInputBorder(),
                            ),
                            value: _selectedTechnician,
                            items: _technicians.map((technician) {
                              return DropdownMenuItem<Employee>(
                                value: technician,
                                child: Row(
                                  children: [
                                    // أيقونة نوع الدفع
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: technician.paymentType == PaymentType.daily
                                          ? Colors.orange.withOpacity(0.2)
                                          : Colors.blue.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Icon(
                                        technician.paymentType == PaymentType.daily
                                          ? Icons.attach_money
                                          : Icons.calendar_month,
                                        size: 16,
                                        color: technician.paymentType == PaymentType.daily
                                          ? Colors.orange
                                          : Colors.blue,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            technician.name,
                                            style: const TextStyle(fontWeight: FontWeight.w500),
                                          ),
                                          Row(
                                            children: [
                                              Text(
                                                technician.position,
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                              const Text(' • ', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                              Text(
                                                technician.paymentType == PaymentType.daily ? 'أجر يومي' : 'راتب شهري',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: technician.paymentType == PaymentType.daily
                                                    ? Colors.orange
                                                    : Colors.blue,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    // عرض الأجر المقترح
                                    if (technician.paymentType == PaymentType.daily)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.green.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(color: Colors.green.withOpacity(0.3)),
                                        ),
                                        child: Text(
                                          '${technician.salary.toStringAsFixed(0)} ر.س',
                                          style: const TextStyle(
                                            fontSize: 11,
                                            color: Colors.green,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (Employee? value) {
                              setState(() {
                                _selectedTechnician = value;
                                // Reset daily rate when technician changes
                                _technicianDailyRate = null;
                              });
                            },
                          ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // Conditional Daily Rate Field (only for daily wage technicians)
                      if (_selectedTechnician != null && _selectedTechnician!.hasDailyWage)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'أجر الموظف الفني اليومي',
                              style: AppTextStyles.heading3,
                            ),
                            const SizedBox(height: AppDimensions.paddingS),
                            TextFormField(
                              decoration: InputDecoration(
                                labelText: 'أجر الموظف الفني لهذه الخدمة',
                                prefixIcon: const Icon(Icons.attach_money, color: Colors.green),
                                suffixText: 'ر.س',
                                border: const OutlineInputBorder(),
                                helperText: _selectedTechnician != null
                                  ? 'الأجر المقترح: ${_selectedTechnician!.salary.toStringAsFixed(0)} ر.س'
                                  : 'يتم تحديد الأجر لكل خدمة منفصلة',
                                helperStyle: TextStyle(
                                  color: _selectedTechnician != null ? Colors.blue : Colors.grey,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                              ],
                              validator: (value) {
                                if (_selectedTechnician != null && _selectedTechnician!.hasDailyWage) {
                                  if (value == null || value.isEmpty) {
                                    return 'يرجى إدخال أجر الموظف الفني اليومي';
                                  }
                                  final rate = double.tryParse(value);
                                  if (rate == null || rate <= 0) {
                                    return 'يرجى إدخال أجر صحيح أكبر من صفر';
                                  }
                                  if (rate > 1000) {
                                    return 'الأجر اليومي يبدو مرتفعاً جداً (أكثر من 1000 ر.س)';
                                  }
                                }
                                return null;
                              },
                              onSaved: (value) {
                                if (value != null && value.isNotEmpty) {
                                  _technicianDailyRate = double.parse(value);
                                }
                              },
                            ),
                            if (_selectedTechnician != null && _selectedTechnician!.hasDailyWage)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: ElevatedButton.icon(
                                  onPressed: () {
                                    setState(() {
                                      _technicianDailyRate = _selectedTechnician!.salary;
                                    });
                                  },
                                  icon: const Icon(Icons.auto_fix_high, size: 16),
                                  label: Text('استخدام الأجر المقترح (${_selectedTechnician!.salary.toStringAsFixed(0)} ر.س)'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue.shade50,
                                    foregroundColor: Colors.blue.shade700,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  ),
                                ),
                              ),
                            const SizedBox(height: AppDimensions.paddingM),
                          ],
                        ),

                      // موعد الزيارة
                      const Text(
                        'موعد الزيارة',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: _selectScheduledDate,
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'التاريخ',
                                  prefixIcon: Icon(Icons.calendar_today),
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(
                                  DateFormat('yyyy/MM/dd').format(_scheduledDate),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: InkWell(
                              onTap: _selectScheduledTime,
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'الوقت',
                                  prefixIcon: Icon(Icons.access_time),
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(
                                  '${_scheduledTime.hour.toString().padLeft(2, '0')}:${_scheduledTime.minute.toString().padLeft(2, '0')}',
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // عنوان قسم التذكيرات
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: const [
                            Icon(Icons.notifications_active, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(
                              'إعدادات التذكير',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),

                      // وقت التذكير
                      DropdownButtonFormField<int>(
                        decoration: const InputDecoration(
                          labelText: 'التذكير قبل الموعد بـ',
                          prefixIcon: Icon(Icons.notifications_active),
                          border: OutlineInputBorder(),
                        ),
                        value: _reminderMinutes,
                        items: [
                          DropdownMenuItem<int>(
                            value: 5,
                            child: Text('5 دقائق'),
                          ),
                          DropdownMenuItem<int>(
                            value: 10,
                            child: Text('10 دقائق'),
                          ),
                          DropdownMenuItem<int>(
                            value: 15,
                            child: Text('15 دقيقة'),
                          ),
                          DropdownMenuItem<int>(
                            value: 30,
                            child: Text('30 دقيقة'),
                          ),
                          DropdownMenuItem<int>(
                            value: 60,
                            child: Text('ساعة واحدة'),
                          ),
                          DropdownMenuItem<int>(
                            value: 120,
                            child: Text('ساعتين'),
                          ),
                          DropdownMenuItem<int>(
                            value: 180,
                            child: Text('3 ساعات'),
                          ),
                          DropdownMenuItem<int>(
                            value: 360,
                            child: Text('6 ساعات'),
                          ),
                          DropdownMenuItem<int>(
                            value: 720,
                            child: Text('12 ساعة'),
                          ),
                          DropdownMenuItem<int>(
                            value: 1440,
                            child: Text('يوم كامل'),
                          ),
                          DropdownMenuItem<int>(
                            value: 2880,
                            child: Text('يومين'),
                          ),
                        ],
                        onChanged: (int? value) {
                          if (value != null) {
                            setState(() {
                              _reminderMinutes = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 8),

                      // نص توضيحي لنظام التذكيرات
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.amber.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber.withAlpha(50)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: const [
                                Icon(Icons.info_outline, color: Colors.amber, size: 16),
                                SizedBox(width: 8),
                                Text(
                                  'نظام التذكيرات المتعدد',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'سيتم إرسال التذكيرات التالية:',
                              style: TextStyle(fontSize: 12),
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              '• تذكير رئيسي في الوقت المحدد أعلاه',
                              style: TextStyle(fontSize: 12),
                            ),
                            const Text(
                              '• تذكير قبل الموعد بساعة (إذا كان التذكير الرئيسي أكثر من ساعة)',
                              style: TextStyle(fontSize: 12),
                            ),
                            const Text(
                              '• تذكير قبل الموعد بـ 15 دقيقة (إذا كان التذكير الرئيسي أكثر من 15 دقيقة)',
                              style: TextStyle(fontSize: 12),
                            ),
                            const Text(
                              '• تذكير في وقت الموعد تمامًا',
                              style: TextStyle(fontSize: 12),
                            ),
                            const Text(
                              '• تذكير بعد الموعد بـ 15 دقيقة لتسجيل النتائج',
                              style: TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // معلومات الجهاز
                      const Text(
                        'معلومات الجهاز',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'نوع الجهاز',
                          prefixIcon: Icon(Icons.devices),
                          border: OutlineInputBorder(),
                        ),
                        onSaved: (value) {
                          _deviceType = value;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'الماركة',
                                prefixIcon: Icon(Icons.branding_watermark),
                                border: OutlineInputBorder(),
                              ),
                              onSaved: (value) {
                                _deviceBrand = value;
                              },
                            ),
                          ),
                          const SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'الموديل',
                                prefixIcon: Icon(Icons.model_training),
                                border: OutlineInputBorder(),
                              ),
                              onSaved: (value) {
                                _deviceModel = value;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'الرقم التسلسلي',
                          prefixIcon: Icon(Icons.pin),
                          border: OutlineInputBorder(),
                        ),
                        onSaved: (value) {
                          _serialNumber = value;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'تاريخ التركيب',
                                prefixIcon: Icon(Icons.date_range),
                                border: OutlineInputBorder(),
                              ),
                              onSaved: (value) {
                                _installationDate = value;
                              },
                            ),
                          ),
                          const SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'معلومات الضمان',
                                prefixIcon: Icon(Icons.verified_user),
                                border: OutlineInputBorder(),
                              ),
                              onSaved: (value) {
                                _warrantyInfo = value;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // صور المشكلة
                      const Text(
                        'صور المشكلة',
                        style: AppTextStyles.heading3,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8.0),
                              child: ElevatedButton.icon(
                                onPressed: _takeProblemImage,
                                icon: const Icon(Icons.camera_alt, size: 18),
                                label: const Text('التقاط صورة'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8.0),
                              child: ElevatedButton.icon(
                                onPressed: _pickProblemImage,
                                icon: const Icon(Icons.photo_library, size: 18),
                                label: const Text('اختيار من المعرض'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      if (_problemImages.isNotEmpty)
                        SizedBox(
                          height: 120,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _problemImages.length,
                            itemBuilder: (context, index) {
                              return Stack(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(right: AppDimensions.paddingS),
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                                      image: DecorationImage(
                                        image: FileImage(_problemImages[index]),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 5,
                                    right: 10,
                                    child: GestureDetector(
                                      onTap: () => _removeProblemImage(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        )
                      else
                        const Center(
                          child: Text(
                            'لم يتم إضافة صور بعد',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
          if (_isLoading)
            Container(
              color: Colors.black.withAlpha(128),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
