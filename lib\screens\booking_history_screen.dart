import 'package:flutter/material.dart';

class BookingHistoryScreen extends StatefulWidget {
  const BookingHistoryScreen({Key? key}) : super(key: key);

  @override
  State<BookingHistoryScreen> createState() => _BookingHistoryScreenState();
}

class _BookingHistoryScreenState extends State<BookingHistoryScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking History'),
      ),
      body: ListView.builder(
        itemCount: 5,
        itemBuilder: (context, index) {
          return Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Booking Details'),
                  const Text('Status: Confirmed'),
                  ElevatedButton(
                    onPressed: () {},
                    child: const Text('Cancel Booking'),
                  ),
                  ElevatedButton(
                    onPressed: () {},
                    child: const Text('Review Tour'),
                  ),
                  ElevatedButton(
                    onPressed: () {},
                    child: const Text('Rebook'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
