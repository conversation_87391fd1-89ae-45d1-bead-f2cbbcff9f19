import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

/// خدمة الصوت للتطبيق
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;

  // مشغل الصوت
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // حالة تشغيل الصوت
  bool _isSoundEnabled = true;

  AudioService._internal();

  /// تهيئة خدمة الصوت
  Future<void> init() async {
    try {
      // تحميل ملف الصوت الافتراضي
      await _audioPlayer.setAsset('assets/sounds/ringtonesettings.mp3');
      
      if (kDebugMode) {
        print('تم تهيئة خدمة الصوت بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تهيئة خدمة الصوت: $e');
      }
    }
  }

  /// تشغيل صوت الإشعار
  Future<void> playNotificationSound() async {
    if (!_isSoundEnabled) return;
    
    try {
      // إعادة تعيين الصوت إلى البداية
      await _audioPlayer.seek(Duration.zero);
      
      // تشغيل الصوت
      await _audioPlayer.play();
      
      if (kDebugMode) {
        print('تم تشغيل صوت الإشعار');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تشغيل صوت الإشعار: $e');
      }
    }
  }

  /// إيقاف صوت الإشعار
  Future<void> stopNotificationSound() async {
    try {
      await _audioPlayer.stop();
      
      if (kDebugMode) {
        print('تم إيقاف صوت الإشعار');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في إيقاف صوت الإشعار: $e');
      }
    }
  }

  /// تفعيل/تعطيل الصوت
  void setSoundEnabled(bool enabled) {
    _isSoundEnabled = enabled;
    
    if (kDebugMode) {
      print('تم ${enabled ? 'تفعيل' : 'تعطيل'} صوت الإشعارات');
    }
  }

  /// الحصول على حالة تفعيل الصوت
  bool get isSoundEnabled => _isSoundEnabled;

  /// التخلص من موارد الصوت
  Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}
