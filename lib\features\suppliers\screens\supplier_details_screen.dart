import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/supplier.dart';

class SupplierDetailsScreen extends StatefulWidget {
  final Supplier supplier;

  const SupplierDetailsScreen({
    super.key,
    required this.supplier,
  });

  @override
  State<SupplierDetailsScreen> createState() => _SupplierDetailsScreenState();
}

class _SupplierDetailsScreenState extends State<SupplierDetailsScreen> {
  late Supplier _supplier;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _supplier = widget.supplier;
  }

  void _updateSupplierStatus(bool isActive) {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _supplier = _supplier.copyWith(isActive: isActive);
          _isLoading = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث حالة المورد إلى ${isActive ? 'نشط' : 'غير نشط'}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('بيانات ${_supplier.name}'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit') {
                Navigator.pushNamed(
                  context,
                  AppRoutes.supplierEdit,
                  arguments: _supplier,
                ).then((updatedSupplier) {
                  if (updatedSupplier != null) {
                    setState(() {
                      _supplier = updatedSupplier as Supplier;
                    });
                  }
                });
              } else if (value == 'activate') {
                _updateSupplierStatus(true);
              } else if (value == 'deactivate') {
                _updateSupplierStatus(false);
              } else if (value == 'report') {
                Navigator.pushNamed(
                  context,
                  AppRoutes.detailedReport,
                  arguments: {'type': 'supplier', 'id': _supplier.id},
                );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20),
                    SizedBox(width: 8),
                    Text('تعديل البيانات'),
                  ],
                ),
              ),
              if (!_supplier.isActive)
                const PopupMenuItem(
                  value: 'activate',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, size: 20, color: Colors.green),
                      SizedBox(width: 8),
                      Text('تفعيل المورد'),
                    ],
                  ),
                ),
              if (_supplier.isActive)
                const PopupMenuItem(
                  value: 'deactivate',
                  child: Row(
                    children: [
                      Icon(Icons.cancel, size: 20, color: Colors.red),
                      SizedBox(width: 8),
                      Text('تعطيل المورد'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.assessment, size: 20, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('عرض التقرير'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeaderCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildContactCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildFinancialCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildTransactionsCard(),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showPaymentDialog(context);
        },
        icon: const Icon(Icons.payment),
        label: const Text('تسجيل دفعة'),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: Supplier.getCategoryColor(_supplier.category).withAlpha(51),
                  child: Icon(
                    Supplier.getCategoryIcon(_supplier.category),
                    color: Supplier.getCategoryColor(_supplier.category),
                    size: 40,
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _supplier.name,
                        style: AppTextStyles.heading2,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        Supplier.getCategoryName(_supplier.category),
                        style: TextStyle(
                          fontSize: 16,
                          color: Supplier.getCategoryColor(_supplier.category),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: _supplier.isActive ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          _supplier.isActive ? 'نشط' : 'غير نشط',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الاتصال',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'البريد الإلكتروني',
              _supplier.email ?? 'غير متوفر',
              icon: Icons.email,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'رقم الهاتف',
              _supplier.phone ?? 'غير متوفر',
              icon: Icons.phone,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'العنوان',
              _supplier.address ?? 'غير متوفر',
              icon: Icons.location_on,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الشخص المسؤول',
              _supplier.contactPerson ?? 'غير متوفر',
              icon: Icons.person,
            ),
            if (_supplier.website != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow(
                'الموقع الإلكتروني',
                _supplier.website!,
                icon: Icons.language,
              ),
            ],
            if (_supplier.taxNumber != null) ...[
              const SizedBox(height: AppDimensions.paddingS),
              _buildDetailRow(
                'الرقم الضريبي',
                _supplier.taxNumber!,
                icon: Icons.receipt,
              ),
            ],
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'تاريخ الإضافة',
              DateFormat('dd/MM/yyyy').format(_supplier.createdAt),
              icon: Icons.calendar_today,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات المالية',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الرصيد الحالي',
              '${_supplier.balance} ر.س',
              icon: Icons.account_balance_wallet,
              valueColor: _supplier.balance > 0 ? Colors.red : Colors.green,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'إجمالي المشتريات',
              '${_supplier.balance + 5000} ر.س', // Mock data
              icon: Icons.shopping_cart,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'إجمالي المدفوعات',
              '5000 ر.س', // Mock data
              icon: Icons.payments,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'آخر إيراد',
              DateFormat('dd/MM/yyyy').format(DateTime.now().subtract(const Duration(days: 15))),
              icon: Icons.history,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsCard() {
    // Mock transactions
    final transactions = [
      {'date': DateTime.now().subtract(const Duration(days: 15)), 'type': 'شراء', 'amount': 2000.0},
      {'date': DateTime.now().subtract(const Duration(days: 30)), 'type': 'دفعة', 'amount': -1500.0},
      {'date': DateTime.now().subtract(const Duration(days: 45)), 'type': 'شراء', 'amount': 3000.0},
      {'date': DateTime.now().subtract(const Duration(days: 60)), 'type': 'دفعة', 'amount': -2000.0},
    ];

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'آخر المعاملات',
                  style: AppTextStyles.heading3,
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.detailedReport,
                      arguments: {'type': 'supplier', 'id': _supplier.id},
                    );
                  },
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            ...transactions.map((transaction) {
              final isPayment = transaction['amount'] as double < 0;
              return Padding(
                padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: isPayment ? Colors.green.withAlpha(51) : Colors.red.withAlpha(51),
                      child: Icon(
                        isPayment ? Icons.arrow_upward : Icons.arrow_downward,
                        color: isPayment ? Colors.green : Colors.red,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.paddingS),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction['type'] as String,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            DateFormat('dd/MM/yyyy').format(transaction['date'] as DateTime),
                            style: const TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '${(transaction['amount'] as double).abs()} ر.س',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isPayment ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {IconData? icon, Color? valueColor}) {
    return Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 8),
        ],
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 16,
              color: valueColor,
              fontWeight: valueColor != null ? FontWeight.bold : null,
            ),
          ),
        ),
      ],
    );
  }

  void _showPaymentDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    double amount = 0;
    String note = ''; // Variable para almacenar la nota (se usará en el futuro para mostrar detalles adicionales)

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تسجيل دفعة لـ ${_supplier.name}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'المبلغ',
                    prefixIcon: Icon(Icons.money),
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    amount = double.parse(value!);
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  onSaved: (value) {
                    note = value ?? '';
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // En el futuro, podríamos usar la nota para mostrar detalles adicionales
                  // print('Nota: $note');

                  // Update supplier balance
                  setState(() {
                    _supplier = _supplier.copyWith(
                      balance: _supplier.balance - amount,
                    );
                  });

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم تسجيل دفعة بمبلغ $amount ريال لـ ${_supplier.name}',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('تسجيل'),
            ),
          ],
        );
      },
    );
  }
}
