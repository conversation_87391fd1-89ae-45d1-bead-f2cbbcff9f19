import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../shared/widgets/enhanced_card.dart';
import '../../../shared/widgets/enhanced_form_field.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../../../shared/widgets/rtl_aware_widget.dart';
import '../services/enhanced_report_service.dart';

/// Enhanced reports dashboard with comprehensive Arabic support
class EnhancedReportsDashboard extends StatefulWidget {
  const EnhancedReportsDashboard({super.key});

  @override
  State<EnhancedReportsDashboard> createState() => _EnhancedReportsDashboardState();
}

class _EnhancedReportsDashboardState extends State<EnhancedReportsDashboard> {
  final EnhancedReportService _reportService = EnhancedReportService();
  
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return RTLAwareWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text(localizations.translate('reports')),
          elevation: 0,
        ),
        body: ResponsiveContainer(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date Range Selection
                _buildDateRangeSection(localizations, theme),
                
                const SizedBox(height: 24),
                
                // Report Categories
                ResponsiveGrid(
                  mobileColumns: 1,
                  tabletColumns: 2,
                  desktopColumns: 3,
                  children: [
                    _buildReportCard(
                      title: localizations.translate('customer_reports'),
                      description: localizations.translate('customer_reports_desc'),
                      icon: Icons.people,
                      color: Colors.blue,
                      onTap: () => _showCustomerReportOptions(localizations),
                    ),
                    _buildReportCard(
                      title: localizations.translate('service_reports'),
                      description: localizations.translate('service_reports_desc'),
                      icon: Icons.build,
                      color: Colors.green,
                      onTap: () => _showServiceReportOptions(localizations),
                    ),
                    _buildReportCard(
                      title: localizations.translate('financial_reports'),
                      description: localizations.translate('financial_reports_desc'),
                      icon: Icons.attach_money,
                      color: Colors.orange,
                      onTap: () => _showFinancialReportOptions(localizations),
                    ),
                    _buildReportCard(
                      title: localizations.translate('employee_reports'),
                      description: localizations.translate('employee_reports_desc'),
                      icon: Icons.person_outline,
                      color: Colors.purple,
                      onTap: () => _showEmployeeReportOptions(localizations),
                    ),
                    _buildReportCard(
                      title: localizations.translate('inventory_reports'),
                      description: localizations.translate('inventory_reports_desc'),
                      icon: Icons.inventory,
                      color: Colors.teal,
                      onTap: () => _showInventoryReportOptions(localizations),
                    ),
                    _buildReportCard(
                      title: localizations.translate('custom_reports'),
                      description: localizations.translate('custom_reports_desc'),
                      icon: Icons.analytics,
                      color: Colors.indigo,
                      onTap: () => _showCustomReportOptions(localizations),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Quick Actions
                _buildQuickActionsSection(localizations, theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeSection(AppLocalizations localizations, ThemeData theme) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.translate('date_range'),
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ResponsiveColumnRow(
            children: [
              Expanded(
                child: EnhancedDateField(
                  label: localizations.translate('from_date'),
                  value: _startDate,
                  onChanged: (date) {
                    if (date != null) {
                      setState(() {
                        _startDate = date;
                      });
                    }
                  },
                  dateFormatter: (date) => localizations.formatDate(date),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: EnhancedDateField(
                  label: localizations.translate('to_date'),
                  value: _endDate,
                  onChanged: (date) {
                    if (date != null) {
                      setState(() {
                        _endDate = date;
                      });
                    }
                  },
                  dateFormatter: (date) => localizations.formatDate(date),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            children: [
              _buildQuickDateButton(
                localizations.translate('this_week'),
                () => _setDateRange(DateTime.now().subtract(const Duration(days: 7)), DateTime.now()),
              ),
              _buildQuickDateButton(
                localizations.translate('this_month'),
                () => _setDateRange(DateTime(DateTime.now().year, DateTime.now().month, 1), DateTime.now()),
              ),
              _buildQuickDateButton(
                localizations.translate('this_year'),
                () => _setDateRange(DateTime(DateTime.now().year, 1, 1), DateTime.now()),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickDateButton(String text, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      child: Text(text),
    );
  }

  void _setDateRange(DateTime start, DateTime end) {
    setState(() {
      _startDate = start;
      _endDate = end;
    });
  }

  Widget _buildReportCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return EnhancedCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(AppLocalizations localizations, ThemeData theme) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.translate('quick_actions'),
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              EnhancedButton(
                text: localizations.translate('export_all_data'),
                icon: const Icon(Icons.download),
                type: ButtonType.secondary,
                onPressed: _isGenerating ? null : () => _exportAllData(localizations),
              ),
              EnhancedButton(
                text: localizations.translate('schedule_report'),
                icon: const Icon(Icons.schedule),
                type: ButtonType.secondary,
                onPressed: () => _scheduleReport(localizations),
              ),
              EnhancedButton(
                text: localizations.translate('report_templates'),
                icon: const Icon(Icons.description_outlined),
                type: ButtonType.secondary,
                onPressed: () => _showReportTemplates(localizations),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showCustomerReportOptions(AppLocalizations localizations) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildReportOptionsSheet(
        localizations.translate('customer_reports'),
        [
          _ReportOption(
            title: localizations.translate('customer_summary'),
            description: localizations.translate('customer_summary_desc'),
            onTap: () => _generateCustomerReport(localizations),
          ),
          _ReportOption(
            title: localizations.translate('customer_service_history'),
            description: localizations.translate('customer_service_history_desc'),
            onTap: () => _generateCustomerServiceHistory(localizations),
          ),
          _ReportOption(
            title: localizations.translate('customer_payment_history'),
            description: localizations.translate('customer_payment_history_desc'),
            onTap: () => _generateCustomerPaymentHistory(localizations),
          ),
        ],
      ),
    );
  }

  void _showServiceReportOptions(AppLocalizations localizations) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildReportOptionsSheet(
        localizations.translate('service_reports'),
        [
          _ReportOption(
            title: localizations.translate('service_summary'),
            description: localizations.translate('service_summary_desc'),
            onTap: () => _generateServiceReport(localizations),
          ),
          _ReportOption(
            title: localizations.translate('technician_performance'),
            description: localizations.translate('technician_performance_desc'),
            onTap: () => _generateTechnicianReport(localizations),
          ),
          _ReportOption(
            title: localizations.translate('service_efficiency'),
            description: localizations.translate('service_efficiency_desc'),
            onTap: () => _generateServiceEfficiencyReport(localizations),
          ),
        ],
      ),
    );
  }

  void _showFinancialReportOptions(AppLocalizations localizations) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildReportOptionsSheet(
        localizations.translate('financial_reports'),
        [
          _ReportOption(
            title: localizations.translate('revenue_report'),
            description: localizations.translate('revenue_report_desc'),
            onTap: () => _generateFinancialReport(localizations),
          ),
          _ReportOption(
            title: localizations.translate('profit_loss'),
            description: localizations.translate('profit_loss_desc'),
            onTap: () => _generateProfitLossReport(localizations),
          ),
          _ReportOption(
            title: localizations.translate('cash_flow'),
            description: localizations.translate('cash_flow_desc'),
            onTap: () => _generateCashFlowReport(localizations),
          ),
        ],
      ),
    );
  }

  void _showEmployeeReportOptions(AppLocalizations localizations) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildReportOptionsSheet(
        localizations.translate('employee_reports'),
        [
          _ReportOption(
            title: localizations.translate('employee_performance'),
            description: localizations.translate('employee_performance_desc'),
            onTap: () => _generateTechnicianReport(localizations),
          ),
          _ReportOption(
            title: localizations.translate('employee_workload'),
            description: localizations.translate('employee_workload_desc'),
            onTap: () => _generateEmployeeWorkloadReport(localizations),
          ),
        ],
      ),
    );
  }

  void _showInventoryReportOptions(AppLocalizations localizations) {
    // Implementation for inventory reports
  }

  void _showCustomReportOptions(AppLocalizations localizations) {
    // Implementation for custom reports
  }

  Widget _buildReportOptionsSheet(String title, List<_ReportOption> options) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...options.map((option) => ListTile(
            title: Text(option.title),
            subtitle: Text(option.description),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pop(context);
              option.onTap();
            },
          )),
        ],
      ),
    );
  }

  Future<void> _generateCustomerReport(AppLocalizations localizations) async {
    setState(() => _isGenerating = true);
    try {
      final filePath = await _reportService.generateCustomerReport(
        context: context,
        startDate: _startDate,
        endDate: _endDate,
      );
      
      if (filePath != null && mounted) {
        _showReportGenerated(localizations, filePath);
      }
    } finally {
      if (mounted) setState(() => _isGenerating = false);
    }
  }

  Future<void> _generateServiceReport(AppLocalizations localizations) async {
    setState(() => _isGenerating = true);
    try {
      final filePath = await _reportService.generateServiceReport(
        context: context,
        startDate: _startDate,
        endDate: _endDate,
      );
      
      if (filePath != null && mounted) {
        _showReportGenerated(localizations, filePath);
      }
    } finally {
      if (mounted) setState(() => _isGenerating = false);
    }
  }

  Future<void> _generateFinancialReport(AppLocalizations localizations) async {
    setState(() => _isGenerating = true);
    try {
      final filePath = await _reportService.generateFinancialReport(
        context: context,
        startDate: _startDate,
        endDate: _endDate,
      );
      
      if (filePath != null && mounted) {
        _showReportGenerated(localizations, filePath);
      }
    } finally {
      if (mounted) setState(() => _isGenerating = false);
    }
  }

  Future<void> _generateTechnicianReport(AppLocalizations localizations) async {
    setState(() => _isGenerating = true);
    try {
      final filePath = await _reportService.generateTechnicianReport(
        context: context,
        startDate: _startDate,
        endDate: _endDate,
      );
      
      if (filePath != null && mounted) {
        _showReportGenerated(localizations, filePath);
      }
    } finally {
      if (mounted) setState(() => _isGenerating = false);
    }
  }

  void _generateCustomerServiceHistory(AppLocalizations localizations) {
    // Implementation
  }

  void _generateCustomerPaymentHistory(AppLocalizations localizations) {
    // Implementation
  }

  void _generateServiceEfficiencyReport(AppLocalizations localizations) {
    // Implementation
  }

  void _generateProfitLossReport(AppLocalizations localizations) {
    // Implementation
  }

  void _generateCashFlowReport(AppLocalizations localizations) {
    // Implementation
  }

  void _generateEmployeeWorkloadReport(AppLocalizations localizations) {
    // Implementation
  }

  void _exportAllData(AppLocalizations localizations) {
    // Implementation
  }

  void _scheduleReport(AppLocalizations localizations) {
    // Implementation
  }

  void _showReportTemplates(AppLocalizations localizations) {
    // Implementation
  }

  void _showReportGenerated(AppLocalizations localizations, String filePath) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(localizations.translate('report_generated_successfully')),
        action: SnackBarAction(
          label: localizations.translate('open'),
          onPressed: () {
            // Open the generated report
          },
        ),
      ),
    );
  }
}

class _ReportOption {
  final String title;
  final String description;
  final VoidCallback onTap;

  _ReportOption({
    required this.title,
    required this.description,
    required this.onTap,
  });
}
