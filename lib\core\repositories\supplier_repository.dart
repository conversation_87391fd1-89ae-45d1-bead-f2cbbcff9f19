import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/supplier.dart';

class SupplierRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all suppliers
  Future<List<Supplier>> getAllSuppliers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('suppliers');

      return List.generate(maps.length, (i) {
        return Supplier.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting suppliers: $e');
      }
      return [];
    }
  }

  // Get supplier by ID
  Future<Supplier?> getSupplierById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'suppliers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Supplier.fromMap(maps[0]);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting supplier by ID: $e');
      }
      return null;
    }
  }

  // Insert a new supplier
  Future<int> insertSupplier(Supplier supplier) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'suppliers',
        {
          'name': supplier.name,
          'email': supplier.email,
          'phone': supplier.phone,
          'address': supplier.address,
          'contact_person': supplier.contactPerson,
          'category': supplier.category.toString().split('.').last,
          'status': supplier.isActive ? 'active' : 'inactive',
          'balance': supplier.balance,
          'created_at': DateTime.now().toIso8601String(),
          'notes': supplier.notes,
          'tax_number': supplier.taxNumber,
          'website': supplier.website,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting supplier: $e');
      }
      return -1;
    }
  }

  // Update an existing supplier
  Future<int> updateSupplier(Supplier supplier) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'suppliers',
        {
          'name': supplier.name,
          'email': supplier.email,
          'phone': supplier.phone,
          'address': supplier.address,
          'contact_person': supplier.contactPerson,
          'category': supplier.category.toString().split('.').last,
          'status': supplier.isActive ? 'active' : 'inactive',
          'balance': supplier.balance,
          'notes': supplier.notes,
          'tax_number': supplier.taxNumber,
          'website': supplier.website,
        },
        where: 'id = ?',
        whereArgs: [supplier.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating supplier: $e');
      }
      return 0;
    }
  }

  // Delete a supplier
  Future<int> deleteSupplier(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'suppliers',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting supplier: $e');
      }
      return 0;
    }
  }

  // Update supplier balance
  Future<int> updateSupplierBalance(int id, double newBalance) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'suppliers',
        {'balance': newBalance},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating supplier balance: $e');
      }
      return 0;
    }
  }

  // The _parseCategory method has been moved to the Supplier model as a static method
}
