import 'dart:convert';
import 'package:flutter/material.dart';
import '../../shared/models/user.dart';
import '../../core/repositories/user_repository.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/simple_loading_indicator.dart';
import '../../config/constants.dart';

/// شاشة إدارة صلاحيات المستخدم
class UserPermissionsScreen extends StatefulWidget {
  final User user;

  const UserPermissionsScreen({super.key, required this.user});

  @override
  State<UserPermissionsScreen> createState() => _UserPermissionsScreenState();
}

class _UserPermissionsScreenState extends State<UserPermissionsScreen> {
  final UserRepository _repository = UserRepository();
  bool _isLoading = false;
  bool _isSaving = false;
  Map<String, dynamic> _permissions = {};

  // قائمة الصلاحيات المتاحة
  final List<Map<String, dynamic>> _permissionGroups = [
    {
      'name': 'لوحة التحكم',
      'key': 'dashboard',
      'permissions': [
        {'key': 'dashboard_view', 'name': 'عرض'},
      ],
    },
    {
      'name': 'المستخدمين',
      'key': 'users',
      'permissions': [
        {'key': 'users_view', 'name': 'عرض'},
        {'key': 'users_add', 'name': 'إضافة'},
        {'key': 'users_edit', 'name': 'تعديل'},
        {'key': 'users_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'العملاء',
      'key': 'customers',
      'permissions': [
        {'key': 'customers_view', 'name': 'عرض'},
        {'key': 'customers_add', 'name': 'إضافة'},
        {'key': 'customers_edit', 'name': 'تعديل'},
        {'key': 'customers_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'الموردين',
      'key': 'suppliers',
      'permissions': [
        {'key': 'suppliers_view', 'name': 'عرض'},
        {'key': 'suppliers_add', 'name': 'إضافة'},
        {'key': 'suppliers_edit', 'name': 'تعديل'},
        {'key': 'suppliers_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'الموظفين',
      'key': 'employees',
      'permissions': [
        {'key': 'employees_view', 'name': 'عرض'},
        {'key': 'employees_add', 'name': 'إضافة'},
        {'key': 'employees_edit', 'name': 'تعديل'},
        {'key': 'employees_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'المعاملات المالية',
      'key': 'transactions',
      'permissions': [
        {'key': 'transactions_view', 'name': 'عرض'},
        {'key': 'transactions_add', 'name': 'إضافة'},
        {'key': 'transactions_edit', 'name': 'تعديل'},
        {'key': 'transactions_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'الفواتير',
      'key': 'invoices',
      'permissions': [
        {'key': 'invoices_view', 'name': 'عرض'},
        {'key': 'invoices_add', 'name': 'إضافة'},
        {'key': 'invoices_edit', 'name': 'تعديل'},
        {'key': 'invoices_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'طلبات الخدمة',
      'key': 'service_requests',
      'permissions': [
        {'key': 'service_requests_view', 'name': 'عرض'},
        {'key': 'service_requests_add', 'name': 'إضافة'},
        {'key': 'service_requests_edit', 'name': 'تعديل'},
        {'key': 'service_requests_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'المخزون',
      'key': 'inventory',
      'permissions': [
        {'key': 'inventory_view', 'name': 'عرض'},
        {'key': 'inventory_add', 'name': 'إضافة'},
        {'key': 'inventory_edit', 'name': 'تعديل'},
        {'key': 'inventory_delete', 'name': 'حذف'},
      ],
    },
    {
      'name': 'التقارير',
      'key': 'reports',
      'permissions': [
        {'key': 'reports_view', 'name': 'عرض'},
      ],
    },
    {
      'name': 'الإعدادات',
      'key': 'settings',
      'permissions': [
        {'key': 'settings_view', 'name': 'عرض'},
        {'key': 'settings_edit', 'name': 'تعديل'},
      ],
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadPermissions();
  }

  Future<void> _loadPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل صلاحيات المستخدم
      if (widget.user.permissions != null) {
        setState(() {
          _permissions = jsonDecode(widget.user.permissions!);
        });
      } else {
        // إذا لم تكن هناك صلاحيات، نضع القيم الافتراضية
        final defaultPermissions = <String, dynamic>{};
        for (final group in _permissionGroups) {
          for (final permission in group['permissions']) {
            defaultPermissions[permission['key']] = false;
          }
        }
        setState(() {
          _permissions = defaultPermissions;
        });
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل الصلاحيات');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _savePermissions() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // تحويل الصلاحيات إلى نص JSON
      final permissionsJson = jsonEncode(_permissions);

      // تحديث صلاحيات المستخدم فقط
      final result = await _repository.updateUserPermissions(widget.user.id, permissionsJson);

      if (result > 0) {
        _showSuccessSnackBar('تم حفظ الصلاحيات بنجاح');
      } else {
        _showErrorSnackBar('حدث خطأ أثناء حفظ الصلاحيات');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء حفظ الصلاحيات');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _togglePermission(String key, bool value) {
    setState(() {
      _permissions[key] = value;
    });
  }

  void _toggleGroupPermissions(String groupKey, bool value) {
    setState(() {
      final group = _permissionGroups.firstWhere((g) => g['key'] == groupKey);
      for (final permission in group['permissions']) {
        _permissions[permission['key']] = value;
      }
    });
  }

  bool _isGroupChecked(String groupKey) {
    final group = _permissionGroups.firstWhere((g) => g['key'] == groupKey);
    final permissions = group['permissions'] as List;
    return permissions.every((p) => _permissions[p['key']] == true);
  }

  bool _isGroupPartiallyChecked(String groupKey) {
    final group = _permissionGroups.firstWhere((g) => g['key'] == groupKey);
    final permissions = group['permissions'] as List;
    final checkedCount = permissions.where((p) => _permissions[p['key']] == true).length;
    return checkedCount > 0 && checkedCount < permissions.length;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'صلاحيات ${widget.user.name}',
      ),
      body: _isLoading
          ? const SimpleLoadingIndicator()
          : Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemCount: _permissionGroups.length,
                    itemBuilder: (context, index) {
                      final group = _permissionGroups[index];
                      final isChecked = _isGroupChecked(group['key']);
                      final isPartiallyChecked = _isGroupPartiallyChecked(group['key']);
                      // Usar isPartiallyChecked para mostrar un estado intermedio en el checkbox

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingM,
                          vertical: AppDimensions.paddingS,
                        ),
                        child: Column(
                          children: [
                            // عنوان المجموعة
                            CheckboxListTile(
                              title: Text(
                                group['name'],
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              value: isPartiallyChecked ? null : isChecked,
                              tristate: true,
                              controlAffinity: ListTileControlAffinity.leading,
                              onChanged: (value) {
                                _toggleGroupPermissions(group['key'], value ?? false);
                              },
                              activeColor: AppColors.primary,
                              checkColor: Colors.white,
                              secondary: Icon(
                                _getIconForGroup(group['key']),
                                color: AppColors.primary,
                              ),
                            ),
                            const Divider(height: 1),
                            // الصلاحيات
                            ...List.generate(
                              (group['permissions'] as List).length,
                              (i) {
                                final permission = group['permissions'][i];
                                return Padding(
                                  padding: const EdgeInsets.only(right: 32.0),
                                  child: CheckboxListTile(
                                    title: Text(permission['name']),
                                    value: _permissions[permission['key']] ?? false,
                                    onChanged: (value) {
                                      _togglePermission(permission['key'], value ?? false);
                                    },
                                    controlAffinity: ListTileControlAffinity.leading,
                                    activeColor: AppColors.primary,
                                    dense: true,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                // زر الحفظ
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isSaving ? null : _savePermissions,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isSaving
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text('حفظ الصلاحيات'),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  IconData _getIconForGroup(String groupKey) {
    switch (groupKey) {
      case 'dashboard':
        return Icons.dashboard;
      case 'users':
        return Icons.people;
      case 'customers':
        return Icons.person;
      case 'suppliers':
        return Icons.local_shipping;
      case 'employees':
        return Icons.badge;
      case 'transactions':
        return Icons.account_balance_wallet;
      case 'invoices':
        return Icons.receipt;
      case 'service_requests':
        return Icons.build;
      case 'inventory':
        return Icons.inventory;
      case 'reports':
        return Icons.bar_chart;
      case 'settings':
        return Icons.settings;
      default:
        return Icons.check_box;
    }
  }
}
