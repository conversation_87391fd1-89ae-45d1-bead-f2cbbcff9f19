import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../core/analytics/kpi_calculator.dart';
import '../../../shared/widgets/enhanced_ui_components.dart';
import '../../../shared/widgets/responsive_layout.dart';

class EnhancedDashboardScreen extends StatefulWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  State<EnhancedDashboardScreen> createState() => _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen>
    with TickerProviderStateMixin {
  final KPICalculator _kpiCalculator = KPICalculator();
  
  KPIReport? _currentReport;
  bool _isLoading = false;
  String? _errorMessage;
  
  late AnimationController _refreshAnimationController;
  late Animation<double> _refreshAnimation;
  
  DateTime _selectedStartDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _selectedEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadKPIData();
  }

  void _initializeAnimations() {
    _refreshAnimationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _refreshAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _refreshAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadKPIData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    _refreshAnimationController.repeat();

    try {
      final report = await _kpiCalculator.calculateKPIs(
        startDate: _selectedStartDate,
        endDate: _selectedEndDate,
      );

      setState(() {
        _currentReport = report;
        _isLoading = false;
      });

      if (kDebugMode) {
        print('✅ تم تحميل بيانات لوحة المعلومات بنجاح');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'خطأ في تحميل البيانات: $e';
      });

      if (kDebugMode) {
        print('❌ خطأ في تحميل بيانات لوحة المعلومات: $e');
      }
    } finally {
      _refreshAnimationController.stop();
      _refreshAnimationController.reset();
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _selectedStartDate, end: _selectedEndDate),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedStartDate = picked.start;
        _selectedEndDate = picked.end;
      });
      _loadKPIData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const EnhancedText('لوحة المعلومات المحسنة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          AnimatedBuilder(
            animation: _refreshAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _refreshAnimation.value * 2 * 3.14159,
                child: IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _isLoading ? null : _loadKPIData,
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
          ),
        ],
      ),
      body: ResponsiveContainer(
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: EnhancedLoadingIndicator(
          message: 'جاري تحميل بيانات لوحة المعلومات...',
          size: 60,
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: EnhancedColumn(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            EnhancedText(
              _errorMessage!,
              style: const TextStyle(fontSize: 16, color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            EnhancedButton(
              text: 'إعادة المحاولة',
              onPressed: _loadKPIData,
              icon: Icons.refresh,
              type: ButtonType.elevated,
            ),
          ],
        ),
      );
    }

    if (_currentReport == null) {
      return const Center(
        child: EnhancedText(
          'لا توجد بيانات للعرض',
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadKPIData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: EnhancedColumn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPeriodHeader(),
            const SizedBox(height: 24),
            _buildKPIOverview(),
            const SizedBox(height: 24),
            _buildServiceMetrics(),
            const SizedBox(height: 24),
            _buildFinancialMetrics(),
            const SizedBox(height: 24),
            _buildEmployeeMetrics(),
            const SizedBox(height: 24),
            _buildCustomerMetrics(),
            const SizedBox(height: 24),
            _buildEfficiencyMetrics(),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodHeader() {
    final period = _currentReport!.period;
    final startDate = DateFormat('dd/MM/yyyy').format(period.startDate);
    final endDate = DateFormat('dd/MM/yyyy').format(period.endDate);

    return EnhancedCard(
      backgroundColor: AppColors.primary.withOpacity(0.1),
      child: EnhancedRow(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          EnhancedColumn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const EnhancedText(
                'فترة التقرير',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 4),
              EnhancedText(
                '$startDate - $endDate',
                style: const TextStyle(fontSize: 14),
              ),
              EnhancedText(
                '${period.daysCount} يوم',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          EnhancedIcon(
            Icons.calendar_today,
            color: AppColors.primary,
            size: 32,
            onTap: _selectDateRange,
            semanticsLabel: 'تغيير فترة التقرير',
          ),
        ],
      ),
    );
  }

  Widget _buildKPIOverview() {
    return EnhancedColumn(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const EnhancedText(
          'نظرة عامة على المؤشرات',
          style: AppTextStyles.heading2,
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            _buildKPICard(
              'إجمالي الطلبات',
              _currentReport!.serviceKPIs.totalRequests.toString(),
              Icons.assignment,
              Colors.blue,
            ),
            _buildKPICard(
              'الطلبات المكتملة',
              _currentReport!.serviceKPIs.completedRequests.toString(),
              Icons.check_circle,
              Colors.green,
            ),
            _buildKPICard(
              'إجمالي الإيرادات',
              '${_currentReport!.financialKPIs.totalRevenue.toStringAsFixed(0)} ر.س',
              Icons.attach_money,
              Colors.orange,
            ),
            _buildKPICard(
              'صافي الربح',
              '${_currentReport!.financialKPIs.totalProfit.toStringAsFixed(0)} ر.س',
              Icons.trending_up,
              _currentReport!.financialKPIs.totalProfit >= 0 ? Colors.green : Colors.red,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKPICard(String title, String value, IconData icon, Color color) {
    return EnhancedCard(
      child: EnhancedColumn(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          EnhancedIcon(
            icon,
            size: 32,
            color: color,
            semanticsLabel: title,
          ),
          const SizedBox(height: 8),
          EnhancedText(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          EnhancedText(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceMetrics() {
    final serviceKPIs = _currentReport!.serviceKPIs;
    
    return EnhancedCard(
      child: EnhancedColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const EnhancedText(
            'مؤشرات الخدمات',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          _buildMetricRow(
            'معدل الإنجاز',
            '${serviceKPIs.completionRate.toStringAsFixed(1)}%',
            Icons.pie_chart,
          ),
          _buildMetricRow(
            'متوسط وقت الإنجاز',
            '${serviceKPIs.averageCompletionTime.toStringAsFixed(1)} ساعة',
            Icons.schedule,
          ),
          _buildMetricRow(
            'الإنجاز في الوقت المحدد',
            '${serviceKPIs.onTimeCompletionRate.toStringAsFixed(1)}%',
            Icons.timer,
          ),
          _buildMetricRow(
            'رضا العملاء',
            '${serviceKPIs.customerSatisfactionRate.toStringAsFixed(1)}%',
            Icons.sentiment_satisfied,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialMetrics() {
    final financialKPIs = _currentReport!.financialKPIs;
    
    return EnhancedCard(
      child: EnhancedColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const EnhancedText(
            'المؤشرات المالية',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          _buildMetricRow(
            'إجمالي الإيرادات',
            '${financialKPIs.totalRevenue.toStringAsFixed(0)} ر.س',
            Icons.trending_up,
          ),
          _buildMetricRow(
            'إجمالي المصروفات',
            '${financialKPIs.totalExpenses.toStringAsFixed(0)} ر.س',
            Icons.trending_down,
          ),
          _buildMetricRow(
            'هامش الربح',
            '${financialKPIs.profitMargin.toStringAsFixed(1)}%',
            Icons.percent,
          ),
          _buildMetricRow(
            'التدفق النقدي',
            '${financialKPIs.cashFlow.toStringAsFixed(0)} ر.س',
            Icons.account_balance_wallet,
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeMetrics() {
    final employeeKPIs = _currentReport!.employeeKPIs;
    
    return EnhancedCard(
      child: EnhancedColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const EnhancedText(
            'مؤشرات الموظفين',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          _buildMetricRow(
            'إجمالي الموظفين',
            employeeKPIs.totalEmployees.toString(),
            Icons.people,
          ),
          _buildMetricRow(
            'الموظفين النشطين',
            employeeKPIs.activeEmployees.toString(),
            Icons.person,
          ),
          _buildMetricRow(
            'متوسط الإنتاجية',
            '${employeeKPIs.averageProductivity.toStringAsFixed(1)} خدمة/موظف',
            Icons.bar_chart,
          ),
          _buildMetricRow(
            'تكلفة الموظفين',
            '${employeeKPIs.totalEmployeeCost.toStringAsFixed(0)} ر.س',
            Icons.money_off,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerMetrics() {
    final customerKPIs = _currentReport!.customerKPIs;
    
    return EnhancedCard(
      child: EnhancedColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const EnhancedText(
            'مؤشرات العملاء',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          _buildMetricRow(
            'إجمالي العملاء',
            customerKPIs.totalCustomers.toString(),
            Icons.group,
          ),
          _buildMetricRow(
            'العملاء النشطين',
            customerKPIs.activeCustomers.toString(),
            Icons.person_outline,
          ),
          _buildMetricRow(
            'العملاء الجدد',
            customerKPIs.newCustomers.toString(),
            Icons.person_add,
          ),
          _buildMetricRow(
            'متوسط قيمة العميل',
            '${customerKPIs.averageCustomerValue.toStringAsFixed(0)} ر.س',
            Icons.attach_money,
          ),
        ],
      ),
    );
  }

  Widget _buildEfficiencyMetrics() {
    final efficiencyKPIs = _currentReport!.efficiencyKPIs;
    
    return EnhancedCard(
      child: EnhancedColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const EnhancedText(
            'مؤشرات الكفاءة',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          _buildMetricRow(
            'استخدام الموارد',
            '${efficiencyKPIs.resourceUtilization.toStringAsFixed(1)}%',
            Icons.speed,
          ),
          _buildMetricRow(
            'التكلفة لكل خدمة',
            '${efficiencyKPIs.costPerService.toStringAsFixed(0)} ر.س',
            Icons.calculate,
          ),
          _buildMetricRow(
            'الإيراد لكل موظف',
            '${efficiencyKPIs.revenuePerEmployee.toStringAsFixed(0)} ر.س',
            Icons.person_pin_circle,
          ),
          _buildMetricRow(
            'معدل إكمال الخدمات',
            '${efficiencyKPIs.serviceCompletionRate.toStringAsFixed(1)}%',
            Icons.done_all,
          ),
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: EnhancedRow(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          EnhancedRow(
            children: [
              EnhancedIcon(
                icon,
                size: 20,
                color: AppColors.primary,
                semanticsLabel: label,
              ),
              const SizedBox(width: 8),
              EnhancedText(
                label,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
          EnhancedText(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
