import 'package:flutter/material.dart';
import '../../core/localization/app_localizations.dart';
import '../../core/error_handling/error_handler.dart';

/// Enhanced feedback widgets with Arabic support
class FeedbackWidgets {
  /// Show confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    Color? confirmColor,
    IconData? icon,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: confirmColor ?? Theme.of(context).colorScheme.primary),
              const SizedBox(width: 8),
            ],
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText ?? localizations.translate('cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: confirmColor != null
                ? ElevatedButton.styleFrom(backgroundColor: confirmColor)
                : null,
            child: Text(confirmText ?? localizations.translate('confirm')),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  /// Show delete confirmation dialog
  static Future<bool> showDeleteConfirmation(
    BuildContext context, {
    required String itemName,
    String? customMessage,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    
    return await showConfirmationDialog(
      context,
      title: localizations.translate('confirm_delete'),
      message: customMessage ?? localizations.translateWithParams(
        'confirm_delete_item',
        {'item': itemName},
      ),
      confirmText: localizations.translate('delete'),
      confirmColor: Colors.red,
      icon: Icons.delete_forever,
    );
  }

  /// Show input dialog
  static Future<String?> showInputDialog(
    BuildContext context, {
    required String title,
    String? message,
    String? initialValue,
    String? hintText,
    String? confirmText,
    String? cancelText,
    TextInputType? keyboardType,
    int? maxLines,
    String? Function(String?)? validator,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    final controller = TextEditingController(text: initialValue);
    final formKey = GlobalKey<FormState>();
    
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (message != null) ...[
              Text(message),
              const SizedBox(height: 16),
            ],
            Form(
              key: formKey,
              child: TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: const OutlineInputBorder(),
                ),
                keyboardType: keyboardType,
                maxLines: maxLines ?? 1,
                validator: validator,
                autofocus: true,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(cancelText ?? localizations.translate('cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context, controller.text);
              }
            },
            child: Text(confirmText ?? localizations.translate('ok')),
          ),
        ],
      ),
    );
    
    controller.dispose();
    return result;
  }

  /// Show selection dialog
  static Future<T?> showSelectionDialog<T>(
    BuildContext context, {
    required String title,
    required List<SelectionItem<T>> items,
    T? selectedValue,
    String? cancelText,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    
    return await showDialog<T>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              final isSelected = item.value == selectedValue;
              
              return ListTile(
                leading: item.icon != null ? Icon(item.icon) : null,
                title: Text(item.title),
                subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
                selected: isSelected,
                onTap: () => Navigator.pop(context, item.value),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(cancelText ?? localizations.translate('cancel')),
          ),
        ],
      ),
    );
  }

  /// Show multi-selection dialog
  static Future<List<T>?> showMultiSelectionDialog<T>(
    BuildContext context, {
    required String title,
    required List<SelectionItem<T>> items,
    List<T>? selectedValues,
    String? confirmText,
    String? cancelText,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    final selected = List<T>.from(selectedValues ?? []);
    
    return await showDialog<List<T>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(title),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = selected.contains(item.value);
                
                return CheckboxListTile(
                  secondary: item.icon != null ? Icon(item.icon) : null,
                  title: Text(item.title),
                  subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                  value: isSelected,
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        selected.add(item.value);
                      } else {
                        selected.remove(item.value);
                      }
                    });
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(cancelText ?? localizations.translate('cancel')),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, selected),
              child: Text(confirmText ?? localizations.translate('ok')),
            ),
          ],
        ),
      ),
    );
  }

  /// Show date picker dialog
  static Future<DateTime?> showDatePickerDialog(
    BuildContext context, {
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    String? helpText,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    
    return await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(1900),
      lastDate: lastDate ?? DateTime(2100),
      helpText: helpText,
      locale: Locale(localizations.locale.languageCode),
    );
  }

  /// Show time picker dialog
  static Future<TimeOfDay?> showTimePickerDialog(
    BuildContext context, {
    TimeOfDay? initialTime,
    String? helpText,
  }) async {
    return await showTimePicker(
      context: context,
      initialTime: initialTime ?? TimeOfDay.now(),
      helpText: helpText,
    );
  }

  /// Show progress dialog
  static void showProgressDialog(
    BuildContext context, {
    required String title,
    required Stream<double> progressStream,
    String? cancelText,
    VoidCallback? onCancel,
  }) {
    final localizations = AppLocalizations.of(context)!;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            StreamBuilder<double>(
              stream: progressStream,
              builder: (context, snapshot) {
                final progress = snapshot.data ?? 0.0;
                return Column(
                  children: [
                    LinearProgressIndicator(value: progress),
                    const SizedBox(height: 8),
                    Text('${(progress * 100).toInt()}%'),
                  ],
                );
              },
            ),
          ],
        ),
        actions: [
          if (onCancel != null)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                onCancel();
              },
              child: Text(cancelText ?? localizations.translate('cancel')),
            ),
        ],
      ),
    );
  }

  /// Show bottom sheet with options
  static Future<T?> showOptionsBottomSheet<T>(
    BuildContext context, {
    required String title,
    required List<BottomSheetOption<T>> options,
  }) async {
    return await showModalBottomSheet<T>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
          const Divider(height: 1),
          ...options.map((option) => ListTile(
            leading: option.icon != null ? Icon(option.icon) : null,
            title: Text(option.title),
            subtitle: option.subtitle != null ? Text(option.subtitle!) : null,
            onTap: () => Navigator.pop(context, option.value),
          )),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// Show loading overlay
  static OverlayEntry showLoadingOverlay(
    BuildContext context, {
    String? message,
  }) {
    final localizations = AppLocalizations.of(context)!;
    
    final overlay = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black54,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  message ?? localizations.translate('loading'),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
      ),
    );
    
    Overlay.of(context).insert(overlay);
    return overlay;
  }
}

/// Selection item for dialogs
class SelectionItem<T> {
  final T value;
  final String title;
  final String? subtitle;
  final IconData? icon;

  SelectionItem({
    required this.value,
    required this.title,
    this.subtitle,
    this.icon,
  });
}

/// Bottom sheet option
class BottomSheetOption<T> {
  final T value;
  final String title;
  final String? subtitle;
  final IconData? icon;

  BottomSheetOption({
    required this.value,
    required this.title,
    this.subtitle,
    this.icon,
  });
}

/// Loading button widget
class LoadingButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Widget? icon;
  final ButtonStyle? style;

  const LoadingButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  icon!,
                  const SizedBox(width: 8),
                ],
                Text(text),
              ],
            ),
    );
  }
}
