import 'dart:convert';
import 'package:flutter/material.dart';

class CategoryModel {
  final int id;
  final String name;
  final String type; // 'income' or 'expense'
  final IconData icon;
  final Color color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? description;

  CategoryModel({
    required this.id,
    required this.name,
    required this.type,
    required this.icon,
    required this.color,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.description,
  });

  factory CategoryModel.fromMap(Map<String, dynamic> map) {
    // Default icon and color if not in database
    final IconData defaultIcon = map['type'] == 'income' ? Icons.arrow_upward : Icons.arrow_downward;
    final Color defaultColor = map['type'] == 'income' ? Colors.green : Colors.red;

    return CategoryModel(
      id: map['id'] is int ? map['id'] as int : int.parse(map['id'].toString()),
      name: map['name'] as String,
      type: map['type'] as String,
      icon: map['icon_code'] != null
          ? IconData(map['icon_code'] as int, fontFamily: 'MaterialIcons')
          : defaultIcon,
      color: map['color'] != null
          ? Color(map['color'] as int)
          : defaultColor,
      isActive: map['status'] is String
          ? (map['status'] as String) == 'active'
          : map['is_active'] is bool
              ? map['is_active'] as bool
              : (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] is DateTime
          ? map['created_at'] as DateTime
          : DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? map['updated_at'] is DateTime
              ? map['updated_at'] as DateTime
              : DateTime.parse(map['updated_at'] as String)
          : null,
      description: map['description'] as String?,
    );
  }

  factory CategoryModel.fromJson(String source) =>
      CategoryModel.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'icon_code': icon.codePoint,
      'color': color.value,
      'status': isActive ? 'active' : 'inactive',
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'description': description,
    };
  }

  String toJson() => json.encode(toMap());

  CategoryModel copyWith({
    int? id,
    String? name,
    String? type,
    IconData? icon,
    Color? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? description,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      description: description ?? this.description,
    );
  }

  // Helper methods
  bool get isIncome => type == 'income';
  bool get isExpense => type == 'expense';

  // Static methods for mock data
  static List<CategoryModel> getMockIncomeCategories() {
    return [
      CategoryModel(
        id: 1,
        name: 'مبيعات',
        type: 'income',
        icon: Icons.shopping_cart,
        color: Colors.blue,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 2,
        name: 'خدمات',
        type: 'income',
        icon: Icons.miscellaneous_services,
        color: Colors.green,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 3,
        name: 'استرداد',
        type: 'income',
        icon: Icons.assignment_return,
        color: Colors.orange,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 4,
        name: 'استثمارات',
        type: 'income',
        icon: Icons.trending_up,
        color: Colors.purple,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 5,
        name: 'إيرادات أخرى',
        type: 'income',
        icon: Icons.attach_money,
        color: Colors.teal,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
    ];
  }

  static List<CategoryModel> getMockExpenseCategories() {
    return [
      CategoryModel(
        id: 6,
        name: 'مشتريات',
        type: 'expense',
        icon: Icons.shopping_bag,
        color: Colors.red,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 7,
        name: 'رواتب',
        type: 'expense',
        icon: Icons.people,
        color: Colors.deepOrange,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 8,
        name: 'إيجار',
        type: 'expense',
        icon: Icons.home,
        color: Colors.brown,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 9,
        name: 'مرافق',
        type: 'expense',
        icon: Icons.power,
        color: Colors.indigo,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 10,
        name: 'صيانة',
        type: 'expense',
        icon: Icons.build,
        color: Colors.amber,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 11,
        name: 'وقود',
        type: 'expense',
        icon: Icons.local_gas_station,
        color: Colors.deepPurple,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 12,
        name: 'أدوات',
        type: 'expense',
        icon: Icons.handyman,
        color: Colors.lightBlue,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 13,
        name: 'تسويق',
        type: 'expense',
        icon: Icons.campaign,
        color: Colors.pink,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 14,
        name: 'تأمين',
        type: 'expense',
        icon: Icons.security,
        color: Colors.cyan,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 15,
        name: 'ضرائب',
        type: 'expense',
        icon: Icons.account_balance,
        color: Colors.blueGrey,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 16,
        name: 'مصروفات أخرى',
        type: 'expense',
        icon: Icons.money_off,
        color: Colors.grey,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      CategoryModel(
        id: 17,
        name: 'سداد للمورد',
        type: 'expense',
        icon: Icons.store,
        color: Colors.deepPurple,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
    ];
  }

  static List<CategoryModel> getAllMockCategories() {
    return [...getMockIncomeCategories(), ...getMockExpenseCategories()];
  }
}
