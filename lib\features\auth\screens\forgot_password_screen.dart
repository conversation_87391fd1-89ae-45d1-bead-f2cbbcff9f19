import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../state/auth_state.dart';
import '../widgets/auth_header.dart';
import '../../../shared/widgets/custom_button.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;
  bool _isSuccess = false;
  String? _errorMessage;
  
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('نسيت كلمة المرور'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 450,
                minHeight: size.height - 2 * AppDimensions.paddingL - kToolbarHeight,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(
                    title: 'نسيت كلمة المرور؟',
                    subtitle: 'أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور',
                  ),
                  const SizedBox(height: AppDimensions.paddingL),
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingL),
                      child: _isSuccess
                          ? _buildSuccessMessage()
                          : _buildForgotPasswordForm(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildForgotPasswordForm() {
    return FormBuilder(
      key: _formKey,
      autovalidateMode: AutovalidateMode.disabled,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email field
          FormBuilderTextField(
            name: 'email',
            decoration: const InputDecoration(
              labelText: 'البريد الإلكتروني',
              prefixIcon: Icon(Icons.email),
              hintText: 'أدخل البريد الإلكتروني',
            ),
            textDirection: TextDirection.ltr,
            keyboardType: TextInputType.emailAddress,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: 'يرجى إدخال البريد الإلكتروني',
              ),
              FormBuilderValidators.email(
                errorText: 'يرجى إدخال بريد إلكتروني صحيح',
              ),
            ]),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          
          // Error message
          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(
                top: AppDimensions.paddingS,
              ),
              child: Text(
                _errorMessage!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          
          const SizedBox(height: AppDimensions.paddingL),
          
          // Send reset link button
          CustomButton(
            text: 'إرسال رابط إعادة التعيين',
            isLoading: _isLoading,
            onPressed: _isLoading ? null : _handleForgotPassword,
          ),
          
          const SizedBox(height: AppDimensions.paddingM),
          
          // Back to login link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'تذكرت كلمة المرور؟',
                style: TextStyle(
                  color: AppColors.textSecondary,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pushReplacementNamed(
                    context,
                    AppRoutes.login,
                  );
                },
                child: const Text('تسجيل الدخول'),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildSuccessMessage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(
          Icons.check_circle_outline,
          color: AppColors.success,
          size: 64,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        const Text(
          'تم إرسال رابط إعادة التعيين',
          style: AppTextStyles.heading2,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        const Text(
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من بريدك الإلكتروني واتباع التعليمات.',
          style: AppTextStyles.body1,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.paddingL),
        CustomButton(
          text: 'العودة إلى تسجيل الدخول',
          onPressed: () {
            Navigator.pushReplacementNamed(
              context,
              AppRoutes.login,
            );
          },
        ),
      ],
    );
  }
  
  Future<void> _handleForgotPassword() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
      
      final formData = _formKey.currentState!.value;
      final email = formData['email'] as String;
      
      final authState = Provider.of<AuthState>(context, listen: false);
      final success = await authState.forgotPassword(email);
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isSuccess = success;
          if (!success) {
            _errorMessage = 'حدث خطأ أثناء إرسال رابط إعادة التعيين. يرجى المحاولة مرة أخرى.';
          }
        });
      }
    }
  }
}
