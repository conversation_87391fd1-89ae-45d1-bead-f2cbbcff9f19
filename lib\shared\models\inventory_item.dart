import 'dart:convert';
import 'package:flutter/material.dart';

class InventoryItem {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final String? category;
  final double quantity;
  final double minQuantity;
  final String? unit;
  final double costPrice;
  final double sellingPrice;
  final int? supplierId;
  final String? supplierName;
  final String? location;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  InventoryItem({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.category,
    required this.quantity,
    required this.minQuantity,
    this.unit,
    required this.costPrice,
    required this.sellingPrice,
    this.supplierId,
    this.supplierName,
    this.location,
    required this.isActive,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  factory InventoryItem.fromMap(Map<String, dynamic> map) {
    return InventoryItem(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      category: map['category'] as String?,
      quantity: (map['quantity'] as num).toDouble(),
      minQuantity: (map['min_quantity'] as num).toDouble(),
      unit: map['unit'] as String?,
      costPrice: map['cost_price'] as double,
      sellingPrice: map['selling_price'] as double,
      supplierId: map['supplier_id'] as int?,
      supplierName: map['supplier_name'] as String?,
      location: map['location'] as String?,
      isActive: map['is_active'] as bool,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory InventoryItem.fromJson(String source) =>
      InventoryItem.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'category': category,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'unit': unit,
      'cost_price': costPrice,
      'selling_price': sellingPrice,
      'supplier_id': supplierId,
      'supplier_name': supplierName,
      'location': location,
      'is_active': isActive,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  InventoryItem copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    String? category,
    double? quantity,
    double? minQuantity,
    String? unit,
    double? costPrice,
    double? sellingPrice,
    int? supplierId,
    String? supplierName,
    String? location,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      unit: unit ?? this.unit,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      supplierId: supplierId ?? this.supplierId,
      supplierName: supplierName ?? this.supplierName,
      location: location ?? this.location,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isLowStock => quantity <= minQuantity;

  Color getStockStatusColor() {
    if (quantity <= 0) {
      return Colors.red;
    } else if (isLowStock) {
      return Colors.amber;
    } else {
      return Colors.green;
    }
  }

  String getStockStatusText() {
    if (quantity <= 0) {
      return 'نفذت الكمية';
    } else if (isLowStock) {
      return 'كمية منخفضة';
    } else {
      return 'متوفر';
    }
  }
}
