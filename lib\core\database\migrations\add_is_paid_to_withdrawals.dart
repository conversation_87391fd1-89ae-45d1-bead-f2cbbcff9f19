import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// Migration to add is_paid field to withdrawals table
class AddIsPaidToWithdrawals {
  /// Apply the migration to the database
  static Future<void> migrate(Database db) async {
    try {
      // Check if is_paid column already exists
      final List<Map<String, dynamic>> columns = await db.rawQuery(
        "PRAGMA table_info(withdrawals)"
      );
      
      final bool columnExists = columns.any((column) => column['name'] == 'is_paid');
      
      if (!columnExists) {
        if (kDebugMode) {
          print('Adding is_paid column to withdrawals table');
        }
        
        // Add is_paid column to withdrawals table
        await db.execute(
          'ALTER TABLE withdrawals ADD COLUMN is_paid INTEGER NOT NULL DEFAULT 0'
        );
        
        // Add salary_id column to withdrawals table to track which salary paid this withdrawal
        await db.execute(
          'ALTER TABLE withdrawals ADD COLUMN salary_id INTEGER'
        );
        
        if (kDebugMode) {
          print('Migration completed successfully');
        }
      } else {
        if (kDebugMode) {
          print('is_paid column already exists in withdrawals table');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during migration: $e');
      }
      rethrow;
    }
  }
}
