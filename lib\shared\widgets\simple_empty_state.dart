import 'package:flutter/material.dart';

/// Widget para mostrar un estado vacío simplificado
class SimpleEmptyState extends StatelessWidget {
  /// Mensaje a mostrar
  final String message;
  
  /// Función a ejecutar al refrescar
  final VoidCallback? onRefresh;
  
  /// Icono a mostrar (opcional)
  final IconData icon;
  
  /// Título a mostrar (opcional)
  final String title;

  /// Constructor
  const SimpleEmptyState({
    super.key,
    required this.message,
    this.onRefresh,
    this.icon = Icons.info_outline,
    this.title = 'لا توجد بيانات',
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRefresh != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
