import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/service_request.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/service_request_card.dart';
import '../../../core/repositories/service_request_repository.dart';

class ServiceListScreen extends StatefulWidget {
  const ServiceListScreen({super.key});

  @override
  State<ServiceListScreen> createState() => _ServiceListScreenState();
}

class _ServiceListScreenState extends State<ServiceListScreen> {
  bool _isLoading = true;
  List<ServiceRequest> _serviceRequests = [];
  String _searchQuery = '';
  String _statusFilter = 'all';

  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();

  @override
  void initState() {
    super.initState();
    _loadServiceRequests();
  }

  Future<void> _loadServiceRequests() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام ServiceRequestRepository للحصول على طلبات الخدمة من قاعدة البيانات
      final serviceRequests = await _serviceRequestRepository.getAllServiceRequests();

      if (mounted) {
        setState(() {
          _serviceRequests = serviceRequests;
          _isLoading = false;
        });
      }

      // إذا لم يكن هناك طلبات خدمة، استخدم بيانات وهمية للعرض
      if (serviceRequests.isEmpty && mounted) {
        setState(() {
          _serviceRequests = [
            ServiceRequest(
              id: 1,
              reference: 'SR-2023-001',
              customerName: 'أحمد محمد',
              customerPhone: '0501234567',
              requestType: ServiceRequestType.maintenance,
              description: 'تكييف لا يبرد بشكل جيد',
              priority: ServiceRequestPriority.high,
              status: ServiceRequestStatus.completed,
              location: 'الرياض - حي النزهة',
              assignedTo: 1,
              assignedToName: 'محمد علي',
              scheduledDate: DateTime.now().subtract(const Duration(days: 5)),
              completedDate: DateTime.now().subtract(const Duration(days: 3)),
              solution: 'تم تنظيف الفلتر وإعادة شحن الفريون',
              createdAt: DateTime.now().subtract(const Duration(days: 7)),
            ),
            ServiceRequest(
              id: 2,
              reference: 'SR-2023-002',
              customerName: 'شركة الرياض للتكييف',
              customerPhone: '0112345678',
              requestType: ServiceRequestType.installation,
              description: 'تركيب تكييف سبليت جديد',
              priority: ServiceRequestPriority.medium,
              status: ServiceRequestStatus.inProgress,
              location: 'الرياض - حي الملز',
              assignedTo: 2,
              assignedToName: 'خالد عبدالله',
              scheduledDate: DateTime.now().subtract(const Duration(days: 2)),
              createdAt: DateTime.now().subtract(const Duration(days: 3)),
            ),
            ServiceRequest(
              id: 3,
              reference: 'SR-2023-003',
              customerName: 'سارة أحمد',
              customerPhone: '0567891234',
              requestType: ServiceRequestType.inspection,
              description: 'فحص دوري للتكييف',
              priority: ServiceRequestPriority.low,
              status: ServiceRequestStatus.pending,
              location: 'الرياض - حي الورود',
              scheduledDate: DateTime.now().add(const Duration(days: 2)),
              createdAt: DateTime.now().subtract(const Duration(days: 1)),
            ),
          ];
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading service requests: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;

          // في حالة حدوث خطأ، استخدم بيانات وهمية للعرض
          _serviceRequests = [
            ServiceRequest(
              id: 1,
              reference: 'SR-2023-001',
              customerName: 'أحمد محمد',
              customerPhone: '0501234567',
              requestType: ServiceRequestType.maintenance,
              description: 'تكييف لا يبرد بشكل جيد',
              priority: ServiceRequestPriority.high,
              status: ServiceRequestStatus.completed,
              location: 'الرياض - حي النزهة',
              assignedTo: 1,
              assignedToName: 'محمد علي',
              scheduledDate: DateTime.now().subtract(const Duration(days: 5)),
              completedDate: DateTime.now().subtract(const Duration(days: 3)),
              solution: 'تم تنظيف الفلتر وإعادة شحن الفريون',
              createdAt: DateTime.now().subtract(const Duration(days: 7)),
            ),
          ];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل طلبات الخدمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<ServiceRequest> get _filteredServiceRequests {
    List<ServiceRequest> filtered = _serviceRequests;

    // Apply status filter
    if (_statusFilter != 'all') {
      filtered = filtered.where((request) {
        return request.status.toString().split('.').last == _statusFilter;
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((request) {
        final query = _searchQuery.toLowerCase();
        return request.reference.toLowerCase().contains(query) ||
            request.customerName.toLowerCase().contains(query) ||
            ServiceRequest.getRequestTypeName(request.requestType).toLowerCase().contains(query) ||
            (request.description.toLowerCase().contains(query));
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلبات الخدمة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            tooltip: 'تقرير طلبات الخدمة',
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRoutes.serviceRequestReport,
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن طلب خدمة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          _buildStatusFilterChips(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredServiceRequests.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد طلبات خدمة',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadServiceRequests,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredServiceRequests.length,
                          itemBuilder: (context, index) {
                            final request = _filteredServiceRequests[index];
                            return ServiceRequestCard(
                              serviceRequest: request,
                              onTap: () {
                                _showServiceRequestActions(context, request);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add service request screen
          Navigator.pushNamed(context, AppRoutes.serviceRequestAdd).then((_) {
            _loadServiceRequests();
          });
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Row(
        children: [
          _buildFilterChip('all', 'الكل'),
          const SizedBox(width: 8),
          _buildFilterChip('pending', 'معلق'),
          const SizedBox(width: 8),
          _buildFilterChip('inProgress', 'قيد التنفيذ'),
          const SizedBox(width: 8),
          _buildFilterChip('completed', 'مكتمل'),
          const SizedBox(width: 8),
          _buildFilterChip('cancelled', 'ملغى'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _statusFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _statusFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.white,
      selectedColor: AppColors.primary.withAlpha(50),
      checkmarkColor: AppColors.primary,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primary : AppColors.textPrimary,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية طلبات الخدمة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Date range filter would go here
              const Text('فلترة حسب التاريخ'),
              // Priority filter would go here
              const Text('فلترة حسب الأولوية'),
              // Technician filter would go here
              const Text('فلترة حسب الموظف الفني'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filters
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _showServiceRequestActions(BuildContext context, ServiceRequest request) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.paddingL,
            horizontal: AppDimensions.paddingM,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'طلب خدمة ${request.reference}',
                style: AppTextStyles.heading2,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                request.customerName,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.visibility),
                title: const Text('عرض التفاصيل'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to service request details screen
                  Navigator.pushNamed(
                    context,
                    AppRoutes.serviceRequestDetails,
                    arguments: request,
                  );
                },
              ),
              if (request.status != ServiceRequestStatus.completed &&
                  request.status != ServiceRequestStatus.cancelled)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('تعديل الطلب'),
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to edit service request screen
                    Navigator.pushNamed(
                      context,
                      AppRoutes.serviceRequestEdit,
                      arguments: request,
                    ).then((updatedRequest) {
                      if (updatedRequest != null) {
                        setState(() {
                          final index = _serviceRequests.indexWhere((r) => r.id == request.id);
                          if (index != -1) {
                            _serviceRequests[index] = updatedRequest as ServiceRequest;
                          }
                        });
                      }
                    });
                  },
                ),
              if (request.status == ServiceRequestStatus.pending)
                ListTile(
                  leading: const Icon(Icons.play_arrow),
                  title: const Text('بدء العمل'),
                  onTap: () {
                    Navigator.pop(context);
                    // Update status to in progress
                    _updateServiceRequestStatus(
                      request,
                      ServiceRequestStatus.inProgress,
                    );
                  },
                ),
              if (request.status == ServiceRequestStatus.inProgress)
                ListTile(
                  leading: const Icon(Icons.check_circle),
                  title: const Text('إكمال الطلب'),
                  onTap: () {
                    Navigator.pop(context);
                    // Show complete dialog
                    _showCompleteServiceRequestDialog(context, request);
                  },
                ),
              if (request.status != ServiceRequestStatus.completed &&
                  request.status != ServiceRequestStatus.cancelled)
                ListTile(
                  leading: const Icon(Icons.cancel, color: Colors.red),
                  title: const Text(
                    'إلغاء الطلب',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // Show cancel confirmation
                    _showCancelConfirmation(context, request);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _showCompleteServiceRequestDialog(BuildContext context, ServiceRequest request) {
    final formKey = GlobalKey<FormState>();
    String solution = '';
    String? technicalNotes;
    List<String> usedParts = [];
    String currentPart = '';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('إكمال الطلب ${request.reference}'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الحل / الإجراء المتخذ',
                      prefixIcon: Icon(Icons.build),
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الحل أو الإجراء المتخذ';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      solution = value!;
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'قطع الغيار المستخدمة:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  // Lista de piezas usadas
                  if (usedParts.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        children: [
                          ...usedParts.map((part) => Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4),
                                child: Row(
                                  children: [
                                    const Icon(Icons.build, size: 16),
                                    const SizedBox(width: 8),
                                    Expanded(child: Text(part)),
                                    IconButton(
                                      icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                                      onPressed: () {
                                        usedParts.remove(part);
                                        (context as Element).markNeedsBuild();
                                      },
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    ),
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ),
                  const SizedBox(height: 8),
                  // Campo para agregar nueva pieza
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'إضافة قطعة غيار',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          onChanged: (value) {
                            currentPart = value;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          if (currentPart.isNotEmpty) {
                            usedParts.add(currentPart);
                            currentPart = '';
                            (context as Element).markNeedsBuild();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.all(12),
                          minimumSize: const Size(0, 0),
                        ),
                        child: const Icon(Icons.add, size: 20),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات فنية (اختياري)',
                      prefixIcon: Icon(Icons.note),
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                    onSaved: (value) {
                      technicalNotes = value;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // Update service request
                  setState(() {
                    final index = _serviceRequests.indexWhere((r) => r.id == request.id);
                    if (index != -1) {
                      _serviceRequests[index] = request.copyWith(
                        status: ServiceRequestStatus.completed,
                        solution: solution,
                        usedParts: usedParts.isNotEmpty ? usedParts : null,
                        technicalNotes: technicalNotes,
                        completedDate: DateTime.now(),
                      );
                    }
                  });

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم إكمال الطلب ${request.reference} بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('إكمال'),
            ),
          ],
        );
      },
    );
  }

  void _updateServiceRequestStatus(ServiceRequest request, ServiceRequestStatus status) {
    setState(() {
      final index = _serviceRequests.indexWhere((r) => r.id == request.id);
      if (index != -1) {
        _serviceRequests[index] = request.copyWith(
          status: status,
        );
      }
    });

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تحديث حالة الطلب ${request.reference} إلى ${ServiceRequest.getStatusName(status)}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showCancelConfirmation(BuildContext context, ServiceRequest request) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الإلغاء'),
          content: Text(
            'هل أنت متأكد من رغبتك في إلغاء الطلب ${request.reference}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('لا'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);

                // Update service request status
                _updateServiceRequestStatus(request, ServiceRequestStatus.cancelled);
              },
              child: const Text(
                'نعم، إلغاء الطلب',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }


}
