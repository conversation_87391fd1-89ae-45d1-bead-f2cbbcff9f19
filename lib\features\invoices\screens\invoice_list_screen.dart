import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/invoice.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/models/transaction.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/models/bank_account.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/invoice_card.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../core/repositories/bank_account_repository.dart';

class InvoiceListScreen extends StatefulWidget {
  const InvoiceListScreen({super.key});

  @override
  State<InvoiceListScreen> createState() => _InvoiceListScreenState();
}

class _InvoiceListScreenState extends State<InvoiceListScreen> {
  bool _isLoading = true;
  List<Invoice> _invoices = [];
  String _searchQuery = '';
  String _statusFilter = 'all';

  final InvoiceRepository _invoiceRepository = InvoiceRepository();

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام InvoiceRepository للحصول على الفواتير من قاعدة البيانات
      final invoices = await _invoiceRepository.getAllInvoices();

      if (mounted) {
        setState(() {
          _invoices = invoices;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading invoices: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;

          // في حالة حدوث خطأ، استخدم بيانات وهمية للعرض
          final mockCustomers = [
            Customer(
              localId: 1,
              name: 'شركة الرياض للتكييف',
              email: '<EMAIL>',
              phone: '0112345678',
              type: CustomerType.company,
              createdAt: DateTime.now(),
            ),
            Customer(
              localId: 2,
              name: 'أحمد محمد',
              email: '<EMAIL>',
              phone: '0501234567',
              type: CustomerType.individual,
              createdAt: DateTime.now(),
            ),
          ];

          _invoices = [
            Invoice(
              id: 1,
              invoiceNumber: 'INV-2023-001',
              customer: mockCustomers[0],
              issueDate: DateTime.now().subtract(const Duration(days: 30)),
              dueDate: DateTime.now().subtract(const Duration(days: 15)),
              items: [],
              status: InvoiceStatus.paid,
              subtotal: 5000,
              taxRate: 0.15,
              taxAmount: 750,
              discount: 0,
              total: 5750,
              createdAt: DateTime.now().subtract(const Duration(days: 30)),
            ),
            Invoice(
              id: 2,
              invoiceNumber: 'INV-2023-002',
              customer: mockCustomers[1],
              issueDate: DateTime.now().subtract(const Duration(days: 20)),
              dueDate: DateTime.now().subtract(const Duration(days: 5)),
              items: [],
              status: InvoiceStatus.paid,
              subtotal: 2500,
              taxRate: 0.15,
              taxAmount: 375,
              discount: 200,
              total: 2675,
              createdAt: DateTime.now().subtract(const Duration(days: 20)),
            ),
          ];
        });

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الفواتير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Invoice> get _filteredInvoices {
    List<Invoice> filtered = _invoices;

    // Apply status filter
    if (_statusFilter != 'all') {
      filtered = filtered.where((invoice) {
        if (_statusFilter == 'unpaid') {
          // غير مدفوعة: جميع الفواتير ما عدا المدفوعة والملغاة
          return !invoice.isPaid && invoice.status != InvoiceStatus.cancelled;
        } else if (_statusFilter == 'overdue') {
          // متأخرة: الفواتير غير المدفوعة والتي تجاوزت تاريخ الاستحقاق
          return !invoice.isPaid &&
                 invoice.status != InvoiceStatus.cancelled &&
                 invoice.dueDate.isBefore(DateTime.now());
        } else {
          // الفلاتر العادية حسب الحالة
          return invoice.status.toString().split('.').last == _statusFilter;
        }
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((invoice) {
        final query = _searchQuery.toLowerCase();
        return invoice.invoiceNumber.toLowerCase().contains(query) ||
            invoice.customer.name.toLowerCase().contains(query);
      }).toList();
    }

    // Sort invoices: unpaid and overdue first, then by date
    filtered.sort((a, b) {
      // Priority sorting
      final aIsOverdue = !a.isPaid && a.status != InvoiceStatus.cancelled && a.dueDate.isBefore(DateTime.now());
      final bIsOverdue = !b.isPaid && b.status != InvoiceStatus.cancelled && b.dueDate.isBefore(DateTime.now());
      final aIsUnpaid = !a.isPaid && a.status != InvoiceStatus.cancelled;
      final bIsUnpaid = !b.isPaid && b.status != InvoiceStatus.cancelled;

      // Overdue invoices first
      if (aIsOverdue && !bIsOverdue) return -1;
      if (!aIsOverdue && bIsOverdue) return 1;

      // Then unpaid invoices
      if (aIsUnpaid && !bIsUnpaid) return -1;
      if (!aIsUnpaid && bIsUnpaid) return 1;

      // Finally sort by issue date (newest first)
      return b.issueDate.compareTo(a.issueDate);
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفواتير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            tooltip: 'تقرير الفواتير',
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.invoiceReport);
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن فاتورة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          _buildStatusFilterChips(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredInvoices.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد فواتير',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadInvoices,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredInvoices.length,
                          itemBuilder: (context, index) {
                            final invoice = _filteredInvoices[index];
                            return InvoiceCard(
                              invoice: invoice,
                              onTap: () {
                                _showInvoiceActions(context, invoice);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add invoice screen
          Navigator.pushNamed(context, AppRoutes.addInvoice);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatusFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Row(
        children: [
          _buildFilterChip('all', 'الكل'),
          const SizedBox(width: 8),
          _buildFilterChip('unpaid', 'غير مدفوعة', isSpecial: true),
          const SizedBox(width: 8),
          _buildFilterChip('overdue', 'متأخرة', isSpecial: true),
          const SizedBox(width: 8),
          _buildFilterChip('draft', 'مسودة'),
          const SizedBox(width: 8),
          _buildFilterChip('issued', 'صادرة'),
          const SizedBox(width: 8),
          _buildFilterChip('paid', 'مدفوعة'),
          const SizedBox(width: 8),
          _buildFilterChip('partiallyPaid', 'مدفوعة جزئياً'),
          const SizedBox(width: 8),
          _buildFilterChip('cancelled', 'ملغاة'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label, {bool isSpecial = false}) {
    final isSelected = _statusFilter == value;

    Color chipColor = AppColors.primary;
    if (isSpecial) {
      chipColor = value == 'overdue' ? Colors.red : Colors.orange;
    }

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _statusFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.white,
      selectedColor: chipColor.withAlpha(50),
      checkmarkColor: chipColor,
      side: isSpecial ? BorderSide(color: chipColor, width: 1.5) : null,
      labelStyle: TextStyle(
        color: isSelected ? chipColor : (isSpecial ? chipColor : AppColors.textPrimary),
        fontWeight: isSelected ? FontWeight.bold : (isSpecial ? FontWeight.w600 : FontWeight.normal),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية الفواتير'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Date range filter would go here
              const Text('فلترة حسب التاريخ'),
              // Customer filter would go here
              const Text('فلترة حسب العميل'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filters
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _showInvoiceActions(BuildContext context, Invoice invoice) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.paddingL,
            horizontal: AppDimensions.paddingM,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'فاتورة ${invoice.invoiceNumber}',
                style: AppTextStyles.heading2,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                invoice.customer.name,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.visibility),
                title: const Text('عرض التفاصيل'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to invoice details
                  _showInvoiceDetails(context, invoice);
                },
              ),
              ListTile(
                leading: const Icon(Icons.receipt_long),
                title: const Text('عرض التفاصيل الكاملة'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to full invoice details
                  if (invoice.id != null) {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.invoiceDetails,
                      arguments: invoice.id,
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('لا يمكن عرض التفاصيل الكاملة للفاتورة'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
              ),
              if (invoice.status == InvoiceStatus.draft)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('تعديل الفاتورة'),
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to edit invoice
                  },
                ),
              if (invoice.status == InvoiceStatus.draft || invoice.status == InvoiceStatus.issued)
                ListTile(
                  leading: const Icon(Icons.payment),
                  title: const Text('تسجيل دفعة'),
                  onTap: () {
                    Navigator.pop(context);
                    // Show payment dialog
                    _showPaymentDialog(context, invoice);
                  },
                ),
              ListTile(
                leading: const Icon(Icons.print),
                title: const Text('طباعة الفاتورة'),
                onTap: () {
                  Navigator.pop(context);
                  // Print invoice
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('جاري طباعة الفاتورة...'),
                    ),
                  );
                },
              ),
              if (invoice.status == InvoiceStatus.draft)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'حذف الفاتورة',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // Show delete confirmation
                    _showDeleteConfirmation(context, invoice);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _showInvoiceDetails(BuildContext context, Invoice invoice) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تفاصيل الفاتورة ${invoice.invoiceNumber}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('العميل', invoice.customer.name),
              _buildDetailRow('تاريخ الإصدار', DateFormat('dd/MM/yyyy').format(invoice.issueDate)),
              _buildDetailRow('تاريخ الاستحقاق', DateFormat('dd/MM/yyyy').format(invoice.dueDate)),
              _buildDetailRow('الحالة', Invoice.getStatusName(invoice.status)),
              _buildDetailRow('المجموع الفرعي', '${invoice.subtotal} ر.س'),
              _buildDetailRow('الضريبة (${invoice.taxRate * 100}%)', '${invoice.taxAmount} ر.س'),
              _buildDetailRow('الخصم', '${invoice.discount} ر.س'),
              _buildDetailRow('الإجمالي', '${invoice.total} ر.س'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to full invoice details
                if (invoice.id != null) {
                  Navigator.pushNamed(
                    context,
                    AppRoutes.invoiceDetails,
                    arguments: invoice.id,
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('لا يمكن عرض التفاصيل الكاملة للفاتورة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('عرض التفاصيل الكاملة'),
            ),
          ],
        );
      },
    );
  }

  void _showPaymentDialog(BuildContext context, Invoice invoice) {
    final formKey = GlobalKey<FormState>();
    double amount = 0;
    PaymentMethod paymentMethod = PaymentMethod.cash;
    int? employeeId;
    int? bankAccountId;

    // قوائم للموظفين والحسابات البنكية
    List<Employee> employees = [];
    List<BankAccount> bankAccounts = [];

    // تحميل الموظفين والحسابات البنكية
    final employeeRepository = EmployeeRepository();
    final bankAccountRepository = BankAccountRepository();

    // استخدام Future.wait لتحميل البيانات بشكل متوازي
    Future.wait([
      employeeRepository.getAllEmployees().then((value) => employees = value),
      bankAccountRepository.getAllBankAccounts().then((value) => bankAccounts = value),
    ]).then((_) {
      // تصفية الموظفين النشطين فقط
      employees = employees.where((emp) => emp.status == EmployeeStatus.active).toList();
      // تصفية الحسابات البنكية النشطة فقط
      bankAccounts = bankAccounts.where((acc) => acc.isActive).toList();

      if (mounted) {
        setState(() {
          // تحديث واجهة المستخدم إذا لزم الأمر
        });
      }
    });

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('تسجيل دفعة للفاتورة ${invoice.invoiceNumber}'),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'المبلغ',
                          prefixIcon: Icon(Icons.money),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال المبلغ';
                          }
                          final parsedValue = double.tryParse(value);
                          if (parsedValue == null || parsedValue <= 0) {
                            return 'يرجى إدخال مبلغ صحيح';
                          }
                          if (parsedValue > invoice.total) {
                            return 'المبلغ أكبر من إجمالي الفاتورة';
                          }
                          return null;
                        },
                        onSaved: (value) {
                          amount = double.parse(value!);
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      DropdownButtonFormField<PaymentMethod>(
                        decoration: const InputDecoration(
                          labelText: 'طريقة الدفع',
                          border: OutlineInputBorder(),
                        ),
                        value: paymentMethod,
                        items: [
                          DropdownMenuItem(
                            value: PaymentMethod.cash,
                            child: Row(
                              children: [
                                Icon(Transaction.getPaymentMethodIcon(PaymentMethod.cash), size: 20),
                                const SizedBox(width: 8),
                                Text(Transaction.getPaymentMethodName(PaymentMethod.cash)),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: PaymentMethod.bankTransfer,
                            child: Row(
                              children: [
                                Icon(Transaction.getPaymentMethodIcon(PaymentMethod.bankTransfer), size: 20),
                                const SizedBox(width: 8),
                                Text(Transaction.getPaymentMethodName(PaymentMethod.bankTransfer)),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: PaymentMethod.creditCard,
                            child: Row(
                              children: [
                                Icon(Transaction.getPaymentMethodIcon(PaymentMethod.creditCard), size: 20),
                                const SizedBox(width: 8),
                                Text(Transaction.getPaymentMethodName(PaymentMethod.creditCard)),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            paymentMethod = value!;
                            // إعادة تعيين القيم عند تغيير طريقة الدفع
                            if (paymentMethod != PaymentMethod.cash) {
                              employeeId = null;
                            }
                            if (paymentMethod != PaymentMethod.bankTransfer) {
                              bankAccountId = null;
                            }
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'يرجى اختيار طريقة الدفع';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDimensions.paddingM),

                      // عرض قائمة الموظفين عند اختيار الدفع نقداً
                      if (paymentMethod == PaymentMethod.cash)
                        DropdownButtonFormField<int>(
                          decoration: const InputDecoration(
                            labelText: 'الصندوق ',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.person),
                          ),
                          value: employeeId,
                          items: employees.map((employee) {
                            return DropdownMenuItem<int>(
                              value: employee.id,
                              child: Text(employee.name),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              employeeId = value;
                            });
                          },
                          validator: (value) {
                            if (paymentMethod == PaymentMethod.cash && value == null) {
                              return 'يرجى اختيار الصندوق ';
                            }
                            return null;
                          },
                        ),

                      // عرض قائمة الحسابات البنكية عند اختيار التحويل البنكي
                      if (paymentMethod == PaymentMethod.bankTransfer)
                        DropdownButtonFormField<int>(
                          decoration: const InputDecoration(
                            labelText: 'الحساب البنكي',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.account_balance),
                          ),
                          value: bankAccountId,
                          items: bankAccounts.map((account) {
                            return DropdownMenuItem<int>(
                              value: account.id,
                              child: Text('${account.bankName} - ${account.accountNumber}'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              bankAccountId = value;
                            });
                          },
                          validator: (value) {
                            if (paymentMethod == PaymentMethod.bankTransfer && value == null) {
                              return 'يرجى اختيار الحساب البنكي';
                            }
                            return null;
                          },
                        ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      formKey.currentState!.save();
                      Navigator.pop(context);

                      // استخدام دالة منفصلة لتسجيل الدفعة
                      _recordPayment(
                        invoice: invoice,
                        amount: amount,
                        paymentMethod: paymentMethod,
                        employeeId: employeeId,
                        bankAccountId: bankAccountId,
                      );
                    }
                  },
                  child: const Text('تسجيل'),
                ),
              ],
            );
          }
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, Invoice invoice) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من رغبتك في حذف الفاتورة ${invoice.invoiceNumber}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);

                // استخدام دالة منفصلة للحذف لتجنب مشاكل BuildContext
                _deleteInvoice(invoice);
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _recordPayment({
    required Invoice invoice,
    required double amount,
    required PaymentMethod paymentMethod,
    int? employeeId,
    int? bankAccountId,
  }) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      if (invoice.id != null) {
        // تحديد الحالة الجديدة للفاتورة
        final newStatus = amount >= invoice.total
            ? InvoiceStatus.paid
            : InvoiceStatus.partiallyPaid;

        // إنشاء نسخة محدثة من الفاتورة
        final updatedInvoice = invoice.copyWith(
          status: newStatus,
        );

        // تحديث الفاتورة في قاعدة البيانات
        final result = await _invoiceRepository.updateInvoice(updatedInvoice);

        // الحصول على اسم الموظف أو الحساب البنكي إذا كان متاحاً
        String? employeeName;
        String? bankAccountName;

        if (employeeId != null) {
          final employeeRepository = EmployeeRepository();
          final employee = await employeeRepository.getEmployeeById(employeeId);
          employeeName = employee?.name;
        }

        if (bankAccountId != null) {
          final bankAccountRepository = BankAccountRepository();
          final bankAccount = await bankAccountRepository.getBankAccountById(bankAccountId);
          bankAccountName = bankAccount?.bankName;
        }

        // إنشاء معاملة مالية لتسجيل الدفعة
        final transactionRepository = TransactionRepository();
        final transaction = Transaction(
          reference: 'دفعة للفاتورة ${invoice.invoiceNumber}',
          date: DateTime.now(),
          amount: amount,
          type: TransactionType.income,
          category: 'مدفوعات الفواتير',
          description: 'دفعة للفاتورة ${invoice.invoiceNumber}',
          paymentMethod: paymentMethod,
          invoiceId: invoice.id,
          customerId: invoice.customer.localId,
          customerName: invoice.customer.name,
          employeeId: employeeId,
          employeeName: employeeName,
          bankAccountId: bankAccountId,
          bankAccountName: bankAccountName,
          createdAt: DateTime.now(),
        );

        // حفظ المعاملة المالية في قاعدة البيانات
        final transactionResult = await transactionRepository.insertTransaction(transaction);

        if (mounted) {
          if (result > 0 && transactionResult > 0) {
            // تم التحديث بنجاح، قم بتحديث القائمة
            _loadInvoices(); // إعادة تحميل الفواتير من قاعدة البيانات

            setState(() {
              _isLoading = false;
            });

            // إنشاء نص يصف طريقة الدفع
            String paymentMethodText = Transaction.getPaymentMethodName(paymentMethod);
            String additionalInfo = '';

            if (paymentMethod == PaymentMethod.cash && employeeId != null) {
              additionalInfo = ' (استلام نقدي بواسطة ${employeeName ?? "موظف"})';
            } else if (paymentMethod == PaymentMethod.bankTransfer && bankAccountId != null) {
              additionalInfo = ' (تحويل بنكي إلى ${bankAccountName ?? "حساب بنكي"})';
            }

            // عرض رسالة نجاح
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تسجيل دفعة بمبلغ $amount ر.س للفاتورة ${invoice.invoiceNumber} - $paymentMethodText$additionalInfo'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // فشل في التحديث
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في تسجيل الدفعة'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // الفاتورة ليس لها معرف
        if (mounted) {
          setState(() {
            // تحديث الفاتورة محلياً
            final index = _invoices.indexWhere((inv) => inv.invoiceNumber == invoice.invoiceNumber);
            if (index != -1) {
              final newStatus = amount >= invoice.total
                  ? InvoiceStatus.paid
                  : InvoiceStatus.partiallyPaid;

              _invoices[index] = invoice.copyWith(
                status: newStatus,
              );
            }
            _isLoading = false;
          });

          // إنشاء نص يصف طريقة الدفع
          String paymentMethodText = Transaction.getPaymentMethodName(paymentMethod);
          String additionalInfo = '';

          if (paymentMethod == PaymentMethod.cash && employeeId != null) {
            additionalInfo = ' (استلام نقدي بواسطة موظف)';
          } else if (paymentMethod == PaymentMethod.bankTransfer && bankAccountId != null) {
            additionalInfo = ' (تحويل بنكي)';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تسجيل دفعة بمبلغ $amount ر.س للفاتورة ${invoice.invoiceNumber} - $paymentMethodText$additionalInfo'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      // حدث خطأ أثناء تسجيل الدفعة
      if (kDebugMode) {
        print('Error recording payment: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تسجيل الدفعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteInvoice(Invoice invoice) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // حذف الفاتورة من قاعدة البيانات
      if (invoice.id != null) {
        final result = await _invoiceRepository.deleteInvoice(invoice.id!);

        if (mounted) {
          if (result > 0) {
            // تم الحذف بنجاح، قم بتحديث القائمة
            setState(() {
              _invoices.removeWhere((inv) => inv.id == invoice.id);
              _isLoading = false;
            });

            // عرض رسالة نجاح
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم حذف الفاتورة ${invoice.invoiceNumber}'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // فشل في الحذف
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في حذف الفاتورة'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // الفاتورة ليس لها معرف
        if (mounted) {
          setState(() {
            _invoices.removeWhere((inv) => inv.invoiceNumber == invoice.invoiceNumber);
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الفاتورة ${invoice.invoiceNumber}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      // حدث خطأ أثناء الحذف
      if (kDebugMode) {
        print('Error deleting invoice: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حذف الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
