import 'dart:convert';
import 'package:flutter/material.dart';

enum NotificationType {
  serviceRequest,
  invoice,
  appointment,
  payment,
  customer,
  report,
  other,
}

class NotificationModel {
  final int id;
  final String title;
  final String body;
  final NotificationType type;
  final bool isRead;
  final DateTime createdAt;
  final Map<String, dynamic>? data;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.isRead,
    required this.createdAt,
    this.data,
  });

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] as int,
      title: map['title'] as String,
      body: map['body'] as String,
      type: _parseType(map['type'] as String),
      isRead: map['is_read'] as bool,
      createdAt: DateTime.parse(map['created_at'] as String),
      data: map['data'] != null ? Map<String, dynamic>.from(map['data'] as Map) : null,
    );
  }

  factory NotificationModel.fromJson(String source) =>
      NotificationModel.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'data': data,
    };
  }

  String toJson() => json.encode(toMap());

  NotificationModel copyWith({
    int? id,
    String? title,
    String? body,
    NotificationType? type,
    bool? isRead,
    DateTime? createdAt,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      data: data ?? this.data,
    );
  }

  static NotificationType _parseType(String type) {
    switch (type.toLowerCase()) {
      case 'servicerequest':
        return NotificationType.serviceRequest;
      case 'invoice':
        return NotificationType.invoice;
      case 'appointment':
        return NotificationType.appointment;
      case 'payment':
        return NotificationType.payment;
      case 'customer':
        return NotificationType.customer;
      case 'report':
        return NotificationType.report;
      default:
        return NotificationType.other;
    }
  }

  IconData getIcon() {
    switch (type) {
      case NotificationType.serviceRequest:
        return Icons.build;
      case NotificationType.invoice:
        return Icons.receipt;
      case NotificationType.appointment:
        return Icons.event;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.customer:
        return Icons.person;
      case NotificationType.report:
        return Icons.assessment;
      case NotificationType.other:
        return Icons.notifications;
    }
  }

  Color getColor() {
    switch (type) {
      case NotificationType.serviceRequest:
        return Colors.blue;
      case NotificationType.invoice:
        return Colors.green;
      case NotificationType.appointment:
        return Colors.orange;
      case NotificationType.payment:
        return Colors.purple;
      case NotificationType.customer:
        return Colors.teal;
      case NotificationType.report:
        return Colors.indigo;
      case NotificationType.other:
        return Colors.grey;
    }
  }
}
