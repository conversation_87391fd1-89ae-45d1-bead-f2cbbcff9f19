import 'package:flutter/material.dart';
import '../../config/constants.dart';
import '../../features/reports/models/report_models.dart';

/// A customizable badge widget for displaying status information
class StatusBadge extends StatelessWidget {
  /// The text to display in the badge
  final String text;

  /// The background color of the badge
  final Color color;

  /// Optional icon to display before the text
  final IconData? icon;

  /// Size of the badge (small, medium, large)
  final StatusBadgeSize size;

  /// Style of the badge (filled, outlined, subtle)
  final StatusBadgeStyle style;

  /// Whether to use a rounded or pill shape
  final bool isPill;

  /// Creates a status badge with the given parameters
  const StatusBadge({
    super.key,
    required this.text,
    required this.color,
    this.icon,
    this.size = StatusBadgeSize.medium,
    this.style = StatusBadgeStyle.filled,
    this.isPill = true,
  });

  /// Creates a badge for an invoice status
  factory StatusBadge.invoice({
    required InvoiceStatus status,
    StatusBadgeSize size = StatusBadgeSize.medium,
    StatusBadgeStyle style = StatusBadgeStyle.filled,
    bool isPill = true,
  }) {
    IconData icon;
    switch (status) {
      case InvoiceStatus.draft:
        icon = Icons.edit;
        break;
      case InvoiceStatus.issued:
        icon = Icons.send;
        break;
      case InvoiceStatus.partiallyPaid:
        icon = Icons.payments;
        break;
      case InvoiceStatus.paid:
        icon = Icons.check_circle;
        break;
      case InvoiceStatus.overdue:
        icon = Icons.warning;
        break;
      case InvoiceStatus.cancelled:
        icon = Icons.cancel;
        break;
    }

    return StatusBadge(
      text: Invoice.getStatusName(status),
      color: Invoice.getStatusColor(status),
      icon: icon,
      size: size,
      style: style,
      isPill: isPill,
    );
  }

  /// Creates a badge for a service request status
  factory StatusBadge.service({
    required ServiceStatus status,
    StatusBadgeSize size = StatusBadgeSize.medium,
    StatusBadgeStyle style = StatusBadgeStyle.filled,
    bool isPill = true,
  }) {
    IconData icon;
    switch (status) {
      case ServiceStatus.pending:
        icon = Icons.schedule;
        break;
      case ServiceStatus.inProgress:
        icon = Icons.engineering;
        break;
      case ServiceStatus.completed:
        icon = Icons.check_circle;
        break;
      case ServiceStatus.cancelled:
        icon = Icons.cancel;
        break;
      case ServiceStatus.onHold:
        icon = Icons.pause_circle;
        break;
      case ServiceStatus.followUp:
        icon = Icons.replay;
        break;
    }

    return StatusBadge(
      text: ServiceRequest.getStatusName(status),
      color: ServiceRequest.getStatusColor(status),
      icon: icon,
      size: size,
      style: style,
      isPill: isPill,
    );
  }

  /// Creates a badge for a service request priority
  factory StatusBadge.priority({
    required ServicePriority priority,
    StatusBadgeSize size = StatusBadgeSize.medium,
    StatusBadgeStyle style = StatusBadgeStyle.filled,
    bool isPill = true,
  }) {
    IconData icon;
    switch (priority) {
      case ServicePriority.low:
        icon = Icons.arrow_downward;
        break;
      case ServicePriority.normal:
        icon = Icons.remove;
        break;
      case ServicePriority.high:
        icon = Icons.arrow_upward;
        break;
      case ServicePriority.urgent:
        icon = Icons.priority_high;
        break;
    }

    return StatusBadge(
      text: ServiceRequest.getPriorityName(priority),
      color: ServiceRequest.getPriorityColor(priority),
      icon: icon,
      size: size,
      style: style,
      isPill: isPill,
    );
  }

  /// Creates a badge for a transaction type
  factory StatusBadge.transaction({
    required TransactionType type,
    StatusBadgeSize size = StatusBadgeSize.medium,
    StatusBadgeStyle style = StatusBadgeStyle.filled,
    bool isPill = true,
  }) {
    return StatusBadge(
      text: Transaction.getTypeName(type),
      color: Transaction.getTypeColor(type),
      icon: type == TransactionType.income ? Icons.arrow_upward : Icons.arrow_downward,
      size: size,
      style: style,
      isPill: isPill,
    );
  }

  /// Creates a badge for a payment method
  factory StatusBadge.paymentMethod({
    required PaymentMethod method,
    StatusBadgeSize size = StatusBadgeSize.medium,
    StatusBadgeStyle style = StatusBadgeStyle.filled,
    bool isPill = true,
  }) {
    IconData icon;
    switch (method) {
      case PaymentMethod.cash:
        icon = Icons.money;
        break;
      case PaymentMethod.bankTransfer:
        icon = Icons.account_balance;
        break;
      case PaymentMethod.creditCard:
        icon = Icons.credit_card;
        break;
      case PaymentMethod.debitCard:
        icon = Icons.credit_card;
        break;
      case PaymentMethod.check:
        icon = Icons.note;
        break;
      case PaymentMethod.online:
        icon = Icons.language;
        break;
      case PaymentMethod.other:
        icon = Icons.more_horiz;
        break;
    }

    return StatusBadge(
      text: Transaction.getPaymentMethodName(method),
      color: AppColors.primary,
      icon: icon,
      size: size,
      style: style,
      isPill: isPill,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Determine text style based on size
    final TextStyle textStyle = _getTextStyle();

    // Determine padding based on size
    final EdgeInsets padding = _getPadding();

    // Determine colors based on style
    final Color backgroundColor = _getBackgroundColor();
    final Color textColor = _getTextColor();
    final Color borderColor = _getBorderColor();

    // Determine border radius
    final BorderRadius borderRadius = _getBorderRadius();

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
        border: style == StatusBadgeStyle.outlined
            ? Border.all(color: borderColor, width: 1.5)
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: _getIconSize(),
              color: textColor,
            ),
            SizedBox(width: size == StatusBadgeSize.small ? 4 : 6),
          ],
          Text(
            text,
            style: textStyle.copyWith(color: textColor),
          ),
        ],
      ),
    );
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case StatusBadgeSize.small:
        return const TextStyle(fontSize: 10, fontWeight: FontWeight.bold);
      case StatusBadgeSize.medium:
        return const TextStyle(fontSize: 12, fontWeight: FontWeight.bold);
      case StatusBadgeSize.large:
        return const TextStyle(fontSize: 14, fontWeight: FontWeight.bold);
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case StatusBadgeSize.small:
        return const EdgeInsets.symmetric(horizontal: 6, vertical: 2);
      case StatusBadgeSize.medium:
        return const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
      case StatusBadgeSize.large:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
    }
  }

  Color _getBackgroundColor() {
    switch (style) {
      case StatusBadgeStyle.filled:
        return color;
      case StatusBadgeStyle.outlined:
        return Colors.transparent;
      case StatusBadgeStyle.subtle:
        return color.withAlpha(38);
    }
  }

  Color _getTextColor() {
    switch (style) {
      case StatusBadgeStyle.filled:
        return Colors.white;
      case StatusBadgeStyle.outlined:
      case StatusBadgeStyle.subtle:
        return color;
    }
  }

  Color _getBorderColor() {
    return color;
  }

  BorderRadius _getBorderRadius() {
    final double radius = isPill ? 50 : 4;
    return BorderRadius.circular(radius);
  }

  double _getIconSize() {
    switch (size) {
      case StatusBadgeSize.small:
        return 10;
      case StatusBadgeSize.medium:
        return 14;
      case StatusBadgeSize.large:
        return 18;
    }
  }
}

/// Size options for the status badge
enum StatusBadgeSize {
  /// Small badge (10px font, minimal padding)
  small,

  /// Medium badge (12px font, standard padding)
  medium,

  /// Large badge (14px font, generous padding)
  large,
}

/// Style options for the status badge
enum StatusBadgeStyle {
  /// Solid background color with white text
  filled,

  /// Transparent background with colored border and text
  outlined,

  /// Light background color with colored text
  subtle,
}
