import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../config/constants.dart';
import 'data_card.dart';

/// A card widget for displaying charts with a consistent style
class ChartCard extends StatelessWidget {
  /// Title of the chart
  final String title;

  /// Optional subtitle for additional context
  final String? subtitle;

  /// Optional icon to display in the header
  final IconData? icon;

  /// Color of the icon and accent elements
  final Color? accentColor;

  /// The chart widget to display
  final Widget chart;

  /// Height of the chart
  final double chartHeight;

  /// Optional legend to display below the chart
  final Widget? legend;

  /// Optional footer widget
  final Widget? footer;

  /// Whether to show a divider between header and content
  final bool showDivider;

  /// Whether the card is expandable
  final bool isExpandable;

  /// Whether the card is initially expanded (if expandable)
  final bool initiallyExpanded;

  /// Optional actions to display in the header
  final List<Widget>? actions;

  /// Creates a chart card with the given parameters
  const ChartCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.accentColor,
    required this.chart,
    this.chartHeight = 200,
    this.legend,
    this.footer,
    this.showDivider = true,
    this.isExpandable = false,
    this.initiallyExpanded = true,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return DataCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      accentColor: accentColor,
      showDivider: showDivider,
      isExpandable: isExpandable,
      initiallyExpanded: initiallyExpanded,
      actions: actions,
      footer: footer,
      content: Column(
        children: [
          SizedBox(
            height: chartHeight,
            child: chart,
          ),
          if (legend != null) ...[
            const SizedBox(height: AppDimensions.paddingM),
            legend!,
          ],
        ],
      ),
    );
  }
}

/// A specialized chart card for displaying pie charts
class PieChartCard extends StatelessWidget {
  /// Title of the chart
  final String title;

  /// Optional subtitle for additional context
  final String? subtitle;

  /// Optional icon to display in the header
  final IconData? icon;

  /// Data sections for the pie chart
  final List<PieChartSectionData> sections;

  /// Height of the chart
  final double chartHeight;

  /// Whether to show a legend
  final bool showLegend;

  /// Optional footer widget
  final Widget? footer;

  /// Whether the card is expandable
  final bool isExpandable;

  /// Whether the card is initially expanded (if expandable)
  final bool initiallyExpanded;

  /// Optional actions to display in the header
  final List<Widget>? actions;

  /// Creates a pie chart card with the given parameters
  const PieChartCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.sections,
    this.chartHeight = 200,
    this.showLegend = true,
    this.footer,
    this.isExpandable = false,
    this.initiallyExpanded = true,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return ChartCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      chartHeight: chartHeight,
      isExpandable: isExpandable,
      initiallyExpanded: initiallyExpanded,
      actions: actions,
      footer: footer,
      chart: PieChart(
        PieChartData(
          sections: sections,
          sectionsSpace: 2,
          centerSpaceRadius: 40,
          startDegreeOffset: 180,
        ),
      ),
      legend: showLegend ? _buildLegend() : null,
    );
  }

  Widget _buildLegend() {
    return Wrap(
      spacing: AppDimensions.paddingM,
      runSpacing: AppDimensions.paddingS,
      alignment: WrapAlignment.center,
      children: sections.map((section) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: section.color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              section.title,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        );
      }).toList(),
    );
  }
}

/// A specialized chart card for displaying bar charts
class BarChartCard extends StatelessWidget {
  /// Title of the chart
  final String title;

  /// Optional subtitle for additional context
  final String? subtitle;

  /// Optional icon to display in the header
  final IconData? icon;

  /// Data for the bar chart
  final List<BarChartGroupData> barGroups;

  /// Labels for the X axis
  final List<String> xLabels;

  /// Title for the Y axis
  final String? yTitle;

  /// Maximum value for the Y axis
  final double? maxY;

  /// Height of the chart
  final double chartHeight;

  /// Optional footer widget
  final Widget? footer;

  /// Whether the card is expandable
  final bool isExpandable;

  /// Whether the card is initially expanded (if expandable)
  final bool initiallyExpanded;

  /// Optional actions to display in the header
  final List<Widget>? actions;

  /// Creates a bar chart card with the given parameters
  const BarChartCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.barGroups,
    required this.xLabels,
    this.yTitle,
    this.maxY,
    this.chartHeight = 200,
    this.footer,
    this.isExpandable = false,
    this.initiallyExpanded = true,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return ChartCard(
      title: title,
      subtitle: subtitle,
      icon: icon,
      chartHeight: chartHeight,
      isExpandable: isExpandable,
      initiallyExpanded: initiallyExpanded,
      actions: actions,
      footer: footer,
      chart: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxY,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              tooltipBgColor: Colors.blueGrey,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  '${xLabels[groupIndex]}: ${rod.toY.toStringAsFixed(1)}',
                  const TextStyle(color: Colors.white),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() >= 0 && value.toInt() < xLabels.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        xLabels[value.toInt()],
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    );
                  }
                  return const SizedBox();
                },
              ),
            ),
            leftTitles: AxisTitles(
              axisNameWidget: yTitle != null
                  ? Text(
                      yTitle!,
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 10,
                      ),
                    )
                  : null,
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 10,
                    ),
                  );
                },
              ),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          gridData: const FlGridData(
            show: true,
            drawVerticalLine: false,
          ),
          borderData: FlBorderData(
            show: true,
            border: Border(
              bottom: BorderSide(color: Colors.grey[300]!),
              left: BorderSide(color: Colors.grey[300]!),
            ),
          ),
          barGroups: barGroups,
        ),
      ),
    );
  }
}
