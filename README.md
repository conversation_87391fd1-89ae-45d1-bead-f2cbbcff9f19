# HVAC Service Manager - ركن الجليد للتكييف والتبريد

A comprehensive HVAC (Heating, Ventilation, and Air Conditioning) service management system built with Flutter, featuring full Arabic localization and modern UI design.

## Features

### 🌟 Core Features
- **Customer Management**: Complete customer database with contact information, service history, and subscription management
- **Service Request Management**: Track service requests from creation to completion
- **Technician Management**: Manage technician profiles, schedules, and performance
- **Invoice Management**: Generate, track, and manage invoices with multiple payment methods
- **Inventory Management**: Track parts, supplies, and equipment
- **Comprehensive Reporting**: Generate detailed reports in Arabic and English

### 🌍 Localization & UI
- **Full Arabic Support**: Complete RTL (Right-to-Left) layout support
- **Bilingual Interface**: Arabic and English localization
- **Modern UI Design**: Material Design 3 with responsive layouts
- **Professional Theming**: Consistent design system across all screens
- **Responsive Design**: Works seamlessly on phones, tablets, and desktops

### 🔄 Data Management
- **Hybrid Database System**: SQLite for offline operations, Firebase Firestore for cloud sync
- **Real-time Synchronization**: Bidirectional sync with conflict resolution
- **Offline Capability**: Full functionality without internet connection
- **Data Validation**: Comprehensive validation with Arabic error messages
- **Error Handling**: Robust error handling with user-friendly feedback

### 🔐 Security & Authentication
- **Firebase Authentication**: Secure user authentication
- **Role-based Permissions**: Admin, Manager, Technician, and Customer roles
- **Data Encryption**: Secure data storage and transmission
- **User Management**: Complete user profile and permission management

## Technology Stack

### Frontend
- **Flutter**: Cross-platform mobile development framework
- **Material Design 3**: Modern UI components and theming
- **Provider**: State management
- **Go Router**: Navigation and routing

### Backend & Database
- **Firebase Firestore**: Cloud database
- **Firebase Authentication**: User authentication
- **SQLite**: Local database for offline operations
- **Cloud Functions**: Server-side logic (if needed)

### Localization & Fonts
- **Flutter Localizations**: Internationalization support
- **Cairo Font**: Arabic font support
- **Intl Package**: Date, time, and number formatting

### Development & Testing
- **Flutter Test**: Unit and widget testing
- **Integration Tests**: End-to-end testing
- **Mockito**: Mocking for unit tests
- **Build Runner**: Code generation

## Getting Started

### Prerequisites
- Flutter SDK (3.0 or higher)
- Dart SDK (3.0 or higher)
- Android Studio / VS Code
- Firebase project setup

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-repo/hvac-service-manager.git
   cd hvac-service-manager
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a Firebase project
   - Add your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Enable Firestore and Authentication in Firebase Console

4. **Run the app**
   ```bash
   flutter run
   ```

### Configuration

#### Firebase Configuration
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
2. Enable the following services:
   - Authentication (Email/Password)
   - Firestore Database
   - Cloud Storage (for file uploads)

#### Database Setup
The app automatically creates the required SQLite tables on first run. Firebase Firestore collections are created automatically when data is first synced.

## Project Structure

```
lib/
├── app.dart                    # Main app configuration
├── main.dart                   # App entry point
├── config/                     # Configuration files
│   ├── constants.dart          # App constants
│   ├── enhanced_theme.dart     # Material Design 3 theme
│   └── routes.dart            # App routing
├── core/                       # Core functionality
│   ├── database/              # Database layer
│   ├── localization/          # Internationalization
│   ├── repositories/          # Data repositories
│   ├── services/              # Business logic services
│   ├── validation/            # Input validation
│   └── error_handling/        # Error management
├── features/                   # Feature modules
│   ├── auth/                  # Authentication
│   ├── customers/             # Customer management
│   ├── dashboard/             # Main dashboard
│   ├── invoices/              # Invoice management
│   ├── reports/               # Reporting system
│   └── settings/              # App settings
├── shared/                     # Shared components
│   ├── models/                # Data models
│   └── widgets/               # Reusable widgets
└── state/                      # State management
```

## Testing

### Running Tests

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/unit/validation_service_test.dart

# Run integration tests
flutter test integration_test/

# Run tests with coverage
flutter test --coverage
```

### Test Structure
- **Unit Tests**: Test individual functions and classes
- **Widget Tests**: Test UI components
- **Integration Tests**: Test complete user flows
- **Repository Tests**: Test database operations

## Localization

### Adding New Translations

1. Add new keys to `assets/i18n/ar.json` (Arabic) and `assets/i18n/en.json` (English)
2. Use the translation in code:
   ```dart
   final localizations = AppLocalizations.of(context)!;
   Text(localizations.translate('your_key'))
   ```

### Supported Languages
- Arabic (ar) - Primary language with RTL support
- English (en) - Secondary language

## Database Schema

### SQLite Tables
- `customers` - Customer information
- `service_requests` - Service requests and work orders
- `invoices` - Billing and payment information
- `inventory_items` - Parts and supplies
- `employees` - Technician and staff information
- `users` - User accounts and permissions

### Firestore Collections
- `customers` - Synced customer data
- `serviceRequests` - Service request data
- `invoices` - Invoice data
- `users` - User profiles and settings

## API Documentation

### Customer Repository
```dart
// Create customer
final customerId = await customerRepository.insertCustomer(customer);

// Get customer
final customer = await customerRepository.getCustomerById(customerId);

// Update customer
await customerRepository.updateCustomer(customer);

// Search customers
final results = await customerRepository.searchCustomers('search term');
```

### Validation Service
```dart
// Validate email
final error = ValidationService.validateEmail(email, localizations);

// Validate phone (Saudi format)
final error = ValidationService.validatePhone(phone, localizations);

// Validate amount
final error = ValidationService.validateAmount(amount, localizations);
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style
- Follow Dart/Flutter style guidelines
- Use meaningful variable and function names
- Add comments for complex logic
- Write tests for new features

## Deployment

### Android
```bash
flutter build apk --release
# or
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

### Web
```bash
flutter build web --release
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Wiki](https://github.com/your-repo/hvac-service-manager/wiki)
- Issues: [GitHub Issues](https://github.com/your-repo/hvac-service-manager/issues)

## Acknowledgments

- Flutter team for the amazing framework
- Firebase team for backend services
- Material Design team for design guidelines
- Cairo font for Arabic typography support

---

**ركن الجليد للتكييف والتبريد** - نظام إدارة خدمات التكييف والتبريد الشامل
