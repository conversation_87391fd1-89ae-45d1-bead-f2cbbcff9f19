import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:printing/printing.dart';

import '../../../shared/models/customer.dart';
import '../../../shared/models/bank_account.dart';
import '../../../features/settings/repositories/company_info_repository.dart';
import '../screens/pdf_viewer_screen.dart';

/// مساعد لتصدير البيانات إلى ملف PDF مع دعم الصفحات المتعددة
class PaginatedPdfExport {
  /// عرض ملف PDF داخل التطبيق
  static void showPdfInApp(BuildContext context, String filePath, String title) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PdfViewerScreen(
          pdfPath: filePath,
          title: title,
        ),
      ),
    );
  }

  /// طباعة ملف PDF
  static Future<void> printPdf(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final pdfData = await file.readAsBytes();
        await Printing.layoutPdf(
          onLayout: (_) => pdfData,
          name: 'تقرير',
        );
      }
    } catch (e) {
      debugPrint('Error printing PDF: $e');
    }
  }

  /// تصدير تقرير الأرباح والخسائر إلى ملف PDF
  static Future<String?> exportProfitLossReport({
    required BuildContext context,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> tableData,
    required Map<String, dynamic> summaryData,
    bool shareFile = false,
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخطوط العربية Cairo
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // تحميل معلومات الشركة
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // تحميل صورة الشعار
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // إنشاء صورة بسيطة بديلة
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // استخدام نفس الصورة للعلامة المائية
      final watermarkImageBytes = logoImageBytes;

      // الحصول على اسم المستخدم الحالي
      final String username = 'admin'; // TODO: الحصول من حالة المصادقة

      // استخراج البيانات المالية من الملخص
      final double totalRevenue = summaryData['totalRevenue'] ?? 0.0;
      final double totalExpenses = summaryData['totalExpenses'] ?? 0.0;
      final double grossProfit = summaryData['grossProfit'] ?? 0.0;
      final double netProfit = summaryData['netProfit'] ?? 0.0;

      // تقسيم البيانات إلى مجموعات أصغر للتعامل مع مشكلة الصفحات الكثيرة
      const int rowsPerPage = 20;
      final int totalRows = tableData.length;
      final int totalPages = (totalRows / rowsPerPage).ceil() + 1; // +1 للصفحة الأولى (الملخص)

      // إنشاء صفحة للملخص
      pdf.addPage(
        pw.Page(
          margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.stretch,
              children: [
                _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                pw.SizedBox(height: 20),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 8),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      title,
                      style: pw.TextStyle(font: arabicBoldFont, fontSize: 18, color: PdfColors.blue900),
                    ),
                  ),
                ),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 16),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      'الفترة من ${DateFormat('yyyy/MM/dd').format(startDate)} إلى ${DateFormat('yyyy/MM/dd').format(endDate)}',
                      style: pw.TextStyle(font: arabicFont, fontSize: 14, color: PdfColors.blue700),
                    ),
                  ),
                ),
                _buildProfitLossSummary(
                  totalRevenue: totalRevenue,
                  totalExpenses: totalExpenses,
                  grossProfit: grossProfit,
                  netProfit: netProfit,
                  arabicFont: arabicFont,
                  arabicBoldFont: arabicBoldFont,
                ),
                pw.SizedBox(height: 20),
                _buildProfitLossTable(
                  tableData: tableData.take(10).toList(), // عرض أول 10 صفوف فقط في الصفحة الأولى
                  arabicFont: arabicFont,
                  arabicBoldFont: arabicBoldFont,
                ),
                pw.SizedBox(height: 15),
                // العلامة المائية
                pw.Center(
                  child: pw.Opacity(
                    opacity: 0.05,
                    child: pw.Image(pw.MemoryImage(watermarkImageBytes), width: 120, height: 120, fit: pw.BoxFit.contain),
                  ),
                ),
                pw.Spacer(),
                _buildFooter(arabicFont, username, pageNumber: 1, totalPages: totalPages),
              ],
            );
          },
        ),
      );

      // إنشاء صفحات البيانات الإضافية إذا كان هناك المزيد من البيانات
      if (tableData.length > 10) {
        for (int pageIndex = 0; pageIndex < ((tableData.length - 10) / rowsPerPage).ceil(); pageIndex++) {
          final int startIndex = 10 + pageIndex * rowsPerPage;
          final int endIndex = (startIndex + rowsPerPage < tableData.length) ? startIndex + rowsPerPage : tableData.length;
          final List<Map<String, dynamic>> pageData = tableData.sublist(startIndex, endIndex);

          pdf.addPage(
            pw.Page(
              margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              pageFormat: PdfPageFormat.a4,
              build: (pw.Context context) {
                return pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                  children: [
                    _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                    pw.SizedBox(height: 10),
                    pw.Container(
                      margin: const pw.EdgeInsets.only(bottom: 8),
                      child: pw.Directionality(
                        textDirection: pw.TextDirection.rtl,
                        child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text(
                              title,
                              style: pw.TextStyle(font: arabicBoldFont, fontSize: 16, color: PdfColors.blue900),
                            ),
                            pw.Text(
                              'صفحة ${pageIndex + 2} من $totalPages',
                              style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey700),
                            ),
                          ],
                        ),
                      ),
                    ),
                    _buildProfitLossTable(
                      tableData: pageData,
                      arabicFont: arabicFont,
                      arabicBoldFont: arabicBoldFont,
                    ),
                    pw.Spacer(),
                    _buildFooter(arabicFont, username, pageNumber: pageIndex + 2, totalPages: totalPages),
                  ],
                );
              },
            ),
          );
        }
      }

      // حفظ ملف PDF في مجلد مؤقت
      final tempDir = await getTemporaryDirectory();

      // إنشاء اسم الملف مع التاريخ والوقت
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'تقرير_الأرباح_والخسائر_$timestamp.pdf';
      final filePath = '${tempDir.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF created in temp directory: ${file.path}');

      // مشاركة الملف أو عرضه داخل التطبيق
      if (shareFile) {
        try {
          await Share.shareXFiles(
            [XFile(file.path)],
            text: 'تقرير الأرباح والخسائر',
            subject: title,
          );
          debugPrint('PDF shared successfully');
        } catch (e) {
          debugPrint('Error sharing PDF: $e');
        }
      } else {
        // عرض الملف داخل التطبيق مباشرة
        if (context.mounted) {
          showPdfInApp(context, file.path, title);
        }
      }

      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating profit loss report PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// تصدير تقرير تحليل ربحية الخدمات إلى ملف PDF
  static Future<String?> exportServiceProfitabilityReport({
    required BuildContext context,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> tableData,
    required Map<String, dynamic> summaryData,
    bool shareFile = false,
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخطوط العربية Cairo
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // تحميل معلومات الشركة
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // تحميل صورة الشعار
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // إنشاء صورة بسيطة بديلة
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // استخدام نفس الصورة للعلامة المائية
      final watermarkImageBytes = logoImageBytes;

      // الحصول على اسم المستخدم الحالي
      final String username = 'admin'; // TODO: الحصول من حالة المصادقة

      // استخراج البيانات من الملخص
      final int highProfitabilityCount = summaryData['highProfitabilityCount'] ?? 0;
      final int mediumProfitabilityCount = summaryData['mediumProfitabilityCount'] ?? 0;
      final int lowProfitabilityCount = summaryData['lowProfitabilityCount'] ?? 0;
      final int totalServices = summaryData['totalServices'] ?? 0;

      // تقسيم البيانات إلى مجموعات أصغر للتعامل مع مشكلة الصفحات الكثيرة
      const int rowsPerPage = 15;
      final int totalRows = tableData.length;
      final int totalPages = (totalRows / rowsPerPage).ceil() + 1; // +1 للصفحة الأولى (الملخص)

      // إنشاء صفحة للملخص
      pdf.addPage(
        pw.Page(
          margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.stretch,
              children: [
                _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                pw.SizedBox(height: 20),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 8),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      title,
                      style: pw.TextStyle(font: arabicBoldFont, fontSize: 18, color: PdfColors.blue900),
                    ),
                  ),
                ),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 16),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      'الفترة من ${DateFormat('yyyy/MM/dd').format(startDate)} إلى ${DateFormat('yyyy/MM/dd').format(endDate)}',
                      style: pw.TextStyle(font: arabicFont, fontSize: 14, color: PdfColors.blue700),
                    ),
                  ),
                ),
                _buildServiceProfitabilitySummary(
                  highProfitabilityCount: highProfitabilityCount,
                  mediumProfitabilityCount: mediumProfitabilityCount,
                  lowProfitabilityCount: lowProfitabilityCount,
                  totalServices: totalServices,
                  arabicFont: arabicFont,
                  arabicBoldFont: arabicBoldFont,
                ),
                pw.SizedBox(height: 20),
                _buildServiceProfitabilityTable(
                  tableData: tableData.take(10).toList(), // عرض أول 10 صفوف فقط في الصفحة الأولى
                  arabicFont: arabicFont,
                  arabicBoldFont: arabicBoldFont,
                ),
                pw.SizedBox(height: 15),
                // العلامة المائية
                pw.Center(
                  child: pw.Opacity(
                    opacity: 0.05,
                    child: pw.Image(pw.MemoryImage(watermarkImageBytes), width: 120, height: 120, fit: pw.BoxFit.contain),
                  ),
                ),
                pw.Spacer(),
                _buildFooter(arabicFont, username, pageNumber: 1, totalPages: totalPages),
              ],
            );
          },
        ),
      );

      // إنشاء صفحات البيانات الإضافية إذا كان هناك المزيد من البيانات
      if (tableData.length > 10) {
        for (int pageIndex = 0; pageIndex < ((tableData.length - 10) / rowsPerPage).ceil(); pageIndex++) {
          final int startIndex = 10 + pageIndex * rowsPerPage;
          final int endIndex = (startIndex + rowsPerPage < tableData.length) ? startIndex + rowsPerPage : tableData.length;
          final List<Map<String, dynamic>> pageData = tableData.sublist(startIndex, endIndex);

          pdf.addPage(
            pw.Page(
              margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              pageFormat: PdfPageFormat.a4,
              build: (pw.Context context) {
                return pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                  children: [
                    _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                    pw.SizedBox(height: 10),
                    pw.Container(
                      margin: const pw.EdgeInsets.only(bottom: 8),
                      child: pw.Directionality(
                        textDirection: pw.TextDirection.rtl,
                        child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text(
                              title,
                              style: pw.TextStyle(font: arabicBoldFont, fontSize: 16, color: PdfColors.blue900),
                            ),
                            pw.Text(
                              'صفحة ${pageIndex + 2} من $totalPages',
                              style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey700),
                            ),
                          ],
                        ),
                      ),
                    ),
                    _buildServiceProfitabilityTable(
                      tableData: pageData,
                      arabicFont: arabicFont,
                      arabicBoldFont: arabicBoldFont,
                    ),
                    pw.Spacer(),
                    _buildFooter(arabicFont, username, pageNumber: pageIndex + 2, totalPages: totalPages),
                  ],
                );
              },
            ),
          );
        }
      }

      // حفظ ملف PDF في مجلد مؤقت
      final tempDir = await getTemporaryDirectory();

      // إنشاء اسم الملف مع التاريخ والوقت
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'تقرير_ربحية_الخدمات_$timestamp.pdf';
      final filePath = '${tempDir.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF created in temp directory: ${file.path}');

      // مشاركة الملف أو عرضه داخل التطبيق
      if (shareFile) {
        try {
          await Share.shareXFiles(
            [XFile(file.path)],
            text: 'تقرير تحليل ربحية الخدمات',
            subject: title,
          );
          debugPrint('PDF shared successfully');
        } catch (e) {
          debugPrint('Error sharing PDF: $e');
        }
      } else {
        // عرض الملف داخل التطبيق مباشرة
        if (context.mounted) {
          showPdfInApp(context, file.path, title);
        }
      }

      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating service profitability report PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// تصدير تقرير التدفقات النقدية إلى ملف PDF
  static Future<String?> exportCashFlowReport({
    required BuildContext context,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required List<Map<String, dynamic>> tableData,
    required Map<String, double> summaryData,
    required List<BankAccount> bankAccounts,
    bool shareFile = false, // إلغاء خاصية الحفظ التلقائي
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخطوط العربية Cairo
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // تحميل معلومات الشركة
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // تحميل صورة الشعار
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // إنشاء صورة بسيطة بديلة
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // استخدام نفس الصورة للعلامة المائية
      final watermarkImageBytes = logoImageBytes;

      // الحصول على اسم المستخدم الحالي
      final String username = 'admin'; // TODO: الحصول من حالة المصادقة

      // استخراج البيانات المالية من الملخص
      final double totalIncome = summaryData['totalIncome'] ?? 0.0;
      final double totalExpenses = summaryData['totalExpenses'] ?? 0.0;
      final double totalSalaries = summaryData['totalSalaries'] ?? 0.0;
      final double totalWithdrawals = summaryData['totalWithdrawals'] ?? 0.0;
      final double totalTransfers = summaryData['totalTransfers'] ?? 0.0;
      final double netCashFlow = summaryData['netCashFlow'] ?? 0.0;
      final double totalBankBalance = summaryData['totalBankBalance'] ?? 0.0;

      // تقسيم البيانات إلى مجموعات أصغر للتعامل مع مشكلة الصفحات الكثيرة
      const int rowsPerPage = 20; // زيادة عدد الصفوف في كل صفحة لتضمين المزيد من البيانات
      final int totalRows = tableData.length;
      final int totalPages = (totalRows / rowsPerPage).ceil() + 1; // +1 للصفحة الأولى (الملخص)

      // سجل عدد الصفوف والصفحات للتصحيح
      debugPrint('Exportando PDF: Total de filas: $totalRows, Filas por página: $rowsPerPage, Total de páginas: $totalPages');

      // Verificar que todos los datos se incluirán en el PDF
      if (totalRows > 0) {
        debugPrint('Primera fila: ${tableData.first}');
        debugPrint('Última fila: ${tableData.last}');
      }

      // إنشاء صفحة للملخص
      pdf.addPage(
        pw.Page(
          margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.stretch,
              children: [
                _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                pw.SizedBox(height: 20),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 8),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      title,
                      style: pw.TextStyle(font: arabicBoldFont, fontSize: 18, color: PdfColors.blue900),
                    ),
                  ),
                ),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 16),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      'الفترة من ${DateFormat('yyyy/MM/dd').format(startDate)} إلى ${DateFormat('yyyy/MM/dd').format(endDate)}',
                      style: pw.TextStyle(font: arabicFont, fontSize: 14, color: PdfColors.blue700),
                    ),
                  ),
                ),
                _buildCashFlowSummary(
                  totalIncome: totalIncome,
                  totalExpenses: totalExpenses,
                  totalSalaries: totalSalaries,
                  totalWithdrawals: totalWithdrawals,
                  totalTransfers: totalTransfers,
                  netCashFlow: netCashFlow,
                  arabicFont: arabicFont,
                  arabicBoldFont: arabicBoldFont,
                ),
                pw.SizedBox(height: 20),
                _buildBankAccountsSummary(
                  bankAccounts: bankAccounts,
                  totalBankBalance: totalBankBalance,
                  arabicFont: arabicFont,
                  arabicBoldFont: arabicBoldFont,
                ),
                pw.SizedBox(height: 15),
                // العلامة المائية
                pw.Center(
                  child: pw.Opacity(
                    opacity: 0.05,
                    child: pw.Image(pw.MemoryImage(watermarkImageBytes), width: 120, height: 120, fit: pw.BoxFit.contain),
                  ),
                ),
                pw.Spacer(),
                _buildFooter(arabicFont, username, pageNumber: 1, totalPages: totalPages),
              ],
            );
          },
        ),
      );

      // إنشاء صفحات البيانات
      for (int pageIndex = 0; pageIndex < (totalRows / rowsPerPage).ceil(); pageIndex++) {
        final int startIndex = pageIndex * rowsPerPage;
        final int endIndex = (startIndex + rowsPerPage < totalRows) ? startIndex + rowsPerPage : totalRows;
        final List<Map<String, dynamic>> pageData = tableData.sublist(startIndex, endIndex);

        // Registrar información de depuración para cada página
        debugPrint('Página ${pageIndex + 1}: Índice inicial: $startIndex, Índice final: $endIndex, Número de registros: ${pageData.length}');

        pdf.addPage(
          pw.Page(
            margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                children: [
                  _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                  pw.SizedBox(height: 10),
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 8),
                    child: pw.Directionality(
                      textDirection: pw.TextDirection.rtl,
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            title,
                            style: pw.TextStyle(font: arabicBoldFont, fontSize: 16, color: PdfColors.blue900),
                          ),
                          pw.Text(
                            'صفحة ${pageIndex + 2} من $totalPages',
                            style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey700),
                          ),
                        ],
                      ),
                    ),
                  ),
                  _buildCashFlowDataTable(pageData, arabicFont, arabicBoldFont),
                  pw.Spacer(),
                  _buildFooter(arabicFont, username, pageNumber: pageIndex + 2, totalPages: totalPages),
                ],
              );
            },
          ),
        );
      }

      // حفظ ملف PDF في مجلد مؤقت
      final tempDir = await getTemporaryDirectory();

      // إنشاء اسم الملف مع التاريخ والوقت
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'تقرير_التدفقات_النقدية_$timestamp.pdf';
      final filePath = '${tempDir.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF created in temp directory: ${file.path}');

      // مشاركة الملف باستخدام share_plus فقط إذا كان الخيار مفعلاً
      if (shareFile) {
        try {
          // استخدام Share.shareXFiles لمشاركة الملف
          await Share.shareXFiles(
            [XFile(file.path)],
            text: 'تقرير التدفقات النقدية',
            subject: title,
          );
          debugPrint('PDF shared successfully');
        } catch (e) {
          debugPrint('Error sharing PDF: $e');
        }
      } else {
        // عرض الملف داخل التطبيق مباشرة
        if (context.mounted) {
          showPdfInApp(context, file.path, title);
        }
      }

      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }
  /// تصدير جدول بيانات العميل إلى ملف PDF مع تقسيم البيانات إلى صفحات
  static Future<String?> exportCustomerTableToPdf({
    required BuildContext context,
    required String title,
    Customer? customer,
    required List<Map<String, dynamic>> tableData,
    required Map<String, double> summaryData,
    bool shareFile = true, // إضافة خيار لمشاركة الملف تلقائياً
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخطوط العربية Cairo
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // تحميل معلومات الشركة
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // تحميل صورة الشعار
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // إنشاء صورة بسيطة بديلة
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // استخدام نفس الصورة للعلامة المائية
      final watermarkImageBytes = logoImageBytes;

      // الحصول على اسم المستخدم الحالي
      final String username = 'admin'; // TODO: الحصول من حالة المصادقة

      // حساب إجمالي المدين والدائن
      double totalDebit = 0.0;
      double totalCredit = 0.0;

      // إنشاء قاموس للربط بين الفواتير والمعاملات المالية المرتبطة بها
      Map<String, List<Map<String, dynamic>>> invoicePayments = {};

      // أولاً، نحدد المعاملات المالية التي تمثل دفعات للفواتير
      for (final item in tableData) {
        if (item['type'] == 'transaction' &&
            item.containsKey('invoiceId') &&
            item['invoiceId'] != null) {
          // تحويل معرف الفاتورة إلى نص لاستخدامه كمفتاح في القاموس
          String invoiceId = item['invoiceId'].toString();
          if (!invoicePayments.containsKey(invoiceId)) {
            invoicePayments[invoiceId] = [];
          }
          invoicePayments[invoiceId]!.add(item);
        }
      }

      // حساب إجمالي المدين والدائن
      for (final item in tableData) {
        double amount = item['amount'] ?? 0.0;

        if (item['type'] == 'invoice') {
          // الفواتير تضاف إلى المدين
          totalDebit += amount;

          // التحقق من وجود دفعات مرتبطة بهذه الفاتورة
          if (item.containsKey('id') && item['id'] != null && invoicePayments.containsKey(item['id'].toString())) {
            // حساب إجمالي المدفوعات لهذه الفاتورة
            double totalPaid = 0.0;
            for (var payment in invoicePayments[item['id'].toString()]!) {
              totalPaid += payment['amount'] ?? 0.0;
            }

            // إضافة المبالغ المدفوعة إلى الدائن (لن يتم حسابها مرة أخرى لأننا نستثني المعاملات المرتبطة بالفواتير)
            totalCredit += totalPaid;
          }
          // إذا كانت الفاتورة مدفوعة (حسب حقل الحالة) ولكن ليس لها معاملات مرتبطة
          else if (item.containsKey('status') && (item['status'] == 'paid' || item['status'] == 'partiallyPaid')) {
            // نضيف المبلغ إلى الدائن
            totalCredit += amount;
          }
        }
        else if (item['type'] == 'transaction') {
          // المعاملات المالية تضاف إلى الدائن فقط إذا لم تكن مرتبطة بفاتورة
          // لتجنب الحساب المزدوج
          if (!item.containsKey('invoiceId') || item['invoiceId'] == null) {
            totalCredit += amount;
          }
        }
        else if (item['type'] == 'service') {
          // الخدمات تضاف إلى المدين إلا إذا كانت ضمن الاشتراك
          if (!(item.containsKey('customerPaymentMethod') &&
              item['customerPaymentMethod'] == CustomerPaymentMethod.monthlySubscription)) {
            totalDebit += amount;
          }
        }
      }

      // إضافة إجمالي المدين والدائن إلى بيانات الملخص
      summaryData['totalDebit'] = totalDebit;
      summaryData['totalCredit'] = totalCredit;

      // تقسيم البيانات إلى مجموعات أصغر للتعامل مع مشكلة الصفحات الكثيرة
      const int rowsPerPage = 21; // عدد الصفوف في كل صفحة
      final int totalRows = tableData.length;
      final int totalPages = (totalRows / rowsPerPage).ceil();

      // إنشاء صفحة للملخص
      pdf.addPage(
        pw.Page(
          margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.stretch,
              children: [
                _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                pw.SizedBox(height: 20),
                pw.Container(
                  alignment: pw.Alignment.center,
                  margin: const pw.EdgeInsets.only(bottom: 8),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      'ملخص تقرير البيانات',
                      style: pw.TextStyle(font: arabicBoldFont, fontSize: 18, color: PdfColors.blue900),
                    ),
                  ),
                ),
                if (customer != null)
                  pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    margin: const pw.EdgeInsets.only(bottom: 10),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.blue50,
                      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                      border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Directionality(
                          textDirection: pw.TextDirection.rtl,
                          child: pw.Text(
                            'معلومات العميل',
                            style: pw.TextStyle(
                              font: arabicBoldFont,
                              fontSize: 12,
                              color: PdfColors.blue800,
                            ),
                          ),
                        ),
                        pw.Divider(color: PdfColors.blue200, thickness: 0.5),
                        pw.SizedBox(height: 5),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Expanded(
                              child: _buildCustomerInfoItem(
                                'اسم العميل',
                                customer.name,
                                arabicFont,
                                arabicBoldFont,
                              ),
                            ),
                            pw.SizedBox(width: 10),
                            pw.Expanded(
                              child: _buildCustomerInfoItem(
                                'رقم الهاتف',
                                customer.phone ?? 'غير محدد',
                                arabicFont,
                                arabicBoldFont,
                              ),
                            ),
                          ],
                        ),
                        pw.SizedBox(height: 5),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Expanded(
                              child: _buildCustomerInfoItem(
                                'العنوان',
                                customer.address ?? 'غير محدد',
                                arabicFont,
                                arabicBoldFont,
                              ),
                            ),
                            pw.SizedBox(width: 10),
                            pw.Expanded(
                              child: _buildCustomerInfoItem(
                                'طريقة الدفع',
                                customer.paymentMethod == CustomerPaymentMethod.monthlySubscription
                                    ? 'اشتراك شهري'
                                    : 'دفع لكل خدمة',
                                arabicFont,
                                arabicBoldFont,
                              ),
                            ),
                          ],
                        ),
                        if (customer.paymentMethod == CustomerPaymentMethod.monthlySubscription)
                          pw.Padding(
                            padding: const pw.EdgeInsets.only(top: 5),
                            child: pw.Row(
                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Expanded(
                                  child: _buildCustomerInfoItem(
                                    'مبلغ الاشتراك الشهري',
                                    '${customer.monthlySubscriptionAmount?.toStringAsFixed(2) ?? '0.00'} ر.س',
                                    arabicFont,
                                    arabicBoldFont,
                                  ),
                                ),
                                pw.SizedBox(width: 10),
                                pw.Expanded(
                                  child: _buildCustomerInfoItem(
                                    'تاريخ الاشتراك',
                                    customer.subscriptionStartDate != null
                                        ? DateFormat('dd/MM/yyyy').format(customer.subscriptionStartDate!)
                                        : 'غير محدد',
                                    arabicFont,
                                    arabicBoldFont,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                _buildSummarySection(summaryData, arabicFont, arabicBoldFont),
                pw.SizedBox(height: 15),
                // العلامة المائية
                pw.Center(
                  child: pw.Opacity(
                    opacity: 0.05,
                    child: pw.Image(pw.MemoryImage(watermarkImageBytes), width: 120, height: 120, fit: pw.BoxFit.contain),
                  ),
                ),
                pw.Spacer(),
                _buildFooter(arabicFont, username, pageNumber: 1, totalPages: totalPages),
              ],
            );
          },
        ),
      );

      // إنشاء صفحات البيانات
      for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
        final int startIndex = pageIndex * rowsPerPage;
        final int endIndex = (startIndex + rowsPerPage < totalRows) ? startIndex + rowsPerPage : totalRows;
        final List<Map<String, dynamic>> pageData = tableData.sublist(startIndex, endIndex);

        pdf.addPage(
          pw.Page(
            margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.stretch,
                children: [
                  _buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
                  pw.SizedBox(height: 10),
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 8),
                    child: pw.Directionality(
                      textDirection: pw.TextDirection.rtl,
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            'تقرير البيانات',
                            style: pw.TextStyle(font: arabicBoldFont, fontSize: 16, color: PdfColors.blue900),
                          ),
                          pw.Text(
                            'صفحة ${pageIndex + 1} من $totalPages',
                            style: pw.TextStyle(font: arabicFont, fontSize: 12, color: PdfColors.grey700),
                          ),
                        ],
                      ),
                    ),
                  ),
                  _buildDataTable(pageData, arabicFont, arabicBoldFont),
                  pw.Spacer(),
                  _buildFooter(arabicFont, username, pageNumber: pageIndex + 1, totalPages: totalPages),
                ],
              );
            },
          ),
        );
      }

      // حفظ ملف PDF في مجلد مؤقت بدلاً من مجلد التنزيلات
      final tempDir = await getTemporaryDirectory();

      // إنشاء اسم الملف مع التاريخ والوقت
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'تقرير_عميل_$timestamp.pdf';
      final filePath = '${tempDir.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF created in temp directory: ${file.path}');

      // مشاركة الملف باستخدام share_plus فقط إذا كان الخيار مفعلاً
      if (shareFile) {
        try {
          // استخدام Share.shareXFiles لمشاركة الملف
          await Share.shareXFiles(
            [XFile(file.path)],
            text: 'تقرير العميل',
            subject: title,
          );
          debugPrint('PDF shared successfully');
        } catch (e) {
          debugPrint('Error sharing PDF: $e');
          // عرض رسالة للمستخدم
          debugPrint('Error sharing PDF: $e');
        }
      } else {
        // عرض الملف داخل التطبيق مباشرة
        if (context.mounted) {
          showPdfInApp(context, file.path, title);
        }
      }

      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// بناء ترويسة الصفحة
  static pw.Widget _buildHeader(pw.Font arabicFont, pw.Font arabicBoldFont, dynamic companyInfo, Uint8List logoImageBytes) {
    return pw.Container(
      width: double.infinity,
      margin: pw.EdgeInsets.zero,
      padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border(
          bottom: pw.BorderSide(color: PdfColors.blue200, width: 1.0),
          top: pw.BorderSide(color: PdfColors.blue900, width: 2.0),
        ),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 2,
          ),
        ],
      ),
      child: pw.Column(
        children: [
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              // بيانات الشركة بالإنجليزي (يسار)
              pw.Expanded(
                flex: 4,
                child: pw.Padding(
                  padding: const pw.EdgeInsets.only(left: 8),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    mainAxisAlignment: pw.MainAxisAlignment.center,
                    children: [
                      pw.Text(
                        companyInfo?.nameEn ?? 'Ice Corner',
                        style: pw.TextStyle(font: arabicBoldFont, fontSize: 12, color: PdfColors.blue900),
                      ),
                      pw.SizedBox(height: 2),
                      if (companyInfo?.addressEn != null)
                        pw.Text(companyInfo!.addressEn!, style: pw.TextStyle(font: arabicFont, fontSize: 9)),
                      if (companyInfo?.phoneEn != null)
                        pw.Text(companyInfo!.phoneEn!, style: pw.TextStyle(font: arabicFont, fontSize: 9)),
                    ],
                  ),
                ),
              ),
              // الشعار في المنتصف
              pw.Container(
                width: 60,
                height: 60,
                alignment: pw.Alignment.center,
                margin: const pw.EdgeInsets.symmetric(horizontal: 8),
                decoration: pw.BoxDecoration(
                  shape: pw.BoxShape.circle,
                  color: PdfColors.white,
                  border: pw.Border.all(color: PdfColors.blue100, width: 1),
                ),
                child: pw.ClipOval(
                  child: pw.Image(pw.MemoryImage(logoImageBytes), fit: pw.BoxFit.contain),
                ),
              ),
              // بيانات الشركة بالعربي (يمين)
              pw.Expanded(
                flex: 4,
                child: pw.Padding(
                  padding: const pw.EdgeInsets.only(right: 8),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    mainAxisAlignment: pw.MainAxisAlignment.center,
                    children: [
                      pw.Text(
                        companyInfo?.nameAr ?? 'ركن الجليد',
                        style: pw.TextStyle(font: arabicBoldFont, fontSize: 12, color: PdfColors.blue900),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.SizedBox(height: 2),
                      if (companyInfo?.addressAr != null)
                        pw.Text(companyInfo!.addressAr!, style: pw.TextStyle(font: arabicFont, fontSize: 9), textDirection: pw.TextDirection.rtl),
                      if (companyInfo?.phoneAr != null)
                        pw.Text(companyInfo!.phoneAr!, style: pw.TextStyle(font: arabicFont, fontSize: 9), textDirection: pw.TextDirection.rtl),
                    ],
                  ),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 4),
          pw.Container(
            height: 1,
            color: PdfColors.blue200,
            margin: const pw.EdgeInsets.symmetric(horizontal: 20),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الصفحة
  static pw.Widget _buildFooter(pw.Font arabicFont, String username, {int? pageNumber, int? totalPages}) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      decoration: pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300, width: 0.5),
        ),
        color: PdfColors.grey50,
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            child: pw.Text(
              'المستخدم: $username',
              style: pw.TextStyle(font: arabicFont, fontSize: 8, color: PdfColors.grey700),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          if (pageNumber != null && totalPages != null)
            pw.Container(
              padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue100,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
              ),
              child: pw.Text(
                'صفحة $pageNumber من $totalPages',
                style: pw.TextStyle(font: arabicFont, fontSize: 8, color: PdfColors.blue900),
                textDirection: pw.TextDirection.rtl,
              ),
            ),
          pw.Expanded(
            child: pw.Text(
              'تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
              style: pw.TextStyle(font: arabicFont, fontSize: 8, color: PdfColors.grey700),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الملخص
  static pw.Widget _buildSummarySection(
    Map<String, double> summaryData,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    // استخراج البيانات المالية
    final double totalDebit = summaryData['totalDebit'] ?? summaryData['totalAmount'] ?? 0.0; // إجمالي المدين (الفواتير والخدمات)
    final double totalCredit = summaryData['totalCredit'] ?? summaryData['totalTransactions'] ?? 0.0; // إجمالي الدائن (المدفوعات)
    final double totalInvoices = summaryData['totalAmount'] ?? 0.0; // إجمالي الفواتير

    // استخدام قيمة totalServices من البيانات المرسلة
    // هذه القيمة تحتوي على مبلغ الاشتراك الشهري للعملاء ذوي الاشتراك الشهري
    final double totalServices = summaryData['totalServices'] ?? 0.0; // إجمالي الخدمات أو مبلغ الاشتراك الشهري

    // حساب الرصيد المتبقي (الفرق بين الدائن والمدين)
    // نضمن أن totalDebit يتضمن الفواتير، ونضيف totalServices للحصول على إجمالي المدين
    final double totalExpenses = totalDebit + totalServices;
    final double remainingBalance = totalCredit - totalExpenses;

    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [PdfColors.blue50, PdfColors.white],
          begin: pw.Alignment.topCenter,
          end: pw.Alignment.bottomCenter,
        ),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 3,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.stretch,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue800,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Directionality(
              textDirection: pw.TextDirection.rtl,
              child: pw.Text(
                'ملخص الإحصائيات',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 12,
                  color: PdfColors.white,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),
          pw.SizedBox(height: 12),
          // الصف الأول من الملخص
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildEnhancedSummaryItem(
                'إجمالي المدين',
                '-${totalDebit.toStringAsFixed(2)} ر.س',
                regularFont,
                boldFont,
                PdfColors.red700,
                PdfColors.red50,
                '-', // Replacing arrow down symbol
              ),
              _buildEnhancedSummaryItem(
                'إجمالي الدائن',
                '+${totalCredit.toStringAsFixed(2)} ر.س',
                regularFont,
                boldFont,
                PdfColors.green700,
                PdfColors.green50,
                '+', // Replacing arrow up symbol
              ),
            ],
          ),

          pw.SizedBox(height: 10),

          // الصف الثاني من الملخص
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildEnhancedSummaryItem(
                'إجمالي الفواتير',
                '-${totalInvoices.toStringAsFixed(2)} ر.س',
                regularFont,
                boldFont,
                PdfColors.blue700,
                PdfColors.blue50,
                '✓', // Check mark symbol as text
              ),
              _buildEnhancedSummaryItem(
                'إجمالي مبلغ طلبات الخدمات',
                totalServices > 0 ? '-${totalServices.toStringAsFixed(2)} ر.س' : '0.00 ر.س',
                regularFont,
                boldFont,
                PdfColors.orange,
                PdfColors.amber50,
                'S', // Service symbol as text
              ),
              _buildEnhancedSummaryItem(
                'الرصيد المتبقي',
                '${remainingBalance >= 0 ? '+' : ''}${remainingBalance.toStringAsFixed(2)} ر.س',
                regularFont,
                boldFont,
                remainingBalance >= 0 ? PdfColors.green700 : PdfColors.red700,
                remainingBalance >= 0 ? PdfColors.green50 : PdfColors.red50,
                remainingBalance >= 0 ? '\$' : '!', // Money or warning symbol as text
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص محسن
  static pw.Widget _buildEnhancedSummaryItem(
    String title,
    String value,
    pw.Font regularFont,
    pw.Font boldFont,
    PdfColor valueColor,
    PdfColor backgroundColor,
    String iconText, // Changed from int to String
  ) {
    return pw.Expanded(
      child: pw.Container(
        margin: const pw.EdgeInsets.symmetric(horizontal: 4),
        padding: const pw.EdgeInsets.all(8),
        decoration: pw.BoxDecoration(
          color: backgroundColor,
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(6)),
          border: pw.Border.all(color: valueColor.shade(50), width: 0.5),
          boxShadow: [
            pw.BoxShadow(
              color: PdfColors.grey200,
              offset: const PdfPoint(0, 1),
              blurRadius: 2,
            ),
          ],
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  iconText, // Use the string directly
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 10,
                    color: valueColor,
                  ),
                ),
                pw.SizedBox(width: 4),
                pw.Text(
                  title,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 9,
                    color: PdfColors.grey800,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
            pw.SizedBox(height: 6),
            pw.Container(
              padding: const pw.EdgeInsets.symmetric(vertical: 3, horizontal: 6),
              decoration: pw.BoxDecoration(
                color: PdfColors.white,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
                border: pw.Border.all(color: valueColor.shade(50), width: 0.5),
              ),
              child: pw.Text(
                value,
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 10,
                  color: valueColor,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// بناء جدول البيانات
  static pw.Widget _buildDataTable(
    List<Map<String, dynamic>> tableData,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    // تحديد عناوين الأعمدة
    final headers = [
      'النوع',
      'الرقم المرجعي',
      'التاريخ',
      'الوصف',
      'مدين',
      'دائن',
      'العملة',
      'الحالة',
    ];

    // تحويل بيانات الجدول إلى تنسيق مناسب
    final List<List<String>> rows = [];
    final List<PdfColor> rowColors = []; // لتخزين ألوان الصفوف حسب النوع

    // إنشاء قاموس للربط بين الفواتير والمعاملات المالية المرتبطة بها
    Map<String, List<Map<String, dynamic>>> invoicePayments = {};

    // أولاً، نحدد المعاملات المالية التي تمثل دفعات للفواتير
    for (final item in tableData) {
      if (item['type'] == 'transaction' &&
          item.containsKey('invoiceId') &&
          item['invoiceId'] != null) {
        // تحويل معرف الفاتورة إلى نص لاستخدامه كمفتاح في القاموس
        String invoiceId = item['invoiceId'].toString();
        if (!invoicePayments.containsKey(invoiceId)) {
          invoicePayments[invoiceId] = [];
        }
        invoicePayments[invoiceId]!.add(item);
      }
    }

    for (final item in tableData) {
      String debitText = '';
      String creditText = '';
      String currencyText = 'ر.س';
      double amount = item['amount'] ?? 0.0;

      if (item['type'] == 'invoice') {
        // الفواتير: تظهر في عمود المدين
        debitText = amount.toStringAsFixed(2);

        // التحقق من وجود دفعات مرتبطة بهذه الفاتورة
        if (item.containsKey('id') && item['id'] != null && invoicePayments.containsKey(item['id'].toString())) {
          // حساب إجمالي المدفوعات لهذه الفاتورة
          double totalPaid = 0.0;
          for (var payment in invoicePayments[item['id'].toString()]!) {
            totalPaid += payment['amount'] ?? 0.0;
          }

          // إذا كانت الفاتورة مدفوعة بالكامل
          if (totalPaid >= amount) {
            creditText = amount.toStringAsFixed(2);
          }
          // إذا كانت الفاتورة مدفوعة جزئياً
          else if (totalPaid > 0) {
            creditText = totalPaid.toStringAsFixed(2);
          }
          // إذا كانت الفاتورة غير مدفوعة
          else {
            creditText = '0.00';
          }
        }
        // إذا كانت الفاتورة مدفوعة (حسب حقل الحالة)
        else if (item.containsKey('status')) {
          if (item['status'] == 'paid') {
            // مدفوعة بالكامل
            creditText = amount.toStringAsFixed(2);
          } else if (item['status'] == 'partiallyPaid') {
            // مدفوعة جزئياً - نفترض أنها مدفوعة بنسبة 50% إذا لم تكن هناك معاملات مرتبطة
            // هذا افتراض بسيط، يمكن تعديله لاحقاً بناءً على البيانات الفعلية
            creditText = (amount * 0.5).toStringAsFixed(2);
          } else {
            // غير مدفوعة
            creditText = '0.00';
          }
        }
        // إذا كانت الفاتورة غير مدفوعة
        else {
          creditText = '0.00';
        }
      }
      else if (item['type'] == 'transaction') {
        // المعاملات المالية: تظهر في عمود الدائن
        creditText = amount.toStringAsFixed(2);
        debitText = '0.00';
      }
      else if (item['type'] == 'service') {
        // الخدمات: تظهر في عمود المدين
        if (item.containsKey('customerPaymentMethod') &&
            item['customerPaymentMethod'] == CustomerPaymentMethod.monthlySubscription) {
          debitText = 'ضمن الاشتراك';
          creditText = '';
          currencyText = '';
        } else {
          debitText = amount.toStringAsFixed(2);
          creditText = '0.00';
        }
      }

      final row = <String>[
        _getTypeText(item['type']),
        item['reference'] ?? '',
        item['date'] != null ? DateFormat('dd/MM/yyyy').format(item['date']) : '',
        item['description'] ?? '',
        debitText,
        creditText,
        currencyText,
        item['status'] ?? '',
      ];
      rows.add(row);

      // تحديد لون الصف حسب النوع
      PdfColor rowColor;
      switch (item['type']) {
        case 'invoice':
          // تمييز الفواتير المدفوعة بلون مختلف
          if (creditText != '0.00' && creditText.isNotEmpty) {
            if (creditText == debitText) {
              // مدفوعة بالكامل
              rowColor = PdfColors.blue100;
            } else {
              // مدفوعة جزئياً
              rowColor = PdfColors.blue200.shade(50);
            }
          } else {
            // غير مدفوعة
            rowColor = PdfColors.blue50;
          }
          break;
        case 'transaction':
          rowColor = PdfColors.green50;
          break;
        case 'service':
          rowColor = PdfColors.amber50;
          break;
        default:
          rowColor = PdfColors.white;
      }
      rowColors.add(rowColor);
    }

    // تحديد عرض الأعمدة
    final columnWidths = [0.09, 0.15, 0.11, 0.18, 0.11, 0.11, 0.07, 0.18];

    // بناء الجدول
    return pw.Container(
      decoration: pw.BoxDecoration(
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 3,
          ),
        ],
      ),
      child: pw.ClipRRect(
        horizontalRadius: 8,
        verticalRadius: 8,
        child: pw.Table(
          tableWidth: pw.TableWidth.max,
          border: pw.TableBorder.all(
            color: PdfColors.grey300,
            width: 0.5,
          ),
          columnWidths: {
            for (int i = 0; i < columnWidths.length; i++)
              i: pw.FlexColumnWidth(columnWidths[i] * 100),
          },
          children: [
            // صف العناوين
            pw.TableRow(
              decoration: pw.BoxDecoration(
                gradient: pw.LinearGradient(
                  colors: [PdfColors.blue800, PdfColors.blue700],
                  begin: pw.Alignment.topCenter,
                  end: pw.Alignment.bottomCenter,
                ),
              ),
              children: List.generate(
                headers.length,
                (index) => pw.Container(
                  height: 30,
                  padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 6),
                  alignment: pw.Alignment.centerRight,
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      headers[index],
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 10,
                        color: PdfColors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // صفوف البيانات
            ...rows.asMap().entries.map(
              (entry) {
                final rowIndex = entry.key;
                final row = entry.value;
                final rowColor = rowColors[rowIndex];
                final borderColor = _getTypeColor(tableData[rowIndex]['type']);

                return pw.TableRow(
                  decoration: pw.BoxDecoration(
                    color: rowColor,
                    border: pw.Border(
                      left: pw.BorderSide(color: borderColor, width: 3),
                    ),
                  ),
                  children: List.generate(
                    headers.length,
                    (colIndex) {
                      // تحديد لون النص حسب العمود
                      PdfColor textColor = PdfColors.black;
                      if (colIndex == 0) { // عمود النوع
                        textColor = _getTypeColor(tableData[rowIndex]['type']);
                      } else if (colIndex == 4) { // عمود المدين
                        // المدين دائماً باللون الأحمر
                        if (row[colIndex].isNotEmpty && row[colIndex] != '0.00' && row[colIndex] != 'ضمن الاشتراك') {
                          textColor = PdfColors.red700;
                        }
                      } else if (colIndex == 5) { // عمود الدائن
                        // الدائن دائماً باللون الأخضر
                        if (row[colIndex].isNotEmpty && row[colIndex] != '0.00') {
                          textColor = PdfColors.green700;
                        }
                      } else if (colIndex == 7) { // عمود الحالة
                        textColor = PdfColors.blue700;
                      }

                      return pw.Container(
                        height: 28,
                        padding: const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                        alignment: pw.Alignment.centerRight,
                        child: pw.Directionality(
                          textDirection: pw.TextDirection.rtl,
                          child: pw.Text(
                            row[colIndex],
                            style: pw.TextStyle(
                              font: colIndex == 0 || colIndex == 6 ? boldFont : regularFont,
                              fontSize: 9,
                              color: textColor,
                            ),
                            maxLines: 1,
                            overflow: pw.TextOverflow.clip,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون حسب نوع العنصر
  static PdfColor _getTypeColor(String type) {
    switch (type) {
      case 'invoice':
        return PdfColors.blue700;
      case 'transaction':
        return PdfColors.green700;
      case 'service':
        return PdfColors.orange;
      default:
        return PdfColors.grey700;
    }
  }

  /// الحصول على نص نوع العنصر
  static String _getTypeText(String type) {
    switch (type) {
      case 'invoice':
        return 'فاتورة';
      case 'transaction':
        return 'إيراد';
      case 'service':
        return 'خدمة';
      default:
        return type;
    }
  }

  /// بناء عنصر معلومات العميل
  static pw.Widget _buildCustomerInfoItem(
    String title,
    String value,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              title,
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 9,
                color: PdfColors.grey800,
              ),
            ),
          ),
          pw.SizedBox(height: 3),
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: regularFont,
                fontSize: 10,
                color: PdfColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ملخص التدفقات النقدية
  static pw.Widget _buildCashFlowSummary({
    required double totalIncome,
    required double totalExpenses,
    required double totalSalaries,
    required double totalWithdrawals,
    required double totalTransfers,
    required double netCashFlow,
    required pw.Font arabicFont,
    required pw.Font arabicBoldFont,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [PdfColors.blue50, PdfColors.white],
          begin: pw.Alignment.topCenter,
          end: pw.Alignment.bottomCenter,
        ),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 3,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.stretch,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue800,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Directionality(
              textDirection: pw.TextDirection.rtl,
              child: pw.Text(
                'ملخص التدفقات النقدية',
                style: pw.TextStyle(
                  font: arabicBoldFont,
                  fontSize: 12,
                  color: PdfColors.white,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),
          pw.SizedBox(height: 12),
          // الصف الأول من الملخص
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildEnhancedSummaryItem(
                'إجمالي الإيرادات',
                '+${totalIncome.toStringAsFixed(2)} ر.س',
                arabicFont,
                arabicBoldFont,
                PdfColors.green700,
                PdfColors.green50,
                '+',
              ),
              _buildEnhancedSummaryItem(
                'إجمالي المصروفات',
                '-${totalExpenses.toStringAsFixed(2)} ر.س',
                arabicFont,
                arabicBoldFont,
                PdfColors.red700,
                PdfColors.red50,
                '-',
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          // الصف الثاني من الملخص
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildEnhancedSummaryItem(
                'إجمالي الرواتب',
                '-${totalSalaries.toStringAsFixed(2)} ر.س',
                arabicFont,
                arabicBoldFont,
                PdfColors.orange,
                PdfColors.amber50,
                'S',
              ),
              _buildEnhancedSummaryItem(
                'إجمالي السحبيات',
                '-${totalWithdrawals.toStringAsFixed(2)} ر.س',
                arabicFont,
                arabicBoldFont,
                PdfColors.orange,
                PdfColors.amber50,
                'W',
              ),
              _buildEnhancedSummaryItem(
                'إجمالي التحويلات',
                '${totalTransfers.toStringAsFixed(2)} ر.س',
                arabicFont,
                arabicBoldFont,
                PdfColors.blue700,
                PdfColors.blue50,
                'T',
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          // الصف الثالث من الملخص
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              _buildEnhancedSummaryItem(
                'صافي التدفق النقدي',
                '${netCashFlow >= 0 ? '+' : ''}${netCashFlow.toStringAsFixed(2)} ر.س',
                arabicFont,
                arabicBoldFont,
                netCashFlow >= 0 ? PdfColors.green700 : PdfColors.red700,
                netCashFlow >= 0 ? PdfColors.green50 : PdfColors.red50,
                netCashFlow >= 0 ? '\$' : '!',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء ملخص الحسابات البنكية
  static pw.Widget _buildBankAccountsSummary({
    required List<BankAccount> bankAccounts,
    required double totalBankBalance,
    required pw.Font arabicFont,
    required pw.Font arabicBoldFont,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [PdfColors.blue50, PdfColors.white],
          begin: pw.Alignment.topCenter,
          end: pw.Alignment.bottomCenter,
        ),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 3,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.stretch,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue800,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Directionality(
              textDirection: pw.TextDirection.rtl,
              child: pw.Text(
                'أرصدة الحسابات البنكية',
                style: pw.TextStyle(
                  font: arabicBoldFont,
                  fontSize: 12,
                  color: PdfColors.white,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ),
          pw.SizedBox(height: 12),
          // قائمة الحسابات البنكية
          ...bankAccounts.map((account) => pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 8),
            child: pw.Row(
              children: [
                pw.Container(
                  width: 24,
                  height: 24,
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue100,
                    shape: pw.BoxShape.circle,
                  ),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    'B',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 12,
                      color: PdfColors.blue800,
                    ),
                  ),
                ),
                pw.SizedBox(width: 8),
                pw.Expanded(
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      '${account.bankName} - ${account.accountName}',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
                pw.SizedBox(width: 8),
                pw.Directionality(
                  textDirection: pw.TextDirection.rtl,
                  child: pw.Text(
                    '${account.balance.toStringAsFixed(2)} ر.س',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 10,
                      color: account.balance >= 0 ? PdfColors.green700 : PdfColors.red700,
                    ),
                  ),
                ),
              ],
            ),
          )),
          pw.Divider(color: PdfColors.grey300),
          pw.SizedBox(height: 8),
          // إجمالي الأرصدة
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'إجمالي الأرصدة البنكية',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 12,
                  ),
                ),
              ),
              pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  '${totalBankBalance.toStringAsFixed(2)} ر.س',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 12,
                    color: totalBankBalance >= 0 ? PdfColors.green700 : PdfColors.red700,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء جدول بيانات التدفقات النقدية
  static pw.Widget _buildCashFlowDataTable(
    List<Map<String, dynamic>> tableData,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    // تحديد عناوين الأعمدة
    final headers = [
      'النوع',
      'التاريخ',
      'المرجع',
      'الوصف',
      'المبلغ',
      'الحالة',
    ];

    // تحويل بيانات الجدول إلى تنسيق مناسب
    final List<List<String>> rows = [];
    final List<PdfColor> rowColors = []; // لتخزين ألوان الصفوف حسب النوع

    for (final item in tableData) {
      String amountText = '';
      double amount = item['amount'] ?? 0.0;

      // تنسيق المبلغ حسب النوع
      if (item['type'] == 'income') {
        amountText = '+${amount.toStringAsFixed(2)} ر.س';
      } else if (item['type'] == 'expense' || item['type'] == 'salary' || item['type'] == 'withdrawal') {
        amountText = '${amount.toStringAsFixed(2)} ر.س';
      } else if (item['type'] == 'transfer') {
        amountText = '${amount.toStringAsFixed(2)} ر.س';
      }

      final row = <String>[
        _getCashFlowTypeText(item['type']),
        item['date'] != null ? DateFormat('dd/MM/yyyy').format(item['date']) : '',
        item['reference'] ?? '',
        item['description'] ?? '',
        amountText,
        item['status'] ?? '',
      ];
      rows.add(row);

      // تحديد لون الصف حسب النوع
      PdfColor rowColor;
      switch (item['type']) {
        case 'income':
          rowColor = PdfColors.green50;
          break;
        case 'expense':
          rowColor = PdfColors.red50;
          break;
        case 'salary':
          rowColor = PdfColors.orange50;
          break;
        case 'withdrawal':
          rowColor = PdfColors.amber50;
          break;
        case 'transfer':
          rowColor = PdfColors.blue50;
          break;
        default:
          rowColor = PdfColors.white;
      }
      rowColors.add(rowColor);
    }

    // تحديد عرض الأعمدة
    final columnWidths = [0.12, 0.15, 0.15, 0.28, 0.15, 0.15];

    // بناء الجدول
    return pw.Container(
      decoration: pw.BoxDecoration(
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColors.grey300,
            offset: const PdfPoint(0, 2),
            blurRadius: 3,
          ),
        ],
      ),
      child: pw.ClipRRect(
        horizontalRadius: 8,
        verticalRadius: 8,
        child: pw.Table(
          tableWidth: pw.TableWidth.max,
          border: pw.TableBorder.all(
            color: PdfColors.grey300,
            width: 0.5,
          ),
          columnWidths: {
            for (int i = 0; i < columnWidths.length; i++)
              i: pw.FlexColumnWidth(columnWidths[i] * 100),
          },
          children: [
            // صف العناوين
            pw.TableRow(
              decoration: pw.BoxDecoration(
                gradient: pw.LinearGradient(
                  colors: [PdfColors.blue800, PdfColors.blue700],
                  begin: pw.Alignment.topCenter,
                  end: pw.Alignment.bottomCenter,
                ),
              ),
              children: List.generate(
                headers.length,
                (index) => pw.Container(
                  height: 26, // Reducir la altura del encabezado
                  padding: const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 4), // Reducir el padding
                  alignment: pw.Alignment.centerRight,
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      headers[index],
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 9, // Reducir el tamaño de la fuente
                        color: PdfColors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // صفوف البيانات
            ...rows.asMap().entries.map(
              (entry) {
                final rowIndex = entry.key;
                final row = entry.value;
                final rowColor = rowColors[rowIndex];
                final borderColor = _getCashFlowTypeColor(tableData[rowIndex]['type']);

                return pw.TableRow(
                  decoration: pw.BoxDecoration(
                    color: rowColor,
                    border: pw.Border(
                      right: pw.BorderSide(color: borderColor, width: 3),
                    ),
                  ),
                  children: List.generate(
                    headers.length,
                    (colIndex) {
                      // تحديد لون النص حسب العمود
                      PdfColor textColor = PdfColors.black;
                      if (colIndex == 0) { // عمود النوع
                        textColor = _getCashFlowTypeColor(tableData[rowIndex]['type']);
                      } else if (colIndex == 4) { // عمود المبلغ
                        final type = tableData[rowIndex]['type'];
                        if (type == 'income') {
                          textColor = PdfColors.green700;
                        } else if (type == 'expense' || type == 'salary' || type == 'withdrawal') {
                          textColor = PdfColors.red700;
                        } else if (type == 'transfer') {
                          textColor = PdfColors.blue700;
                        }
                      } else if (colIndex == 5) { // عمود الحالة
                        textColor = PdfColors.blue700;
                      }

                      return pw.Container(
                        height: 24, // Reducir la altura para que quepan más filas
                        padding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4), // Reducir el padding
                        alignment: pw.Alignment.centerRight,
                        child: pw.Directionality(
                          textDirection: pw.TextDirection.rtl,
                          child: pw.Text(
                            row[colIndex],
                            style: pw.TextStyle(
                              font: colIndex == 0 ? boldFont : regularFont,
                              fontSize: 8, // Reducir el tamaño de la fuente
                              color: textColor,
                            ),
                            maxLines: 1,
                            overflow: pw.TextOverflow.clip,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون حسب نوع العنصر في التدفقات النقدية
  static PdfColor _getCashFlowTypeColor(String type) {
    switch (type) {
      case 'income':
        return PdfColors.green700;
      case 'expense':
        return PdfColors.red700;
      case 'salary':
        return PdfColors.orange;
      case 'withdrawal':
        return PdfColors.amber700;
      case 'transfer':
        return PdfColors.blue700;
      default:
        return PdfColors.grey700;
    }
  }

  /// الحصول على نص نوع العنصر في التدفقات النقدية
  static String _getCashFlowTypeText(String type) {
    switch (type) {
      case 'income':
        return 'إيراد';
      case 'expense':
        return 'مصروف';
      case 'salary':
        return 'راتب';
      case 'withdrawal':
        return 'سحبية';
      case 'transfer':
        return 'تحويل';
      default:
        return type;
    }
  }



  /// بناء ملخص الأرباح والخسائر
  static pw.Widget _buildProfitLossSummary({
    required double totalRevenue,
    required double totalExpenses,
    required double grossProfit,
    required double netProfit,
    required pw.Font arabicFont,
    required pw.Font arabicBoldFont,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              'ملخص الأرباح والخسائر',
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 12,
                color: PdfColors.blue800,
              ),
            ),
          ),
          pw.Divider(color: PdfColors.blue200, thickness: 0.5),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildFinancialSummaryItem(
                  'إجمالي الإيرادات',
                  totalRevenue,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.green,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: _buildFinancialSummaryItem(
                  'إجمالي المصروفات',
                  totalExpenses,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.red,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildFinancialSummaryItem(
                  'إجمالي الربح',
                  grossProfit,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.blue,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: _buildFinancialSummaryItem(
                  'صافي الربح',
                  netProfit,
                  arabicFont,
                  arabicBoldFont,
                  netProfit >= 0 ? PdfColors.green : PdfColors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء ملخص تحليل ربحية الخدمات
  static pw.Widget _buildServiceProfitabilitySummary({
    required int highProfitabilityCount,
    required int mediumProfitabilityCount,
    required int lowProfitabilityCount,
    required int totalServices,
    required pw.Font arabicFont,
    required pw.Font arabicBoldFont,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              'ملخص تحليل ربحية الخدمات',
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 12,
                color: PdfColors.blue800,
              ),
            ),
          ),
          pw.Divider(color: PdfColors.blue200, thickness: 0.5),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildCountSummaryItem(
                  'خدمات عالية الربحية',
                  highProfitabilityCount,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.green,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: _buildCountSummaryItem(
                  'خدمات متوسطة الربحية',
                  mediumProfitabilityCount,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.orange,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildCountSummaryItem(
                  'خدمات منخفضة الربحية',
                  lowProfitabilityCount,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.red,
                ),
              ),
              pw.SizedBox(width: 10),
              pw.Expanded(
                child: _buildCountSummaryItem(
                  'إجمالي الخدمات',
                  totalServices,
                  arabicFont,
                  arabicBoldFont,
                  PdfColors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص مالي
  static pw.Widget _buildFinancialSummaryItem(
    String label,
    double value,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 10,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              '${value.toStringAsFixed(2)} ر.س',
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 12,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص عددي
  static pw.Widget _buildCountSummaryItem(
    String label,
    int count,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 10,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              count.toString(),
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 12,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء جدول الأرباح والخسائر
  static pw.Widget _buildProfitLossTable({
    required List<Map<String, dynamic>> tableData,
    required pw.Font arabicFont,
    required pw.Font arabicBoldFont,
  }) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.blue100),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'الفئة',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'المبلغ (ر.س)',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
          ],
        ),
        // صفوف البيانات
        ...tableData.map((row) {
          final type = row['type'] as String;
          final category = row['category'] as String;
          final amount = row['amount'] as double;

          // تحديد لون الصف بناءً على النوع
          PdfColor? rowColor;
          pw.TextStyle textStyle;

          if (type == 'header') {
            rowColor = PdfColors.grey200;
            textStyle = pw.TextStyle(font: arabicBoldFont, fontSize: 10);
          } else if (type == 'revenue') {
            rowColor = PdfColors.green50;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          } else if (type == 'expense') {
            rowColor = PdfColors.red50;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          } else if (type == 'profit') {
            rowColor = PdfColors.blue50;
            textStyle = pw.TextStyle(font: arabicBoldFont, fontSize: 10);
          } else {
            rowColor = null;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          }

          return pw.TableRow(
            decoration: rowColor != null ? pw.BoxDecoration(color: rowColor) : null,
            children: [
              pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: pw.Directionality(
                  textDirection: pw.TextDirection.rtl,
                  child: pw.Text(
                    category,
                    style: textStyle,
                  ),
                ),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(5),
                child: pw.Directionality(
                  textDirection: pw.TextDirection.rtl,
                  child: pw.Text(
                    type == 'header' ? '' : amount.toStringAsFixed(2),
                    style: textStyle,
                    textAlign: pw.TextAlign.left,
                  ),
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء جدول تحليل ربحية الخدمات
  static pw.Widget _buildServiceProfitabilityTable({
    required List<Map<String, dynamic>> tableData,
    required pw.Font arabicFont,
    required pw.Font arabicBoldFont,
  }) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(3), // الخدمة
        1: const pw.FlexColumnWidth(2), // الإيرادات
        2: const pw.FlexColumnWidth(2), // التكاليف
        3: const pw.FlexColumnWidth(2), // الربح
        4: const pw.FlexColumnWidth(1.5), // هامش الربح
        5: const pw.FlexColumnWidth(1.5), // العائد
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.blue100),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'الخدمة',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'الإيرادات',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'التكاليف',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'الربح',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'هامش الربح',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(5),
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'العائد',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ),
          ],
        ),
        // صفوف البيانات
        ...tableData.map((row) {
          final type = row['type'] as String;

          // تحديد لون الصف بناءً على النوع
          PdfColor? rowColor;
          pw.TextStyle textStyle;

          if (type == 'header') {
            rowColor = PdfColors.grey200;
            textStyle = pw.TextStyle(font: arabicBoldFont, fontSize: 10);
          } else if (type == 'category') {
            rowColor = PdfColors.blue100;
            textStyle = pw.TextStyle(font: arabicBoldFont, fontSize: 10);
          } else if (type == 'high') {
            rowColor = PdfColors.green50;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          } else if (type == 'medium') {
            rowColor = PdfColors.orange50;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          } else if (type == 'low') {
            rowColor = PdfColors.red50;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          } else {
            rowColor = null;
            textStyle = pw.TextStyle(font: arabicFont, fontSize: 10);
          }

          // إذا كان نوع الصف هو عنوان أو فئة، نعرض فقط اسم الخدمة
          if (type == 'header' || type == 'category') {
            return pw.TableRow(
              decoration: rowColor != null ? pw.BoxDecoration(color: rowColor) : null,
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      row['service'] as String,
                      style: textStyle,
                    ),
                  ),
                ),
                pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Container()),
                pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Container()),
                pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Container()),
                pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Container()),
                pw.Padding(padding: const pw.EdgeInsets.all(5), child: pw.Container()),
              ],
            );
          } else {
            // إذا كان نوع الصف هو بيانات، نعرض جميع البيانات
            return pw.TableRow(
              decoration: rowColor != null ? pw.BoxDecoration(color: rowColor) : null,
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      row['service'] as String,
                      style: textStyle,
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      (row['revenue'] as double).toStringAsFixed(2),
                      style: textStyle,
                      textAlign: pw.TextAlign.left,
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      (row['costs'] as double).toStringAsFixed(2),
                      style: textStyle,
                      textAlign: pw.TextAlign.left,
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      (row['profit'] as double).toStringAsFixed(2),
                      style: textStyle,
                      textAlign: pw.TextAlign.left,
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      '${(row['margin'] as double).toStringAsFixed(1)}%',
                      style: textStyle,
                      textAlign: pw.TextAlign.left,
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(5),
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      '${(row['roi'] as double).toStringAsFixed(1)}%',
                      style: textStyle,
                      textAlign: pw.TextAlign.left,
                    ),
                  ),
                ),
              ],
            );
          }
        }),
      ],
    );
  }
}