import 'package:flutter/material.dart';
import 'package:icecorner/config/constants.dart';
import 'package:provider/provider.dart';
import '../../../config/routes.dart';
import '../../../shared/models/user.dart';
import '../../../state/user_state.dart';
import '../widgets/user_info_card.dart';

class UserDetailsScreen extends StatefulWidget {
  final User user;

  const UserDetailsScreen({
    super.key,
    required this.user,
  });

  @override
  State<UserDetailsScreen> createState() => _UserDetailsScreenState();
}

class _UserDetailsScreenState extends State<UserDetailsScreen> {
  late User _user;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _user = widget.user;

    // Use a post-frame callback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadUserDetails();
      }
    });
  }

  Future<void> _loadUserDetails() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<UserState>(context, listen: false)
          .loadUserById(_user.id.toString()); // Convert id to String

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Use a safe way to show the snackbar
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmation() {
    // Get the BuildContext before any async operations
    final currentContext = context;

    showDialog(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستخدم "${_user.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // Close the dialog first
              Navigator.pop(dialogContext);

              // Then perform the delete operation
              _deleteUser();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // Separate method to handle the delete operation
  Future<void> _deleteUser() async {
    if (!mounted) return;

    // Get references before any async operations
    final currentContext = context;
    final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
    final navigator = Navigator.of(currentContext);

    final success = await Provider.of<UserState>(currentContext, listen: false)
        .deleteUser(_user.id.toString()); // Convert id to String

    if (!mounted) return;

    setState(() {
      // Update local state if needed
    });

    // Show the snackbar
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(
          success
              ? 'تم حذف المستخدم بنجاح'
              : 'فشل حذف المستخدم',
        ),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );

    // Navigate if successful
    if (success) {
      navigator.pop();
    }
  }

  Future<void> _toggleUserStatus() async {
    setState(() {
      _isLoading = true;
    });

    final updatedUser = _user.copyWith(isActive: !_user.isActive);

    final success = await Provider.of<UserState>(context, listen: false)
        .updateUser(updatedUser);

    if (mounted) {
      setState(() {
        _isLoading = false;
        if (success) {
          _user = updatedUser;
        }
      });

      // Use a safe way to show the snackbar
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'تم تحديث حالة المستخدم بنجاح'
                : 'فشل تحديث حالة المستخدم',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserState>(
      builder: (context, userState, child) {
        final user = userState.selectedUser ?? _user;

        return Scaffold(
          appBar: AppBar(
            title: Text('بيانات ${user.name}'),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) async {
                  if (value == 'edit') {
                    await Navigator.pushNamed(
                      context,
                      AppRoutes.userEdit,
                      arguments: user,
                    );
                    await _loadUserDetails();
                  } else if (value == 'permissions') {
                    await Navigator.pushNamed(
                      context,
                      AppRoutes.userPermissions,
                      arguments: user,
                    );
                    await _loadUserDetails();
                  } else if (value == 'activate') {
                    _toggleUserStatus();
                  } else if (value == 'deactivate') {
                    _toggleUserStatus();
                  } else if (value == 'delete') {
                    _showDeleteConfirmation();
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل البيانات'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'permissions',
                    child: Row(
                      children: [
                        Icon(Icons.security, size: 20),
                        SizedBox(width: 8),
                        Text('إدارة الصلاحيات'),
                      ],
                    ),
                  ),
                  if (!user.isActive)
                    const PopupMenuItem(
                      value: 'activate',
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, size: 20, color: Colors.green),
                          SizedBox(width: 8),
                          Text('تفعيل المستخدم'),
                        ],
                      ),
                    ),
                  if (user.isActive)
                    const PopupMenuItem(
                      value: 'deactivate',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('تعطيل المستخدم'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف المستخدم', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // User info card
                      UserInfoCard(user: user),
                      const SizedBox(height: AppDimensions.paddingL),

                      // Actions
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                await Navigator.pushNamed(
                                  context,
                                  AppRoutes.userEdit,
                                  arguments: user,
                                );
                                await _loadUserDetails();
                              },
                              icon: const Icon(Icons.edit),
                              label: const Text('تعديل البيانات'),
                            ),
                          ),
                          const SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                await Navigator.pushNamed(
                                  context,
                                  AppRoutes.userPermissions,
                                  arguments: user,
                                );
                                await _loadUserDetails();
                              },
                              icon: const Icon(Icons.security),
                              label: const Text('الصلاحيات'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}
