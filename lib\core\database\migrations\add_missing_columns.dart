import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';

/// إضافة الأعمدة المفقودة في الجداول
class AddMissingColumns {
  static const String _tag = 'AddMissingColumns';

  /// تطبيق التحديثات على قاعدة البيانات
  static Future<void> migrate(Database db) async {
    try {
      if (kDebugMode) {
        print('$_tag: بدء إضافة الأعمدة المفقودة...');
      }

      // إضافة أعمدة جدول cash_boxes
      await _addCashBoxColumns(db);
      
      // إضافة أعمدة جدول employees
      await _addEmployeeColumns(db);
      
      // إضافة أعمدة جدول service_requests
      await _addServiceRequestColumns(db);

      if (kDebugMode) {
        print('$_tag: ✅ تم إضافة جميع الأعمدة المفقودة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة الأعمدة المفقودة: $e');
      }
      rethrow;
    }
  }

  /// إضافة أعمدة جدول cash_boxes
  static Future<void> _addCashBoxColumns(Database db) async {
    try {
      // التحقق من وجود العمود currency
      final cashBoxColumns = await _getTableColumns(db, 'cash_boxes');
      
      if (!cashBoxColumns.contains('currency')) {
        await db.execute('ALTER TABLE cash_boxes ADD COLUMN currency TEXT DEFAULT "SAR"');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود currency إلى جدول cash_boxes');
        }
      }

      if (!cashBoxColumns.contains('firestore_id')) {
        await db.execute('ALTER TABLE cash_boxes ADD COLUMN firestore_id TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود firestore_id إلى جدول cash_boxes');
        }
      }

      if (!cashBoxColumns.contains('last_sync_time')) {
        await db.execute('ALTER TABLE cash_boxes ADD COLUMN last_sync_time TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود last_sync_time إلى جدول cash_boxes');
        }
      }

      if (!cashBoxColumns.contains('sync_status')) {
        await db.execute('ALTER TABLE cash_boxes ADD COLUMN sync_status TEXT DEFAULT "pending"');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود sync_status إلى جدول cash_boxes');
        }
      }

      if (!cashBoxColumns.contains('version')) {
        await db.execute('ALTER TABLE cash_boxes ADD COLUMN version INTEGER DEFAULT 1');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود version إلى جدول cash_boxes');
        }
      }

      if (!cashBoxColumns.contains('is_deleted')) {
        await db.execute('ALTER TABLE cash_boxes ADD COLUMN is_deleted INTEGER DEFAULT 0');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود is_deleted إلى جدول cash_boxes');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة أعمدة جدول cash_boxes: $e');
      }
    }
  }

  /// إضافة أعمدة جدول employees
  static Future<void> _addEmployeeColumns(Database db) async {
    try {
      // التحقق من وجود الأعمدة
      final employeeColumns = await _getTableColumns(db, 'employees');
      
      if (!employeeColumns.contains('payment_type')) {
        await db.execute('ALTER TABLE employees ADD COLUMN payment_type TEXT DEFAULT "monthly"');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود payment_type إلى جدول employees');
        }
      }

      if (!employeeColumns.contains('firestore_id')) {
        await db.execute('ALTER TABLE employees ADD COLUMN firestore_id TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود firestore_id إلى جدول employees');
        }
      }

      if (!employeeColumns.contains('last_sync_time')) {
        await db.execute('ALTER TABLE employees ADD COLUMN last_sync_time TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود last_sync_time إلى جدول employees');
        }
      }

      if (!employeeColumns.contains('sync_status')) {
        await db.execute('ALTER TABLE employees ADD COLUMN sync_status TEXT DEFAULT "pending"');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود sync_status إلى جدول employees');
        }
      }

      if (!employeeColumns.contains('version')) {
        await db.execute('ALTER TABLE employees ADD COLUMN version INTEGER DEFAULT 1');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود version إلى جدول employees');
        }
      }

      if (!employeeColumns.contains('is_deleted')) {
        await db.execute('ALTER TABLE employees ADD COLUMN is_deleted INTEGER DEFAULT 0');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود is_deleted إلى جدول employees');
        }
      }

      if (!employeeColumns.contains('updated_at')) {
        await db.execute('ALTER TABLE employees ADD COLUMN updated_at TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود updated_at إلى جدول employees');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة أعمدة جدول employees: $e');
      }
    }
  }

  /// إضافة أعمدة جدول service_requests
  static Future<void> _addServiceRequestColumns(Database db) async {
    try {
      // التحقق من وجود الأعمدة
      final serviceRequestColumns = await _getTableColumns(db, 'service_requests');
      
      if (!serviceRequestColumns.contains('technician_daily_rate')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN technician_daily_rate REAL');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود technician_daily_rate إلى جدول service_requests');
        }
      }

      if (!serviceRequestColumns.contains('firestore_id')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN firestore_id TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود firestore_id إلى جدول service_requests');
        }
      }

      if (!serviceRequestColumns.contains('last_sync_time')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN last_sync_time TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود last_sync_time إلى جدول service_requests');
        }
      }

      if (!serviceRequestColumns.contains('sync_status')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN sync_status TEXT DEFAULT "pending"');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود sync_status إلى جدول service_requests');
        }
      }

      if (!serviceRequestColumns.contains('version')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN version INTEGER DEFAULT 1');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود version إلى جدول service_requests');
        }
      }

      if (!serviceRequestColumns.contains('is_deleted')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN is_deleted INTEGER DEFAULT 0');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود is_deleted إلى جدول service_requests');
        }
      }

      if (!serviceRequestColumns.contains('updated_at')) {
        await db.execute('ALTER TABLE service_requests ADD COLUMN updated_at TEXT');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود updated_at إلى جدول service_requests');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة أعمدة جدول service_requests: $e');
      }
    }
  }

  /// الحصول على أعمدة الجدول
  static Future<List<String>> _getTableColumns(Database db, String tableName) async {
    try {
      final result = await db.rawQuery('PRAGMA table_info($tableName)');
      return result.map((row) => row['name'] as String).toList();
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في الحصول على أعمدة الجدول $tableName: $e');
      }
      return [];
    }
  }

  /// إضافة أعمدة المزامنة لجدول محدد
  static Future<void> addSyncColumnsToTable(Database db, String tableName) async {
    try {
      final columns = await _getTableColumns(db, tableName);
      
      if (!columns.contains('firestore_id')) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN firestore_id TEXT');
      }

      if (!columns.contains('last_sync_time')) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN last_sync_time TEXT');
      }

      if (!columns.contains('sync_status')) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN sync_status TEXT DEFAULT "pending"');
      }

      if (!columns.contains('version')) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN version INTEGER DEFAULT 1');
      }

      if (!columns.contains('is_deleted')) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN is_deleted INTEGER DEFAULT 0');
      }

      if (!columns.contains('updated_at')) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN updated_at TEXT');
      }

      if (kDebugMode) {
        print('$_tag: ✅ تم إضافة أعمدة المزامنة إلى جدول $tableName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة أعمدة المزامنة إلى جدول $tableName: $e');
      }
    }
  }

  /// إضافة أعمدة المزامنة لجميع الجداول
  static Future<void> addSyncColumnsToAllTables(Database db) async {
    final tables = [
      'customers',
      'suppliers',
      'inventory',
      'invoices',
      'cash_box_transactions',
      'categories',
    ];

    for (final table in tables) {
      await addSyncColumnsToTable(db, table);
    }
  }
}
