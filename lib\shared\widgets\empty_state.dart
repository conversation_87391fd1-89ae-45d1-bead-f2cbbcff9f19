import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// عنصر لعرض حالة فارغة
class EmptyState extends StatelessWidget {
  /// أيقونة الحالة الفارغة
  final IconData icon;

  /// عنوان الحالة الفارغة
  final String title;

  /// رسالة الحالة الفارغة
  final String message;

  /// إجراء اختياري (زر)
  final Widget? action;

  /// لون الأيقونة
  final Color? iconColor;

  /// حجم الأيقونة
  final double iconSize;

  /// تباعد داخلي
  final EdgeInsetsGeometry padding;

  /// إنشاء عنصر حالة فارغة
  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.message,
    this.action,
    this.iconColor,
    this.iconSize = 80,
    this.padding = const EdgeInsets.all(AppDimensions.spacingL),
  });

  @override
  Widget build(BuildContext context) {
    final effectiveIconColor = iconColor ?? AppColors.primary.withAlpha(100);

    return Center(
      child: Padding(
        padding: padding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة مع تأثير ظل
            Container(
              padding: const EdgeInsets.all(AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: effectiveIconColor.withAlpha(15),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: effectiveIconColor,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingM),

            // عنوان
            Text(
              title,
              style: AppTextStyles.headingMedium.copyWith(
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingS),

            // رسالة
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            // إجراء (اختياري)
            if (action != null) ...[
              const SizedBox(height: AppDimensions.spacingL),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
