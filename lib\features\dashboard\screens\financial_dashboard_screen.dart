import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/ui_components.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/bank_account_repository.dart';
import '../../../core/repositories/customer_repository.dart';
import '../../../core/repositories/supplier_repository.dart';
import '../../../shared/models/transaction.dart' as transaction_models;
import '../../../shared/models/bank_account.dart';
import '../widgets/financial_summary.dart';

/// شاشة لوحة التحكم المالية
class FinancialDashboardScreen extends StatefulWidget {
  const FinancialDashboardScreen({super.key});

  @override
  State<FinancialDashboardScreen> createState() => _FinancialDashboardScreenState();
}

class _FinancialDashboardScreenState extends State<FinancialDashboardScreen> {
  final TransactionRepository _transactionRepository = TransactionRepository();
  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final BankAccountRepository _bankAccountRepository = BankAccountRepository();
  final CustomerRepository _customerRepository = CustomerRepository();
  final SupplierRepository _supplierRepository = SupplierRepository();

  bool _isLoading = true;
  String _selectedPeriod = 'month'; // day, week, month, quarter, year
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  
  // بيانات مالية
  double _totalRevenue = 0;
  double _totalExpenses = 0;
  double _netProfit = 0;
  double _pendingInvoicesAmount = 0;
  double _cashBalance = 0;
  double _bankBalance = 0;
  double _accountsReceivable = 0;
  double _accountsPayable = 0;
  
  // بيانات الرسم البياني
  List<FlSpot> _revenueSpots = [];
  List<FlSpot> _expenseSpots = [];
  List<FlSpot> _profitSpots = [];
  List<String> _periodLabels = [];
  
  // بيانات الحسابات البنكية
  List<BankAccount> _bankAccounts = [];
  
  // بيانات المؤشرات
  int _totalCustomers = 0;
  int _totalSuppliers = 0;
  int _pendingInvoices = 0;
  int _pendingServiceRequests = 0;
  
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }
  
  /// تحميل بيانات لوحة التحكم
  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // تحديث نطاق التاريخ بناءً على الفترة المحددة
      _updateDateRange();
      
      // تحميل المعاملات المالية
      final transactions = await _transactionRepository.getTransactionsByDateRange(
        _startDate,
        _endDate,
      );
      
      // تحميل الفواتير
      final invoices = await _invoiceRepository.getInvoicesByDateRange(
        _startDate,
        _endDate,
      );
      
      // تحميل طلبات الخدمة
      final serviceRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(
        _startDate,
        _endDate,
      );
      
      // تحميل الحسابات البنكية
      _bankAccounts = await _bankAccountRepository.getAllBankAccounts();
      
      // تحميل العملاء
      final customers = await _customerRepository.getAllCustomers();
      
      // تحميل الموردين
      final suppliers = await _supplierRepository.getAllSuppliers();
      
      // حساب الإيرادات والمصروفات
      _calculateFinancials(transactions, invoices, serviceRequests);
      
      // حساب أرصدة الحسابات
      _calculateBalances();
      
      // إعداد بيانات المؤشرات
      _totalCustomers = customers.length;
      _totalSuppliers = suppliers.length;
      _pendingInvoices = invoices.where((invoice) => invoice.status == 'unpaid').length;
      _pendingServiceRequests = serviceRequests.where((request) => request.status == 'pending').length;
      
      // إعداد بيانات الرسم البياني
      _prepareChartData(transactions);
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
  
  /// تحديث نطاق التاريخ بناءً على الفترة المحددة
  void _updateDateRange() {
    final now = DateTime.now();
    
    switch (_selectedPeriod) {
      case 'day':
        _startDate = DateTime(now.year, now.month, now.day);
        _endDate = now;
        break;
      case 'week':
        _startDate = now.subtract(Duration(days: now.weekday - 1));
        _endDate = now;
        break;
      case 'month':
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = now;
        break;
      case 'quarter':
        final currentQuarter = (now.month - 1) ~/ 3;
        _startDate = DateTime(now.year, currentQuarter * 3 + 1, 1);
        _endDate = now;
        break;
      case 'year':
        _startDate = DateTime(now.year, 1, 1);
        _endDate = now;
        break;
    }
  }
  
  /// حساب البيانات المالية
  void _calculateFinancials(
    List<transaction_models.Transaction> transactions,
    List<dynamic> invoices,
    List<dynamic> serviceRequests,
  ) {
    // إعادة تعيين القيم
    _totalRevenue = 0;
    _totalExpenses = 0;
    _pendingInvoicesAmount = 0;
    
    // حساب الإيرادات والمصروفات من المعاملات
    for (final transaction in transactions) {
      if (transaction.type == transaction_models.TransactionType.income) {
        _totalRevenue += transaction.amount;
      } else {
        _totalExpenses += transaction.amount;
      }
    }
    
    // حساب مبلغ الفواتير المعلقة
    for (final invoice in invoices) {
      if (invoice.status == 'unpaid') {
        _pendingInvoicesAmount += invoice.total;
      }
    }
    
    // حساب صافي الربح
    _netProfit = _totalRevenue - _totalExpenses;
  }
  
  /// حساب أرصدة الحسابات
  void _calculateBalances() {
    // إعادة تعيين القيم
    _cashBalance = 0;
    _bankBalance = 0;
    
    // حساب رصيد البنك
    for (final account in _bankAccounts) {
      _bankBalance += account.balance;
    }
    
    // تعيين قيم افتراضية للذمم المدينة والدائنة (يجب استبدالها بحسابات حقيقية)
    _accountsReceivable = _pendingInvoicesAmount;
    _accountsPayable = _totalExpenses * 0.3; // قيمة افتراضية
  }
  
  /// إعداد بيانات الرسم البياني
  void _prepareChartData(List<transaction_models.Transaction> transactions) {
    _revenueSpots = [];
    _expenseSpots = [];
    _profitSpots = [];
    _periodLabels = [];
    
    // تحديد عدد النقاط في الرسم البياني
    int points;
    switch (_selectedPeriod) {
      case 'day':
        points = 24; // ساعة
        break;
      case 'week':
        points = 7; // يوم
        break;
      case 'month':
        points = 30; // يوم
        break;
      case 'quarter':
        points = 3; // شهر
        break;
      case 'year':
        points = 12; // شهر
        break;
      default:
        points = 30;
    }
    
    // إنشاء قائمة بالفترات
    List<DateTime> periods = [];
    for (int i = 0; i < points; i++) {
      switch (_selectedPeriod) {
        case 'day':
          periods.add(_startDate.add(Duration(hours: i)));
          break;
        case 'week':
        case 'month':
          periods.add(_startDate.add(Duration(days: i)));
          break;
        case 'quarter':
          periods.add(DateTime(_startDate.year, _startDate.month + i, 1));
          break;
        case 'year':
          periods.add(DateTime(_startDate.year, i + 1, 1));
          break;
      }
    }
    
    // تجميع المعاملات حسب الفترة
    for (int i = 0; i < periods.length; i++) {
      final periodStart = periods[i];
      final periodEnd = i < periods.length - 1 ? periods[i + 1] : _endDate;
      
      double periodRevenue = 0;
      double periodExpense = 0;
      
      for (final transaction in transactions) {
        if (transaction.date.isAfter(periodStart) && transaction.date.isBefore(periodEnd)) {
          if (transaction.type == transaction_models.TransactionType.income) {
            periodRevenue += transaction.amount;
          } else {
            periodExpense += transaction.amount;
          }
        }
      }
      
      _revenueSpots.add(FlSpot(i.toDouble(), periodRevenue));
      _expenseSpots.add(FlSpot(i.toDouble(), periodExpense));
      _profitSpots.add(FlSpot(i.toDouble(), periodRevenue - periodExpense));
      
      // إضافة تسميات الفترات
      switch (_selectedPeriod) {
        case 'day':
          _periodLabels.add(DateFormat('HH:mm').format(periodStart));
          break;
        case 'week':
        case 'month':
          _periodLabels.add(DateFormat('d').format(periodStart));
          break;
        case 'quarter':
        case 'year':
          _periodLabels.add(DateFormat('MMM').format(periodStart));
          break;
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم المالية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildDashboardContent(),
    );
  }
  
  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPeriodSelector(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildFinancialSummary(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildFinancialChart(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildAccountsOverview(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildQuickActions(),
          const SizedBox(height: AppDimensions.spacingL),
          _buildKeyMetrics(),
        ],
      ),
    );
  }
  
  Widget _buildPeriodSelector() {
    return StyledCard(
      title: 'الفترة الزمنية',
      icon: Icons.date_range,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildPeriodButton('اليوم', 'day'),
            _buildPeriodButton('الأسبوع', 'week'),
            _buildPeriodButton('الشهر', 'month'),
            _buildPeriodButton('الربع', 'quarter'),
            _buildPeriodButton('السنة', 'year'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPeriodButton(String label, String period) {
    final isSelected = _selectedPeriod == period;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingS),
      child: StyledButton(
        label: label,
        type: isSelected ? StyledButtonType.primary : StyledButtonType.secondary,
        onPressed: () {
          setState(() {
            _selectedPeriod = period;
          });
          _loadDashboardData();
        },
      ),
    );
  }
  
  Widget _buildFinancialSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص مالي',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'إجمالي الإيرادات',
                _totalRevenue,
                Icons.arrow_upward,
                AppColors.success,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildSummaryCard(
                'إجمالي المصروفات',
                _totalExpenses,
                Icons.arrow_downward,
                AppColors.error,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'صافي الربح',
                _netProfit,
                Icons.account_balance,
                _netProfit >= 0 ? AppColors.success : AppColors.error,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildSummaryCard(
                'الفواتير المعلقة',
                _pendingInvoicesAmount,
                Icons.pending_actions,
                AppColors.warning,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return StyledCard(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: AppTextStyles.labelMedium,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          MoneyCounterAnimation(
            endValue: amount,
            currency: 'ر.س',
            duration: const Duration(milliseconds: 1500),
            style: AppTextStyles.titleLarge.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFinancialChart() {
    return StyledCard(
      title: 'تحليل مالي',
      icon: Icons.insert_chart,
      child: Column(
        children: [
          SizedBox(
            height: 300,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() % 5 == 0 && value.toInt() < _periodLabels.length) {
                            return Text(
                              _periodLabels[value.toInt()],
                              style: AppTextStyles.labelSmall,
                            );
                          }
                          return const Text('');
                        },
                        reservedSize: 30,
                      ),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    // خط الإيرادات
                    LineChartBarData(
                      spots: _revenueSpots,
                      isCurved: true,
                      color: AppColors.success,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.success.withOpacity(0.1),
                      ),
                    ),
                    // خط المصروفات
                    LineChartBarData(
                      spots: _expenseSpots,
                      isCurved: true,
                      color: AppColors.error,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.error.withOpacity(0.1),
                      ),
                    ),
                    // خط الأرباح
                    LineChartBarData(
                      spots: _profitSpots,
                      isCurved: true,
                      color: AppColors.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.primary.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildChartLegendItem('الإيرادات', AppColors.success),
                const SizedBox(width: AppDimensions.spacingL),
                _buildChartLegendItem('المصروفات', AppColors.error),
                const SizedBox(width: AppDimensions.spacingL),
                _buildChartLegendItem('صافي الربح', AppColors.primary),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildChartLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Text(
          label,
          style: AppTextStyles.labelMedium,
        ),
      ],
    );
  }
  
  Widget _buildAccountsOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظرة عامة على الحسابات',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildAccountCard(
                'النقد',
                _cashBalance,
                Icons.money,
                AppColors.success,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildAccountCard(
                'البنك',
                _bankBalance,
                Icons.account_balance,
                AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildAccountCard(
                'الذمم المدينة',
                _accountsReceivable,
                Icons.arrow_circle_up,
                AppColors.info,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildAccountCard(
                'الذمم الدائنة',
                _accountsPayable,
                Icons.arrow_circle_down,
                AppColors.warning,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildAccountCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return StyledCard(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: AppTextStyles.labelMedium,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          MoneyCounterAnimation(
            endValue: amount,
            currency: 'ر.س',
            duration: const Duration(milliseconds: 1500),
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'تقرير الأرباح والخسائر',
                Icons.assessment,
                AppColors.primary,
                () => Navigator.pushNamed(context, AppRoutes.profitLossReport),
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildActionCard(
                'تحليل ربحية الخدمات',
                Icons.pie_chart,
                AppColors.success,
                () => Navigator.pushNamed(context, AppRoutes.serviceProfitability),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'تقرير التدفق النقدي',
                Icons.account_balance_wallet,
                AppColors.info,
                () => Navigator.pushNamed(context, AppRoutes.cashFlowReport),
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildActionCard(
                'إدارة الضرائب',
                Icons.receipt_long,
                AppColors.warning,
                () => Navigator.pushNamed(context, AppRoutes.taxManagement),
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return StyledCard(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildKeyMetrics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مؤشرات رئيسية',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'العملاء',
                _totalCustomers.toString(),
                Icons.people,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildMetricCard(
                'الموردين',
                _totalSuppliers.toString(),
                Icons.store,
                AppColors.secondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'الفواتير المعلقة',
                _pendingInvoices.toString(),
                Icons.receipt,
                AppColors.warning,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildMetricCard(
                'طلبات الخدمة المعلقة',
                _pendingServiceRequests.toString(),
                Icons.build,
                AppColors.info,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return StyledCard(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: AppTextStyles.labelMedium,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
