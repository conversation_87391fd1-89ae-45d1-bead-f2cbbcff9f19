import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/employee_card.dart';

class EmployeesListScreen extends StatefulWidget {
  const EmployeesListScreen({super.key});

  @override
  State<EmployeesListScreen> createState() => _EmployeesListScreenState();
}

class _EmployeesListScreenState extends State<EmployeesListScreen> {
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  bool _isLoading = true;
  List<Employee> _employees = [];

  // Helper method to show snackbar safely
  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
        ),
      );
    }
  }
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final employees = await _employeeRepository.getAllEmployees();

      if (mounted) {
        setState(() {
          _employees = employees;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تحميل بيانات الموظفين: $e', isError: true);
      }
    }
  }

  List<Employee> get _filteredEmployees {
    if (_searchQuery.isEmpty) {
      return _employees;
    }

    return _employees.where((employee) {
      final query = _searchQuery.toLowerCase();
      return employee.name.toLowerCase().contains(query) ||
          (employee.email?.toLowerCase().contains(query) ?? false) ||
          (employee.phone?.toLowerCase().contains(query) ?? false) ||
          employee.position.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الموظفين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن موظف...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              textDirection: TextDirection.rtl,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredEmployees.isEmpty
                    ? const Center(
                        child: Text(
                          'لا يوجد موظفين',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadEmployees,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredEmployees.length,
                          itemBuilder: (context, index) {
                            final employee = _filteredEmployees[index];
                            return EmployeeCard(
                              employee: employee,
                              onTap: () {
                                // Navigate to employee details
                                _showEmployeeActions(context, employee);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add employee screen
          _showAddEmployeeDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية الموظفين'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Filter options would go here
              const Text('خيارات التصفية'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filters
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _showAddEmployeeDialog(BuildContext context) {
    Navigator.pushNamed(
      context,
      AppRoutes.employeeEdit,
      arguments: null,
    ).then((newEmployee) async {
      if (newEmployee != null && mounted) {
        try {
          final employee = newEmployee as Employee;

          // Insert employee into database
          final id = await _employeeRepository.insertEmployee(employee);

          if (id > 0 && mounted) {
            // Reload employees from database
            _loadEmployees();

            // Show success message
            _showSnackBar('تم إضافة الموظف بنجاح');
          }
        } catch (e) {
          if (mounted) {
            _showSnackBar('حدث خطأ أثناء إضافة الموظف: $e', isError: true);
          }
        }
      }
    });
  }

  void _showEmployeeActions(BuildContext context, Employee employee) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.4,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return ListView(
              controller: scrollController,
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.paddingL,
                horizontal: AppDimensions.paddingM,
              ),
              children: [
                Text(
                  employee.name,
                  style: AppTextStyles.heading2,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.paddingS),
                Text(
                  employee.position,
                  style: const TextStyle(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.person),
                  title: const Text('عرض التفاصيل'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(
                      context,
                      AppRoutes.employeeDetails,
                      arguments: employee,
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('تعديل البيانات'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(
                      context,
                      AppRoutes.employeeEdit,
                      arguments: employee,
                    ).then((updatedEmployee) async {
                      if (updatedEmployee != null && mounted) {
                        try {
                          final updatedEmployeeData = updatedEmployee as Employee;

                          // Update employee in database
                          final result = await _employeeRepository.updateEmployee(updatedEmployeeData);

                          if (result > 0 && mounted) {
                            // Reload employees from database
                            _loadEmployees();

                            // Show success message
                            _showSnackBar('تم تحديث بيانات الموظف بنجاح');
                          }
                        } catch (e) {
                          if (mounted) {
                            _showSnackBar('حدث خطأ أثناء تحديث بيانات الموظف: $e', isError: true);
                          }
                        }
                      }
                    });
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.payments),
                  title: const Text('إدارة الراتب'),
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to employee salary management
                    Navigator.pushNamed(context, AppRoutes.payroll);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.money),
                  title: const Text('تسجيل سحب نقدي'),
                  onTap: () {
                    Navigator.pop(context);
                    // Show withdrawal dialog
                    _showWithdrawalDialog(context, employee);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.bar_chart),
                  title: const Text('عرض التقرير التفصيلي'),
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to detailed report for this employee
                    Navigator.pushNamed(
                      context,
                      AppRoutes.detailedReport,
                      arguments: {
                        'type': 'employee',
                        'id': employee.id,
                      },
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'حذف الموظف',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // Show delete confirmation
                    _showDeleteConfirmation(context, employee);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showWithdrawalDialog(BuildContext context, Employee employee) {
    final formKey = GlobalKey<FormState>();
    double amount = 0;
    String description = ''; // Used in onSaved callback

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تسجيل سحب نقدي لـ ${employee.name}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'المبلغ',
                    prefixIcon: Icon(Icons.money),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    amount = double.parse(value!);
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'سبب السحب',
                    prefixIcon: Icon(Icons.note),
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال سبب السحب';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    description = value!;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  try {
                    // Create a transaction for the withdrawal
                    // This would typically use TransactionRepository with the description
                    // For now, we'll just show a success message
                    // Note: description variable is saved but not used in this implementation
                    // Using the variable to avoid warning
                    if (kDebugMode) {
                      print('Description: $description');
                    }

                    if (mounted) {
                      // Show success message
                      _showSnackBar(
                        'تم تسجيل سحب نقدي بمبلغ $amount ريال لـ ${employee.name}',
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      _showSnackBar(
                        'حدث خطأ أثناء تسجيل السحب النقدي: $e',
                        isError: true,
                      );
                    }
                  }
                }
              },
              child: const Text('تسجيل'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, Employee employee) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من رغبتك في حذف الموظف ${employee.name}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);

                try {
                  // Delete employee from database
                  final result = await _employeeRepository.deleteEmployee(employee.id!);

                  if (result > 0 && mounted) {
                    // Reload employees from database
                    _loadEmployees();

                    // Show success message
                    _showSnackBar('تم حذف الموظف ${employee.name}', isError: true);
                  } else if (mounted) {
                    _showSnackBar('فشل حذف الموظف', isError: true);
                  }
                } catch (e) {
                  if (mounted) {
                    _showSnackBar('حدث خطأ أثناء حذف الموظف: $e', isError: true);
                  }
                }
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
