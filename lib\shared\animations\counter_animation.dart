import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../config/constants.dart';

/// تأثير عداد متزايد للأرقام
class CounterAnimation extends StatefulWidget {
  /// القيمة النهائية للعداد
  final double endValue;
  
  /// وحدة القياس (مثل ر.س)
  final String? unit;
  
  /// مدة الحركة
  final Duration duration;
  
  /// تنسيق الرقم
  final String? numberFormat;
  
  /// عدد الأرقام العشرية
  final int decimalPlaces;
  
  /// نمط النص
  final TextStyle? style;
  
  /// إنشاء تأثير عداد متزايد
  const CounterAnimation({
    super.key,
    required this.endValue,
    this.unit,
    this.duration = const Duration(milliseconds: 1500),
    this.numberFormat,
    this.decimalPlaces = 0,
    this.style,
  });

  @override
  State<CounterAnimation> createState() => _CounterAnimationState();
}

class _CounterAnimationState extends State<CounterAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late NumberFormat _formatter;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    
    _animation = Tween<double>(
      begin: 0,
      end: widget.endValue,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ),
    );
    
    _formatter = widget.numberFormat != null
        ? NumberFormat(widget.numberFormat)
        : NumberFormat.decimalPattern()
          ..minimumFractionDigits = widget.decimalPlaces
          ..maximumFractionDigits = widget.decimalPlaces;
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final value = _animation.value;
        final formattedValue = _formatter.format(value);
        final displayText = widget.unit != null
            ? '$formattedValue ${widget.unit}'
            : formattedValue;
        
        return Text(
          displayText,
          style: widget.style ?? AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        );
      },
    );
  }
}

/// تأثير عداد متزايد للمبالغ المالية
class MoneyCounterAnimation extends StatelessWidget {
  /// القيمة النهائية للعداد
  final double endValue;
  
  /// العملة (مثل ر.س)
  final String currency;
  
  /// مدة الحركة
  final Duration duration;
  
  /// نمط النص
  final TextStyle? style;
  
  /// لون النص الإيجابي
  final Color positiveColor;
  
  /// لون النص السلبي
  final Color negativeColor;
  
  /// إظهار علامة + للقيم الموجبة
  final bool showPositiveSign;
  
  /// إنشاء تأثير عداد متزايد للمبالغ المالية
  const MoneyCounterAnimation({
    super.key,
    required this.endValue,
    this.currency = 'ر.س',
    this.duration = const Duration(milliseconds: 1500),
    this.style,
    this.positiveColor = AppColors.success,
    this.negativeColor = AppColors.error,
    this.showPositiveSign = false,
  });

  @override
  Widget build(BuildContext context) {
    final isPositive = endValue >= 0;
    final absValue = endValue.abs();
    final valueColor = isPositive ? positiveColor : negativeColor;
    final sign = isPositive && showPositiveSign ? '+' : (isPositive ? '' : '-');
    
    return Flexible(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            sign,
            style: (style ?? AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            )).copyWith(color: valueColor),
          ),
          Flexible(
            child: CounterAnimation(
              endValue: absValue,
              unit: currency,
              duration: duration,
              numberFormat: '#,##0.00',
              decimalPlaces: 2,
              style: (style ?? AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              )).copyWith(color: valueColor),
            ),
          ),
        ],
      ),
    );
  }
}

/// تأثير عداد متزايد للنسب المئوية
class PercentageCounterAnimation extends StatelessWidget {
  /// القيمة النهائية للعداد
  final double endValue;
  
  /// مدة الحركة
  final Duration duration;
  
  /// نمط النص
  final TextStyle? style;
  
  /// لون النص الإيجابي
  final Color positiveColor;
  
  /// لون النص السلبي
  final Color negativeColor;
  
  /// إظهار علامة + للقيم الموجبة
  final bool showPositiveSign;
  
  /// إنشاء تأثير عداد متزايد للنسب المئوية
  const PercentageCounterAnimation({
    super.key,
    required this.endValue,
    this.duration = const Duration(milliseconds: 1500),
    this.style,
    this.positiveColor = AppColors.success,
    this.negativeColor = AppColors.error,
    this.showPositiveSign = false,
  });

  @override
  Widget build(BuildContext context) {
    final isPositive = endValue >= 0;
    final absValue = endValue.abs();
    final valueColor = isPositive ? positiveColor : negativeColor;
    final sign = isPositive && showPositiveSign ? '+' : (isPositive ? '' : '-');
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          sign,
          style: (style ?? AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
          )).copyWith(color: valueColor),
        ),
        CounterAnimation(
          endValue: absValue,
          unit: '%',
          duration: duration,
          numberFormat: '#,##0.0',
          decimalPlaces: 1,
          style: (style ?? AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
          )).copyWith(color: valueColor),
        ),
      ],
    );
  }
}
