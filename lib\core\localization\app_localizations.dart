import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// Enhanced localization system with comprehensive Arabic support
class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// Load localization strings from JSON files
  Future<bool> load() async {
    try {
      String jsonString = await rootBundle.loadString(
        'assets/i18n/${locale.languageCode}.json',
      );
      Map<String, dynamic> jsonMap = json.decode(jsonString);

      _localizedStrings = jsonMap.map((key, value) {
        return MapEntry(key, value.toString());
      });

      return true;
    } catch (e) {
      // Fallback to hardcoded strings if JSON loading fails
      _localizedStrings = _getHardcodedStrings();
      return true;
    }
  }

  /// Get localized string by key
  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  /// Shorthand for translate
  String t(String key) => translate(key);

  /// Get localized string with parameters
  String translateWithParams(String key, Map<String, String> params) {
    String text = translate(key);
    params.forEach((paramKey, paramValue) {
      text = text.replaceAll('{$paramKey}', paramValue);
    });
    return text;
  }

  /// Format date according to locale
  String formatDate(DateTime date, {String? pattern}) {
    if (locale.languageCode == 'ar') {
      // Arabic date formatting
      pattern ??= 'dd/MM/yyyy';
      final formatter = DateFormat(pattern, 'ar');
      return formatter.format(date);
    } else {
      // English date formatting
      pattern ??= 'MM/dd/yyyy';
      final formatter = DateFormat(pattern, 'en');
      return formatter.format(date);
    }
  }

  /// Format time according to locale
  String formatTime(DateTime time, {bool use24Hour = false}) {
    if (locale.languageCode == 'ar') {
      final pattern = use24Hour ? 'HH:mm' : 'hh:mm a';
      final formatter = DateFormat(pattern, 'ar');
      return formatter.format(time);
    } else {
      final pattern = use24Hour ? 'HH:mm' : 'hh:mm a';
      final formatter = DateFormat(pattern, 'en');
      return formatter.format(time);
    }
  }

  /// Format currency according to locale
  String formatCurrency(double amount, {String? symbol}) {
    if (locale.languageCode == 'ar') {
      symbol ??= 'ر.س'; // Saudi Riyal
      final formatter = NumberFormat.currency(
        locale: 'ar',
        symbol: symbol,
        decimalDigits: 2,
      );
      return formatter.format(amount);
    } else {
      symbol ??= '\$';
      final formatter = NumberFormat.currency(
        locale: 'en_US',
        symbol: symbol,
        decimalDigits: 2,
      );
      return formatter.format(amount);
    }
  }

  /// Format number according to locale
  String formatNumber(num number, {int? decimalDigits}) {
    if (locale.languageCode == 'ar') {
      final formatter = NumberFormat.decimalPattern('ar');
      if (decimalDigits != null) {
        formatter.minimumFractionDigits = decimalDigits;
        formatter.maximumFractionDigits = decimalDigits;
      }
      return formatter.format(number);
    } else {
      final formatter = NumberFormat.decimalPattern('en_US');
      if (decimalDigits != null) {
        formatter.minimumFractionDigits = decimalDigits;
        formatter.maximumFractionDigits = decimalDigits;
      }
      return formatter.format(number);
    }
  }

  /// Check if current locale is RTL
  bool get isRTL => locale.languageCode == 'ar';


  /// Get hardcoded strings as fallback
  Map<String, String> _getHardcodedStrings() {
    if (locale.languageCode == 'ar') {
      return {
        // App General
        'app_name': 'ركن الجليد للتكييف والتبريد',
        'welcome': 'مرحباً',
        'loading': 'جاري التحميل...',
        'error': 'خطأ',
        'success': 'نجح',
        'warning': 'تحذير',
        'info': 'معلومات',
        'ok': 'موافق',
        'cancel': 'إلغاء',
        'save': 'حفظ',
        'delete': 'حذف',
        'edit': 'تعديل',
        'add': 'إضافة',
        'search': 'بحث',
        'filter': 'تصفية',
        'refresh': 'تحديث',
        'back': 'رجوع',
        'next': 'التالي',
        'previous': 'السابق',
        'close': 'إغلاق',
        'yes': 'نعم',
        'no': 'لا',

        // Authentication
        'login': 'تسجيل الدخول',
        'logout': 'تسجيل الخروج',
        'register': 'إنشاء حساب',
        'forgot_password': 'نسيت كلمة المرور؟',
        'username': 'اسم المستخدم',
        'email': 'البريد الإلكتروني',
        'password': 'كلمة المرور',
        'confirm_password': 'تأكيد كلمة المرور',
        'remember_me': 'تذكرني',
        'sign_in': 'دخول',
        'sign_up': 'تسجيل',

        // Dashboard
        'dashboard': 'لوحة التحكم',
        'overview': 'نظرة عامة',
        'statistics': 'الإحصائيات',
        'recent_activities': 'الأنشطة الحديثة',
        'quick_actions': 'إجراءات سريعة',

        // Customers
        'customers': 'العملاء',
        'customer': 'عميل',
        'customer_name': 'اسم العميل',
        'customer_phone': 'هاتف العميل',
        'customer_email': 'بريد العميل',
        'customer_address': 'عنوان العميل',
        'add_customer': 'إضافة عميل',
        'edit_customer': 'تعديل عميل',
        'customer_details': 'تفاصيل العميل',

        // Employees
        'employees': 'الموظفين',
        'employee': 'موظف',
        'employee_name': 'اسم الموظف',
        'employee_email': 'بريد الموظف',
        'employee_phone': 'هاتف الموظف',
        'employee_position': 'المنصب',
        'employee_salary': 'الراتب',
        'join_date': 'تاريخ الانضمام',
        'employee_status': 'حالة الموظف',
        'add_employee': 'إضافة موظف',
        'edit_employee': 'تعديل موظف',
        'employee_details': 'تفاصيل الموظف',

        // Payment Types
        'payment_type': 'نوع الأجر',
        'monthly_salary': 'راتب شهري',
        'daily_wage': 'أجر يومي',
        'monthly_salary_amount': 'مبلغ الراتب الشهري',
        'technician_daily_rate': 'أجر الموظف الفني اليومي',
        'daily_rate_for_service': 'أجر الموظف الفني لهذه الخدمة',
        'daily_wage_note': 'الموظفون ذوو الأجر اليومي لا يحتاجون إلى راتب ثابت. سيتم تحديد الأجر عند تعيينهم للخدمات.',
        'enter_daily_rate': 'يرجى إدخال أجر الموظف الفني اليومي',
        'enter_valid_rate': 'يرجى إدخال أجر صحيح',
        'rate_per_service': 'يتم تحديد الأجر لكل خدمة منفصلة',

        // Service Requests
        'service_requests': 'طلبات الخدمة',
        'service_request': 'طلب خدمة',
        'new_service_request': 'طلب خدمة جديد',
        'service_type': 'نوع الخدمة',
        'service_date': 'تاريخ الخدمة',
        'service_time': 'وقت الخدمة',
        'service_status': 'حالة الخدمة',
        'service_description': 'وصف الخدمة',
        'technician': 'الموظف الفني',
        'priority': 'الأولوية',

        // Invoices
        'invoices': 'الفواتير',
        'invoice': 'فاتورة',
        'invoice_number': 'رقم الفاتورة',
        'invoice_date': 'تاريخ الفاتورة',
        'due_date': 'تاريخ الاستحقاق',
        'amount': 'المبلغ',
        'total': 'الإجمالي',
        'subtotal': 'المجموع الفرعي',
        'tax': 'الضريبة',
        'discount': 'الخصم',
        'paid': 'مدفوع',
        'unpaid': 'غير مدفوع',
        'partial': 'مدفوع جزئياً',

        // Inventory
        'inventory': 'المخزون',
        'inventory_item': 'عنصر مخزون',
        'item_name': 'اسم العنصر',
        'item_code': 'كود العنصر',
        'quantity': 'الكمية',
        'unit_price': 'سعر الوحدة',
        'stock_level': 'مستوى المخزون',
        'low_stock': 'مخزون منخفض',
        'out_of_stock': 'نفد المخزون',

        // Reports
        'reports': 'التقارير',
        'report': 'تقرير',
        'generate_report': 'إنشاء تقرير',
        'report_type': 'نوع التقرير',
        'date_range': 'نطاق التاريخ',
        'from_date': 'من تاريخ',
        'to_date': 'إلى تاريخ',
        'export': 'تصدير',
        'print': 'طباعة',

        // Settings
        'settings': 'الإعدادات',
        'profile': 'الملف الشخصي',
        'preferences': 'التفضيلات',
        'language': 'اللغة',
        'theme': 'المظهر',
        'notifications': 'الإشعارات',
        'backup': 'النسخ الاحتياطي',
        'restore': 'الاستعادة',

        // Status
        'pending': 'في الانتظار',
        'in_progress': 'قيد التنفيذ',
        'completed': 'مكتمل',
        'cancelled': 'ملغي',
        'active': 'نشط',
        'inactive': 'غير نشط',

        // Messages
        'no_data': 'لا توجد بيانات',
        'no_results': 'لا توجد نتائج',
        'data_saved': 'تم حفظ البيانات بنجاح',
        'data_deleted': 'تم حذف البيانات بنجاح',
        'operation_failed': 'فشلت العملية',
        'network_error': 'خطأ في الشبكة',
        'invalid_input': 'مدخل غير صحيح',
        'required_field': 'هذا الحقل مطلوب',

        // Time
        'today': 'اليوم',
        'yesterday': 'أمس',
        'tomorrow': 'غداً',
        'this_week': 'هذا الأسبوع',
        'this_month': 'هذا الشهر',
        'this_year': 'هذا العام',

        // Days of week
        'monday': 'الاثنين',
        'tuesday': 'الثلاثاء',
        'wednesday': 'الأربعاء',
        'thursday': 'الخميس',
        'friday': 'الجمعة',
        'saturday': 'السبت',
        'sunday': 'الأحد',

        // Months
        'january': 'يناير',
        'february': 'فبراير',
        'march': 'مارس',
        'april': 'أبريل',
        'may': 'مايو',
        'june': 'يونيو',
        'july': 'يوليو',
        'august': 'أغسطس',
        'september': 'سبتمبر',
        'october': 'أكتوبر',
        'november': 'نوفمبر',
        'december': 'ديسمبر',
      };
    } else {
      // English fallback strings
      return {
        'app_name': 'Ice Corner HVAC Services',
        'welcome': 'Welcome',
        'loading': 'Loading...',
        // Add English translations here...
      };
    }
  }
}

/// Localization delegate
class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
