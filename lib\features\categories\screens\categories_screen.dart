
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/category.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/empty_state.dart';
import '../../../state/category_state.dart';
import '../widgets/category_list_item.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  String? _searchQuery;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);

    // Use a post-frame callback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadCategories();
      }
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    final type = _tabController.index == 0 ? 'income' : 'expense';
    await Provider.of<CategoryState>(context, listen: false).loadCategoriesByType(type);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<CategoryModel> _getFilteredCategories(List<CategoryModel> categories) {
    if (_searchQuery == null || _searchQuery!.isEmpty) {
      return categories;
    }

    final query = _searchQuery!.toLowerCase();
    return categories.where((category) {
      return category.name.toLowerCase().contains(query);
    }).toList();
  }

  void _showDeleteConfirmation(CategoryModel category) {
    // Get the BuildContext before any async operations
    final currentContext = context;

    showDialog(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الفئة "${category.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // Close the dialog first
              Navigator.pop(dialogContext);

              // Then perform the delete operation
              _deleteCategory(category);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // Separate method to handle the delete operation
  Future<void> _deleteCategory(CategoryModel category) async {
    if (!mounted) return;

    // Get references before any async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await Provider.of<CategoryState>(context, listen: false)
        .deleteCategory(category.id);

    if (!mounted) return;

    // Show the snackbar
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(
          success
              ? 'تم حذف الفئة بنجاح'
              : 'فشل حذف الفئة',
        ),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفئات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'فئات الإيرادات'),
            Tab(text: 'فئات المصروفات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCategories,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(
            context,
            AppRoutes.categoryEdit,
            arguments: {
              'type': _tabController.index == 0 ? 'income' : 'expense',
            },
          ).then((_) {
            _loadCategories();
          });
        },
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن فئة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Categories list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      // Income categories tab
                      Consumer<CategoryState>(
                        builder: (context, categoryState, child) {
                          final filteredCategories = _getFilteredCategories(categoryState.incomeCategories);

                          if (filteredCategories.isEmpty) {
                            return const EmptyState(
                              icon: Icons.category,
                              title: 'لا توجد فئات إيرادات',
                              message: 'لم يتم العثور على أي فئات إيرادات. يمكنك إضافة فئة جديدة بالضغط على زر الإضافة.',
                            );
                          }

                          return RefreshIndicator(
                            onRefresh: _loadCategories,
                            child: ListView.builder(
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              itemCount: filteredCategories.length,
                              itemBuilder: (context, index) {
                                final category = filteredCategories[index];
                                return CategoryListItem(
                                  category: category,
                                  onTap: () {
                                    // View category details (if needed)
                                  },
                                  onEdit: () {
                                    Navigator.pushNamed(
                                      context,
                                      AppRoutes.categoryEdit,
                                      arguments: {
                                        'category': category,
                                        'type': 'income',
                                      },
                                    ).then((_) {
                                      _loadCategories();
                                    });
                                  },
                                  onDelete: () => _showDeleteConfirmation(category),
                                );
                              },
                            ),
                          );
                        },
                      ),

                      // Expense categories tab
                      Consumer<CategoryState>(
                        builder: (context, categoryState, child) {
                          final filteredCategories = _getFilteredCategories(categoryState.expenseCategories);

                          if (filteredCategories.isEmpty) {
                            return const EmptyState(
                              icon: Icons.category,
                              title: 'لا توجد فئات مصروفات',
                              message: 'لم يتم العثور على أي فئات مصروفات. يمكنك إضافة فئة جديدة بالضغط على زر الإضافة.',
                            );
                          }

                          return RefreshIndicator(
                            onRefresh: _loadCategories,
                            child: ListView.builder(
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              itemCount: filteredCategories.length,
                              itemBuilder: (context, index) {
                                final category = filteredCategories[index];
                                return CategoryListItem(
                                  category: category,
                                  onTap: () {
                                    // View category details (if needed)
                                  },
                                  onEdit: () {
                                    Navigator.pushNamed(
                                      context,
                                      AppRoutes.categoryEdit,
                                      arguments: {
                                        'category': category,
                                        'type': 'expense',
                                      },
                                    ).then((_) {
                                      _loadCategories();
                                    });
                                  },
                                  onDelete: () => _showDeleteConfirmation(category),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
}
