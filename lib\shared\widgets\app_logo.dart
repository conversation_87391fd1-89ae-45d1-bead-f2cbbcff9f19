import 'package:flutter/material.dart';

/// A widget that displays the app logo with the app name
class AppLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final Color? color;
  final Color? backgroundColor;
  final TextStyle? textStyle;

  const AppLogo({
    super.key,
    this.size = 80.0,
    this.showText = true,
    this.color,
    this.backgroundColor,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? const Color(0xFF3498DB);
    final defaultTextStyle = TextStyle(
      fontSize: size * 0.3,
      fontWeight: FontWeight.bold,
      color: theme.primaryColor,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Circular container with app name instead of snowflake icon
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: bgColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.with<PERSON><PERSON><PERSON>(51), // 0.2 opacity
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Center(
            child: Text(
              'ركن الجليد',
              style: TextStyle(
                fontSize: size * 0.2,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        // App name text below the circle
        if (showText) ...[
          const SizedBox(height: 8),
          Text(
            'ركن الجليد',
            style: textStyle ?? defaultTextStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
