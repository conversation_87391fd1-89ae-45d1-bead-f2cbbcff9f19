import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/service_request.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../core/repositories/employee_repository.dart';

class ServiceScheduleScreen extends StatefulWidget {
  const ServiceScheduleScreen({super.key});

  @override
  State<ServiceScheduleScreen> createState() => _ServiceScheduleScreenState();
}

class _ServiceScheduleScreenState extends State<ServiceScheduleScreen> with TickerProviderStateMixin {
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();

  bool _isLoading = true;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;
  Map<DateTime, List<ServiceRequest>> _events = {};

  // فلترة حسب الموظف الفني
  Employee? _selectedTechnician;
  List<Employee> _technicians = [];

  // فلترة حسب الحالة
  ServiceRequestStatus? _statusFilter;

  // تبويبات العرض
  late TabController _tabController;

  // إشعارات
  List<ServiceRequest> _upcomingRequests = [];



  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadServiceRequests();
    _loadTechnicians();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTechnicians() async {
    try {
      final technicians = await _employeeRepository.getAllEmployees();
      setState(() {
        _technicians = technicians.where((emp) => emp.position.toLowerCase().contains('فني')).toList();
      });
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  Future<void> _loadServiceRequests() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final requests = await _serviceRequestRepository.getAllServiceRequests();

      // Group service requests by date
      final events = <DateTime, List<ServiceRequest>>{};
      final upcomingRequests = <ServiceRequest>[];

      final now = DateTime.now();
      final nextWeek = DateTime(now.year, now.month, now.day + 7);

      for (final request in requests) {
        // تجميع الطلبات حسب التاريخ للتقويم
        final date = DateTime(
          request.scheduledDate.year,
          request.scheduledDate.month,
          request.scheduledDate.day,
        );

        if (events[date] == null) {
          events[date] = [];
        }

        events[date]!.add(request);

        // تجميع الطلبات القادمة للإشعارات
        if (request.status != ServiceRequestStatus.completed &&
            request.status != ServiceRequestStatus.cancelled &&
            request.scheduledDate.isAfter(now) &&
            request.scheduledDate.isBefore(nextWeek)) {
          upcomingRequests.add(request);
        }
      }

      // ترتيب الطلبات القادمة حسب التاريخ
      upcomingRequests.sort((a, b) => a.scheduledDate.compareTo(b.scheduledDate));

      setState(() {
        _events = events;
        _upcomingRequests = upcomingRequests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<ServiceRequest> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('جدول الزيارات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'التقويم'),
            Tab(text: 'الإشعارات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // تبويب التقويم
                _buildCalendarTab(),

                // تبويب الإشعارات
                _buildNotificationsTab(),
              ],
            ),
      floatingActionButton: SizedBox(
        width: 60,
        height: 60,
        child: FloatingActionButton(
          onPressed: () {
            // Navigate to add service request screen
            Navigator.pushNamed(context, AppRoutes.serviceRequestAdd).then((_) {
              _loadServiceRequests();
            });
          },
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildCalendarTab() {
    return Column(
      children: [
        // فلترة حسب الموظف الفني
        if (_technicians.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            child: DropdownButtonFormField<Employee?>(
              decoration: const InputDecoration(
                labelText: 'فلترة حسب الموظف الفني',
                prefixIcon: Icon(Icons.engineering),
                border: OutlineInputBorder(),
              ),
              value: _selectedTechnician,
              items: [
                const DropdownMenuItem<Employee?>(
                  value: null,
                  child: Text('جميع الموظف الفنيين'),
                ),
                ..._technicians.map((technician) {
                  return DropdownMenuItem<Employee?>(
                    value: technician,
                    child: Text(technician.name),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedTechnician = value;
                  // إعادة تحميل البيانات مع الفلتر الجديد
                  _loadServiceRequests();
                });
              },
            ),
          ),

        TableCalendar<ServiceRequest>(
          firstDay: DateTime.now().subtract(const Duration(days: 365)),
          lastDay: DateTime.now().add(const Duration(days: 365)),
          focusedDay: _focusedDay,
          selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
          calendarFormat: _calendarFormat,
          eventLoader: _getEventsForDay,
          startingDayOfWeek: StartingDayOfWeek.saturday,
          calendarStyle: const CalendarStyle(
            markersMaxCount: 3,
            markerDecoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
          ),
          onDaySelected: (selectedDay, focusedDay) {
            setState(() {
              _selectedDay = selectedDay;
              _focusedDay = focusedDay;
            });
          },
          onFormatChanged: (format) {
            setState(() {
              _calendarFormat = format;
            });
          },
          onPageChanged: (focusedDay) {
            _focusedDay = focusedDay;
          },
        ),
        const Divider(),
        Expanded(
          child: _buildEventList(),
        ),
      ],
    );
  }

  Widget _buildNotificationsTab() {
    if (_upcomingRequests.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد زيارات قادمة في الأسبوع القادم',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      itemCount: _upcomingRequests.length,
      itemBuilder: (context, index) {
        final request = _upcomingRequests[index];
        final isToday = request.scheduledDate.day == DateTime.now().day &&
                        request.scheduledDate.month == DateTime.now().month &&
                        request.scheduledDate.year == DateTime.now().year;

        return Card(
          margin: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
          color: isToday ? AppColors.primary.withAlpha(30) : null,
          child: ListTile(
            leading: _getStatusIcon(request.status),
            title: Row(
              children: [
                Expanded(child: Text(request.customerName)),
                if (isToday)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'اليوم',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(ServiceRequest.getRequestTypeName(request.requestType)),
                Row(
                  children: [
                    const Icon(Icons.calendar_today, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      DateFormat('yyyy/MM/dd').format(request.scheduledDate),
                      style: const TextStyle(fontSize: 12),
                    ),
                    const SizedBox(width: 8),
                    const Icon(Icons.access_time, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      DateFormat('hh:mm a').format(request.scheduledDate),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                if (request.location != null)
                  Row(
                    children: [
                      const Icon(Icons.location_on, size: 14),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          request.location!,
                          style: const TextStyle(fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                AppRoutes.serviceRequestDetails,
                arguments: request,
              ).then((_) {
                _loadServiceRequests();
              });
            },
          ),
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('فلترة الزيارات'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // فلترة حسب الحالة
                  const Text(
                    'حالة الطلب:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('الكل'),
                        selected: _statusFilter == null,
                        onSelected: (selected) {
                          setState(() {
                            _statusFilter = null;
                          });
                        },
                      ),
                      ...ServiceRequestStatus.values.map((status) {
                        return FilterChip(
                          label: Text(ServiceRequest.getStatusName(status)),
                          selected: _statusFilter == status,
                          onSelected: (selected) {
                            setState(() {
                              _statusFilter = selected ? status : null;
                            });
                          },
                        );
                      }),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // تطبيق الفلتر وإعادة تحميل البيانات
                    _loadServiceRequests();
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildEventList() {
    final events = _getEventsForDay(_selectedDay);

    if (events.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد زيارات مجدولة لهذا اليوم',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return Card(
          margin: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
          child: ListTile(
            leading: _getStatusIcon(event.status),
            title: Text(event.customerName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(ServiceRequest.getRequestTypeName(event.requestType)),
                if (event.location != null)
                  Text(
                    event.location!,
                    style: const TextStyle(fontSize: 12),
                  ),
              ],
            ),
            trailing: Text(
              DateFormat('hh:mm a').format(event.scheduledDate),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                '/service-request-details',
                arguments: event,
              ).then((_) {
                _loadServiceRequests();
              });
            },
          ),
        );
      },
    );
  }

  Widget _getStatusIcon(ServiceRequestStatus status) {
    IconData icon;
    Color color;

    switch (status) {
      case ServiceRequestStatus.pending:
        icon = Icons.schedule;
        color = Colors.orange;
        break;
      case ServiceRequestStatus.inProgress:
        icon = Icons.engineering;
        color = Colors.blue;
        break;
      case ServiceRequestStatus.completed:
        icon = Icons.check_circle;
        color = Colors.green;
        break;
      case ServiceRequestStatus.cancelled:
        icon = Icons.cancel;
        color = Colors.red;
        break;
    }

    return CircleAvatar(
      backgroundColor: color.withAlpha(50),
      child: Icon(icon, color: color, size: 20),
    );
  }
}
