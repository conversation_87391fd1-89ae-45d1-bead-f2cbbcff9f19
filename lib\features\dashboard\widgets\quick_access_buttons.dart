import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/widgets/ui_components.dart';

class QuickAccessButtons extends StatelessWidget {
  const QuickAccessButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return StyledCard(
      elevation: AppDimensions.elevationS,
      borderRadius: AppDimensions.radiusM,
      title: 'الوصول السريع',
      icon: Icons.dashboard,
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          const SizedBox(height: AppDimensions.spacingS),
          Wrap(
            spacing: AppDimensions.spacingM,
            runSpacing: AppDimensions.spacingM,
            alignment: WrapAlignment.center,
            children: [
              _buildQuickAccessButton(
                context,
                'الفواتير',
                Icons.receipt_long,
                AppColors.primary,
                () => Navigator.pushNamed(context, AppRoutes.invoices),
              ),
              _buildQuickAccessButton(
                context,
                'المعاملات',
                Icons.account_balance_wallet,
                AppColors.primaryLight,
                () => Navigator.pushNamed(context, AppRoutes.transactions),
              ),
              _buildQuickAccessButton(
                context,
                'الصناديق',
                Icons.account_balance_wallet_outlined,
                Colors.blue,
                () => Navigator.pushNamed(context, AppRoutes.cashBoxes),
              ),
              _buildQuickAccessButton(
                context,
                'العملاء',
                Icons.people,
                AppColors.accent,
                () => Navigator.pushNamed(context, AppRoutes.customers),
              ),
              _buildQuickAccessButton(
                context,
                'طلبات الخدمة',
                Icons.build,
                Colors.purple,
                () => Navigator.pushNamed(context, AppRoutes.serviceRequests),
              ),
              _buildQuickAccessButton(
                context,
                'المخزون',
                Icons.inventory_2,
                Colors.teal,
                () => Navigator.pushNamed(context, AppRoutes.inventory),
              ),
              _buildQuickAccessButton(
                context,
                'الموظفين',
                Icons.badge,
                Colors.indigo,
                () => Navigator.pushNamed(context, AppRoutes.employees),
              ),
              _buildQuickAccessButton(
                context,
                'الرواتب',
                Icons.payments,
                Colors.green,
                () => Navigator.pushNamed(context, AppRoutes.payroll),
              ),
              _buildQuickAccessButton(
                context,
                'التقارير',
                Icons.bar_chart,
                Colors.orange,
                () => Navigator.pushNamed(context, AppRoutes.reports),
              ),
              _buildQuickAccessButton(
                context,
                'تقرير الفواتير',
                Icons.receipt,
                Colors.deepOrange,
                () => Navigator.pushNamed(context, AppRoutes.invoiceReport),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ScaleOnTap(
      onTap: onTap,
      scaleFactor: 0.95,
      duration: AnimationStyles.shortDuration,
      child: Container(
        width: 80,
        padding: const EdgeInsets.all(AppDimensions.spacingM),
        decoration: BoxDecoration(
          color: color.withAlpha(25),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: color.withAlpha(50), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(15),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
