
import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/models/category.dart';

class CategoryListItem extends StatelessWidget {
  final CategoryModel category;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const CategoryListItem({
    super.key,
    required this.category,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: category.color.withAlpha(51),
          child: Icon(
            category.icon,
            color: category.color,
            size: 20,
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          category.isActive ? 'نشط' : 'غير نشط',
          style: TextStyle(
            color: category.isActive ? Colors.green : Colors.red,
            fontSize: 12,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              color: Colors.blue,
              onPressed: onEdit,
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              color: Colors.red,
              onPressed: onDelete,
              tooltip: 'حذف',
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}
