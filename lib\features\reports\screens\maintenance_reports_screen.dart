import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../models/maintenance_report_models.dart';
import '../widgets/report_summary_card.dart';
import '../widgets/report_filter_dialog.dart';

class MaintenanceReportsScreen extends StatefulWidget {
  const MaintenanceReportsScreen({super.key});

  @override
  State<MaintenanceReportsScreen> createState() => _MaintenanceReportsScreenState();
}

class _MaintenanceReportsScreenState extends State<MaintenanceReportsScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  late TabController _tabController;

  // بيانات التقارير
  late MaintenanceReportSummary _summary;
  List<CommonIssueReport> _commonIssues = [];
  List<TechnicianPerformanceReport> _technicianPerformance = [];
  List<SparePartUsageReport> _sparePartUsage = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReportData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    // محاكاة استدعاء API
    await Future.delayed(const Duration(seconds: 1));

    // بيانات وهمية للعرض
    _summary = MaintenanceReportSummary(
      totalServiceRequests: 120,
      completedRequests: 95,
      pendingRequests: 25,
      completionRate: 0.79,
      avgResolutionTime: 4.5,
      totalRevenue: 45000,
      totalCost: 28000,
      profitMargin: 0.38,
    );

    _commonIssues = [
      CommonIssueReport(
        issueType: 'تسرب غاز التبريد',
        count: 35,
        percentage: 0.29,
        avgResolutionTime: 3.2,
        avgCost: 450,
      ),
      CommonIssueReport(
        issueType: 'عطل في الكمبروسر',
        count: 22,
        percentage: 0.18,
        avgResolutionTime: 5.5,
        avgCost: 1200,
      ),
      CommonIssueReport(
        issueType: 'انسداد مجاري التصريف',
        count: 18,
        percentage: 0.15,
        avgResolutionTime: 2.0,
        avgCost: 250,
      ),
      CommonIssueReport(
        issueType: 'مشاكل في الثرموستات',
        count: 15,
        percentage: 0.13,
        avgResolutionTime: 1.5,
        avgCost: 350,
      ),
      CommonIssueReport(
        issueType: 'ضعف التبريد',
        count: 12,
        percentage: 0.10,
        avgResolutionTime: 2.8,
        avgCost: 400,
      ),
      CommonIssueReport(
        issueType: 'أخرى',
        count: 18,
        percentage: 0.15,
        avgResolutionTime: 3.0,
        avgCost: 500,
      ),
    ];

    _technicianPerformance = [
      TechnicianPerformanceReport(
        technicianId: 1,
        technicianName: 'أحمد محمد',
        completedRequests: 32,
        totalRequests: 35,
        completionRate: 0.91,
        avgResolutionTime: 3.2,
        customerSatisfaction: 4.8,
        revenueGenerated: 15000,
      ),
      TechnicianPerformanceReport(
        technicianId: 2,
        technicianName: 'محمد علي',
        completedRequests: 28,
        totalRequests: 32,
        completionRate: 0.88,
        avgResolutionTime: 3.8,
        customerSatisfaction: 4.5,
        revenueGenerated: 12000,
      ),
      TechnicianPerformanceReport(
        technicianId: 3,
        technicianName: 'خالد عبدالله',
        completedRequests: 20,
        totalRequests: 25,
        completionRate: 0.80,
        avgResolutionTime: 4.2,
        customerSatisfaction: 4.2,
        revenueGenerated: 9000,
      ),
      TechnicianPerformanceReport(
        technicianId: 4,
        technicianName: 'عمر سعيد',
        completedRequests: 15,
        totalRequests: 20,
        completionRate: 0.75,
        avgResolutionTime: 4.5,
        customerSatisfaction: 4.0,
        revenueGenerated: 7500,
      ),
    ];

    _sparePartUsage = [
      SparePartUsageReport(
        partId: 1,
        partName: 'كمبروسر 1.5 طن',
        partCode: 'COMP-15T',
        usageCount: 12,
        usagePercentage: 0.15,
        totalCost: 9600,
        remainingStock: 5,
        isLowStock: true,
      ),
      SparePartUsageReport(
        partId: 2,
        partName: 'غاز فريون R22',
        partCode: 'GAS-R22',
        usageCount: 35,
        usagePercentage: 0.44,
        totalCost: 5250,
        remainingStock: 15,
        isLowStock: false,
      ),
      SparePartUsageReport(
        partId: 3,
        partName: 'مكثف هواء',
        partCode: 'COND-A1',
        usageCount: 8,
        usagePercentage: 0.10,
        totalCost: 3200,
        remainingStock: 3,
        isLowStock: true,
      ),
      SparePartUsageReport(
        partId: 4,
        partName: 'ثرموستات رقمي',
        partCode: 'THERM-D1',
        usageCount: 15,
        usagePercentage: 0.19,
        totalCost: 2250,
        remainingStock: 8,
        isLowStock: false,
      ),
      SparePartUsageReport(
        partId: 5,
        partName: 'مروحة تبريد',
        partCode: 'FAN-C1',
        usageCount: 10,
        usagePercentage: 0.12,
        totalCost: 1500,
        remainingStock: 0,
        isLowStock: true,
      ),
    ];

    setState(() {
      _isLoading = false;
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return ReportFilterDialog(
          startDate: _startDate,
          endDate: _endDate,
          filterType: 'custom',
          onApplyFilter: (startDate, endDate, filterType) {
            setState(() {
              _startDate = startDate;
              _endDate = endDate;
            });
            _loadReportData();
          },
        );
      },
    );
  }

  void _exportToPdf() async {
    // تنفيذ تصدير PDF
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى PDF...'),
        duration: Duration(seconds: 2),
      ),
    );

    // هنا يمكن إضافة كود التصدير إلى PDF
  }

  void _exportToExcel() async {
    // تنفيذ تصدير Excel
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        duration: Duration(seconds: 2),
      ),
    );

    // هنا يمكن إضافة كود التصدير إلى Excel
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الصيانة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: _showFilterDialog,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'pdf') {
                _exportToPdf();
              } else if (value == 'excel') {
                _exportToExcel();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red),
                    SizedBox(width: 8),
                    Text('تصدير PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'excel',
                child: Row(
                  children: [
                    Icon(Icons.table_chart, color: Colors.green),
                    SizedBox(width: 8),
                    Text('تصدير Excel'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المشاكل الشائعة'),
            Tab(text: 'أداء الموظف الفنيين'),
            Tab(text: 'قطع الغيار'),
          ],
        ),
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // عرض الفترة الزمنية
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Text(
                    'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                    style: AppTextStyles.heading3,
                  ),
                ),

                // بطاقات الملخص
                _buildSummaryCards(),

                // محتوى التبويبات
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildCommonIssuesTab(),
                      _buildTechnicianPerformanceTab(),
                      _buildSparePartsTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildSummaryCards() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ReportSummaryCard(
                  items: [
                    SummaryItem(
                      title: 'إجمالي الطلبات',
                      value: _summary.totalServiceRequests.toString(),
                      icon: Icons.build,
                      color: AppColors.primary,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: ReportSummaryCard(
                  items: [
                    SummaryItem(
                      title: 'الطلبات المكتملة',
                      value: _summary.completedRequests.toString(),
                      icon: Icons.check_circle,
                      color: Colors.green,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: ReportSummaryCard(
                  items: [
                    SummaryItem(
                      title: 'الطلبات المعلقة',
                      value: _summary.pendingRequests.toString(),
                      icon: Icons.pending_actions,
                      color: Colors.orange,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            children: [
              Expanded(
                child: ReportSummaryCard(
                  items: [
                    SummaryItem(
                      title: 'معدل الإنجاز',
                      value: '${(_summary.completionRate * 100).toStringAsFixed(1)}%',
                      icon: Icons.trending_up,
                      color: Colors.blue,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: ReportSummaryCard(
                  items: [
                    SummaryItem(
                      title: 'متوسط وقت الحل',
                      value: '${_summary.avgResolutionTime} ساعة',
                      icon: Icons.timer,
                      color: Colors.purple,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: ReportSummaryCard(
                  items: [
                    SummaryItem(
                      title: 'هامش الربح',
                      value: '${(_summary.profitMargin * 100).toStringAsFixed(1)}%',
                      icon: Icons.attach_money,
                      color: Colors.green.shade700,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCommonIssuesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المشاكل الأكثر شيوعًا',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingM),

          // رسم بياني للمشاكل الشائعة
          SizedBox(
            height: 300,
            child: _buildCommonIssuesChart(),
          ),

          const SizedBox(height: AppDimensions.paddingL),
          const Text(
            'تفاصيل المشاكل',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingS),

          // جدول المشاكل الشائعة
          _buildCommonIssuesTable(),
        ],
      ),
    );
  }

  Widget _buildCommonIssuesChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _commonIssues.map((e) => e.count.toDouble()).reduce((a, b) => a > b ? a : b) * 1.2,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: Colors.blueGrey.shade800,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final issue = _commonIssues[groupIndex];
              return BarTooltipItem(
                '${issue.issueType}\n${issue.count} حالة (${(issue.percentage * 100).toStringAsFixed(1)}%)',
                const TextStyle(color: Colors.white),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value >= _commonIssues.length) return const Text('');
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    _commonIssues[value.toInt()].issueType.split(' ')[0],
                    style: const TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: _commonIssues.asMap().entries.map((entry) {
          final index = entry.key;
          final issue = entry.value;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: issue.count.toDouble(),
                color: AppColors.primary,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCommonIssuesTable() {
    return Card(
      elevation: 2,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          headingRowColor: WidgetStateProperty.all(Colors.blue.shade50),
          dataRowMinHeight: 48,
          dataRowMaxHeight: 60,
          columnSpacing: 20,
          headingTextStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
          columns: const [
            DataColumn(label: Text('نوع المشكلة')),
            DataColumn(label: Text('عدد الحالات')),
            DataColumn(label: Text('النسبة')),
            DataColumn(label: Text('متوسط وقت الحل')),
            DataColumn(label: Text('متوسط التكلفة')),
          ],
          rows: _commonIssues.map((issue) {
            return DataRow(
              cells: [
                DataCell(Text(
                  issue.issueType,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                DataCell(Text('${issue.count}')),
                DataCell(Text(
                  '${(issue.percentage * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: issue.percentage > 0.2 ? Colors.red : Colors.black,
                    fontWeight: issue.percentage > 0.2 ? FontWeight.bold : FontWeight.normal,
                  ),
                )),
                DataCell(Text(
                  '${issue.avgResolutionTime.toStringAsFixed(1)} ساعة',
                  style: TextStyle(
                    color: issue.avgResolutionTime > 5 ? Colors.orange : Colors.black,
                  ),
                )),
                DataCell(Text(
                  '${NumberFormat('#,##0.00', 'ar').format(issue.avgCost)} ر.س',
                  style: TextStyle(
                    color: issue.avgCost > 500 ? Colors.red : Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                )),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildTechnicianPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'أداء الموظف الفنيين',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingM),

          // رسم بياني لمعدل إنجاز الموظف الفنيين
          SizedBox(
            height: 300,
            child: _buildTechnicianCompletionRateChart(),
          ),

          const SizedBox(height: AppDimensions.paddingL),
          const Text(
            'تفاصيل أداء الموظف الفنيين',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingS),

          // جدول أداء الموظف الفنيين
          _buildTechnicianPerformanceTable(),

          const SizedBox(height: AppDimensions.paddingL),
          const Text(
            'تقييم رضا العملاء',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingS),

          // رسم بياني لتقييم رضا العملاء
          SizedBox(
            height: 200,
            child: _buildCustomerSatisfactionChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildTechnicianCompletionRateChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 1.0,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: Colors.blueGrey.shade800,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final technician = _technicianPerformance[groupIndex];
              return BarTooltipItem(
                '${technician.technicianName}\n${(technician.completionRate * 100).toStringAsFixed(1)}%',
                const TextStyle(color: Colors.white),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value >= _technicianPerformance.length) return const Text('');
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    _technicianPerformance[value.toInt()].technicianName.split(' ')[0],
                    style: const TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${(value * 100).toInt()}%',
                  style: const TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: _technicianPerformance.asMap().entries.map((entry) {
          final index = entry.key;
          final technician = entry.value;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: technician.completionRate,
                color: _getCompletionRateColor(technician.completionRate),
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Color _getCompletionRateColor(double rate) {
    if (rate >= 0.9) {
      return Colors.green;
    } else if (rate >= 0.7) {
      return Colors.blue;
    } else if (rate >= 0.5) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  Widget _buildCustomerSatisfactionChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 5.0,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: Colors.blueGrey.shade800,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final technician = _technicianPerformance[groupIndex];
              return BarTooltipItem(
                '${technician.technicianName}\n${technician.customerSatisfaction.toStringAsFixed(1)}/5',
                const TextStyle(color: Colors.white),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value >= _technicianPerformance.length) return const Text('');
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    _technicianPerformance[value.toInt()].technicianName.split(' ')[0],
                    style: const TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value % 1 == 0) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  );
                }
                return const Text('');
              },
              reservedSize: 30,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: _technicianPerformance.asMap().entries.map((entry) {
          final index = entry.key;
          final technician = entry.value;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: technician.customerSatisfaction,
                color: _getSatisfactionColor(technician.customerSatisfaction),
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Color _getSatisfactionColor(double rating) {
    if (rating >= 4.5) {
      return Colors.green.shade700;
    } else if (rating >= 4.0) {
      return Colors.green;
    } else if (rating >= 3.5) {
      return Colors.amber;
    } else if (rating >= 3.0) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  Widget _buildTechnicianPerformanceTable() {
    return Card(
      elevation: 2,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: const [
            DataColumn(label: Text('اسم الموظف الفني')),
            DataColumn(label: Text('الطلبات المكتملة')),
            DataColumn(label: Text('إجمالي الطلبات')),
            DataColumn(label: Text('معدل الإنجاز')),
            DataColumn(label: Text('متوسط وقت الحل')),
            DataColumn(label: Text('رضا العملاء')),
            DataColumn(label: Text('الإيرادات')),
          ],
          rows: _technicianPerformance.map((technician) {
            return DataRow(
              cells: [
                DataCell(Text(technician.technicianName)),
                DataCell(Text('${technician.completedRequests}')),
                DataCell(Text('${technician.totalRequests}')),
                DataCell(Text('${(technician.completionRate * 100).toStringAsFixed(1)}%')),
                DataCell(Text('${technician.avgResolutionTime} ساعة')),
                DataCell(Row(
                  children: [
                    Text(technician.customerSatisfaction.toStringAsFixed(1)),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber,
                    ),
                  ],
                )),
                DataCell(Text('${technician.revenueGenerated} ريال')),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildSparePartsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'قطع الغيار الأكثر استخدامًا',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingM),

          // رسم بياني لاستخدام قطع الغيار
          SizedBox(
            height: 300,
            child: _buildSparePartsUsageChart(),
          ),

          const SizedBox(height: AppDimensions.paddingL),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'تفاصيل استخدام قطع الغيار',
                style: AppTextStyles.heading3,
              ),
              TextButton.icon(
                onPressed: () {
                  // التنقل إلى شاشة المخزون
                  Navigator.pushNamed(context, '/inventory');
                },
                icon: const Icon(Icons.inventory, size: 16),
                label: const Text('عرض المخزون'),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingS),

          // جدول استخدام قطع الغيار
          _buildSparePartsTable(),

          const SizedBox(height: AppDimensions.paddingL),
          const Text(
            'حالة المخزون',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: AppDimensions.paddingS),

          // بطاقات حالة المخزون
          _buildStockStatusCards(),
        ],
      ),
    );
  }

  Widget _buildSparePartsUsageChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _sparePartUsage.map((e) => e.usageCount.toDouble()).reduce((a, b) => a > b ? a : b) * 1.2,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: Colors.blueGrey.shade800,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final part = _sparePartUsage[groupIndex];
              return BarTooltipItem(
                '${part.partName}\n${part.usageCount} مرة (${(part.usagePercentage * 100).toStringAsFixed(1)}%)',
                const TextStyle(color: Colors.white),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value >= _sparePartUsage.length) return const Text('');
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    _sparePartUsage[value.toInt()].partCode,
                    style: const TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: _sparePartUsage.asMap().entries.map((entry) {
          final index = entry.key;
          final part = entry.value;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: part.usageCount.toDouble(),
                color: AppColors.primary,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSparePartsTable() {
    return Card(
      elevation: 2,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: const [
            DataColumn(label: Text('اسم القطعة')),
            DataColumn(label: Text('الكود')),
            DataColumn(label: Text('عدد مرات الاستخدام')),
            DataColumn(label: Text('النسبة')),
            DataColumn(label: Text('التكلفة الإجمالية')),
            DataColumn(label: Text('المخزون المتبقي')),
            DataColumn(label: Text('حالة المخزون')),
          ],
          rows: _sparePartUsage.map((part) {
            return DataRow(
              cells: [
                DataCell(Text(part.partName)),
                DataCell(Text(part.partCode)),
                DataCell(Text('${part.usageCount}')),
                DataCell(Text('${(part.usagePercentage * 100).toStringAsFixed(1)}%')),
                DataCell(Text('${part.totalCost} ريال')),
                DataCell(Text('${part.remainingStock}')),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: part.getStockStatusColor().withAlpha(51), // 0.2 * 255 = 51
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      part.getStockStatusText(),
                      style: TextStyle(
                        color: part.getStockStatusColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildStockStatusCards() {
    // حساب عدد العناصر في كل حالة
    int outOfStock = _sparePartUsage.where((part) => part.remainingStock <= 0).length;
    int lowStock = _sparePartUsage.where((part) => part.isLowStock && part.remainingStock > 0).length;
    int inStock = _sparePartUsage.where((part) => !part.isLowStock && part.remainingStock > 0).length;

    return Row(
      children: [
        Expanded(
          child: Card(
            color: Colors.red.shade50,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                children: [
                  Icon(Icons.inventory_2, color: Colors.red, size: 32),
                  const SizedBox(height: 8),
                  Text(
                    '$outOfStock',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'نفذت الكمية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.red.shade900,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: Card(
            color: Colors.orange.shade50,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange, size: 32),
                  const SizedBox(height: 8),
                  Text(
                    '$lowStock',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'مخزون منخفض',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange.shade900,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  const SizedBox(height: 8),
                  Text(
                    '$inStock',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'متوفر',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green.shade900,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
