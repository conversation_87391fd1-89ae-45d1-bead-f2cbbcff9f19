import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../../../config/constants.dart';
import '../../../shared/models/inventory_item.dart';
import '../../../core/repositories/inventory_repository.dart';

class InventoryFormScreen extends StatefulWidget {
  final InventoryItem? inventoryItem;

  const InventoryFormScreen({super.key, this.inventoryItem});

  @override
  State<InventoryFormScreen> createState() => _InventoryFormScreenState();
}

class _InventoryFormScreenState extends State<InventoryFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _inventoryRepository = InventoryRepository();
  bool _isLoading = false;
  bool _isEditing = false;

  // Form controllers
  final _codeController = TextEditingController();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minQuantityController = TextEditingController();
  final _unitController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _supplierNameController = TextEditingController();
  final _locationController = TextEditingController();
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.inventoryItem != null;

    if (_isEditing) {
      // Populate form fields with existing data
      _codeController.text = widget.inventoryItem!.code;
      _nameController.text = widget.inventoryItem!.name;
      _descriptionController.text = widget.inventoryItem?.description ?? '';
      _categoryController.text = widget.inventoryItem?.category ?? '';
      _quantityController.text = widget.inventoryItem!.quantity.toString();
      _minQuantityController.text = widget.inventoryItem!.minQuantity.toString();
      _unitController.text = widget.inventoryItem?.unit ?? '';
      _costPriceController.text = widget.inventoryItem!.costPrice.toString();
      _sellingPriceController.text = widget.inventoryItem!.sellingPrice.toString();
      _supplierNameController.text = widget.inventoryItem?.supplierName ?? '';
      _locationController.text = widget.inventoryItem?.location ?? '';
      _isActive = widget.inventoryItem!.isActive;
    } else {
      // Set default values for new items
      _quantityController.text = '0';
      _minQuantityController.text = '0';
      _costPriceController.text = '0';
      _sellingPriceController.text = '0';
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    _quantityController.dispose();
    _minQuantityController.dispose();
    _unitController.dispose();
    _costPriceController.dispose();
    _sellingPriceController.dispose();
    _supplierNameController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _saveInventoryItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final inventoryItem = InventoryItem(
        id: _isEditing ? widget.inventoryItem!.id : null,
        code: _codeController.text,
        name: _nameController.text,
        description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
        category: _categoryController.text.isEmpty ? null : _categoryController.text,
        quantity: double.parse(_quantityController.text),
        minQuantity: double.parse(_minQuantityController.text),
        unit: _unitController.text.isEmpty ? null : _unitController.text,
        costPrice: double.parse(_costPriceController.text),
        sellingPrice: double.parse(_sellingPriceController.text),
        supplierName: _supplierNameController.text.isEmpty ? null : _supplierNameController.text,
        location: _locationController.text.isEmpty ? null : _locationController.text,
        isActive: _isActive,
        createdAt: _isEditing ? widget.inventoryItem!.createdAt : DateTime.now(),
      );

      if (_isEditing) {
        await _inventoryRepository.updateInventoryItem(inventoryItem);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث العنصر بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        await _inventoryRepository.insertInventoryItem(inventoryItem);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة العنصر بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving inventory item: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حفظ العنصر: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل عنصر' : 'إضافة عنصر جديد'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: AppDimensions.paddingL),
                    _buildInventorySection(),
                    const SizedBox(height: AppDimensions.paddingL),
                    _buildPricingSection(),
                    const SizedBox(height: AppDimensions.paddingL),
                    _buildAdditionalInfoSection(),
                    const SizedBox(height: AppDimensions.paddingXL),
                    ElevatedButton(
                      onPressed: _saveInventoryItem,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                      ),
                      child: Text(
                        _isEditing ? 'تحديث العنصر' : 'إضافة العنصر',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'الكود *',
                hintText: 'أدخل كود العنصر',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال كود العنصر';
                }
                return null;
              },
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم *',
                hintText: 'أدخل اسم العنصر',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال اسم العنصر';
                }
                return null;
              },
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                hintText: 'أدخل وصف العنصر',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _categoryController,
              decoration: const InputDecoration(
                labelText: 'الفئة',
                hintText: 'أدخل فئة العنصر',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventorySection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المخزون',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية *',
                      hintText: 'أدخل الكمية',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال الكمية';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: TextFormField(
                    controller: _minQuantityController,
                    decoration: const InputDecoration(
                      labelText: 'الحد الأدنى *',
                      hintText: 'أدخل الحد الأدنى',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال الحد الأدنى';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _unitController,
              decoration: const InputDecoration(
                labelText: 'الوحدة',
                hintText: 'مثال: قطعة، كرتون، كيلو',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات التسعير',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _costPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر التكلفة *',
                      hintText: 'أدخل سعر التكلفة',
                      border: OutlineInputBorder(),
                      prefixText: 'ر.س ',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال سعر التكلفة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: TextFormField(
                    controller: _sellingPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر البيع *',
                      hintText: 'أدخل سعر البيع',
                      border: OutlineInputBorder(),
                      prefixText: 'ر.س ',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال سعر البيع';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات إضافية',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _supplierNameController,
              decoration: const InputDecoration(
                labelText: 'المورد',
                hintText: 'أدخل اسم المورد',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextFormField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: 'الموقع',
                hintText: 'مثال: المستودع الرئيسي - رف A1',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            SwitchListTile(
              title: const Text('نشط'),
              subtitle: const Text('هل العنصر متاح للاستخدام؟'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
