import 'package:flutter/material.dart';

/// نموذج لتقرير المشاكل الشائعة
class CommonIssueReport {
  final String issueType;
  final int count;
  final double percentage;
  final double avgResolutionTime; // متوسط وقت الحل بالساعات
  final double avgCost; // متوسط التكلفة

  CommonIssueReport({
    required this.issueType,
    required this.count,
    required this.percentage,
    required this.avgResolutionTime,
    required this.avgCost,
  });

  factory CommonIssueReport.fromJson(Map<String, dynamic> json) {
    return CommonIssueReport(
      issueType: json['issueType'] as String,
      count: json['count'] as int,
      percentage: json['percentage'] as double,
      avgResolutionTime: json['avgResolutionTime'] as double,
      avgCost: json['avgCost'] as double,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'issueType': issueType,
      'count': count,
      'percentage': percentage,
      'avgResolutionTime': avgResolutionTime,
      'avgCost': avgCost,
    };
  }
}

/// نموذج لتقرير أداء الموظف الفنيين
class TechnicianPerformanceReport {
  final int technicianId;
  final String technicianName;
  final int completedRequests;
  final int totalRequests;
  final double completionRate;
  final double avgResolutionTime; // متوسط وقت الحل بالساعات
  final double customerSatisfaction; // تقييم رضا العملاء (من 5)
  final double revenueGenerated; // الإيرادات المحققة

  TechnicianPerformanceReport({
    required this.technicianId,
    required this.technicianName,
    required this.completedRequests,
    required this.totalRequests,
    required this.completionRate,
    required this.avgResolutionTime,
    required this.customerSatisfaction,
    required this.revenueGenerated,
  });

  factory TechnicianPerformanceReport.fromJson(Map<String, dynamic> json) {
    return TechnicianPerformanceReport(
      technicianId: json['technicianId'] as int,
      technicianName: json['technicianName'] as String,
      completedRequests: json['completedRequests'] as int,
      totalRequests: json['totalRequests'] as int,
      completionRate: json['completionRate'] as double,
      avgResolutionTime: json['avgResolutionTime'] as double,
      customerSatisfaction: json['customerSatisfaction'] as double,
      revenueGenerated: json['revenueGenerated'] as double,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'technicianId': technicianId,
      'technicianName': technicianName,
      'completedRequests': completedRequests,
      'totalRequests': totalRequests,
      'completionRate': completionRate,
      'avgResolutionTime': avgResolutionTime,
      'customerSatisfaction': customerSatisfaction,
      'revenueGenerated': revenueGenerated,
    };
  }

  // حساب معدل الإنجاز
  static double calculateCompletionRate(int completed, int total) {
    if (total == 0) return 0;
    return completed / total;
  }
}

/// نموذج لتقرير قطع الغيار الأكثر استخدامًا
class SparePartUsageReport {
  final int partId;
  final String partName;
  final String partCode;
  final int usageCount;
  final double usagePercentage;
  final double totalCost;
  final int remainingStock;
  final bool isLowStock;

  SparePartUsageReport({
    required this.partId,
    required this.partName,
    required this.partCode,
    required this.usageCount,
    required this.usagePercentage,
    required this.totalCost,
    required this.remainingStock,
    required this.isLowStock,
  });

  factory SparePartUsageReport.fromJson(Map<String, dynamic> json) {
    return SparePartUsageReport(
      partId: json['partId'] as int,
      partName: json['partName'] as String,
      partCode: json['partCode'] as String,
      usageCount: json['usageCount'] as int,
      usagePercentage: json['usagePercentage'] as double,
      totalCost: json['totalCost'] as double,
      remainingStock: json['remainingStock'] as int,
      isLowStock: json['isLowStock'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'partId': partId,
      'partName': partName,
      'partCode': partCode,
      'usageCount': usageCount,
      'usagePercentage': usagePercentage,
      'totalCost': totalCost,
      'remainingStock': remainingStock,
      'isLowStock': isLowStock,
    };
  }

  // الحصول على لون حالة المخزون
  Color getStockStatusColor() {
    if (remainingStock <= 0) {
      return Colors.red;
    } else if (isLowStock) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  // الحصول على نص حالة المخزون
  String getStockStatusText() {
    if (remainingStock <= 0) {
      return 'نفذت الكمية';
    } else if (isLowStock) {
      return 'مخزون منخفض';
    } else {
      return 'متوفر';
    }
  }
}

/// نموذج لملخص تقرير الصيانة
class MaintenanceReportSummary {
  final int totalServiceRequests;
  final int completedRequests;
  final int pendingRequests;
  final double completionRate;
  final double avgResolutionTime;
  final double totalRevenue;
  final double totalCost;
  final double profitMargin;

  MaintenanceReportSummary({
    required this.totalServiceRequests,
    required this.completedRequests,
    required this.pendingRequests,
    required this.completionRate,
    required this.avgResolutionTime,
    required this.totalRevenue,
    required this.totalCost,
    required this.profitMargin,
  });

  factory MaintenanceReportSummary.fromJson(Map<String, dynamic> json) {
    return MaintenanceReportSummary(
      totalServiceRequests: json['totalServiceRequests'] as int,
      completedRequests: json['completedRequests'] as int,
      pendingRequests: json['pendingRequests'] as int,
      completionRate: json['completionRate'] as double,
      avgResolutionTime: json['avgResolutionTime'] as double,
      totalRevenue: json['totalRevenue'] as double,
      totalCost: json['totalCost'] as double,
      profitMargin: json['profitMargin'] as double,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalServiceRequests': totalServiceRequests,
      'completedRequests': completedRequests,
      'pendingRequests': pendingRequests,
      'completionRate': completionRate,
      'avgResolutionTime': avgResolutionTime,
      'totalRevenue': totalRevenue,
      'totalCost': totalCost,
      'profitMargin': profitMargin,
    };
  }
}
