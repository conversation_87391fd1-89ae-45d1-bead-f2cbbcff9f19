import 'package:flutter/material.dart';
import 'package:icecorner/config/constants.dart';
import 'package:provider/provider.dart';
import '../../../shared/models/user.dart';
import '../../../shared/models/permission.dart';
import '../../../state/user_state.dart';
import '../widgets/permission_module_card.dart';
// import '../../../shared/models/user_role.dart'; // Remove UserRole import

class UserPermissionsScreen extends StatefulWidget {
  final User user;

  const UserPermissionsScreen({
    super.key,
    required this.user,
  });

  @override
  State<UserPermissionsScreen> createState() => _UserPermissionsScreenState();
}

class _UserPermissionsScreenState extends State<UserPermissionsScreen> {
  late List<Permission> _permissions;
  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _permissions = widget.user.getPermissionsList();
  }

  void _updatePermission(PermissionModule module, PermissionOperation operation, bool value) {
    setState(() {
      final index = _permissions.indexWhere(
        (p) => p.module == module && p.operation == operation,
      );

      if (index >= 0) {
        _permissions[index] = _permissions[index].copyWith(granted: value);
        _hasChanges = true;
      }
    });
  }

  void _updateModulePermissions(PermissionModule module, bool value) {
    setState(() {
      for (int i = 0; i < _permissions.length; i++) {
        if (_permissions[i].module == module) {
          _permissions[i] = _permissions[i].copyWith(granted: value);
        }
      }
      _hasChanges = true;
    });
  }

  bool _isModuleFullyGranted(PermissionModule module) {
    return _permissions
        .where((p) => p.module == module)
        .every((p) => p.granted);
  }

  bool _isModulePartiallyGranted(PermissionModule module) {
    final modulePermissions = _permissions.where((p) => p.module == module);
    return modulePermissions.any((p) => p.granted) &&
           !modulePermissions.every((p) => p.granted);
  }

  Future<void> _savePermissions() async {
    if (!mounted) return;

    // Get references before any async operations
    final currentContext = context;
    final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
    final navigator = Navigator.of(currentContext);

    setState(() {
      _isLoading = true;
    });

    // Convert List<Permission> to Map<String, dynamic>
    final Map<String, dynamic> permissionsMap = {};
    for (var permission in _permissions) {
      if (!permissionsMap.containsKey(permission.module.toString())) {
        permissionsMap[permission.module.toString()] = {};
      }
      permissionsMap[permission.module.toString()]![permission.operation.toString()] = permission.granted;
    }

    final success = await Provider.of<UserState>(currentContext, listen: false)
        .updateUserPermissions(widget.user.id.toString(), permissionsMap); // Convert id to String

    if (!mounted) return;

    setState(() {
      _isLoading = false;
      if (success) {
        _hasChanges = false;
      }
    });

    // Show the snackbar
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(
          success
              ? 'تم حفظ الصلاحيات بنجاح'
              : 'فشل حفظ الصلاحيات',
        ),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );

    // Navigate if successful
    if (success) {
      navigator.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('صلاحيات ${widget.user.name}'),
        actions: [
          if (_hasChanges)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _savePermissions,
              tooltip: 'حفظ التغييرات',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User info card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: AppColors.primary.withAlpha(26),
                            child: Text(
                              widget.user.name.substring(0, 1),
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.user.name,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.user.email ?? 'لا يوجد بريد إلكتروني',
                                  style: const TextStyle(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary.withAlpha(26),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    _getRoleName(widget.user.roles), // Pass the list of roles
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.paddingM),

                  // Admin note
                  if (widget.user.hasRole('admin')) // Check if roles list contains 'admin'
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(26),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.withAlpha(77)),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue),
                          SizedBox(width: AppDimensions.paddingM),
                          Expanded(
                            child: Text(
                              'مدير النظام يمتلك جميع الصلاحيات تلقائياً ولا يمكن تعديلها.',
                              style: TextStyle(color: Colors.blue),
                            ),
                          ),
                        ],
                      ),
                    ),

                  if (!widget.user.hasRole('admin')) ...[ // Check if roles list does NOT contain 'admin'
                    const SizedBox(height: AppDimensions.paddingM),

                    // Permissions tree
                    const Text(
                      'شجرة الصلاحيات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),

                    // Modules
                    ...PermissionModule.values.map((module) {
                      return PermissionModuleCard(
                        module: module,
                        isFullyGranted: _isModuleFullyGranted(module),
                        isPartiallyGranted: _isModulePartiallyGranted(module),
                        permissions: _permissions
                            .where((p) => p.module == module)
                            .toList(),
                        onModuleChanged: (value) {
                          _updateModulePermissions(module, value);
                        },
                        onPermissionChanged: (operation, value) {
                          _updatePermission(module, operation, value);
                        },
                      );
                    }),
                  ],
                ],
              ),
            ),
      bottomNavigationBar: _hasChanges
          ? Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              color: Theme.of(context).cardColor,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _savePermissions,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Text('حفظ الصلاحيات'),
              ),
            )
          : null,
    );
  }

  // Update _getRoleName to accept List<String> and return the first role name
  String _getRoleName(List<String> roles) {
    if (roles.isEmpty) {
      return 'لا يوجد دور';
    }
    // You might want to refine this logic to display multiple roles or a primary role
    // For now, returning the first role in the list
    switch (roles.first) {
      case 'admin':
        return 'مدير النظام';
      case 'manager':
        return 'مدير';
      case 'accountant':
        return 'محاسب';
      case 'technician':
        return 'فني';
      case 'receptionist':
        return 'موظف استقبال';
      case 'employee':
        return 'موظف';
      default:
        return roles.first; // Return the role name if not in the predefined list
    }
  }
}
