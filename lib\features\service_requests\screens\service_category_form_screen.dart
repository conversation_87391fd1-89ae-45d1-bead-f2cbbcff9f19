import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_category.dart';
import '../../../core/repositories/service_category_repository.dart';
import '../../../shared/widgets/color_picker_dialog.dart';
import '../../../shared/widgets/icon_picker_dialog.dart';

class ServiceCategoryFormScreen extends StatefulWidget {
  final ServiceCategory? category;

  const ServiceCategoryFormScreen({
    super.key,
    this.category,
  });

  @override
  State<ServiceCategoryFormScreen> createState() => _ServiceCategoryFormScreenState();
}

class _ServiceCategoryFormScreenState extends State<ServiceCategoryFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  final ServiceCategoryRepository _repository = ServiceCategoryRepository();
  
  bool _isActive = true;
  bool _isLoading = false;
  IconData _selectedIcon = Icons.build;
  Color _selectedColor = Colors.blue;

  bool get _isNewCategory => widget.category == null;

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category!.description;
      _isActive = widget.category!.isActive;
      _selectedIcon = widget.category!.icon;
      _selectedColor = widget.category!.color;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isNewCategory) {
        // إنشاء فئة جديدة
        final newCategory = ServiceCategory(
          name: _nameController.text,
          description: _descriptionController.text,
          icon: _selectedIcon,
          color: _selectedColor,
          isActive: _isActive,
          createdAt: DateTime.now(),
        );

        final result = await _repository.insertServiceCategory(newCategory);
        
        if (result > 0) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة الفئة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context, true);
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حدث خطأ أثناء إضافة الفئة'),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        }
      } else {
        // تحديث فئة موجودة
        final updatedCategory = widget.category!.copyWith(
          name: _nameController.text,
          description: _descriptionController.text,
          icon: _selectedIcon,
          color: _selectedColor,
          isActive: _isActive,
          updatedAt: DateTime.now(),
        );

        final result = await _repository.updateServiceCategory(updatedCategory);
        
        if (result > 0) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث الفئة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context, true);
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حدث خطأ أثناء تحديث الفئة'),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showIconPickerDialog() {
    showDialog(
      context: context,
      builder: (context) => IconPickerDialog(
        selectedIcon: _selectedIcon,
        onIconSelected: (icon) {
          setState(() {
            _selectedIcon = icon;
          });
        },
      ),
    );
  }

  void _showColorPickerDialog() {
    showDialog(
      context: context,
      builder: (context) => ColorPickerDialog(
        selectedColor: _selectedColor,
        onColorSelected: (color) {
          setState(() {
            _selectedColor = color;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isNewCategory ? 'إضافة فئة خدمة جديدة' : 'تعديل فئة خدمة'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الفئة
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم الفئة',
                        hintText: 'أدخل اسم فئة الخدمة',
                        prefixIcon: Icon(Icons.category),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال اسم الفئة';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // وصف الفئة
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'وصف الفئة',
                        hintText: 'أدخل وصف فئة الخدمة',
                        prefixIcon: Icon(Icons.description),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // اختيار الأيقونة واللون
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: _showIconPickerDialog,
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'الأيقونة',
                                border: OutlineInputBorder(),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Icon(_selectedIcon, color: _selectedColor),
                                  const Icon(Icons.arrow_drop_down),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        Expanded(
                          child: InkWell(
                            onTap: _showColorPickerDialog,
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'اللون',
                                border: OutlineInputBorder(),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: _selectedColor,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const Icon(Icons.arrow_drop_down),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // حالة الفئة
                    SwitchListTile(
                      title: const Text('الحالة'),
                      subtitle: Text(_isActive ? 'مفعل' : 'معطل'),
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                      activeColor: AppColors.primary,
                    ),
                    const SizedBox(height: AppDimensions.paddingXL),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveCategory,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                        ),
                        child: Text(
                          _isNewCategory ? 'إضافة الفئة' : 'حفظ التغييرات',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
