import 'package:sqflite/sqflite.dart';
import '../../shared/models/cash_flow.dart';
import '../database/database_helper.dart';

/// مستودع التدفق النقدي
class CashFlowRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع التدفقات النقدية
  Future<List<CashFlow>> getAllCashFlows() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return CashFlow.fromMap(maps[i]);
    });
  }

  /// الحصول على التدفقات النقدية حسب النوع
  Future<List<CashFlow>> getCashFlowsByType(CashFlowType type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      where: 'type = ?',
      whereArgs: [type.toString().split('.').last],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return CashFlow.fromMap(maps[i]);
    });
  }

  /// الحصول على التدفقات النقدية حسب الفئة
  Future<List<CashFlow>> getCashFlowsByCategory(CashFlowCategory category) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      where: 'category = ?',
      whereArgs: [category.toString().split('.').last],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return CashFlow.fromMap(maps[i]);
    });
  }

  /// الحصول على التدفقات النقدية حسب نطاق التاريخ
  Future<List<CashFlow>> getCashFlowsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return CashFlow.fromMap(maps[i]);
    });
  }

  /// الحصول على التدفقات النقدية حسب الحساب البنكي
  Future<List<CashFlow>> getCashFlowsByBankAccount(int bankAccountId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      where: 'bank_account_id = ?',
      whereArgs: [bankAccountId],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return CashFlow.fromMap(maps[i]);
    });
  }

  /// الحصول على التدفقات النقدية حسب الكيان المرتبط
  Future<List<CashFlow>> getCashFlowsByRelatedEntity(
    String relatedEntityType,
    int relatedEntityId,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      where: 'related_entity_type = ? AND related_entity_id = ?',
      whereArgs: [relatedEntityType, relatedEntityId],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return CashFlow.fromMap(maps[i]);
    });
  }

  /// الحصول على تدفق نقدي بواسطة المعرف
  Future<CashFlow?> getCashFlowById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cash_flows',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return CashFlow.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة تدفق نقدي جديد
  Future<int> insertCashFlow(CashFlow cashFlow) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      'cash_flows',
      cashFlow.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث تدفق نقدي
  Future<int> updateCashFlow(CashFlow cashFlow) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'cash_flows',
      cashFlow.toMap(),
      where: 'id = ?',
      whereArgs: [cashFlow.id],
    );
  }

  /// حذف تدفق نقدي
  Future<int> deleteCashFlow(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'cash_flows',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على إجمالي التدفقات النقدية الداخلة حسب نطاق التاريخ
  Future<double> getTotalInflowsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM cash_flows
      WHERE type = ? AND date >= ? AND date <= ?
    ''', [
      CashFlowType.inflow.toString().split('.').last,
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);

    return result.isNotEmpty && result.first['total'] != null
        ? (result.first['total'] as num).toDouble()
        : 0.0;
  }

  /// الحصول على إجمالي التدفقات النقدية الخارجة حسب نطاق التاريخ
  Future<double> getTotalOutflowsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT SUM(amount) as total
      FROM cash_flows
      WHERE type = ? AND date >= ? AND date <= ?
    ''', [
      CashFlowType.outflow.toString().split('.').last,
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);

    return result.isNotEmpty && result.first['total'] != null
        ? (result.first['total'] as num).toDouble()
        : 0.0;
  }
}
