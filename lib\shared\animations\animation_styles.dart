import 'package:flutter/material.dart';

/// أنماط الحركة المستخدمة في التطبيق
class AnimationStyles {
  /// مدة الحركة القصيرة
  static const Duration shortDuration = Duration(milliseconds: 200);
  
  /// مدة الحركة المتوسطة
  static const Duration mediumDuration = Duration(milliseconds: 300);
  
  /// مدة الحركة الطويلة
  static const Duration longDuration = Duration(milliseconds: 500);
  
  /// منحنى الحركة القياسي
  static const Curve standardCurve = Curves.easeInOut;
  
  /// منحنى الحركة السريع
  static const Curve fastCurve = Curves.easeOut;
  
  /// منحنى الحركة البطيء
  static const Curve slowCurve = Curves.easeIn;
  
  /// منحنى الحركة المرتد
  static const Curve bounceCurve = Curves.elasticOut;
  
  /// تأثير تكبير/تصغير عند النقر على الأزرار
  static const double buttonScaleFactor = 0.98;
  
  /// تأثير تكبير/تصغير عند النقر على البطاقات
  static const double cardScaleFactor = 0.98;
  
  /// تأثير تكبير/تصغير عند النقر على العناصر
  static const double itemScaleFactor = 0.95;
}

/// تأثير تكبير/تصغير عند النقر
class ScaleOnTap extends StatefulWidget {
  /// العنصر الذي سيتم تطبيق التأثير عليه
  final Widget child;
  
  /// دالة تنفذ عند النقر
  final VoidCallback? onTap;
  
  /// عامل التكبير/التصغير
  final double scaleFactor;
  
  /// مدة الحركة
  final Duration duration;
  
  /// منحنى الحركة
  final Curve curve;
  
  /// إنشاء تأثير تكبير/تصغير عند النقر
  const ScaleOnTap({
    super.key,
    required this.child,
    this.onTap,
    this.scaleFactor = AnimationStyles.buttonScaleFactor,
    this.duration = AnimationStyles.shortDuration,
    this.curve = AnimationStyles.standardCurve,
  });

  @override
  State<ScaleOnTap> createState() => _ScaleOnTapState();
}

class _ScaleOnTapState extends State<ScaleOnTap> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onTap,
      child: AnimatedScale(
        scale: _isPressed ? widget.scaleFactor : 1.0,
        duration: widget.duration,
        curve: widget.curve,
        child: widget.child,
      ),
    );
  }
}

/// تأثير ظهور تدريجي
class FadeInAnimation extends StatefulWidget {
  /// العنصر الذي سيتم تطبيق التأثير عليه
  final Widget child;
  
  /// مدة الحركة
  final Duration duration;
  
  /// تأخير قبل بدء الحركة
  final Duration delay;
  
  /// منحنى الحركة
  final Curve curve;
  
  /// اتجاه الظهور
  final Offset offset;
  
  /// إنشاء تأثير ظهور تدريجي
  const FadeInAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.delay = Duration.zero,
    this.curve = Curves.easeOut,
    this.offset = const Offset(0, 20),
  });

  @override
  State<FadeInAnimation> createState() => _FadeInAnimationState();
}

class _FadeInAnimationState extends State<FadeInAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: widget.offset,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );

    if (widget.delay == Duration.zero) {
      _controller.forward();
    } else {
      Future.delayed(widget.delay, () {
        if (mounted) {
          _controller.forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: widget.child,
      ),
    );
  }
}

/// تأثير تلاشي عند الخروج
class FadeOutAnimation extends StatefulWidget {
  /// العنصر الذي سيتم تطبيق التأثير عليه
  final Widget child;
  
  /// مدة الحركة
  final Duration duration;
  
  /// منحنى الحركة
  final Curve curve;
  
  /// اتجاه التلاشي
  final Offset offset;
  
  /// دالة تنفذ عند انتهاء الحركة
  final VoidCallback? onComplete;
  
  /// إنشاء تأثير تلاشي عند الخروج
  const FadeOutAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeIn,
    this.offset = const Offset(0, -20),
    this.onComplete,
  });

  @override
  State<FadeOutAnimation> createState() => _FadeOutAnimationState();
}

class _FadeOutAnimationState extends State<FadeOutAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: widget.offset,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed && widget.onComplete != null) {
        widget.onComplete!();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void startAnimation() {
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: widget.child,
      ),
    );
  }
}
