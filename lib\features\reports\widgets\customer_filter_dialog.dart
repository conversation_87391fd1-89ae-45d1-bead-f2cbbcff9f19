import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../config/constants.dart';
import '../../../shared/models/customer.dart';
import '../../../core/repositories/customer_repository.dart';

class CustomerFilterDialog extends StatefulWidget {
  final int? selectedCustomerId;
  final Function(int?) onApplyFilter;

  const CustomerFilterDialog({
    super.key,
    this.selectedCustomerId,
    required this.onApplyFilter,
  });

  @override
  State<CustomerFilterDialog> createState() => _CustomerFilterDialogState();
}

class _CustomerFilterDialogState extends State<CustomerFilterDialog> {
  int? _selectedCustomerId;
  bool _isLoading = true;
  List<Customer> _customers = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedCustomerId = widget.selectedCustomerId;
    _loadCustomers();
  }

  // Repository for customer data
  final CustomerRepository _customerRepository = CustomerRepository();

  Future<void> _loadCustomers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get real customers from database
      final customers = await _customerRepository.getAllCustomers();

      if (mounted) {
        setState(() {
          _customers = customers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading customers: $e');
      }

      if (mounted) {
        setState(() {
          _customers = [];
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل بيانات العملاء: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Customer> get _filteredCustomers {
    if (_searchQuery.isEmpty) {
      return _customers;
    }

    return _customers.where((customer) {
      final query = _searchQuery.toLowerCase();
      return customer.name.toLowerCase().contains(query) ||
          (customer.phone?.toLowerCase().contains(query) ?? false) ||
          (customer.email?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('اختيار العميل'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن عميل...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              textDirection: TextDirection.rtl,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredCustomers.isEmpty
                      ? const Center(
                          child: Text(
                            'لا يوجد عملاء',
                            style: AppTextStyles.heading3,
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          itemCount: _filteredCustomers.length,
                          itemBuilder: (context, index) {
                            final customer = _filteredCustomers[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(vertical: 4),
                              child: RadioListTile<int?>(
                                title: Text(
                                  customer.name,
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      customer.type == CustomerType.company
                                          ? 'شركة'
                                          : 'فرد',
                                      style: TextStyle(
                                        color: customer.type == CustomerType.company
                                            ? AppColors.primary
                                            : Colors.green,
                                      ),
                                    ),
                                    if (customer.phone != null)
                                      Text('هاتف: ${customer.phone}'),
                                  ],
                                ),
                                secondary: CircleAvatar(
                                  backgroundColor: AppColors.primary.withAlpha(51),
                                  child: Text(
                                    customer.name.substring(0, 1),
                                    style: const TextStyle(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                value: customer.localId,
                                groupValue: _selectedCustomerId,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedCustomerId = value;
                                  });
                                },
                                selected: customer.localId == _selectedCustomerId,
                                activeColor: AppColors.primary,
                              ),
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () {
            widget.onApplyFilter(null);
            Navigator.pop(context);
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: const Text('إزالة الفلتر'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApplyFilter(_selectedCustomerId);
            Navigator.pop(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('تطبيق'),
        ),
      ],
    );
  }
}
