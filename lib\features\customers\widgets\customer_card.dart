import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/models/customer.dart';

class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback? onTap;

  const CustomerCard({
    super.key,
    required this.customer,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: _getTypeColor().with<PERSON>l<PERSON>(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                    child: Center(
                      child: Icon(
                        _getTypeIcon(),
                        color: _getTypeColor(),
                        size: 28,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          customer.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          customer.phone ?? 'لا يوجد رقم هاتف',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withAlpha(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      border: Border.all(
                        color: _getStatusColor(),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      customer.isActive ? 'نشط' : 'غير نشط',
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(height: 1),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                children: [
                  _buildInfoItem(
                    Icons.category,
                    Customer.getTypeName(customer.type),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  if (customer.email != null)
                    Expanded(
                      child: _buildInfoItem(
                        Icons.email,
                        customer.email!,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingS),
              if (customer.address != null)
                _buildInfoItem(
                  Icons.location_on,
                  customer.address!,
                ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (customer.type) {
      case CustomerType.individual:
        return Icons.person;
      case CustomerType.company:
        return Icons.business;
    }
  }

  Color _getTypeColor() {
    switch (customer.type) {
      case CustomerType.individual:
        return Colors.blue;
      case CustomerType.company:
        return Colors.purple;
    }
  }

  Color _getStatusColor() {
    return customer.isActive ? Colors.green : Colors.grey;
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
