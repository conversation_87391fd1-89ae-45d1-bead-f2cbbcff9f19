import 'dart:math';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart' as sql;
import '../../shared/models/customer.dart';
import '../../shared/models/supplier.dart';
import '../../shared/models/employee.dart';
import '../../shared/models/category.dart';
import '../../shared/models/bank_account.dart';
import '../../shared/models/service_request.dart';
import '../../shared/models/invoice.dart';
import '../../shared/models/company_info.dart';
import 'database_helper.dart';

/// مولد البيانات التعليمية للتطبيق
class DemoDataGenerator {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Random _random = Random();

  /// إنشاء بيانات تعليمية كاملة
  Future<bool> generateDemoData() async {
    try {
      final sql.Database db = await _dbHelper.database;

      // بدء معاملة قاعدة البيانات
      await db.transaction((txn) async {
        // إنشاء معلومات الشركة
        await _generateCompanyInfo(txn);

        // إنشاء الفئات
        await _generateCategories(txn);

        // إنشاء الحسابات البنكية
        await _generateBankAccounts(txn);

        // إنشاء العملاء
        await _generateCustomers(txn);

        // إنشاء الموردين
        await _generateSuppliers(txn);

        // إنشاء الموظفين
        await _generateEmployees(txn);

        // إنشاء طلبات الخدمة
        await _generateServiceRequests(txn);

        // إنشاء الفواتير
        await _generateInvoices(txn);

        // إنشاء المعاملات المالية
        await _generateTransactions(txn);
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء البيانات التعليمية: $e');
      return false;
    }
  }

  /// إنشاء معلومات الشركة
  Future<void> _generateCompanyInfo(sql.Transaction txn) async {
    // التحقق من وجود معلومات الشركة
    final List<Map<String, dynamic>> companyInfoMaps = await txn.query('company_info');

    if (companyInfoMaps.isEmpty) {
      await txn.insert('company_info', {
        'name_ar': 'ركن الجليد',
        'name_en': 'Ice Corner',
        'address_ar': 'الرياض، المملكة العربية السعودية',
        'address_en': 'Riyadh, Saudi Arabia',
        'phone_ar': '٠٥٠٠٠٠٠٠٠٠',
        'phone_en': '0500000000',
        'email': '<EMAIL>',
        'website': 'www.icecorner.com',
        'tax_number': '*********',
        'commercial_register': '*********',
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إنشاء الفئات
  Future<void> _generateCategories(sql.Transaction txn) async {
    // التحقق من وجود فئات
    final List<Map<String, dynamic>> categoryMaps = await txn.query('categories');

    if (categoryMaps.length <= 5) {
      // فئات الإيرادات
      final incomeCategories = [
        {'name': 'مبيعات', 'type': 'income', 'icon_code': Icons.shopping_cart.codePoint, 'color': 0xFF2196F3}, // أزرق
        {'name': 'خدمات', 'type': 'income', 'icon_code': Icons.miscellaneous_services.codePoint, 'color': 0xFF4CAF50}, // أخضر
        {'name': 'استرداد', 'type': 'income', 'icon_code': Icons.assignment_return.codePoint, 'color': 0xFFFF9800}, // برتقالي
      ];

      // فئات المصروفات
      final expenseCategories = [
        {'name': 'رواتب', 'type': 'expense', 'icon_code': Icons.payments.codePoint, 'color': 0xFFF44336}, // أحمر
        {'name': 'إيجار', 'type': 'expense', 'icon_code': Icons.home.codePoint, 'color': 0xFF9C27B0}, // بنفسجي
        {'name': 'مرافق', 'type': 'expense', 'icon_code': Icons.electrical_services.codePoint, 'color': 0xFFFFEB3B}, // أصفر
        {'name': 'قطع غيار', 'type': 'expense', 'icon_code': Icons.build.codePoint, 'color': 0xFF795548}, // بني
        {'name': 'وقود', 'type': 'expense', 'icon_code': Icons.local_gas_station.codePoint, 'color': 0xFF009688}, // فيروزي
      ];

      // إضافة فئات الإيرادات
      for (final category in incomeCategories) {
        await txn.insert('categories', {
          'name': category['name'],
          'type': category['type'],
          'icon_code': category['icon_code'],
          'color': category['color'],
          'status': 'active',
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      // إضافة فئات المصروفات
      for (final category in expenseCategories) {
        await txn.insert('categories', {
          'name': category['name'],
          'type': category['type'],
          'icon_code': category['icon_code'],
          'color': category['color'],
          'status': 'active',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }
  }

  /// إنشاء الحسابات البنكية
  Future<void> _generateBankAccounts(sql.Transaction txn) async {
    // التحقق من وجود حسابات بنكية
    final List<Map<String, dynamic>> bankAccountMaps = await txn.query('bank_accounts');

    if (bankAccountMaps.isEmpty) {
      final bankAccounts = [
        {
          'bank_name': 'البنك الأهلي',
          'account_number': 'SA0*********',
          'account_name': 'ركن الجليد',
          'type': 'checking',
          'balance': 50000.0,
          'status': 'active',
        },
        {
          'bank_name': 'مصرف الراجحي',
          'account_number': 'SA*********0',
          'account_name': 'ركن الجليد',
          'type': 'savings',
          'balance': 25000.0,
          'status': 'active',
        },
      ];

      for (final account in bankAccounts) {
        await txn.insert('bank_accounts', {
          'bank_name': account['bank_name'],
          'account_number': account['account_number'],
          'account_name': account['account_name'],
          'type': account['type'],
          'balance': account['balance'],
          'status': account['status'],
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }
  }

  /// إنشاء العملاء
  Future<void> _generateCustomers(sql.Transaction txn) async {
    // التحقق من وجود عملاء
    final List<Map<String, dynamic>> customerMaps = await txn.query('customers');

    if (customerMaps.isEmpty) {
      final customers = [
        {
          'name': 'شركة الأمل للمقاولات',
          'type': 'company',
          'email': '<EMAIL>',
          'phone': '**********',
          'address': 'الرياض، حي العليا',
          'contact_person': 'أحمد محمد',
          'payment_method': 'monthlySubscription',
          'monthly_subscription_amount': 1000.0,
          'subscription_start_date': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
          'subscription_end_date': DateTime.now().add(const Duration(days: 335)).toIso8601String(),
          'is_subscription_active': 1,
        },
        {
          'name': 'محمد أحمد',
          'type': 'individual',
          'phone': '0598765432',
          'address': 'جدة، حي الروضة',
          'payment_method': 'perService',
        },
        {
          'name': 'مؤسسة النور',
          'type': 'company',
          'email': '<EMAIL>',
          'phone': '0512345679',
          'address': 'الدمام، حي الفيصلية',
          'contact_person': 'خالد عبدالله',
          'payment_method': 'perService',
        },
        {
          'name': 'سارة علي',
          'type': 'individual',
          'phone': '0598765433',
          'address': 'الرياض، حي النزهة',
          'payment_method': 'monthlySubscription',
          'monthly_subscription_amount': 500.0,
          'subscription_start_date': DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
          'subscription_end_date': DateTime.now().add(const Duration(days: 350)).toIso8601String(),
          'is_subscription_active': 1,
        },
      ];

      for (final customer in customers) {
        await txn.insert('customers', {
          'name': customer['name'],
          'type': customer['type'],
          'email': customer['email'] ?? '',
          'phone': customer['phone'],
          'address': customer['address'],
          'contact_person': customer['contact_person'] ?? '',
          'status': 'active',
          'created_at': DateTime.now().subtract(Duration(days: _random.nextInt(90))).toIso8601String(),
          'notes': '',
          'payment_method': customer['payment_method'],
          'monthly_subscription_amount': customer['monthly_subscription_amount'] ?? 0.0,
          'subscription_start_date': customer['subscription_start_date'],
          'subscription_end_date': customer['subscription_end_date'],
          'is_subscription_active': customer['is_subscription_active'] ?? 0,
        });
      }
    }
  }

  /// إنشاء الموردين
  Future<void> _generateSuppliers(sql.Transaction txn) async {
    // التحقق من وجود موردين
    final List<Map<String, dynamic>> supplierMaps = await txn.query('suppliers');

    if (supplierMaps.isEmpty) {
      final suppliers = [
        {
          'name': 'شركة التبريد المركزي',
          'email': '<EMAIL>',
          'phone': '0512345680',
          'address': 'الرياض، المنطقة الصناعية',
          'contact_person': 'فهد سعيد',
          'category': 'قطع غيار',
          'balance': 0.0,
        },
        {
          'name': 'مؤسسة الخليج للتكييف',
          'email': '<EMAIL>',
          'phone': '0512345681',
          'address': 'جدة، المنطقة الصناعية',
          'contact_person': 'سعد محمد',
          'category': 'أجهزة',
          'balance': 0.0,
        },
      ];

      for (final supplier in suppliers) {
        await txn.insert('suppliers', {
          'name': supplier['name'],
          'email': supplier['email'],
          'phone': supplier['phone'],
          'address': supplier['address'],
          'contact_person': supplier['contact_person'],
          'category': supplier['category'],
          'status': 'active',
          'balance': supplier['balance'],
          'created_at': DateTime.now().subtract(Duration(days: _random.nextInt(90))).toIso8601String(),
          'notes': '',
        });
      }
    }
  }

  /// إنشاء الموظفين
  Future<void> _generateEmployees(sql.Transaction txn) async {
    // التحقق من وجود موظفين
    final List<Map<String, dynamic>> employeeMaps = await txn.query('employees');

    if (employeeMaps.isEmpty) {
      final employees = [
        {
          'name': 'أحمد محمد',
          'email': '<EMAIL>',
          'phone': '**********',
          'address': 'الرياض، حي الملز',
          'position': 'مدير',
          'join_date': DateTime(2020, 1, 15).toIso8601String(),
          'salary': 15000.0,
          'status': 'active',
        },
        {
          'name': 'محمد علي',
          'email': '<EMAIL>',
          'phone': '0523456789',
          'address': 'الرياض، حي النسيم',
          'position': 'فني تكييف',
          'join_date': DateTime(2021, 3, 10).toIso8601String(),
          'salary': 8000.0,
          'status': 'active',
        },
        {
          'name': 'خالد عبدالله',
          'email': '<EMAIL>',
          'phone': '0534567890',
          'address': 'الرياض، حي الروضة',
          'position': 'فني تكييف',
          'join_date': DateTime(2022, 5, 20).toIso8601String(),
          'salary': 7500.0,
          'status': 'active',
        },
      ];

      for (final employee in employees) {
        await txn.insert('employees', {
          'name': employee['name'],
          'email': employee['email'],
          'phone': employee['phone'],
          'address': employee['address'],
          'position': employee['position'],
          'join_date': employee['join_date'],
          'salary': employee['salary'],
          'status': employee['status'],
          'created_at': DateTime.now().subtract(Duration(days: _random.nextInt(90))).toIso8601String(),
          'notes': '',
        });
      }
    }
  }

  /// إنشاء طلبات الخدمة
  Future<void> _generateServiceRequests(sql.Transaction txn) async {
    // التحقق من وجود طلبات خدمة
    final List<Map<String, dynamic>> serviceRequestMaps = await txn.query('service_requests');

    if (serviceRequestMaps.isEmpty) {
      // الحصول على معرفات العملاء
      final List<Map<String, dynamic>> customerMaps = await txn.query('customers');
      final List<int> customerIds = customerMaps.map<int>((map) => map['id'] as int).toList();

      // الحصول على معرفات الموظفين
      final List<Map<String, dynamic>> employeeMaps = await txn.query('employees');
      final List<int> employeeIds = employeeMaps.map<int>((map) => map['id'] as int).toList();

      // إنشاء طلبات خدمة متنوعة
      final serviceTypes = ['صيانة تكييف', 'تركيب تكييف', 'إصلاح تكييف', 'تنظيف تكييف'];
      final statuses = ['pending', 'in progress', 'completed', 'cancelled'];

      for (int i = 0; i < 20; i++) {
        final customerId = customerIds[_random.nextInt(customerIds.length)];
        final customerMap = customerMaps.firstWhere((map) => map['id'] == customerId);
        final employeeId = employeeIds[_random.nextInt(employeeIds.length)];
        final serviceType = serviceTypes[_random.nextInt(serviceTypes.length)];
        final status = statuses[_random.nextInt(statuses.length)];
        final createdDate = DateTime.now().subtract(Duration(days: _random.nextInt(60)));
        final scheduledDate = createdDate.add(Duration(days: _random.nextInt(14) + 1));

        // تحديد مبلغ الخدمة بناءً على طريقة الدفع
        double serviceAmount = 0.0;
        if (customerMap['payment_method'] == 'perService') {
          serviceAmount = ((_random.nextInt(5) + 1) * 100).toDouble(); // مبلغ عشوائي بين 100 و 500
        }

        await txn.insert('service_requests', {
          'request_number': 'SR-${10000 + i}',
          'customer_id': customerId,
          'service_type': serviceType,
          'description': 'وصف لطلب الخدمة رقم ${i + 1}',
          'address': customerMap['address'],
          'scheduled_date': scheduledDate.toIso8601String(),
          'status': status,
          'assigned_to': employeeId,
          'completion_date': status == 'completed' ? scheduledDate.add(Duration(hours: _random.nextInt(5) + 1)).toIso8601String() : null,
          'notes': '',
          'created_at': createdDate.toIso8601String(),
          'updated_at': createdDate.toIso8601String(),
          'service_amount': serviceAmount,
          'is_technical_visit': _random.nextBool() ? 1 : 0,
          'technical_report': status == 'completed' ? 'تم إصلاح المشكلة وتنظيف الوحدة' : '',
          'service_category_id': null,
          'reminder_minutes': 60,
        });
      }
    }
  }

  /// إنشاء الفواتير
  Future<void> _generateInvoices(sql.Transaction txn) async {
    // التحقق من وجود فواتير
    final List<Map<String, dynamic>> invoiceMaps = await txn.query('invoices');

    if (invoiceMaps.isEmpty) {
      // الحصول على معرفات العملاء
      final List<Map<String, dynamic>> customerMaps = await txn.query('customers');
      final List<int> customerIds = customerMaps.map<int>((map) => map['id'] as int).toList();

      // إنشاء فواتير متنوعة
      final statuses = ['unpaid', 'paid', 'partially paid'];

      for (int i = 0; i < 15; i++) {
        final customerId = customerIds[_random.nextInt(customerIds.length)];
        final status = statuses[_random.nextInt(statuses.length)];
        final createdDate = DateTime.now().subtract(Duration(days: _random.nextInt(60)));
        final dueDate = createdDate.add(Duration(days: 30));

        // إنشاء الفاتورة
        final subtotal = ((_random.nextInt(10) + 1) * 500).toDouble();
        final tax = subtotal * 0.15;
        final total = subtotal + tax;

        final invoiceId = await txn.insert('invoices', {
          'invoice_number': 'INV-${20000 + i}',
          'customer_id': customerId,
          'date': createdDate.toIso8601String(),
          'due_date': dueDate.toIso8601String(),
          'subtotal': subtotal,
          'tax': tax,
          'discount': 0.0,
          'total': total,
          'notes': '',
          'status': status,
          'payment_date': status == 'paid' ? createdDate.add(Duration(days: _random.nextInt(10) + 1)).toIso8601String() : null,
          'created_at': createdDate.toIso8601String(),
          'updated_at': createdDate.toIso8601String(),
        });

        // إنشاء عناصر الفاتورة
        final itemCount = _random.nextInt(3) + 1;
        for (int j = 0; j < itemCount; j++) {
          final itemPrice = ((_random.nextInt(5) + 1) * 100).toDouble();
          final quantity = _random.nextInt(5) + 1;

          await txn.insert('invoice_items', {
            'invoice_id': invoiceId,
            'description': 'خدمة ${j + 1} للفاتورة ${i + 1}',
            'quantity': quantity,
            'unit_price': itemPrice,
            'total': itemPrice * quantity,
            'created_at': createdDate.toIso8601String(),
          });
        }
      }
    }
  }

  /// إنشاء المعاملات المالية
  Future<void> _generateTransactions(sql.Transaction txn) async {
    // التحقق من وجود معاملات
    final List<Map<String, dynamic>> transactionMaps = await txn.query('transactions');

    if (transactionMaps.isEmpty) {
      // الحصول على معرفات العملاء والموردين والحسابات البنكية والفئات
      final List<Map<String, dynamic>> customerMaps = await txn.query('customers');
      final List<int> customerIds = customerMaps.map<int>((map) => map['id'] as int).toList();

      final List<Map<String, dynamic>> supplierMaps = await txn.query('suppliers');
      final List<int> supplierIds = supplierMaps.map<int>((map) => map['id'] as int).toList();

      final List<Map<String, dynamic>> bankAccountMaps = await txn.query('bank_accounts');
      final List<int> bankAccountIds = bankAccountMaps.map<int>((map) => map['id'] as int).toList();

      final List<Map<String, dynamic>> incomeCategoryMaps = await txn.query(
        'categories',
        where: 'type = ?',
        whereArgs: ['income'],
      );
      final List<int> incomeCategoryIds = incomeCategoryMaps.map<int>((map) => map['id'] as int).toList();

      final List<Map<String, dynamic>> expenseCategoryMaps = await txn.query(
        'categories',
        where: 'type = ?',
        whereArgs: ['expense'],
      );
      final List<int> expenseCategoryIds = expenseCategoryMaps.map<int>((map) => map['id'] as int).toList();

      // إنشاء معاملات إيرادات
      for (int i = 0; i < 20; i++) {
        final createdDate = DateTime.now().subtract(Duration(days: _random.nextInt(60)));
        final amount = ((_random.nextInt(10) + 1) * 500).toDouble();
        final customerId = customerIds[_random.nextInt(customerIds.length)];
        final categoryId = incomeCategoryIds[_random.nextInt(incomeCategoryIds.length)];
        final bankAccountId = bankAccountIds[_random.nextInt(bankAccountIds.length)];

        await txn.insert('transactions', {
          'reference': 'INC-${30000 + i}',
          'date': createdDate.toIso8601String(),
          'amount': amount,
          'type': 'income',
          'category_id': categoryId,
          'description': 'إيراد ${i + 1}',
          'payment_method': _random.nextBool() ? 'cash' : 'bank transfer',
          'bank_account_id': bankAccountId,
          'customer_id': customerId,
          'created_at': createdDate.toIso8601String(),
        });
      }

      // إنشاء معاملات مصروفات
      for (int i = 0; i < 15; i++) {
        final createdDate = DateTime.now().subtract(Duration(days: _random.nextInt(60)));
        final amount = ((_random.nextInt(8) + 1) * 300).toDouble();
        final categoryId = expenseCategoryIds[_random.nextInt(expenseCategoryIds.length)];
        final bankAccountId = bankAccountIds[_random.nextInt(bankAccountIds.length)];

        // تحديد ما إذا كان المصروف مرتبط بمورد
        final isSupplierExpense = _random.nextBool();
        final supplierId = isSupplierExpense ? supplierIds[_random.nextInt(supplierIds.length)] : null;

        await txn.insert('transactions', {
          'reference': 'EXP-${40000 + i}',
          'date': createdDate.toIso8601String(),
          'amount': amount,
          'type': 'expense',
          'category_id': categoryId,
          'description': 'مصروف ${i + 1}',
          'payment_method': _random.nextBool() ? 'cash' : 'bank transfer',
          'bank_account_id': bankAccountId,
          'supplier_id': supplierId,
          'created_at': createdDate.toIso8601String(),
        });
      }
    }
  }
}
