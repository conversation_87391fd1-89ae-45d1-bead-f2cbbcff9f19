import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../state/auth_state.dart';
import '../widgets/auth_header.dart';
import '../../../shared/widgets/custom_button.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  final _secureStorage = const FlutterSecureStorage();
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _savedUsername;
  String? _savedPassword;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  Future<void> _loadSavedCredentials() async {
    try {
      final rememberMe = await _secureStorage.read(key: AppConstants.rememberMeKey);
      if (rememberMe == 'true') {
        final username = await _secureStorage.read(key: AppConstants.usernameKey);
        final password = await _secureStorage.read(key: AppConstants.passwordKey);

        if (username != null && password != null && mounted) {
          setState(() {
            _savedUsername = username;
            _savedPassword = password;
            _rememberMe = true;
          });

          // Check if we should auto-login
          final authState = Provider.of<AuthState>(context, listen: false);
          if (authState.status == AuthStatus.unauthenticated) {
            // Auto-login immediately without delay
            _autoLogin(username, password);
          }
        }
      }
    } catch (e) {
      // Ignore errors when loading saved credentials
      debugPrint('Error loading saved credentials: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = Provider.of<AuthState>(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.background,
      // تطبيق خلفية متدرجة تتناسب مع الشعار
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFECF0F1), // لون فاتح في الأعلى
              Colors.white, // لون أبيض في الأسفل
            ],
          ),
        ),
        child: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 450,
                minHeight: size.height - 2 * AppDimensions.paddingL,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(
                    title: 'تسجيل الدخول',
                    subtitle: 'أدخل بيانات الدخول للوصول إلى لوحة التحكم',
                  ),
                  const SizedBox(height: AppDimensions.paddingL),
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingL),
                      child: FormBuilder(
                        key: _formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Username field
                            FormBuilderTextField(
                              name: 'username',
                              initialValue: _savedUsername,
                              decoration: const InputDecoration(
                                labelText: 'اسم المستخدم',
                                prefixIcon: Icon(Icons.person),
                                hintText: 'أدخل اسم المستخدم',
                              ),
                              textDirection: TextDirection.rtl,
                              textInputAction: TextInputAction.next,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'يرجى إدخال اسم المستخدم',
                                ),
                              ]),
                            ),
                            const SizedBox(height: AppDimensions.paddingM),

                            // Password field
                            FormBuilderTextField(
                              name: 'password',
                              initialValue: _savedPassword,
                              decoration: InputDecoration(
                                labelText: 'كلمة المرور',
                                prefixIcon: const Icon(Icons.lock),
                                hintText: 'أدخل كلمة المرور',
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                              ),
                              textDirection: TextDirection.rtl,
                              obscureText: _obscurePassword,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(
                                  errorText: 'يرجى إدخال كلمة المرور',
                                ),
                              ]),
                            ),
                            const SizedBox(height: AppDimensions.paddingM),

                            // Remember me checkbox
                            FormBuilderCheckbox(
                              name: 'remember_me',
                              initialValue: _rememberMe,
                              title: const Text(
                                'تذكرني (الحفظ التلقائي لتسجيل الدخول)',
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              controlAffinity: ListTileControlAffinity.leading,
                              decoration: const InputDecoration(
                                helperText: 'حفظ بيانات الدخول للمرة القادمة',
                                helperStyle: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.textHint,
                                ),
                                contentPadding: EdgeInsets.zero,
                                border: InputBorder.none,
                              ),
                            ),

                            // Error message
                            if (authState.errorMessage != null)
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: AppDimensions.paddingS,
                                ),
                                child: Text(
                                  authState.errorMessage!,
                                  style: const TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),

                            const SizedBox(height: AppDimensions.paddingL),

                            // Login button
                            CustomButton(
                              text: 'تسجيل الدخول',
                              isLoading: _isLoading,
                              onPressed: _isLoading ? null : _handleLogin,
                            ),

                            const SizedBox(height: AppDimensions.paddingM),

                            // Admin login button
                            OutlinedButton(
                              onPressed: _isLoading ? null : _handleAdminLogin,
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                side: const BorderSide(color: AppColors.primary),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                                ),
                              ),
                              child: const Text(
                                'تسجيل الدخول كمدير النظام',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                            const SizedBox(height: AppDimensions.paddingM),

                            // Forgot password link
                            Align(
                              alignment: Alignment.center,
                              child: TextButton(
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    AppRoutes.forgotPassword,
                                  );
                                },
                                child: const Text('نسيت كلمة المرور؟'),
                              ),
                            ),

                            const Divider(height: 32),

                            // Register link
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  'ليس لديك حساب؟',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.pushNamed(
                                      context,
                                      AppRoutes.register,
                                    );
                                  },
                                  child: const Text('إنشاء حساب جديد'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  Future<void> _handleLogin() async {
    // Clear any previous errors
    Provider.of<AuthState>(context, listen: false).clearError();

    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      final formData = _formKey.currentState!.value;
      final username = formData['username'] as String;
      final password = formData['password'] as String;
      final rememberMe = formData['remember_me'] as bool? ?? false;

      if (kDebugMode) {
        print('Login form submitted:');
        print('Username: $username');
        print('Password: $password');
        print('Remember Me: $rememberMe');
      }

      final success = await Provider.of<AuthState>(context, listen: false)
          .login(username, password, rememberMe);

      if (kDebugMode) {
        print('Login result: $success');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          if (kDebugMode) {
            print('Login successful, navigating to dashboard');
          }
          // Navigate to dashboard on successful login
          Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
        } else {
          if (kDebugMode) {
            print('Login failed');
            print('Error message: ${Provider.of<AuthState>(context, listen: false).errorMessage}');
          }
        }
      }
    } else {
      if (kDebugMode) {
        print('Form validation failed');
        print('Form errors: ${_formKey.currentState?.errors}');
      }
    }
  }

  // Method to handle admin login
  Future<void> _handleAdminLogin() async {
    // Clear any previous errors
    Provider.of<AuthState>(context, listen: false).clearError();

    setState(() {
      _isLoading = true;
    });

    if (kDebugMode) {
      print('Admin login button pressed');
    }

    // Use admin credentials
    final success = await Provider.of<AuthState>(context, listen: false)
        .login('admin', '1', true);

    if (kDebugMode) {
      print('Admin login result: $success');
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        if (kDebugMode) {
          print('Admin login successful, navigating to dashboard');
        }
        // Navigate to dashboard on successful login
        Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      } else {
        if (kDebugMode) {
          print('Admin login failed');
          print('Error message: ${Provider.of<AuthState>(context, listen: false).errorMessage}');
        }
      }
    }
  }

  // Method for auto-login with saved credentials
  Future<void> _autoLogin(String username, String password) async {
    if (kDebugMode) {
      print('Auto-login triggered with:');
      print('Username: $username');
      print('Password: $password');
    }

    setState(() {
      _isLoading = true;
    });

    // Clear any previous errors
    Provider.of<AuthState>(context, listen: false).clearError();

    final success = await Provider.of<AuthState>(context, listen: false)
        .login(username, password, true);

    if (kDebugMode) {
      print('Auto-login result: $success');
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        if (kDebugMode) {
          print('Auto-login successful, navigating to dashboard');
        }
        // Navigate to dashboard on successful login
        Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      } else {
        if (kDebugMode) {
          print('Auto-login failed');
          print('Error message: ${Provider.of<AuthState>(context, listen: false).errorMessage}');
        }
      }
    }
  }
}
