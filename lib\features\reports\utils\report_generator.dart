import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:icecorner/features/reports/utils/simple_pdf_export.dart';
import 'package:icecorner/features/reports/screens/pdf_preview_screen.dart';
import 'package:icecorner/shared/models/customer.dart';
import 'package:icecorner/shared/models/employee.dart';
import 'package:icecorner/shared/models/supplier.dart';
import 'package:icecorner/shared/models/invoice.dart' as app_model;
import 'package:icecorner/shared/models/transaction.dart' as app_transaction;
import 'package:icecorner/shared/models/service_request.dart' as app_service;
import 'package:icecorner/shared/models/service_status.dart' as app_service_status;
import 'package:icecorner/shared/models/inventory_item.dart';
import 'package:icecorner/shared/models/inventory_transaction.dart';
import 'package:icecorner/features/reports/models/report_models.dart' as report_model;
import 'package:icecorner/features/reports/models/report_models.dart';
import 'package:icecorner/features/reports/utils/invoice_adapter.dart';
import 'package:icecorner/core/repositories/customer_repository.dart';
import 'package:icecorner/core/repositories/employee_repository.dart';
import 'package:icecorner/core/repositories/supplier_repository.dart';
import 'package:icecorner/core/repositories/invoice_repository.dart';
import 'package:icecorner/core/repositories/transaction_repository.dart';
import 'package:icecorner/core/repositories/service_request_repository.dart';
import 'package:icecorner/core/repositories/inventory_repository.dart';

/// نوع التقرير
enum ReportType {
  customer,
  employee,
  supplier,
  invoice,
  transaction,
  serviceRequest,
  inventoryItem,
  inventoryTransaction,
  summary,
}

/// مولد التقارير الموحد
class ReportGenerator {
  /// إنشاء تقرير للعميل
  static Future<String?> createCustomerReport({
    required BuildContext context,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required List<dynamic> data,
    required Map<String, double> summaryData,
  }) async {
    return generateAndShowReport(
      context: context,
      reportType: ReportType.customer,
      title: title,
      startDate: startDate,
      endDate: endDate,
      customData: data,
      showPreview: false,
    );
  }
  /// إنشاء تقرير وعرضه
  static Future<String?> generateAndShowReport({
    required BuildContext context,
    required ReportType reportType,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    int? entityId, // معرف الكيان (عميل، موظف، مورد) إذا كان التقرير مخصصًا
    Map<String, dynamic>? filters, // فلاتر إضافية
    List<dynamic>? customData, // بيانات مخصصة من واجهة المستخدم
    bool showPreview = true, // عرض معاينة التقرير بعد إنشائه
  }) async {
    // Guardar una referencia al contexto para verificar si está montado
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    // عرض مؤشر التحميل
    _showLoadingDialog(context);

    try {
      // استخدام البيانات المخصصة إذا كانت متوفرة، وإلا جلب البيانات من قاعدة البيانات
      final data = customData ?? await _fetchReportData(
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        entityId: entityId,
        filters: filters,
      );

      // حساب البيانات الملخصة
      final summaryData = _calculateSummaryData(
        reportType: reportType,
        data: data,
      );

      // إنشاء ملف PDF
      final filePath = await SimplePdfExport.createSimpleReport(
        title: title,
        reportType: _getReportTypeString(reportType),
        startDate: startDate,
        endDate: endDate,
        data: data,
        summaryData: summaryData,
        context: context,
      );

      // Verificar si el contexto sigue montado
      if (navigator.mounted) {
        // إغلاق مؤشر التحميل
        navigator.pop();

        if (filePath == null) {
          throw Exception('فشل في إنشاء ملف PDF');
        }

        // عرض معاينة PDF إذا كان مطلوبًا
        if (showPreview) {
          navigator.push(
            MaterialPageRoute(
              builder: (context) => PdfPreviewScreen(
                filePath: filePath,
                title: title,
              ),
            ),
          );
        }

        // إرجاع مسار الملف
        return filePath;
      }
    } catch (e) {
      // Verificar si el contexto sigue montado
      if (navigator.mounted) {
        // إغلاق مؤشر التحميل إذا كان مفتوحًا
        if (navigator.canPop()) {
          navigator.pop();
        }

        // عرض رسالة الخطأ
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إنشاء التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    // إرجاع null في حالة الفشل
    return null;
  }

  /// طباعة التقرير مباشرة
  static Future<void> printReport({
    required BuildContext context,
    required ReportType reportType,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    int? entityId,
    Map<String, dynamic>? filters,
    List<dynamic>? customData, // بيانات مخصصة من واجهة المستخدم
  }) async {
    // Guardar una referencia al contexto para verificar si está montado
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    // عرض مؤشر التحميل
    _showLoadingDialog(context);

    try {
      // استخدام البيانات المخصصة إذا كانت متوفرة، وإلا جلب البيانات من قاعدة البيانات
      final data = customData ?? await _fetchReportData(
        reportType: reportType,
        startDate: startDate,
        endDate: endDate,
        entityId: entityId,
        filters: filters,
      );

      // حساب البيانات الملخصة
      final summaryData = _calculateSummaryData(
        reportType: reportType,
        data: data,
      );

      // طباعة التقرير مباشرة
      final success = await SimplePdfExport.printReport(
        title: title,
        reportType: _getReportTypeString(reportType),
        startDate: startDate,
        endDate: endDate,
        data: data,
        summaryData: summaryData,
      );

      // Verificar si el contexto sigue montado
      if (navigator.mounted) {
        // إغلاق مؤشر التحميل
        navigator.pop();

        // عرض رسالة النجاح أو الفشل
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'تم إرسال التقرير للطباعة بنجاح'
                  : 'تم إلغاء الطباعة أو حدث خطأ',
            ),
            backgroundColor: success ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      // Verificar si el contexto sigue montado
      if (navigator.mounted) {
        // إغلاق مؤشر التحميل إذا كان مفتوحًا
        if (navigator.canPop()) {
          navigator.pop();
        }

        // عرض رسالة الخطأ
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض مؤشر التحميل
  static void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري إنشاء التقرير...'),
            ],
          ),
        );
      },
    );
  }

  /// جلب البيانات حسب نوع التقرير
  static Future<List<dynamic>> _fetchReportData({
    required ReportType reportType,
    required DateTime startDate,
    required DateTime endDate,
    int? entityId,
    Map<String, dynamic>? filters,
  }) async {
    switch (reportType) {
      case ReportType.customer:
        final repo = CustomerRepository();
        if (entityId != null) {
          final customer = await repo.getCustomerById(entityId);
          return customer != null ? [customer] : [];
        }
        return await repo.getAllCustomers();

      case ReportType.employee:
        final repo = EmployeeRepository();
        if (entityId != null) {
          final employee = await repo.getEmployeeById(entityId);
          return employee != null ? [employee] : [];
        }
        return await repo.getAllEmployees();

      case ReportType.supplier:
        final repo = SupplierRepository();
        if (entityId != null) {
          final supplier = await repo.getSupplierById(entityId);
          return supplier != null ? [supplier] : [];
        }
        return await repo.getAllSuppliers();

      case ReportType.invoice:
        final invoiceRepo = InvoiceRepository();
        final transactionRepo = TransactionRepository();

        // الحصول على الفواتير
        final appInvoices = await invoiceRepo.getInvoicesByDateRange(startDate, endDate);

        // الحصول على المعاملات المالية المرتبطة بالفواتير
        final transactions = await transactionRepo.getTransactionsByDateRange(startDate, endDate);

        // إنشاء قاموس للربط بين الفواتير والمعاملات المالية المرتبطة بها
        final Map<int, List<dynamic>> invoicePayments = {};

        // تجميع المعاملات المالية حسب معرف الفاتورة
        for (final transaction in transactions) {
          if (transaction.invoiceId != null) {
            if (!invoicePayments.containsKey(transaction.invoiceId)) {
              invoicePayments[transaction.invoiceId!] = [];
            }
            invoicePayments[transaction.invoiceId!]!.add(transaction);
          }
        }

        // تحويل الفواتير من نموذج التطبيق إلى نموذج التقرير مع تحديث معلومات الدفع
        final invoices = appInvoices.map((invoice) {
          // حساب المبلغ المدفوع من المعاملات المالية المرتبطة
          double amountPaid = 0.0;
          DateTime? paymentDate;

          if (invoice.id != null && invoicePayments.containsKey(invoice.id)) {
            final payments = invoicePayments[invoice.id!]!;
            amountPaid = payments.fold(0.0, (sum, payment) => sum + (payment.amount ?? 0.0));

            // استخدام تاريخ آخر دفعة كتاريخ الدفع
            if (payments.isNotEmpty) {
              payments.sort((a, b) => b.date.compareTo(a.date)); // ترتيب تنازلي حسب التاريخ
              paymentDate = payments.first.date;
            }
          }

          // تحويل الفاتورة إلى نموذج التقرير
          final reportInvoice = InvoiceAdapter.toReportModel(invoice);

          // تحديث معلومات الدفع إذا كانت متوفرة
          if (amountPaid > 0) {
            return reportInvoice.copyWith(
              amountPaid: amountPaid,
              paymentDate: paymentDate,
              // تحديث حالة الدفع بناءً على المبلغ المدفوع
              isPaid: amountPaid >= invoice.total,
              status: amountPaid >= invoice.total
                  ? report_model.InvoiceStatus.paid
                  : (amountPaid > 0 ? report_model.InvoiceStatus.partiallyPaid : reportInvoice.status),
            );
          }

          return reportInvoice;
        }).toList();

        if (entityId != null) {
          // فلترة الفواتير حسب العميل
          return invoices.where((invoice) => invoice.customerId == entityId).toList();
        }
        return invoices;

      case ReportType.transaction:
        final repo = TransactionRepository();
        final appTransactions = await repo.getTransactionsByDateRange(startDate, endDate);

        // تطبيق الفلاتر بشكل مباشر بدلاً من استخدام الدالة المساعدة
        var filteredTransactions = appTransactions;

        if (filters != null) {
          // فلترة حسب النوع (إيرادات/مصروفات)
          if (filters.containsKey('type')) {
            final type = filters['type'] as String?;
            if (type != null) {
              filteredTransactions = filteredTransactions.where((t) {
                return t.type.toString().toLowerCase().contains(type.toLowerCase());
              }).toList();
            }
          }

          // فلترة حسب الفئة
          if (filters.containsKey('category')) {
            final category = filters['category'] as String?;
            if (category != null) {
              filteredTransactions = filteredTransactions.where((t) =>
                t.category?.toLowerCase().contains(category.toLowerCase()) ?? false
              ).toList();
            }
          }
        }

        // تحويل البيانات إلى نموذج التقرير (يمكن إنشاء محول مشابه لـ InvoiceAdapter)
        final reportTransactions = filteredTransactions.map((t) => Transaction(
          id: t.id ?? 0,
          reference: t.reference,
          date: t.date,
          amount: t.amount,
          type: t.type.toString().contains('income') ? TransactionType.income : TransactionType.expense,
          category: t.category ?? '',
          categoryId: null, // لا يوجد حاجة لهذه القيمة في التقرير
          description: t.description ?? '',
          paymentMethod: PaymentMethod.cash, // قيمة افتراضية
          relatedEntityName: t.reference, // استخدام المرجع كاسم الطرف المرتبط
          createdAt: t.createdAt,
        )).toList();

        return reportTransactions;

      case ReportType.serviceRequest:
        final repo = ServiceRequestRepository();
        final appRequests = await repo.getServiceRequestsByDateRange(startDate, endDate);

        // تطبيق الفلاتر بشكل مباشر
        var filteredRequests = appRequests;

        if (filters != null) {
          // فلترة حسب الحالة
          if (filters.containsKey('status')) {
            final statusStr = filters['status'] as String?;
            if (statusStr != null) {
              filteredRequests = filteredRequests.where((r) {
                return r.status.toString().toLowerCase().contains(statusStr.toLowerCase());
              }).toList();
            }
          }

          // فلترة حسب نوع الخدمة
          if (filters.containsKey('serviceType')) {
            final serviceType = filters['serviceType'] as String?;
            if (serviceType != null) {
              filteredRequests = filteredRequests.where((r) =>
                r.serviceType.toLowerCase().contains(serviceType.toLowerCase())
              ).toList();
            }
          }
        }

        // تحويل البيانات إلى نموذج التقرير (يمكن إنشاء محول مشابه لـ InvoiceAdapter)
        final reportRequests = filteredRequests.map((r) => ServiceRequest(
          id: r.id ?? 0,
          requestNumber: r.requestNumber,
          customerName: r.customerName,
          serviceType: r.serviceType,
          description: r.description,
          scheduledDate: r.scheduledDate,
          status: _mapServiceStatus(r.status.toString()),
          createdAt: r.createdAt,
        )).toList();

        return reportRequests;

      case ReportType.inventoryItem:
        final repo = InventoryRepository();
        return await repo.getAllInventoryItems();

      case ReportType.inventoryTransaction:
        final repo = InventoryRepository();
        return await repo.getInventoryTransactionsByDateRange(startDate, endDate);

      case ReportType.summary:
        // تقرير ملخص يجمع بيانات من عدة مصادر
        return await _fetchSummaryReportData(startDate, endDate);
    }
  }

  /// تحويل حالة طلب الخدمة من نص إلى نوع ServiceStatus
  static ServiceStatus _mapServiceStatus(String statusStr) {
    final lowerStatus = statusStr.toLowerCase();

    if (lowerStatus.contains('pending')) {
      return ServiceStatus.pending;
    } else if (lowerStatus.contains('progress')) {
      return ServiceStatus.inProgress;
    } else if (lowerStatus.contains('completed')) {
      return ServiceStatus.completed;
    } else if (lowerStatus.contains('cancelled')) {
      return ServiceStatus.cancelled;
    } else if (lowerStatus.contains('hold')) {
      return ServiceStatus.onHold;
    } else if (lowerStatus.contains('follow')) {
      return ServiceStatus.followUp;
    }

    // قيمة افتراضية
    return ServiceStatus.pending;
  }

  /// جلب بيانات تقرير الملخص
  static Future<List<Map<String, dynamic>>> _fetchSummaryReportData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // جلب البيانات من مختلف المصادر
    final transactionRepo = TransactionRepository();
    final invoiceRepo = InvoiceRepository();
    final serviceRepo = ServiceRequestRepository();

    final appTransactions = await transactionRepo.getTransactionsByDateRange(startDate, endDate);
    final appInvoices = await invoiceRepo.getInvoicesByDateRange(startDate, endDate);
    final appServiceRequests = await serviceRepo.getServiceRequestsByDateRange(startDate, endDate);

    // تحويل البيانات إلى نماذج التقارير
    final invoices = InvoiceAdapter.toReportModelList(appInvoices);

    // حساب الإحصائيات
    final totalRevenue = appTransactions
        .where((t) => t.type.toString().contains('income'))
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalExpenses = appTransactions
        .where((t) => t.type.toString().contains('expense'))
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalInvoices = invoices.length;
    final paidInvoices = invoices.where((i) => i.isPaid).length;
    final unpaidInvoices = totalInvoices - paidInvoices;

    final totalServiceRequests = appServiceRequests.length;

    // Contar solicitudes de servicio por estado
    final completedRequests = appServiceRequests
        .where((r) => r.status.toString().toLowerCase().contains('completed'))
        .length;

    final pendingRequests = appServiceRequests
        .where((r) =>
          r.status.toString().toLowerCase().contains('pending') ||
          r.status.toString().toLowerCase().contains('progress'))
        .length;

    // إنشاء بيانات الملخص
    return [
      {
        'title': 'ملخص المالية',
        'data': {
          'totalRevenue': totalRevenue,
          'totalExpenses': totalExpenses,
          'profit': totalRevenue - totalExpenses,
        },
      },
      {
        'title': 'ملخص الفواتير',
        'data': {
          'totalInvoices': totalInvoices,
          'paidInvoices': paidInvoices,
          'unpaidInvoices': unpaidInvoices,
        },
      },
      {
        'title': 'ملخص طلبات الخدمة',
        'data': {
          'totalRequests': totalServiceRequests,
          'completedRequests': completedRequests,
          'pendingRequests': pendingRequests,
        },
      },
    ];
  }

  /// حساب البيانات الملخصة للتقرير
  static Map<String, double> _calculateSummaryData({
    required ReportType reportType,
    required List<dynamic> data,
  }) {
    switch (reportType) {
      case ReportType.customer:
        return {
          'total': data.length.toDouble(),
        };

      case ReportType.employee:
        return {
          'total': data.length.toDouble(),
        };

      case ReportType.supplier:
        return {
          'total': data.length.toDouble(),
        };

      case ReportType.invoice:
        // Usar el modelo de reporte para las facturas
        final invoices = data as List<Invoice>;
        final totalAmount = invoices.fold(0.0, (sum, invoice) => sum + invoice.total);
        final paidAmount = invoices
            .where((invoice) => invoice.isPaid)
            .fold(0.0, (sum, invoice) => sum + invoice.total);
        final unpaidAmount = totalAmount - paidAmount;

        return {
          'total': totalAmount,
          'paid': paidAmount,
          'unpaid': unpaidAmount,
          'count': invoices.length.toDouble(),
        };

      case ReportType.transaction:
        final transactions = data as List<Transaction>;
        final totalIncome = transactions
            .where((t) => t.type == TransactionType.income)
            .fold(0.0, (sum, t) => sum + t.amount);
        final totalExpenses = transactions
            .where((t) => t.type == TransactionType.expense)
            .fold(0.0, (sum, t) => sum + t.amount);

        return {
          'income': totalIncome,
          'expenses': totalExpenses,
          'balance': totalIncome - totalExpenses,
          'count': transactions.length.toDouble(),
        };

      case ReportType.serviceRequest:
        final requests = data as List<ServiceRequest>;

        // Contar solicitudes de servicio por estado usando strings
        final completed = requests
            .where((r) => r.status.toString().toLowerCase().contains('completed'))
            .length
            .toDouble();
        final pending = requests
            .where((r) => r.status.toString().toLowerCase().contains('pending'))
            .length
            .toDouble();
        final inProgress = requests
            .where((r) => r.status.toString().toLowerCase().contains('progress'))
            .length
            .toDouble();
        final cancelled = requests
            .where((r) => r.status.toString().toLowerCase().contains('cancelled'))
            .length
            .toDouble();

        return {
          'total': requests.length.toDouble(),
          'completed': completed,
          'pending': pending,
          'inProgress': inProgress,
          'cancelled': cancelled,
        };

      case ReportType.inventoryItem:
        final items = data as List<InventoryItem>;
        final totalValue = items.fold(
          0.0,
          (sum, item) => sum + (item.quantity * item.costPrice),
        );

        return {
          'total': items.length.toDouble(),
          'totalValue': totalValue,
        };

      case ReportType.inventoryTransaction:
        final transactions = data as List<InventoryTransaction>;
        return {
          'total': transactions.length.toDouble(),
        };

      case ReportType.summary:
        // البيانات الملخصة موجودة بالفعل في البيانات
        final summaryData = data as List<Map<String, dynamic>>;
        final result = <String, double>{};

        for (final section in summaryData) {
          final sectionData = section['data'] as Map<String, dynamic>;
          sectionData.forEach((key, value) {
            if (value is num) {
              result[key] = value.toDouble();
            }
          });
        }

        return result;


    }
  }

  /// تحويل نوع التقرير إلى سلسلة نصية
  static String _getReportTypeString(ReportType reportType) {
    switch (reportType) {
      case ReportType.customer:
        return 'customers';
      case ReportType.employee:
        return 'employees';
      case ReportType.supplier:
        return 'suppliers';
      case ReportType.invoice:
        return 'invoices';
      case ReportType.transaction:
        return 'transactions';
      case ReportType.serviceRequest:
        return 'services';
      case ReportType.inventoryItem:
        return 'inventory';
      case ReportType.inventoryTransaction:
        return 'inventory_transactions';
      case ReportType.summary:
        return 'summary';
    }
  }
}
