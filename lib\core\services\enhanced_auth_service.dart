import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../database/database_helper.dart';
import '../auth/permissions.dart';
import '../../shared/models/user.dart' as app_user;

/// Enhanced authentication service with comprehensive user management
class EnhancedAuthService {
  static final EnhancedAuthService _instance = EnhancedAuthService._internal();
  factory EnhancedAuthService() => _instance;
  EnhancedAuthService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// Get Firebase Auth instance (lazy initialization to avoid Firebase errors)
  FirebaseAuth get _auth {
    try {
      return FirebaseAuth.instance;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Firebase not initialized, operating in offline mode: $e');
      }
      rethrow;
    }
  }

  /// Get Firestore instance (lazy initialization to avoid Firebase errors)
  FirebaseFirestore get _firestore {
    try {
      return FirebaseFirestore.instance;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Firebase not initialized, operating in offline mode: $e');
      }
      rethrow;
    }
  }

  final StreamController<app_user.User?> _userController = StreamController<app_user.User?>.broadcast();
  app_user.User? _currentUser;

  /// Stream of authentication state changes
  Stream<app_user.User?> get userStream => _userController.stream;

  /// Current authenticated user
  app_user.User? get currentUser => _currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Initialize authentication service
  Future<void> initialize() async {
    try {
      // Listen to Firebase auth state changes
      _auth.authStateChanges().listen(_onAuthStateChanged);
      
      // Check if user is already signed in
      final firebaseUser = _auth.currentUser;
      if (firebaseUser != null) {
        await _loadUserData(firebaseUser);
      }
      
      if (kDebugMode) {
        print('✅ Enhanced Auth Service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing auth service: $e');
      }
    }
  }

  /// Handle Firebase auth state changes
  Future<void> _onAuthStateChanged(User? firebaseUser) async {
    try {
      if (firebaseUser != null) {
        await _loadUserData(firebaseUser);
      } else {
        _currentUser = null;
        _userController.add(null);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling auth state change: $e');
      }
    }
  }

  /// Load user data from Firestore and local database
  Future<void> _loadUserData(User firebaseUser) async {
    try {
      // Get user data from Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(firebaseUser.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        _currentUser = app_user.User.fromFirestore(userData, firebaseUser.uid);
        
        // Sync with local database
        await _syncUserToLocal(_currentUser!);
        
        _userController.add(_currentUser);
        
        if (kDebugMode) {
          print('✅ User data loaded: ${_currentUser!.email}');
        }
      } else {
        // Create user profile if it doesn't exist
        await _createUserProfile(firebaseUser);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading user data: $e');
      }
    }
  }

  /// Create user profile in Firestore
  Future<void> _createUserProfile(User firebaseUser) async {
    try {
      final userData = {
        'email': firebaseUser.email,
        'name': firebaseUser.displayName ?? '',
        'phone': firebaseUser.phoneNumber ?? '',
        'role': UserRole.technician.toString(),
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('users')
          .doc(firebaseUser.uid)
          .set(userData);

      _currentUser = app_user.User.fromFirestore(userData, firebaseUser.uid);
      await _syncUserToLocal(_currentUser!);
      
      _userController.add(_currentUser);
      
      if (kDebugMode) {
        print('✅ User profile created: ${_currentUser!.email}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating user profile: $e');
      }
      rethrow;
    }
  }

  /// Sync user data to local database
  Future<void> _syncUserToLocal(app_user.User user) async {
    try {
      await _dbHelper.insertOrUpdateUser(user);
      
      if (kDebugMode) {
        print('✅ User synced to local database');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing user to local: $e');
      }
    }
  }

  /// Sign in with email and password
  Future<AuthResult> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update last login time
        await _updateLastLoginTime(credential.user!.uid);

        // Log successful login
        await _logSecurityEvent('user_login_success', {
          'user_id': credential.user!.uid,
          'email': email,
          'method': 'email_password',
        });

        return AuthResult.success(_currentUser);
      } else {
        return AuthResult.failure('Authentication failed');
      }
    } on FirebaseAuthException catch (e) {
      // Log failed login attempt
      await _logSecurityEvent('user_login_failed', {
        'email': email,
        'error_code': e.code,
        'error_message': e.message,
        'method': 'email_password',
      });
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      // Log unexpected error
      await _logSecurityEvent('user_login_error', {
        'email': email,
        'error': e.toString(),
        'method': 'email_password',
      });
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  /// Create user with email and password
  Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String? phone,
    UserRole role = UserRole.technician,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(name);
        
        // Create user profile with additional data
        final userData = {
          'email': email,
          'name': name,
          'phone': phone ?? '',
          'role': role.toString(),
          'isActive': true,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
          'lastLoginAt': FieldValue.serverTimestamp(),
        };

        await _firestore
            .collection('users')
            .doc(credential.user!.uid)
            .set(userData);

        return AuthResult.success(_currentUser);
      } else {
        return AuthResult.failure('User creation failed');
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      // Log sign out event before clearing user data
      if (_currentUser != null) {
        await _logSecurityEvent('user_logout', {
          'user_id': _currentUser!.id,
          'email': _currentUser!.email,
        });
      }

      await _auth.signOut();
      _currentUser = null;
      _userController.add(null);

      if (kDebugMode) {
        print('✅ User signed out');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error signing out: $e');
      }
      rethrow;
    }
  }

  /// Reset password
  Future<AuthResult> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return AuthResult.success(null, message: 'Password reset email sent');
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  /// Update user profile
  Future<AuthResult> updateUserProfile({
    String? name,
    String? phone,
    UserRole? role,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('No user signed in');
      }

      final updates = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (name != null) {
        updates['name'] = name;
        await _auth.currentUser!.updateDisplayName(name);
      }
      
      if (phone != null) {
        updates['phone'] = phone;
      }
      
      if (role != null) {
        updates['role'] = role.toString();
      }

      await _firestore
          .collection('users')
          .doc(_currentUser!.id.toString())
          .update(updates);

      // Reload user data
      await _loadUserData(_auth.currentUser!);

      return AuthResult.success(_currentUser, message: 'Profile updated successfully');
    } catch (e) {
      return AuthResult.failure('Failed to update profile');
    }
  }

  /// Update last login time
  Future<void> _updateLastLoginTime(String userId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .update({
        'lastLoginAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating last login time: $e');
      }
    }
  }

  /// Check if user has permission for a specific action
  bool hasPermission(Permission permission) {
    if (_currentUser == null) return false;

    // Check if user has admin role (admin has all permissions)
    if (_currentUser!.roles.contains('admin')) return true;

    // Get user roles and check permissions
    final userRoles = _currentUser!.roles
        .map((roleString) => RolePermissions.parseRole(roleString))
        .where((role) => role != null)
        .cast<UserRole>()
        .toList();

    // Check if any of the user's roles has the required permission
    for (final role in userRoles) {
      if (RolePermissions.roleHasPermission(role, permission)) {
        return true;
      }
    }

    return false;
  }

  /// Check if user has any of the specified roles
  bool hasAnyRole(List<String> roles) {
    if (_currentUser == null) return false;

    return _currentUser!.roles.any((userRole) => roles.contains(userRole));
  }

  /// Get user permissions based on roles
  List<Permission> getUserPermissions() {
    if (_currentUser == null) return [];

    // Admin has all permissions
    if (_currentUser!.roles.contains('admin')) {
      return Permission.values;
    }

    // Get permissions for user roles
    final userRoles = _currentUser!.roles
        .map((roleString) => RolePermissions.parseRole(roleString))
        .where((role) => role != null)
        .cast<UserRole>()
        .toList();

    return RolePermissions.getPermissionsForRoles(userRoles);
  }

  /// Log security event
  Future<void> _logSecurityEvent(String event, Map<String, dynamic> details) async {
    try {
      if (kDebugMode) {
        print('🔒 Security Event: $event - $details');
      }

      // Store security log in database
      final db = await _dbHelper.database;
      await db.insert('security_logs', {
        'event': event,
        'user_id': _currentUser?.id,
        'user_email': _currentUser?.email,
        'details': jsonEncode(details),
        'timestamp': DateTime.now().toIso8601String(),
        'ip_address': 'unknown', // Could be enhanced with actual IP detection
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error logging security event: $e');
      }
    }
  }

  /// Validate session and refresh if needed
  Future<bool> validateSession() async {
    try {
      if (_currentUser == null) return false;

      // Check if Firebase user is still valid
      final firebaseUser = _auth.currentUser;
      if (firebaseUser == null) {
        await signOut();
        return false;
      }

      // Refresh token if needed
      await firebaseUser.getIdToken(true);

      // Update last activity
      await _updateLastActivity();

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Session validation failed: $e');
      }
      await signOut();
      return false;
    }
  }

  /// Update last activity timestamp
  Future<void> _updateLastActivity() async {
    try {
      if (_currentUser == null) return;

      final db = await _dbHelper.database;
      await db.update(
        'users',
        {'last_activity': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [_currentUser!.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating last activity: $e');
      }
    }
  }

  /// Get authentication error message
  String _getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address';
      case 'wrong-password':
        return 'Incorrect password';
      case 'email-already-in-use':
        return 'An account already exists with this email address';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      default:
        return e.message ?? 'An authentication error occurred';
    }
  }

  /// Dispose resources
  void dispose() {
    _userController.close();
  }
}

/// Authentication result wrapper
class AuthResult {
  final bool success;
  final app_user.User? user;
  final String? message;
  final String? error;

  AuthResult._({
    required this.success,
    this.user,
    this.message,
    this.error,
  });

  factory AuthResult.success(app_user.User? user, {String? message}) {
    return AuthResult._(
      success: true,
      user: user,
      message: message,
    );
  }

  factory AuthResult.failure(String error) {
    return AuthResult._(
      success: false,
      error: error,
    );
  }
}


