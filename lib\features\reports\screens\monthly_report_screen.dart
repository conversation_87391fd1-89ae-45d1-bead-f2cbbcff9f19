import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/financial_summary_card.dart';
import '../widgets/report_chart.dart';

class MonthlyReportScreen extends StatefulWidget {
  const MonthlyReportScreen({super.key});

  @override
  State<MonthlyReportScreen> createState() => _MonthlyReportScreenState();
}

class _MonthlyReportScreenState extends State<MonthlyReportScreen> {
  bool _isLoading = true;
  DateTime _selectedMonth = DateTime.now();
  final Map<String, dynamic> _reportData = {};

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Mock data
    final mockData = {
      'totalRevenue': 45000.0,
      'totalExpenses': 28000.0,
      'profit': 17000.0,
      'profitMargin': 0.38,
      'invoicesCount': 28,
      'paidInvoicesCount': 22,
      'overdueInvoicesCount': 6,
      'serviceRequestsCount': 35,
      'completedServiceRequestsCount': 32,
      'pendingServiceRequestsCount': 3,
      'topCustomers': [
        {'name': 'شركة الرياض للتكييف', 'amount': 12000.0},
        {'name': 'مؤسسة النور', 'amount': 8500.0},
        {'name': 'أحمد محمد', 'amount': 5000.0},
      ],
      'dailyRevenue': [
        {'day': 1, 'amount': 1500.0},
        {'day': 2, 'amount': 2000.0},
        {'day': 3, 'amount': 1800.0},
        {'day': 4, 'amount': 1200.0},
        {'day': 5, 'amount': 2500.0},
        {'day': 6, 'amount': 1900.0},
        {'day': 7, 'amount': 1000.0},
        {'day': 8, 'amount': 1600.0},
        {'day': 9, 'amount': 2200.0},
        {'day': 10, 'amount': 1700.0},
        {'day': 11, 'amount': 1300.0},
        {'day': 12, 'amount': 1800.0},
        {'day': 13, 'amount': 2100.0},
        {'day': 14, 'amount': 1500.0},
        {'day': 15, 'amount': 1900.0},
        {'day': 16, 'amount': 2000.0},
        {'day': 17, 'amount': 1700.0},
        {'day': 18, 'amount': 1400.0},
        {'day': 19, 'amount': 1600.0},
        {'day': 20, 'amount': 2300.0},
        {'day': 21, 'amount': 1800.0},
        {'day': 22, 'amount': 1500.0},
        {'day': 23, 'amount': 1700.0},
        {'day': 24, 'amount': 1900.0},
        {'day': 25, 'amount': 2100.0},
        {'day': 26, 'amount': 1600.0},
        {'day': 27, 'amount': 1400.0},
        {'day': 28, 'amount': 1800.0},
        {'day': 29, 'amount': 2000.0},
        {'day': 30, 'amount': 1500.0},
      ],
      'dailyExpenses': [
        {'day': 1, 'amount': 800.0},
        {'day': 2, 'amount': 1200.0},
        {'day': 3, 'amount': 900.0},
        {'day': 4, 'amount': 700.0},
        {'day': 5, 'amount': 1500.0},
        {'day': 6, 'amount': 1100.0},
        {'day': 7, 'amount': 600.0},
        {'day': 8, 'amount': 900.0},
        {'day': 9, 'amount': 1300.0},
        {'day': 10, 'amount': 1000.0},
        {'day': 11, 'amount': 800.0},
        {'day': 12, 'amount': 1100.0},
        {'day': 13, 'amount': 1200.0},
        {'day': 14, 'amount': 900.0},
        {'day': 15, 'amount': 1000.0},
        {'day': 16, 'amount': 1200.0},
        {'day': 17, 'amount': 1000.0},
        {'day': 18, 'amount': 800.0},
        {'day': 19, 'amount': 900.0},
        {'day': 20, 'amount': 1400.0},
        {'day': 21, 'amount': 1100.0},
        {'day': 22, 'amount': 900.0},
        {'day': 23, 'amount': 1000.0},
        {'day': 24, 'amount': 1100.0},
        {'day': 25, 'amount': 1300.0},
        {'day': 26, 'amount': 900.0},
        {'day': 27, 'amount': 800.0},
        {'day': 28, 'amount': 1000.0},
        {'day': 29, 'amount': 1200.0},
        {'day': 30, 'amount': 900.0},
      ],
      'expenseCategories': [
        {'category': 'رواتب', 'amount': 15000.0},
        {'category': 'إيجار', 'amount': 5000.0},
        {'category': 'مرافق', 'amount': 2000.0},
        {'category': 'قطع غيار', 'amount': 4000.0},
        {'category': 'وقود', 'amount': 1500.0},
        {'category': 'أخرى', 'amount': 500.0},
      ],
    };

    if (mounted) {
      setState(() {
        _reportData.addAll(mockData);
        _isLoading = false;
      });
    }
  }

  Future<void> _selectMonth(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedMonth,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDatePickerMode: DatePickerMode.year,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedMonth) {
      setState(() {
        _selectedMonth = DateTime(picked.year, picked.month, 1);
      });
      _loadReportData();
    }
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير...'),
        duration: Duration(seconds: 2),
      ),
    );

    // Simulate export
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير التقرير بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير...'),
        duration: Duration(seconds: 2),
      ),
    );

    // Simulate printing
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال التقرير للطباعة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final formattedMonth = DateFormat('MMMM yyyy', 'ar').format(_selectedMonth);

    return Scaffold(
      appBar: AppBar(
        title: const Text('التقرير الشهري'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_month),
            tooltip: 'اختيار الشهر',
            onPressed: () => _selectMonth(context),
          ),
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة',
            onPressed: _printReport,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير',
            onPressed: _exportReport,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadReportData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Report header
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'التقرير المالي الشهري',
                                  style: AppTextStyles.heading2,
                                ),
                                Text(
                                  formattedMonth,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppDimensions.paddingS),
                            const Text(
                              'ملخص الأداء المالي للشهر الحالي',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Financial summary
                    Row(
                      children: [
                        Expanded(
                          child: FinancialSummaryCard(
                            title: 'الإيرادات',
                            amount: _reportData['totalRevenue'],
                            icon: Icons.arrow_upward,
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        Expanded(
                          child: FinancialSummaryCard(
                            title: 'المصروفات',
                            amount: _reportData['totalExpenses'],
                            icon: Icons.arrow_downward,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    Row(
                      children: [
                        Expanded(
                          child: FinancialSummaryCard(
                            title: 'صافي الربح',
                            amount: _reportData['profit'],
                            icon: Icons.account_balance_wallet,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        Expanded(
                          child: FinancialSummaryCard(
                            title: 'هامش الربح',
                            amount: _reportData['profitMargin'] * 100,
                            suffix: '%',
                            icon: Icons.trending_up,
                            color: Colors.purple,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Invoices summary
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'ملخص الفواتير',
                          style: AppTextStyles.heading3,
                        ),
                        TextButton.icon(
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              AppRoutes.customerReport,
                            );
                          },
                          icon: const Icon(Icons.arrow_forward, size: 16),
                          label: const Text('عرض التفاصيل'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('إجمالي الفواتير:'),
                                Text(
                                  '${_reportData['invoicesCount']}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppDimensions.paddingS),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('الفواتير المدفوعة:'),
                                Text(
                                  '${_reportData['paidInvoicesCount']}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppDimensions.paddingS),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('الفواتير المتأخرة:'),
                                Text(
                                  '${_reportData['overdueInvoicesCount']}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Service requests summary
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'ملخص طلبات الخدمة',
                          style: AppTextStyles.heading3,
                        ),
                        TextButton.icon(
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              AppRoutes.serviceRequestReport,
                            );
                          },
                          icon: const Icon(Icons.arrow_forward, size: 16),
                          label: const Text('عرض التفاصيل'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('إجمالي الطلبات:'),
                                Text(
                                  '${_reportData['serviceRequestsCount']}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppDimensions.paddingS),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('الطلبات المكتملة:'),
                                Text(
                                  '${_reportData['completedServiceRequestsCount']}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppDimensions.paddingS),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('الطلبات المعلقة:'),
                                Text(
                                  '${_reportData['pendingServiceRequestsCount']}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Revenue and expenses chart
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'الإيرادات والمصروفات اليومية',
                          style: AppTextStyles.heading3,
                        ),
                        TextButton.icon(
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              AppRoutes.transactionReport,
                            );
                          },
                          icon: const Icon(Icons.arrow_forward, size: 16),
                          label: const Text('عرض التفاصيل'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    SizedBox(
                      height: 300,
                      child: ReportChart(
                        revenueData: List<Map<String, dynamic>>.from(_reportData['dailyRevenue']),
                        expensesData: List<Map<String, dynamic>>.from(_reportData['dailyExpenses']),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Expense categories
                    const Text(
                      'توزيع المصروفات',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          children: [
                            for (final category in _reportData['expenseCategories'])
                              Padding(
                                padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 3,
                                      child: Text(category['category']),
                                    ),
                                    Expanded(
                                      flex: 6,
                                      child: LinearProgressIndicator(
                                        value: category['amount'] / _reportData['totalExpenses'],
                                        backgroundColor: Colors.grey[200],
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          _getCategoryColor(category['category']),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: AppDimensions.paddingS),
                                    Expanded(
                                      flex: 3,
                                      child: Text(
                                        '${category['amount']} ر.س',
                                        style: const TextStyle(fontWeight: FontWeight.bold),
                                        textAlign: TextAlign.left,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Top customers
                    const Text(
                      'أفضل العملاء',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          children: [
                            for (int i = 0; i < _reportData['topCustomers'].length; i++)
                              Padding(
                                padding: EdgeInsets.only(
                                  bottom: i < _reportData['topCustomers'].length - 1
                                      ? AppDimensions.paddingS
                                      : 0,
                                ),
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 15,
                                      backgroundColor: AppColors.primary,
                                      child: Text(
                                        '${i + 1}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: AppDimensions.paddingS),
                                    Expanded(
                                      child: Text(_reportData['topCustomers'][i]['name']),
                                    ),
                                    Text(
                                      '${_reportData['topCustomers'][i]['amount']} ر.س',
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Detailed reports section
                    const Text(
                      'التقارير التفصيلية',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          children: [
                            _buildDetailedReportButton(
                              title: 'تقارير الصيانة',
                              icon: Icons.build,
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.maintenanceReports,
                                );
                              },
                            ),
                            const Divider(),
                            _buildDetailedReportButton(
                              title: 'تقرير الموظفين',
                              icon: Icons.badge,
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.detailedReport,
                                  arguments: 'employee',
                                );
                              },
                            ),
                            const Divider(),
                            _buildDetailedReportButton(
                              title: 'تقرير العملاء',
                              icon: Icons.people,
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.detailedReport,
                                  arguments: 'customer',
                                );
                              },
                            ),
                            const Divider(),
                            _buildDetailedReportButton(
                              title: 'تقرير الموردين',
                              icon: Icons.local_shipping,
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  AppRoutes.detailedReport,
                                  arguments: 'supplier',
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingL),
                  ],
                ),
              ),
            ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'رواتب':
        return Colors.blue;
      case 'إيجار':
        return Colors.purple;
      case 'مرافق':
        return Colors.orange;
      case 'قطع غيار':
        return Colors.green;
      case 'وقود':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildDetailedReportButton({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
