import 'dart:convert';
import 'package:flutter/material.dart';

enum EmployeeStatus {
  active,
  inactive,
  onLeave,
  terminated,
}

enum PaymentType {
  monthly,
  daily,
}

class Employee {
  final int? id;
  final String name;
  final String? email;
  final String? phone;
  final String? address;
  final String position;
  final DateTime joinDate;
  final double salary;
  final PaymentType paymentType;
  final EmployeeStatus status;
  final String? nationalId;
  final String? bankAccount;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Employee({
    this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    required this.position,
    required this.joinDate,
    required this.salary,
    this.paymentType = PaymentType.monthly,
    required this.status,
    this.nationalId,
    this.bankAccount,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'] as int?,
      name: map['name'] as String,
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      address: map['address'] as String?,
      position: map['position'] as String,
      joinDate: DateTime.parse(map['join_date'] as String),
      salary: map['salary'] as double,
      paymentType: _parsePaymentType(map['payment_type'] as String?),
      status: _parseStatus(map['status'] as String),
      nationalId: map['national_id'] as String?,
      bankAccount: map['bank_account'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory Employee.fromJson(String source) =>
      Employee.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'position': position,
      'join_date': joinDate.toIso8601String(),
      'salary': salary,
      'payment_type': paymentType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'national_id': nationalId,
      'bank_account': bankAccount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  Employee copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? position,
    DateTime? joinDate,
    double? salary,
    PaymentType? paymentType,
    EmployeeStatus? status,
    String? nationalId,
    String? bankAccount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      position: position ?? this.position,
      joinDate: joinDate ?? this.joinDate,
      salary: salary ?? this.salary,
      paymentType: paymentType ?? this.paymentType,
      status: status ?? this.status,
      nationalId: nationalId ?? this.nationalId,
      bankAccount: bankAccount ?? this.bankAccount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static EmployeeStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return EmployeeStatus.active;
      case 'inactive':
        return EmployeeStatus.inactive;
      case 'on_leave':
      case 'onleave':
        return EmployeeStatus.onLeave;
      case 'terminated':
        return EmployeeStatus.terminated;
      default:
        return EmployeeStatus.active;
    }
  }

  static PaymentType _parsePaymentType(String? paymentType) {
    if (paymentType == null) return PaymentType.monthly;

    switch (paymentType.toLowerCase()) {
      case 'monthly':
        return PaymentType.monthly;
      case 'daily':
        return PaymentType.daily;
      default:
        return PaymentType.monthly;
    }
  }

  static String getStatusName(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return 'نشط';
      case EmployeeStatus.inactive:
        return 'غير نشط';
      case EmployeeStatus.onLeave:
        return 'في إجازة';
      case EmployeeStatus.terminated:
        return 'منتهي الخدمة';
    }
  }

  static Color getStatusColor(EmployeeStatus status) {
    switch (status) {
      case EmployeeStatus.active:
        return Colors.green;
      case EmployeeStatus.inactive:
        return Colors.grey;
      case EmployeeStatus.onLeave:
        return Colors.amber;
      case EmployeeStatus.terminated:
        return Colors.red;
    }
  }

  static String getPaymentTypeName(PaymentType paymentType) {
    switch (paymentType) {
      case PaymentType.monthly:
        return 'راتب شهري';
      case PaymentType.daily:
        return 'أجر يومي';
    }
  }

  static Color getPaymentTypeColor(PaymentType paymentType) {
    switch (paymentType) {
      case PaymentType.monthly:
        return Colors.blue;
      case PaymentType.daily:
        return Colors.orange;
    }
  }

  /// Check if employee has monthly salary
  bool get hasFixedSalary => paymentType == PaymentType.monthly;

  /// Check if employee has daily wage
  bool get hasDailyWage => paymentType == PaymentType.daily;

  // Generate mock employees for testing
  static List<Employee> getMockEmployees() {
    return [
      Employee(
        id: 1,
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '0512345678',
        position: 'مدير',
        joinDate: DateTime(2020, 1, 15),
        salary: 15000,
        status: EmployeeStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 500)),
      ),
      Employee(
        id: 2,
        name: 'محمد علي',
        email: '<EMAIL>',
        phone: '0523456789',
        position: 'فني تكييف',
        joinDate: DateTime(2021, 3, 10),
        salary: 8000,
        status: EmployeeStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 300)),
      ),
      Employee(
        id: 3,
        name: 'فاطمة أحمد',
        email: '<EMAIL>',
        phone: '0534567890',
        position: 'محاسب',
        joinDate: DateTime(2022, 5, 20),
        salary: 10000,
        status: EmployeeStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
      ),
      Employee(
        id: 4,
        name: 'خالد عبدالله',
        email: '<EMAIL>',
        phone: '0545678901',
        position: 'فني تكييف',
        joinDate: DateTime(2021, 8, 5),
        salary: 7500,
        status: EmployeeStatus.onLeave,
        createdAt: DateTime.now().subtract(const Duration(days: 250)),
      ),
      Employee(
        id: 5,
        name: 'سارة محمد',
        email: '<EMAIL>',
        phone: '0556789012',
        position: 'موظف استقبال',
        joinDate: DateTime(2022, 2, 15),
        salary: 6000,
        status: EmployeeStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 220)),
      ),
    ];
  }
}
