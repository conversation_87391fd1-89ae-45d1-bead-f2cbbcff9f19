import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/models/notification_model.dart';
import '../widgets/notification_item.dart';
import '../../../core/repositories/notification_repository.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool _isLoading = true;
  List<NotificationModel> _notifications = [];
  final NotificationRepository _notificationRepository = NotificationRepository();

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام الـ repository للحصول على الإشعارات من قاعدة البيانات
      final notifications = await _notificationRepository.getAllNotifications();

      if (mounted) {
        setState(() {
          _notifications = notifications;
          _isLoading = false;
        });
      }

      // إذا لم تكن هناك إشعارات، قم بإنشاء بعض الإشعارات الافتراضية للعرض
      if (notifications.isEmpty && mounted) {
        await _createSampleNotifications();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading notifications: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _createSampleNotifications() async {
    // إنشاء إشعارات افتراضية للعرض
    final sampleNotifications = [
      NotificationModel(
        title: 'طلب خدمة جديد',
        message: 'تم إنشاء طلب خدمة جديد #12345',
        type: NotificationType.serviceRequest,
        isRead: false,
        relatedId: 12345,
        relatedType: 'service_request',
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
      NotificationModel(
        title: 'فاتورة جديدة',
        message: 'تم إنشاء فاتورة جديدة #INV-2023-05-15',
        type: NotificationType.invoice,
        isRead: true,
        relatedId: 101,
        relatedType: 'invoice',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      NotificationModel(
        title: 'تذكير بموعد',
        message: 'لديك موعد صيانة غداً الساعة 10:00 صباحاً',
        type: NotificationType.info,
        isRead: false,
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      NotificationModel(
        title: 'دفعة جديدة',
        message: 'تم استلام دفعة جديدة بمبلغ 500 ر.س',
        type: NotificationType.transaction,
        isRead: true,
        relatedId: 456,
        relatedType: 'transaction',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      NotificationModel(
        title: 'تحديث حالة الطلب',
        message: 'تم تحديث حالة طلب الخدمة #12340 إلى "مكتمل"',
        type: NotificationType.serviceRequest,
        isRead: true,
        relatedId: 12340,
        relatedType: 'service_request',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    // حفظ الإشعارات في قاعدة البيانات
    for (final notification in sampleNotifications) {
      await _notificationRepository.insertNotification(notification);
    }

    // إعادة تحميل الإشعارات
    final notifications = await _notificationRepository.getAllNotifications();

    if (mounted) {
      setState(() {
        _notifications = notifications;
      });
    }
  }

  void _markAllAsRead() {
    _markAllNotificationsAsRead();
  }

  Future<void> _markAllNotificationsAsRead() async {
    try {
      // تحديث جميع الإشعارات في قاعدة البيانات
      await _notificationRepository.markAllNotificationsAsRead();

      // إعادة تحميل الإشعارات
      await _loadNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعليم جميع الإشعارات كمقروءة'),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error marking all notifications as read: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تعليم الإشعارات كمقروءة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteAllNotifications() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('حذف جميع الإشعارات'),
          content: const Text('هل أنت متأكد من رغبتك في حذف جميع الإشعارات؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deleteAllNotificationsFromDb();
              },
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteAllNotificationsFromDb() async {
    try {
      // حذف جميع الإشعارات من قاعدة البيانات
      await _notificationRepository.deleteAllNotifications();

      // إعادة تحميل الإشعارات (ستكون فارغة)
      await _loadNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف جميع الإشعارات'),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting all notifications: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حذف جميع الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _markAsRead(NotificationModel notification) {
    if (notification.id != null) {
      _markNotificationAsRead(notification.id!);
    }
  }

  Future<void> _markNotificationAsRead(int id) async {
    try {
      // تحديث الإشعار في قاعدة البيانات
      await _notificationRepository.markNotificationAsRead(id);

      // إعادة تحميل الإشعارات
      await _loadNotifications();
    } catch (e) {
      if (kDebugMode) {
        print('Error marking notification as read: $e');
      }
    }
  }

  void _deleteNotification(NotificationModel notification) {
    if (notification.id != null) {
      _deleteNotificationById(notification.id!);
    }
  }

  Future<void> _deleteNotificationById(int id) async {
    try {
      // حذف الإشعار من قاعدة البيانات
      await _notificationRepository.deleteNotification(id);

      // إعادة تحميل الإشعارات
      await _loadNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الإشعار'),
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting notification: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حذف الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    // Mark as read
    if (!notification.isRead) {
      _markAsRead(notification);
    }

    // Navigate based on notification type and relatedId
    if (notification.relatedId != null && notification.relatedType != null) {
      _navigateBasedOnNotificationType(notification);
    }
  }

  void _navigateBasedOnNotificationType(NotificationModel notification) {
    switch (notification.type) {
      case NotificationType.serviceRequest:
        // Navigate to service requests list or detail
        Navigator.pushNamed(context, AppRoutes.serviceRequests);
        break;
      case NotificationType.invoice:
        // Navigate to invoices list or detail
        Navigator.pushNamed(context, AppRoutes.invoices);
        break;
      case NotificationType.transaction:
        // Navigate to transactions list or detail
        Navigator.pushNamed(context, AppRoutes.transactions);
        break;
      case NotificationType.customer:
        // Navigate to customers list or detail
        Navigator.pushNamed(context, AppRoutes.customers);
        break;
      case NotificationType.supplier:
        // Navigate to suppliers list or detail
        Navigator.pushNamed(context, AppRoutes.suppliers);
        break;
      case NotificationType.employee:
        // Navigate to employees list or detail
        Navigator.pushNamed(context, AppRoutes.employees);
        break;
      default:
        // For other types, just show the notification
        break;
    }
  }

  List<Widget> _buildNotificationGroups() {
    final groups = <String, List<NotificationModel>>{};

    // Group notifications by date
    for (final notification in _notifications) {
      final date = DateFormat('yyyy-MM-dd').format(notification.createdAt);
      if (!groups.containsKey(date)) {
        groups[date] = [];
      }
      groups[date]!.add(notification);
    }

    // Sort dates in descending order
    final sortedDates = groups.keys.toList()..sort((a, b) => b.compareTo(a));

    // Build groups
    final widgets = <Widget>[];
    for (final date in sortedDates) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
          child: Text(
            _formatGroupDate(date),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      );

      for (final notification in groups[date]!) {
        widgets.add(
          NotificationItem(
            notification: notification,
            onTap: () => _handleNotificationTap(notification),
            onDelete: () => _deleteNotification(notification),
          ),
        );
      }

      if (date != sortedDates.last) {
        widgets.add(const Divider());
      }
    }

    return widgets;
  }

  String _formatGroupDate(String date) {
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);
    final yesterday = DateFormat('yyyy-MM-dd').format(now.subtract(const Duration(days: 1)));

    if (date == today) {
      return 'اليوم';
    } else if (date == yesterday) {
      return 'الأمس';
    } else {
      final dateTime = DateFormat('yyyy-MM-dd').parse(date);
      return DateFormat('dd MMMM yyyy', 'ar').format(dateTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = _notifications.where((n) => !n.isRead).length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          if (_notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.done_all),
              tooltip: 'تعليم الكل كمقروء',
              onPressed: _markAllAsRead,
            ),
          if (_notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف الكل',
              onPressed: _deleteAllNotifications,
            ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _notifications.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.notifications_off,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      const Text(
                        'لا توجد إشعارات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      const Text(
                        'ستظهر الإشعارات الجديدة هنا',
                        style: TextStyle(
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: AppDimensions.paddingL),
                      ElevatedButton(
                        onPressed: _loadNotifications,
                        child: const Text('تحديث'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadNotifications,
                  child: Column(
                    children: [
                      if (unreadCount > 0)
                        Container(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          color: AppColors.primary.withAlpha(26),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.info,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: AppDimensions.paddingS),
                              Text(
                                'لديك $unreadCount إشعارات غير مقروءة',
                                style: const TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      Expanded(
                        child: ListView(
                          padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                          children: _buildNotificationGroups(),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }
}
