import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../shared/models/user.dart';
import '../core/services/auth_service.dart';
import '../config/constants.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  authenticating,
  error,
}

class AuthState extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _token;
  String? _errorMessage;

  // Getters
  AuthStatus get status => _status;
  User? get user => _user;
  String? get token => _token;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;

  AuthState() {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final token = await _secureStorage.read(key: AppConstants.tokenKey);
      if (token != null) {
        _token = token;
        await _getUserData();
        _status = AuthStatus.authenticated;
        notifyListeners();
        return;
      }

      // Check if we have saved credentials with "remember me" enabled
      final rememberMe = await _secureStorage.read(key: AppConstants.rememberMeKey);
      if (rememberMe == 'true') {
        final username = await _secureStorage.read(key: AppConstants.usernameKey);
        final password = await _secureStorage.read(key: AppConstants.passwordKey);

        if (username != null && password != null) {
          // Auto login with saved credentials
          await login(username, password, true);
          return;
        }
      }

      _status = AuthStatus.unauthenticated;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking auth status: $e');
      }
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  Future<void> _getUserData() async {
    try {
      final userData = await _secureStorage.read(key: AppConstants.userKey);
      if (userData != null) {
        _user = User.fromJson(userData);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user data: $e');
      }
    }
  }

  Future<bool> login(String username, String password, [bool rememberMe = false]) async {
    _status = AuthStatus.authenticating;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _authService.login(username, password);

      if (result.success && result.token != null) {
        _token = result.token;
        _user = result.user;

        // Save to secure storage
        await _secureStorage.write(key: AppConstants.tokenKey, value: _token);
        if (_user != null) {
          await _secureStorage.write(
            key: AppConstants.userKey,
            value: _user!.toJson(),
          );
        }

        // Save credentials if remember me is enabled
        if (rememberMe) {
          if (kDebugMode) {
            print('Saving credentials for auto-login');
          }
          await _secureStorage.write(key: AppConstants.rememberMeKey, value: 'true');
          await _secureStorage.write(key: AppConstants.usernameKey, value: username);
          await _secureStorage.write(key: AppConstants.passwordKey, value: password);
        } else {
          // Clear saved credentials if remember me is disabled
          if (kDebugMode) {
            print('Clearing saved credentials');
          }
          await _secureStorage.delete(key: AppConstants.rememberMeKey);
          await _secureStorage.delete(key: AppConstants.usernameKey);
          await _secureStorage.delete(key: AppConstants.passwordKey);
        }

        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      } else {
        _errorMessage = result.message ?? 'Login failed';
        _status = AuthStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  Future<bool> register(String name, String email, String password) async {
    _status = AuthStatus.authenticating;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _authService.register(name, email, password);

      if (result.success && result.token != null) {
        _token = result.token;
        _user = result.user;

        // Save to secure storage
        await _secureStorage.write(key: AppConstants.tokenKey, value: _token);
        if (_user != null) {
          await _secureStorage.write(
            key: AppConstants.userKey,
            value: _user!.toJson(),
          );
        }

        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      } else {
        _errorMessage = result.message ?? 'Registration failed';
        _status = AuthStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    try {
      final result = await _authService.forgotPassword(email);
      return result.success;
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    }
  }

  Future<void> logout() async {
    try {
      await _authService.logout(_token);
    } catch (e) {
      if (kDebugMode) {
        print('Error during logout: $e');
      }
    } finally {
      // Check if remember me is enabled before clearing credentials
      final rememberMe = await _secureStorage.read(key: AppConstants.rememberMeKey);

      // Clear token and user data
      await _secureStorage.delete(key: AppConstants.tokenKey);
      await _secureStorage.delete(key: AppConstants.userKey);

      // If remember me is not enabled, clear saved credentials
      if (rememberMe != 'true') {
        await _secureStorage.delete(key: AppConstants.rememberMeKey);
        await _secureStorage.delete(key: AppConstants.usernameKey);
        await _secureStorage.delete(key: AppConstants.passwordKey);
      }

      _token = null;
      _user = null;
      _status = AuthStatus.unauthenticated;
      notifyListeners();
    }
  }

  void clearError() {
    _errorMessage = null;
    if (_status == AuthStatus.error) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }
}
