import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'arabic_text_helper.dart';

/// Helper class for handling Arabic text in PDF documents
class ArabicPdfHelper {
  /// Processes Arabic text for proper display
  static String processArabicText(String text) {
    return ArabicTextHelper.processArabicText(text);
  }

  /// Safely processes Arabic text with error handling
  static String _safeProcessArabicText(String text, pw.Font fallbackFont) {
    try {
      if (text.isEmpty) return '';
      return ArabicTextHelper.processArabicText(text);
    } catch (e) {
      // If processing fails, return the original text
      return text;
    }
  }

  /// Creates a report header with logo and title
  static pw.Widget reportHeader({
    required String title,
    required pw.Font boldFont,
    required pw.Font regularFont,
    Uint8List? logoImage,
    double logoSize = 60,
    PdfColor primaryColor = const PdfColor(0.2, 0.6, 0.86), // #3498DB
    String? subtitle,
    String? date,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 10),
      decoration: pw.BoxDecoration(
        border: pw.Border(bottom: pw.BorderSide(
          color: primaryColor,
          width: 2,
        )),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          // Logo (if provided)
          if (logoImage != null) ...[
            pw.Container(
              width: logoSize,
              height: logoSize,
              decoration: pw.BoxDecoration(
                color: primaryColor,
                shape: pw.BoxShape.circle,
              ),
              child: pw.ClipOval(
                child: pw.Image(
                  pw.MemoryImage(logoImage),
                  fit: pw.BoxFit.contain,
                ),
              ),
            ),
            pw.SizedBox(width: 10),
          ] else ...[
            pw.Container(
              width: logoSize,
              height: logoSize,
              decoration: pw.BoxDecoration(
                color: primaryColor,
                shape: pw.BoxShape.circle,
              ),
              child: pw.Center(
                child: pw.Text(
                  'ركن الجليد',
                  style: pw.TextStyle(
                    font: boldFont,
                    color: PdfColors.white,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
            pw.SizedBox(width: 10),
          ],

          // Title and subtitle
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                arabicHeading(
                  title,
                  font: boldFont,
                  fontSize: 20,
                  color: primaryColor,
                ),
                if (subtitle != null) ...[
                  pw.SizedBox(height: 5),
                  arabicText(
                    subtitle,
                    font: regularFont,
                    fontSize: 14,
                  ),
                ],
                if (date != null) ...[
                  pw.SizedBox(height: 5),
                  arabicText(
                    date,
                    font: regularFont,
                    fontSize: 12,
                    color: const PdfColor(0.4, 0.4, 0.4),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Creates a report footer with page number
  static pw.Widget reportFooter({
    required pw.Font font,
    required int currentPage,
    required int totalPages,
    PdfColor color = const PdfColor(0.4, 0.4, 0.4),
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(
          color: color,
          width: 0.5,
        )),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          arabicText(
            'ركن الجليد - نظام إدارة خدمات التكييف',
            font: font,
            fontSize: 10,
            color: color,
          ),
          arabicText(
            'صفحة $currentPage من $totalPages',
            font: font,
            fontSize: 10,
            color: color,
            textAlign: pw.TextAlign.left,
          ),
        ],
      ),
    );
  }

  /// Creates an Arabic heading with right-to-left text direction
  static pw.Widget arabicHeading(
    String text, {
    required pw.Font font,
    double fontSize = 18,
    pw.TextAlign textAlign = pw.TextAlign.right,
    PdfColor color = const PdfColor(0, 0, 0),
  }) {
    try {
      return pw.Container(
        width: double.infinity,
        child: pw.Text(
          _safeProcessArabicText(text, font),
          style: pw.TextStyle(
            font: font,
            fontSize: fontSize,
            color: color,
          ),
          textDirection: pw.TextDirection.rtl,
          textAlign: textAlign,
        ),
      );
    } catch (e) {
      // Fallback to simple text if there's an error
      return pw.Container(
        width: double.infinity,
        child: pw.Text(
          text,
          style: pw.TextStyle(
            font: font,
            fontSize: fontSize,
            color: color,
          ),
          textAlign: textAlign,
        ),
      );
    }
  }

  /// Creates an Arabic text with right-to-left text direction
  static pw.Widget arabicText(
    String text, {
    required pw.Font font,
    double fontSize = 12,
    pw.TextAlign textAlign = pw.TextAlign.right,
    PdfColor color = const PdfColor(0, 0, 0),
  }) {
    try {
      return pw.Container(
        width: double.infinity,
        child: pw.Text(
          _safeProcessArabicText(text, font),
          style: pw.TextStyle(
            font: font,
            fontSize: fontSize,
            color: color,
          ),
          textDirection: pw.TextDirection.rtl,
          textAlign: textAlign,
        ),
      );
    } catch (e) {
      // Fallback to simple text if there's an error
      return pw.Container(
        width: double.infinity,
        child: pw.Text(
          text,
          style: pw.TextStyle(
            font: font,
            fontSize: fontSize,
            color: color,
          ),
          textAlign: textAlign,
        ),
      );
    }
  }

  /// Creates a colored table with Arabic text support and improved styling
  static pw.Widget arabicTable({
    required List<String> headers,
    required List<List<String>> data,
    required pw.Font regularFont,
    required pw.Font boldFont,
    Map<int, pw.Alignment> customAlignments = const {},
    double cellHeight = 30,
    PdfColor headerColor = const PdfColor(0.2, 0.6, 0.86), // #3498DB
    PdfColor headerTextColor = const PdfColor(1, 1, 1), // White
    PdfColor borderColor = const PdfColor(0.5, 0.5, 0.5),
    List<PdfColor>? rowColors,
    Map<int, Map<String, PdfColor>>? conditionalColors,
    List<double>? columnWidths,
  }) {
    try {
      // Default alignments (right-aligned for Arabic text)
      final Map<int, pw.Alignment> alignments = {};

      // Fill with default right alignment for all columns
      for (int i = 0; i < headers.length; i++) {
        alignments[i] = pw.Alignment.centerRight;
      }

      // Override with custom alignments
      alignments.addAll(customAlignments);

      // Default row colors (alternating)
      final defaultRowColors = [
        const PdfColor(1, 1, 1), // White
        const PdfColor(0.95, 0.95, 0.95), // Light gray
      ];

      final effectiveRowColors = rowColors ?? defaultRowColors;

      // Calculate column widths if not provided
      List<double> effectiveColumnWidths = columnWidths ?? List.filled(headers.length, 1.0 / headers.length);

      // Ensure column widths sum to 1.0
      if (effectiveColumnWidths.length != headers.length) {
        effectiveColumnWidths = List.filled(headers.length, 1.0 / headers.length);
      } else {
        double sum = effectiveColumnWidths.fold(0.0, (a, b) => a + b);
        if (sum <= 0 || sum > 1.1) {
          effectiveColumnWidths = List.filled(headers.length, 1.0 / headers.length);
        }
      }

      return pw.Table(
        border: pw.TableBorder.all(
          color: borderColor,
          width: 0.5,
        ),
        columnWidths: {
          for (int i = 0; i < effectiveColumnWidths.length; i++)
            i: pw.FlexColumnWidth(effectiveColumnWidths[i] * 100),
        },
        children: [
          // Header row
          pw.TableRow(
            decoration: pw.BoxDecoration(
              color: headerColor,
            ),
            children: List.generate(
              headers.length,
              (index) => pw.Container(
                height: cellHeight,
                padding: const pw.EdgeInsets.all(5),
                alignment: alignments[index],
                child: pw.Text(
                  _safeProcessArabicText(headers[index], boldFont),
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 12,
                    color: headerTextColor,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ),
          ),
          // Data rows
          ...data.asMap().entries.map(
            (entry) {
              final rowIndex = entry.key;
              final row = entry.value;

              // Determine row background color (alternating)
              final rowColor = effectiveRowColors[rowIndex % effectiveRowColors.length];

              // Ensure row has the correct number of cells
              final List<String> paddedRow = row.length < headers.length
                  ? [...row, ...List.filled(headers.length - row.length, '')]
                  : row.sublist(0, headers.length);

              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: rowColor,
                ),
                children: List.generate(
                  headers.length,
                  (colIndex) {
                    // Check for conditional colors based on cell value
                    PdfColor textColor = const PdfColor(0, 0, 0); // Default black

                    if (conditionalColors != null &&
                        conditionalColors.containsKey(colIndex) &&
                        colIndex < paddedRow.length &&
                        conditionalColors[colIndex]!.containsKey(paddedRow[colIndex])) {
                      textColor = conditionalColors[colIndex]![paddedRow[colIndex]]!;
                    }

                    return pw.Container(
                      height: cellHeight,
                      padding: const pw.EdgeInsets.all(5),
                      alignment: alignments[colIndex],
                      child: pw.Text(
                        colIndex < paddedRow.length
                            ? _safeProcessArabicText(paddedRow[colIndex], regularFont)
                            : '',
                        style: pw.TextStyle(
                          font: regularFont,
                          fontSize: 10,
                          color: textColor,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      );
    } catch (e) {
      // If there's an error, return a simple error message
      return pw.Container(
        padding: const pw.EdgeInsets.all(10),
        decoration: pw.BoxDecoration(
          color: const PdfColor(1, 0.9, 0.9),
          border: pw.Border.all(color: const PdfColor(0.9, 0.3, 0.3)),
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        ),
        child: pw.Text(
          'خطأ في إنشاء الجدول: $e',
          style: pw.TextStyle(
            font: regularFont,
            fontSize: 12,
            color: const PdfColor(0.9, 0.3, 0.3),
          ),
        ),
      );
    }
  }

  /// Creates a summary card with title and value
  static pw.Widget summaryCard({
    required String title,
    required String value,
    required pw.Font titleFont,
    required pw.Font valueFont,
    PdfColor backgroundColor = const PdfColor(0.95, 0.95, 0.95),
    PdfColor titleColor = const PdfColor(0.2, 0.6, 0.86), // #3498DB
    PdfColor valueColor = const PdfColor(0, 0, 0),
    double width = 150,
  }) {
    return pw.Container(
      width: width,
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: const PdfColor(0.8, 0.8, 0.8), width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          arabicText(
            title,
            font: titleFont,
            fontSize: 12,
            color: titleColor,
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 5),
          arabicText(
            value,
            font: valueFont,
            fontSize: 14,
            color: valueColor,
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }
}
