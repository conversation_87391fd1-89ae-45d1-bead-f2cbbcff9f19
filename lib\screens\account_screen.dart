import 'package:flutter/material.dart';

class AccountScreen extends StatefulWidget {
  const AccountScreen({Key? key}) : super(key: key);

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ListTile(
              title: const Text('Manage Account'),
              onTap: () {},
            ),
            ListTile(
              title: const Text('Privacy Controls'),
              onTap: () {},
            ),
            ListTile(
              title: const Text('Choose to'),
              onTap: () {},
            ),
            ListTile(
              title: const Text('Change Push Notifications'),
              onTap: () {},
            ),
            ListTile(
              title: const Text('Help & Support'),
              onTap: () {},
            ),
            ListTile(
              title: const Text('About Section'),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }
}
