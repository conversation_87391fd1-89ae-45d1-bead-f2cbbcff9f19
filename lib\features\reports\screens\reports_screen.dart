import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/report_card.dart';
import '../../../shared/models/report.dart';
import '../../../core/repositories/report_repository.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _reportsDisplay = [];
  String _searchQuery = '';
  String _categoryFilter = 'all';

  final ReportRepository _reportRepository = ReportRepository();

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام الـ repository للحصول على التقارير من قاعدة البيانات
      final reports = await _reportRepository.getAllReports();

      // تحويل التقارير إلى صيغة عرض
      final reportsDisplay = _convertReportsToDisplayFormat(reports);

      if (mounted) {
        setState(() {
          _reportsDisplay = reportsDisplay;
          _isLoading = false;
        });
      }

      // إذا لم تكن هناك تقارير، قم بإنشاء بعض التقارير الافتراضية للعرض
      if (reports.isEmpty && mounted) {
        await _createSampleReports();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading reports: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل التقارير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> _convertReportsToDisplayFormat(List<Report> reports) {
    return reports.map((report) {
      IconData icon;
      Color color;

      switch (report.type) {
        case ReportType.financial:
          icon = Icons.account_balance_wallet;
          color = AppColors.primary;
          break;
        case ReportType.sales:
          icon = Icons.receipt_long;
          color = AppColors.secondary;
          break;
        case ReportType.customer:
          icon = Icons.people;
          color = AppColors.accent;
          break;
        case ReportType.inventory:
          icon = Icons.inventory_2;
          color = Colors.teal;
          break;
        case ReportType.employee:
          icon = Icons.badge;
          color = Colors.indigo;
          break;
        case ReportType.supplier:
          icon = Icons.local_shipping;
          color = Colors.orange;
          break;
        case ReportType.invoice:
          icon = Icons.receipt;
          color = Colors.deepOrange;
          break;
        case ReportType.custom:
          icon = Icons.description;
          color = Colors.grey;
          break;
      }

      return {
        'id': report.id,
        'title': report.title,
        'description': report.description ?? '',
        'category': Report.getReportTypeName(report.type),
        'type': report.type,
        'icon': icon,
        'color': color,
        'lastGenerated': report.updatedAt ?? report.createdAt,
        'report': report, // إضافة مرجع للتقرير الأصلي
      };
    }).toList();
  }

  Future<void> _createSampleReports() async {
    // إنشاء تقارير افتراضية للعرض
    final sampleReports = [
      Report(
        title: 'تقرير الإيرادات والمصروفات',
        description: 'تقرير مفصل عن الإيرادات والمصروفات للشهر الحالي',
        type: ReportType.financial,
        createdAt: DateTime.now(),
      ),
      Report(
        title: 'تقرير المبيعات',
        description: 'تقرير مفصل عن المبيعات والفواتير للشهر الحالي',
        type: ReportType.sales,
        createdAt: DateTime.now(),
      ),
      Report(
        title: 'تقرير العملاء',
        description: 'تقرير مفصل عن العملاء ومعاملاتهم',
        type: ReportType.customer,
        createdAt: DateTime.now(),
      ),
      Report(
        title: 'تقرير الموظفين',
        description: 'تقرير مفصل عن الموظفين وأدائهم',
        type: ReportType.employee,
        createdAt: DateTime.now(),
      ),
      Report(
        title: 'تقرير الموردين',
        description: 'تقرير مفصل عن الموردين ومعاملاتهم',
        type: ReportType.supplier,
        createdAt: DateTime.now(),
      ),
      Report(
        title: 'تقرير الفواتير',
        description: 'تقرير مفصل عن الفواتير وحالتها',
        type: ReportType.invoice,
        createdAt: DateTime.now(),
      ),
    ];

    // حفظ التقارير في قاعدة البيانات
    for (final report in sampleReports) {
      await _reportRepository.insertReport(report);
    }

    // إعادة تحميل التقارير
    final reports = await _reportRepository.getAllReports();
    final reportsDisplay = _convertReportsToDisplayFormat(reports);

    if (mounted) {
      setState(() {
        _reportsDisplay = reportsDisplay;
      });
    }
  }

  Set<String> get _categories {
    final categories = <String>{};
    for (final report in _reportsDisplay) {
      categories.add(report['category'] as String);
    }
    return categories;
  }

  List<Map<String, dynamic>> get _filteredReports {
    List<Map<String, dynamic>> filtered = List<Map<String, dynamic>>.from(_reportsDisplay);

    // Apply category filter
    if (_categoryFilter != 'all') {
      filtered = filtered.where((report) => report['category'] == _categoryFilter).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((report) {
        final query = _searchQuery.toLowerCase();
        return (report['title'] as String).toLowerCase().contains(query) ||
            (report['description'] as String).toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن تقرير...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          _buildCategoryFilterChips(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredReports.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد تقارير',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadReports,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredReports.length,
                          itemBuilder: (context, index) {
                            final report = _filteredReports[index];
                            return ReportCard(
                              title: report['title'] as String,
                              description: report['description'] as String,
                              icon: report['icon'] as IconData,
                              color: report['color'] as Color,
                              lastGenerated: report['lastGenerated'] as DateTime,
                              onTap: () {
                                _showReportOptions(context, report);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateReportDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoryFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Row(
        children: [
          _buildFilterChip('all', 'الكل'),
          const SizedBox(width: 8),
          ..._categories.map((category) {
            return Padding(
              padding: const EdgeInsets.only(left: 8),
              child: _buildFilterChip(category, category),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _categoryFilter == value;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _categoryFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.white,
      selectedColor: AppColors.primary.withAlpha(50),
      checkmarkColor: AppColors.primary,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primary : AppColors.textPrimary,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية التقارير'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Date range filter would go here
              const Text('فلترة حسب التاريخ'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Apply filters
              },
              child: const Text('تطبيق'),
            ),
          ],
        );
      },
    );
  }

  void _showReportOptions(BuildContext context, Map<String, dynamic> report) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.paddingL,
            horizontal: AppDimensions.paddingM,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                report['title'] as String,
                style: AppTextStyles.heading2,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                'آخر تحديث: ${DateFormat('dd/MM/yyyy').format(report['lastGenerated'] as DateTime)}',
                style: const TextStyle(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.visibility),
                title: const Text('عرض التقرير'),
                onTap: () {
                  Navigator.pop(context);
                  _showReportPreview(context, report);
                },
              ),
              ListTile(
                leading: const Icon(Icons.refresh),
                title: const Text('تحديث التقرير'),
                onTap: () {
                  Navigator.pop(context);
                  _showGenerateReportDialog(context, report);
                },
              ),
              ListTile(
                leading: const Icon(Icons.print),
                title: const Text('طباعة التقرير'),
                onTap: () {
                  Navigator.pop(context);
                  // إذا كان التقرير هو تقرير الفواتير، افتح شاشة تقرير الفواتير و المعاملات و طلبات الخدمة الموحدة
                  if (report['type'] == ReportType.invoice) {
                    Navigator.pushNamed(context, AppRoutes.invoiceReport);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('جاري طباعة التقرير...'),
                      ),
                    );
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.share),
                title: const Text('مشاركة التقرير'),
                onTap: () {
                  Navigator.pop(context);
                  // إذا كان التقرير هو تقرير الفواتير، افتح شاشة تقرير الفواتير و المعاملات و طلبات الخدمة الموحدة
                  if (report['type'] == ReportType.invoice) {
                    Navigator.pushNamed(context, AppRoutes.invoiceReport);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('جاري مشاركة التقرير...'),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showReportPreview(BuildContext context, Map<String, dynamic> report) {
    // إذا كان التقرير هو تقرير الفواتير، افتح شاشة تقرير الفواتير و المعاملات و طلبات الخدمة الموحدة
    if (report['type'] == ReportType.invoice) {
      Navigator.pushNamed(context, AppRoutes.invoiceReport);
      return;
    }

    // لباقي أنواع التقارير، عرض الحوار الافتراضي
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(report['title'] as String),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('معاينة التقرير ستظهر هنا'),
              const SizedBox(height: AppDimensions.paddingM),
              const Text('في التطبيق الفعلي، سيتم عرض التقرير بتنسيق مناسب'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  void _showGenerateReportDialog(BuildContext context, Map<String, dynamic> report) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تحديث ${report['title']}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('هل تريد تحديث هذا التقرير؟'),
              const SizedBox(height: AppDimensions.paddingM),
              const Text('سيتم إنشاء نسخة جديدة من التقرير بالبيانات الحالية.'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _updateReport(report);
              },
              child: const Text('تحديث'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _updateReport(Map<String, dynamic> report) async {
    try {
      // Obtener el objeto Report original
      final originalReport = report['report'] as Report;

      // Crear una copia actualizada del reporte
      final updatedReport = originalReport.copyWith(
        updatedAt: DateTime.now(),
      );

      // Actualizar el reporte en la base de datos
      final result = await _reportRepository.updateReport(updatedReport);

      if (result > 0) {
        // Recargar los reportes
        await _loadReports();

        // Mostrar mensaje de éxito
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث ${report['title']} بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تحديث ${report['title']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating report: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCreateReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إنشاء تقرير جديد'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('اختر نوع التقرير الذي تريد إنشاءه'),
              const SizedBox(height: AppDimensions.paddingM),
              // List of report types would go here
              const Text('في التطبيق الفعلي، سيتم عرض قائمة بأنواع التقارير المتاحة'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _createNewReport();
              },
              child: const Text('متابعة'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _createNewReport() async {
    try {
      // Crear un nuevo reporte
      final newReport = Report(
        title: 'تقرير جديد',
        description: 'تقرير مخصص جديد',
        type: ReportType.custom,
        createdAt: DateTime.now(),
      );

      // Guardar el reporte en la base de datos
      final result = await _reportRepository.insertReport(newReport);

      if (result > 0) {
        // Recargar los reportes
        await _loadReports();

        // Mostrar mensaje de éxito
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء تقرير جديد بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إنشاء تقرير جديد'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating new report: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إنشاء تقرير جديد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
