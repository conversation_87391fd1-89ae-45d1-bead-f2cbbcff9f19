import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/constants.dart';
import '../../config/routes.dart';
import '../../state/auth_state.dart';
import '../../shared/models/user.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final authState = Provider.of<AuthState>(context);
    final user = authState.user;

    return Drawer(
      child: Column(
        children: [
          _buildDrawerHeader(context, user),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  title: 'لوحة التحكم',
                  icon: Icons.dashboard,
                  route: AppRoutes.dashboard,
                ),
                _buildDrawerItem(
                  context,
                  title: 'لوحة التحكم المالية',
                  icon: Icons.analytics,
                  route: AppRoutes.financialDashboard,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الفواتير',
                  icon: Icons.receipt_long,
                  route: AppRoutes.invoices,
                ),
                _buildDrawerItem(
                  context,
                  title: 'المعاملات',
                  icon: Icons.account_balance_wallet,
                  route: AppRoutes.transactions,
                ),
                _buildDrawerItem(
                  context,
                  title: 'التحويلات المالية',
                  icon: Icons.swap_horiz,
                  route: AppRoutes.accountTransfer,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الحسابات البنكية',
                  icon: Icons.account_balance,
                  route: AppRoutes.bankAccounts,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الفئات',
                  icon: Icons.category,
                  route: AppRoutes.categories,
                ),
                _buildDrawerItem(
                  context,
                  title: 'العملاء',
                  icon: Icons.people,
                  route: AppRoutes.customers,
                ),
                _buildDrawerItem(
                  context,
                  title: 'طلبات الخدمة',
                  icon: Icons.build,
                  route: AppRoutes.serviceRequests,
                ),
                _buildDrawerItem(
                  context,
                  title: 'طلبات الخدمة (جديد)',
                  icon: Icons.build_circle,
                  route: AppRoutes.newServiceRequests,
                ),
                _buildDrawerItem(
                  context,
                  title: 'جدول الزيارات',
                  icon: Icons.calendar_month,
                  route: AppRoutes.serviceSchedule,
                ),
                _buildDrawerItem(
                  context,
                  title: 'فئات الخدمة',
                  icon: Icons.category_outlined,
                  route: AppRoutes.serviceCategories,
                ),
                _buildDrawerItem(
                  context,
                  title: 'المخزون',
                  icon: Icons.inventory_2,
                  route: AppRoutes.inventory,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الموظفين',
                  icon: Icons.badge,
                  route: AppRoutes.employees,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الموردين',
                  icon: Icons.local_shipping,
                  route: AppRoutes.suppliers,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الرواتب',
                  icon: Icons.payments,
                  route: AppRoutes.payroll,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الإحصائيات',
                  icon: Icons.insert_chart,
                  route: AppRoutes.statistics,
                ),
                ExpansionTile(
                  leading: Icon(
                    Icons.bar_chart,
                    color: AppColors.textSecondary,
                  ),
                  title: const Text('التقارير'),
                  children: [
                    _buildDrawerItem(
                      context,
                      title: 'التقارير المالية',
                      icon: Icons.assessment,
                      route: AppRoutes.reports,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقارير العملاء',
                      icon: Icons.people,
                      route: AppRoutes.customerReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقارير المعاملات',
                      icon: Icons.account_balance_wallet,
                      route: AppRoutes.transactionReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقارير طلبات الخدمة',
                      icon: Icons.build,
                      route: AppRoutes.serviceRequestReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقارير الفواتير',
                      icon: Icons.receipt,
                      route: AppRoutes.invoiceReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقارير الصيانة',
                      icon: Icons.build_circle,
                      route: AppRoutes.maintenanceReports,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقرير الفواتير و المعاملات و طلبات الخدمة الموحد',
                      icon: Icons.receipt_long,
                      route: AppRoutes.invoiceReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقرير التدفقات النقدية',
                      icon: Icons.account_balance_wallet,
                      route: AppRoutes.cashFlowReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تقرير الأرباح والخسائر',
                      icon: Icons.trending_up,
                      route: AppRoutes.profitLossReport,
                      indent: true,
                    ),
                    _buildDrawerItem(
                      context,
                      title: 'تحليل ربحية الخدمات',
                      icon: Icons.pie_chart,
                      route: AppRoutes.serviceProfitability,
                      indent: true,
                    ),
                  ],
                ),
                const Divider(),
                // Check if user has 'admin' role or 'users_view' permission
                if (user != null && (user.hasRole('admin') || user.hasPermission('users_view') == true))
                  _buildDrawerItem(
                    context,
                    title: 'إدارة المستخدمين',
                    icon: Icons.people_alt,
                    route: AppRoutes.users,
                  ),
                _buildDrawerItem(
                  context,
                  title: 'الإشعارات',
                  icon: Icons.notifications,
                  route: AppRoutes.notifications,
                ),
                _buildDrawerItem(
                  context,
                  title: 'إدارة الضرائب',
                  icon: Icons.receipt_long,
                  route: AppRoutes.taxManagement,
                ),
                _buildDrawerItem(
                  context,
                  title: 'الإعدادات',
                  icon: Icons.settings,
                  route: AppRoutes.settings,
                ),
                _buildDrawerItem(
                  context,
                  title: 'معلومات الشركة',
                  icon: Icons.business,
                  route: AppRoutes.companyInfo,
                ),
              ],
            ),
          ),
          _buildLogoutButton(context, authState),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context, User? user) {
    return DrawerHeader(
      decoration: const BoxDecoration(
        color: AppColors.primary,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(
                  child: Text(
                    'ركن الجليد',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user?.name ?? 'المستخدم',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user?.email ?? '',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Text(
              // Display the first role or 'مستخدم' if no roles
              _getRoleName(user?.roles),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required String route,
    bool indent = false,
  }) {
    final isSelected = ModalRoute.of(context)?.settings.name == route;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? AppColors.primary : AppColors.textPrimary,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: AppColors.primary.withAlpha(25),
      contentPadding: EdgeInsets.only(
        left: AppDimensions.paddingM,
        right: indent ? 32.0 : AppDimensions.paddingM,
      ),
      onTap: () {
        Navigator.pop(context); // Close drawer
        if (!isSelected) {
          Navigator.pushReplacementNamed(context, route);
        }
      },
    );
  }

  Widget _buildLogoutButton(BuildContext context, AuthState authState) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: ListTile(
        leading: const Icon(
          Icons.logout,
          color: AppColors.error,
        ),
        title: const Text(
          'تسجيل الخروج',
          style: TextStyle(
            color: AppColors.error,
            fontWeight: FontWeight.bold,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          side: const BorderSide(color: AppColors.error),
        ),
        onTap: () {
          _showLogoutConfirmationDialog(context, authState);
        },
      ),
    );
  }

  void _showLogoutConfirmationDialog(BuildContext context, AuthState authState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await authState.logout();
              if (context.mounted) {
                Navigator.pushReplacementNamed(context, AppRoutes.login);
              }
            },
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleName(List<String>? roles) {
    if (roles == null || roles.isEmpty) {
      return 'مستخدم';
    }
    // You might want more sophisticated logic here, e.g., prioritize 'admin'
    // For now, just return the first role in the list
    return roles.first;
  }
}
