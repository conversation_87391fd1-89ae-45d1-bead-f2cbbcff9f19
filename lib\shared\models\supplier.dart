import 'package:flutter/material.dart';

enum SupplierCategory {
  parts,
  equipment,
  tools,
  services,
  other,
}

class Supplier {
  final int id;
  final String name;
  final String? email;
  final String? phone;
  final String? address;
  final String? contactPerson;
  final SupplierCategory category;
  final bool isActive;
  final double balance;
  final DateTime createdAt;
  final String? notes;
  final String? taxNumber;
  final String? website;

  Supplier({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.contactPerson,
    required this.category,
    required this.isActive,
    required this.balance,
    required this.createdAt,
    this.notes,
    this.taxNumber,
    this.website,
  });

  Supplier copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? contactPerson,
    SupplierCategory? category,
    bool? isActive,
    double? balance,
    DateTime? createdAt,
    String? notes,
    String? taxNumber,
    String? website,
  }) {
    return Supplier(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      contactPerson: contactPerson ?? this.contactPerson,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      balance: balance ?? this.balance,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
      taxNumber: taxNumber ?? this.taxNumber,
      website: website ?? this.website,
    );
  }

  static String getCategoryName(SupplierCategory category) {
    switch (category) {
      case SupplierCategory.parts:
        return 'قطع غيار';
      case SupplierCategory.equipment:
        return 'معدات';
      case SupplierCategory.tools:
        return 'أدوات';
      case SupplierCategory.services:
        return 'خدمات';
      case SupplierCategory.other:
        return 'أخرى';
    }
  }

  static Color getCategoryColor(SupplierCategory category) {
    switch (category) {
      case SupplierCategory.parts:
        return Colors.blue;
      case SupplierCategory.equipment:
        return Colors.green;
      case SupplierCategory.tools:
        return Colors.orange;
      case SupplierCategory.services:
        return Colors.purple;
      case SupplierCategory.other:
        return Colors.grey;
    }
  }

  static IconData getCategoryIcon(SupplierCategory category) {
    switch (category) {
      case SupplierCategory.parts:
        return Icons.settings;
      case SupplierCategory.equipment:
        return Icons.precision_manufacturing;
      case SupplierCategory.tools:
        return Icons.handyman;
      case SupplierCategory.services:
        return Icons.miscellaneous_services;
      case SupplierCategory.other:
        return Icons.category;
    }
  }

  // Mock data generator
  // Factory method to create a Supplier from a map
  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'] as int,
      name: map['name'] as String,
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      address: map['address'] as String?,
      contactPerson: map['contact_person'] as String?,
      category: _parseCategoryFromString(map['category'] as String?),
      isActive: map['status'] != null
          ? map['status'] == 'active'
          : map['is_active'] == 1,
      balance: (map['balance'] as num).toDouble(),
      createdAt: DateTime.parse(map['created_at'] as String),
      notes: map['notes'] as String?,
      taxNumber: map['tax_number'] as String?,
      website: map['website'] as String?,
    );
  }

  // Helper method to convert a Supplier to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'contact_person': contactPerson,
      'category': category.toString().split('.').last,
      'status': isActive ? 'active' : 'inactive',
      'balance': balance,
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
      'tax_number': taxNumber,
      'website': website,
    };
  }

  // Helper method to parse category string to enum
  static SupplierCategory _parseCategoryFromString(String? category) {
    if (category == null) return SupplierCategory.other;

    switch (category.toLowerCase()) {
      case 'parts':
        return SupplierCategory.parts;
      case 'equipment':
        return SupplierCategory.equipment;
      case 'tools':
        return SupplierCategory.tools;
      case 'services':
        return SupplierCategory.services;
      case 'other':
      default:
        return SupplierCategory.other;
    }
  }

  static List<Supplier> getMockSuppliers() {
    return [
      Supplier(
        id: 1,
        name: 'شركة الرياض للتكييف',
        email: '<EMAIL>',
        phone: '0112345678',
        address: 'الرياض، حي العليا، شارع العروبة',
        contactPerson: 'محمد العتيبي',
        category: SupplierCategory.equipment,
        isActive: true,
        balance: 15000.0,
        createdAt: DateTime(2022, 1, 15),
        taxNumber: '*********',
        website: 'www.riyadhac.com',
      ),
      Supplier(
        id: 2,
        name: 'مؤسسة النور للقطع',
        email: '<EMAIL>',
        phone: '0556789012',
        address: 'جدة، حي الصفا',
        contactPerson: 'أحمد الغامدي',
        category: SupplierCategory.parts,
        isActive: true,
        balance: 8500.0,
        createdAt: DateTime(2022, 3, 10),
      ),
      Supplier(
        id: 3,
        name: 'شركة الخليج للأدوات',
        email: '<EMAIL>',
        phone: '0534567890',
        address: 'الدمام، حي الشاطئ',
        contactPerson: 'خالد السعيد',
        category: SupplierCategory.tools,
        isActive: true,
        balance: 12000.0,
        createdAt: DateTime(2021, 11, 5),
        taxNumber: '*********',
      ),
      Supplier(
        id: 4,
        name: 'مؤسسة الموظف الفنية للخدمات',
        email: '<EMAIL>',
        phone: '0501234567',
        address: 'الرياض، حي الملز',
        contactPerson: 'سعد القحطاني',
        category: SupplierCategory.services,
        isActive: false,
        balance: 3000.0,
        createdAt: DateTime(2022, 5, 20),
      ),
      Supplier(
        id: 5,
        name: 'شركة المستقبل للتوريدات',
        email: '<EMAIL>',
        phone: '0567890123',
        address: 'مكة، حي العزيزية',
        contactPerson: 'فهد العنزي',
        category: SupplierCategory.other,
        isActive: true,
        balance: 20000.0,
        createdAt: DateTime(2021, 8, 12),
        website: 'www.future-supplies.com',
      ),
    ];
  }
}
