import 'package:flutter/material.dart';

class BookingScreen extends StatefulWidget {
  const BookingScreen({Key? key}) : super(key: key);

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const Text(
                'Trip Details',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              // Placeholder for trip details
              Container(
                height: 100,
                color: Colors.grey[200],
                child: const Center(
                  child: Text('Trip details will be displayed here'),
                ),
              ),
              const SizedBox(height: 20),

              // Seat Selection
              const Text(
                'Seat Selection',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              // Placeholder for seat selection
              Container(
                height: 100,
                color: Colors.grey[200],
                child: const Center(
                  child: Text('Seat selection will be displayed here'),
                ),
              ),
              const SizedBox(height: 20),

              // Payment Options
              const Text(
                'Payment Options',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              // Placeholder for payment options
              Container(
                height: 100,
                color: Colors.grey[200],
                child: const Center(
                  child: Text('Payment options will be displayed here'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
