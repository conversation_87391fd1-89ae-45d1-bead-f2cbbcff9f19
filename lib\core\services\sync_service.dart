import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import 'firebase_service.dart';
import 'connectivity_service.dart';
import '../repositories/customer_repository.dart';
import '../../shared/models/sync_model.dart';
import '../../shared/models/sync_conflict.dart';

/// Main synchronization service that handles bidirectional sync between local and cloud
class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final FirebaseService _firebaseService = FirebaseService();
  final ConnectivityService _connectivityService = ConnectivityService();
  
  // Repositories
  final CustomerRepository _customerRepository = CustomerRepository();

  // Sync state
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  Timer? _periodicSyncTimer;
  
  // Stream controllers
  final StreamController<SyncStatus> _syncStatusController = StreamController<SyncStatus>.broadcast();
  final StreamController<SyncProgress> _syncProgressController = StreamController<SyncProgress>.broadcast();
  final StreamController<List<SyncConflict>> _conflictsController = StreamController<List<SyncConflict>>.broadcast();

  // Configuration
  Duration syncInterval = const Duration(minutes: 5);
  bool autoSyncEnabled = true;
  bool syncOnlyOnWifi = false;

  /// Initialize the sync service
  Future<void> initialize() async {
    try {
      // Initialize dependencies
      await _firebaseService.initialize();
      await _connectivityService.initialize();

      // Load last sync time
      await _loadLastSyncTime();

      // Start listening to connectivity changes
      _connectivityService.connectivityStream.listen(_onConnectivityChanged);

      // Start periodic sync if enabled
      if (autoSyncEnabled) {
        _startPeriodicSync();
      }

      if (kDebugMode) {
        print('✅ SyncService initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing SyncService: $e');
      }
      rethrow;
    }
  }

  /// Dispose the sync service
  void dispose() {
    _periodicSyncTimer?.cancel();
    _syncStatusController.close();
    _syncProgressController.close();
    _conflictsController.close();
  }

  // Getters

  /// Current sync status
  bool get isSyncing => _isSyncing;

  /// Last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Stream of sync status changes
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Stream of sync progress updates
  Stream<SyncProgress> get syncProgressStream => _syncProgressController.stream;

  /// Stream of sync conflicts
  Stream<List<SyncConflict>> get conflictsStream => _conflictsController.stream;

  // Main sync methods

  /// Perform full synchronization
  Future<SyncResult> performFullSync({bool force = false}) async {
    if (_isSyncing && !force) {
      if (kDebugMode) {
        print('⚠️ Sync already in progress');
      }
      return SyncResult.alreadyInProgress();
    }

    try {
      _isSyncing = true;
      _syncStatusController.add(SyncStatus.syncing);

      if (kDebugMode) {
        print('🔄 Starting full synchronization...');
      }

      // Check connectivity
      if (!await _connectivityService.shouldSync(requireWifi: syncOnlyOnWifi)) {
        _syncStatusController.add(SyncStatus.noConnection);
        return SyncResult.noConnection();
      }

      final result = SyncResult();
      
      // Sync all entities in order of dependencies
      await _syncUsers(result);
      await _syncCustomers(result);
      await _syncServiceRequests(result);
      await _syncInvoices(result);
      await _syncInventoryItems(result);
      await _syncEmployees(result);
      await _syncSuppliers(result);

      // Process sync queue
      await _processSyncQueue(result);

      // Update last sync time
      _lastSyncTime = DateTime.now();
      await _saveLastSyncTime();

      // Check for conflicts
      await _checkForConflicts();

      _syncStatusController.add(SyncStatus.completed);
      
      if (kDebugMode) {
        print('✅ Full synchronization completed: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during full sync: $e');
      }
      _syncStatusController.add(SyncStatus.error);
      return SyncResult.error(e.toString());
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync only pending local changes
  Future<SyncResult> syncPendingChanges() async {
    if (_isSyncing) {
      return SyncResult.alreadyInProgress();
    }

    try {
      _isSyncing = true;
      _syncStatusController.add(SyncStatus.syncing);

      if (kDebugMode) {
        print('🔄 Syncing pending changes...');
      }

      // Check connectivity
      if (!await _connectivityService.shouldSync(requireWifi: syncOnlyOnWifi)) {
        _syncStatusController.add(SyncStatus.noConnection);
        return SyncResult.noConnection();
      }

      final result = SyncResult();
      
      // Process sync queue
      await _processSyncQueue(result);

      _syncStatusController.add(SyncStatus.completed);
      
      if (kDebugMode) {
        print('✅ Pending changes sync completed: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing pending changes: $e');
      }
      _syncStatusController.add(SyncStatus.error);
      return SyncResult.error(e.toString());
    } finally {
      _isSyncing = false;
    }
  }

  /// Force sync a specific entity
  Future<void> forceSyncEntity(String entityType, String entityId) async {
    try {
      if (kDebugMode) {
        print('🔄 Force syncing $entityType with ID: $entityId');
      }

      switch (entityType.toLowerCase()) {
        case 'customer':
          await _customerRepository.sync();
          break;
        // TODO: Add other entity types
        default:
          throw Exception('Unknown entity type: $entityType');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error force syncing entity: $e');
      }
      rethrow;
    }
  }

  // Conflict resolution

  /// Get all unresolved conflicts
  Future<List<SyncConflict>> getUnresolvedConflicts() async {
    try {
      final conflictMaps = await _dbHelper.getUnresolvedConflicts();
      return conflictMaps.map((map) => SyncConflict.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unresolved conflicts: $e');
      }
      rethrow;
    }
  }

  /// Resolve a conflict
  Future<void> resolveConflict(String conflictId, ConflictResolution resolution) async {
    try {
      if (kDebugMode) {
        print('🔧 Resolving conflict $conflictId with resolution: $resolution');
      }

      await _dbHelper.resolveSyncConflict(conflictId, resolution.toString().split('.').last);
      
      // Refresh conflicts stream
      await _checkForConflicts();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resolving conflict: $e');
      }
      rethrow;
    }
  }

  /// Auto-resolve conflicts where possible
  Future<int> autoResolveConflicts() async {
    try {
      final conflicts = await getUnresolvedConflicts();
      int resolvedCount = 0;

      for (final conflict in conflicts) {
        if (conflict.canAutoResolve()) {
          final resolution = conflict.getAutoResolution();
          if (resolution != null) {
            await resolveConflict(conflict.id, resolution);
            resolvedCount++;
          }
        }
      }

      if (kDebugMode) {
        print('✅ Auto-resolved $resolvedCount conflicts');
      }

      return resolvedCount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error auto-resolving conflicts: $e');
      }
      rethrow;
    }
  }

  // Configuration

  /// Enable/disable automatic sync
  void setAutoSyncEnabled(bool enabled) {
    autoSyncEnabled = enabled;
    if (enabled) {
      _startPeriodicSync();
    } else {
      _stopPeriodicSync();
    }
  }

  /// Set sync interval
  void setSyncInterval(Duration interval) {
    syncInterval = interval;
    if (autoSyncEnabled) {
      _stopPeriodicSync();
      _startPeriodicSync();
    }
  }

  /// Set WiFi-only sync
  void setSyncOnlyOnWifi(bool wifiOnly) {
    syncOnlyOnWifi = wifiOnly;
  }

  // Private methods

  /// Handle connectivity changes
  void _onConnectivityChanged(bool isConnected) {
    if (isConnected && autoSyncEnabled) {
      // Trigger sync when connectivity is restored
      Timer(const Duration(seconds: 2), () {
        if (!_isSyncing) {
          performFullSync();
        }
      });
    }
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    _stopPeriodicSync();
    _periodicSyncTimer = Timer.periodic(syncInterval, (_) {
      if (!_isSyncing) {
        performFullSync();
      }
    });
  }

  /// Stop periodic sync timer
  void _stopPeriodicSync() {
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = null;
  }

  /// Sync users
  Future<void> _syncUsers(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing users...', 0.05));
      // TODO: Implement user repository sync when available
      result.usersProcessed++;
      _syncProgressController.add(SyncProgress('Users synced', 0.1));
    } catch (e) {
      result.errors.add('User sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing users: $e');
      }
    }
  }

  /// Sync customers
  Future<void> _syncCustomers(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing customers...', 0.15));
      await _customerRepository.sync();
      result.customersProcessed++;
      _syncProgressController.add(SyncProgress('Customers synced', 0.25));
    } catch (e) {
      result.errors.add('Customer sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing customers: $e');
      }
    }
  }

  /// Sync service requests
  Future<void> _syncServiceRequests(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing service requests...', 0.3));
      // TODO: Implement service request repository sync when available
      result.serviceRequestsProcessed++;
      _syncProgressController.add(SyncProgress('Service requests synced', 0.4));
    } catch (e) {
      result.errors.add('Service request sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing service requests: $e');
      }
    }
  }

  /// Sync invoices
  Future<void> _syncInvoices(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing invoices...', 0.45));
      // TODO: Implement invoice repository sync when available
      result.invoicesProcessed++;
      _syncProgressController.add(SyncProgress('Invoices synced', 0.55));
    } catch (e) {
      result.errors.add('Invoice sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing invoices: $e');
      }
    }
  }

  /// Sync inventory items
  Future<void> _syncInventoryItems(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing inventory items...', 0.6));
      // TODO: Implement inventory repository sync when available
      result.inventoryItemsProcessed++;
      _syncProgressController.add(SyncProgress('Inventory items synced', 0.7));
    } catch (e) {
      result.errors.add('Inventory sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing inventory items: $e');
      }
    }
  }

  /// Sync employees
  Future<void> _syncEmployees(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing employees...', 0.75));
      // TODO: Implement employee repository sync when available
      _syncProgressController.add(SyncProgress('Employees synced', 0.8));
    } catch (e) {
      result.errors.add('Employee sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing employees: $e');
      }
    }
  }

  /// Sync suppliers
  Future<void> _syncSuppliers(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Syncing suppliers...', 0.85));
      // TODO: Implement supplier repository sync when available
      _syncProgressController.add(SyncProgress('Suppliers synced', 0.9));
    } catch (e) {
      result.errors.add('Supplier sync error: $e');
      if (kDebugMode) {
        print('❌ Error syncing suppliers: $e');
      }
    }
  }

  /// Process sync queue
  Future<void> _processSyncQueue(SyncResult result) async {
    try {
      _syncProgressController.add(SyncProgress('Processing sync queue...', 0.8));
      
      final pendingOperations = await _dbHelper.getPendingSyncOperations();
      
      for (final operation in pendingOperations) {
        try {
          await _processSyncOperation(operation);
          await _dbHelper.removeFromSyncQueue(operation['id'] as int);
          result.queueItemsProcessed++;
        } catch (e) {
          await _dbHelper.updateSyncQueueRetry(operation['id'] as int, e.toString());
          result.errors.add('Queue operation error: $e');
        }
      }
      
      _syncProgressController.add(SyncProgress('Sync queue processed', 1.0));
    } catch (e) {
      result.errors.add('Sync queue error: $e');
      if (kDebugMode) {
        print('❌ Error processing sync queue: $e');
      }
    }
  }

  /// Process a single sync operation
  Future<void> _processSyncOperation(Map<String, dynamic> operation) async {
    final entityType = operation['entity_type'] as String;
    final entityId = operation['entity_id'] as String;
    final operationType = operation['operation'] as String;
    final data = json.decode(operation['data'] as String) as Map<String, dynamic>;

    switch (entityType.toLowerCase()) {
      case 'customer':
        await _processSyncOperationForCustomer(operationType, entityId, data);
        break;
      // TODO: Add other entity types
      default:
        throw Exception('Unknown entity type: $entityType');
    }
  }

  /// Process sync operation for customer
  Future<void> _processSyncOperationForCustomer(String operation, String entityId, Map<String, dynamic> data) async {
    switch (operation) {
      case 'create':
        await _firebaseService.createDocument('customers', data);
        break;
      case 'update':
        final customer = await _customerRepository.getById(entityId);
        if (customer?.firestoreId != null) {
          await _firebaseService.updateDocument('customers', customer!.firestoreId!, data);
        }
        break;
      case 'delete':
        final customer = await _customerRepository.getById(entityId);
        if (customer?.firestoreId != null) {
          await _firebaseService.deleteDocument('customers', customer!.firestoreId!);
        }
        break;
      default:
        throw Exception('Unknown operation: $operation');
    }
  }

  /// Check for conflicts
  Future<void> _checkForConflicts() async {
    try {
      final conflicts = await getUnresolvedConflicts();
      _conflictsController.add(conflicts);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking for conflicts: $e');
      }
    }
  }

  /// Load last sync time from storage
  Future<void> _loadLastSyncTime() async {
    // TODO: Implement persistent storage for last sync time
    // For now, we'll use a simple approach
  }

  /// Save last sync time to storage
  Future<void> _saveLastSyncTime() async {
    // TODO: Implement persistent storage for last sync time
  }
}

/// Enum for sync status
enum SyncStatus {
  idle,
  syncing,
  completed,
  error,
  noConnection,
}

/// Class representing sync progress
class SyncProgress {
  final String message;
  final double progress; // 0.0 to 1.0

  const SyncProgress(this.message, this.progress);

  @override
  String toString() => 'SyncProgress(message: $message, progress: ${(progress * 100).toStringAsFixed(1)}%)';
}

/// Class representing sync result
class SyncResult {
  int customersProcessed = 0;
  int serviceRequestsProcessed = 0;
  int usersProcessed = 0;
  int invoicesProcessed = 0;
  int inventoryItemsProcessed = 0;
  int employeesProcessed = 0;
  int suppliersProcessed = 0;
  int queueItemsProcessed = 0;
  List<String> errors = [];
  DateTime? completedAt;

  SyncResult() {
    completedAt = DateTime.now();
  }

  SyncResult.alreadyInProgress() {
    errors.add('Sync already in progress');
  }

  SyncResult.noConnection() {
    errors.add('No internet connection');
  }

  SyncResult.error(String error) {
    errors.add(error);
  }

  bool get hasErrors => errors.isNotEmpty;
  bool get isSuccess => !hasErrors;

  @override
  String toString() {
    return 'SyncResult(users: $usersProcessed, customers: $customersProcessed, services: $serviceRequestsProcessed, invoices: $invoicesProcessed, inventory: $inventoryItemsProcessed, employees: $employeesProcessed, suppliers: $suppliersProcessed, queue: $queueItemsProcessed, errors: ${errors.length})';
  }
}
