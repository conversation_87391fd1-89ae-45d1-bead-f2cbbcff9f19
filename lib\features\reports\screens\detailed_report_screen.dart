import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:icecorner/features/reports/utils/excel_export_util.dart';
import 'package:icecorner/features/reports/utils/simple_pdf_export.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';

import '../../../core/repositories/customer_repository.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/service_request_repository.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/models/invoice.dart' as app_invoice;
import '../../../shared/models/transaction.dart' as app_transaction;
import '../../../shared/models/service_request.dart' as app_service;

import '../models/report_models.dart';
import '../widgets/report_filter_dialog.dart';
import '../widgets/report_summary_card.dart';
import '../widgets/customer_filter_dialog.dart';
import '../screens/pdf_preview_screen.dart';

class DetailedReportScreen extends StatefulWidget {
  final String reportType;
  final int? id;

  const DetailedReportScreen({
    super.key,
    required this.reportType,
    this.id,
  });

  @override
  State<DetailedReportScreen> createState() => _DetailedReportScreenState();
}

class _DetailedReportScreenState extends State<DetailedReportScreen> {
  bool _isLoading = true;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _filterType = 'all';
  int? _selectedCustomerId;
  List<dynamic> _reportData = [];
  Map<String, double> _summaryData = {};

  // Repositories
  final CustomerRepository _customerRepository = CustomerRepository();
  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final TransactionRepository _transactionRepository = TransactionRepository();
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();

  // Customer data
  List<Customer> _customers = [];

  // Function to get entity name
  Future<String> getEntityName() async {
    if (widget.id == null) return '';

    switch (widget.reportType) {
      case 'customer':
        try {
          final customer = await _customerRepository.getCustomerById(widget.id!);
          return customer?.name ?? 'عميل ${widget.id}';
        } catch (e) {
          if (kDebugMode) {
            print('Error getting customer name: $e');
          }
          return 'عميل ${widget.id}';
        }
      case 'employee':
        // In a real app, we would get the employee name from the database
        return 'موظف ${widget.id}';
      case 'supplier':
        // In a real app, we would get the supplier name from the database
        return 'مورد ${widget.id}';
      default:
        return 'عنصر ${widget.id}';
    }
  }

  @override
  void initState() {
    super.initState();
    _loadReportData();
    _updateReportTitle();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load customers for filtering
      _customers = await _customerRepository.getAllCustomers();

      // Generate data based on report type
      List<dynamic> data = [];
      Map<String, double> summary = {};

      // Filter by specific ID if available
      bool filterById = widget.id != null || _selectedCustomerId != null;
      int? customerId = widget.reportType == 'customer' ? (_selectedCustomerId ?? widget.id) : widget.id;

      switch (widget.reportType) {
        case 'invoices':
        case 'customer':
          // Get real invoices from database
          List<app_invoice.Invoice> invoices = [];

          if (customerId != null) {
            // Get invoices for specific customer
            invoices = await _invoiceRepository.getInvoicesByCustomerId(customerId);
          } else {
            // Get all invoices
            invoices = await _invoiceRepository.getAllInvoices();
          }

          // Convert to report model
          data = invoices.map((invoice) => Invoice(
            id: invoice.id ?? 0,
            invoiceNumber: invoice.invoiceNumber,
            customerId: invoice.customerId,
            customerName: invoice.customerName,
            date: invoice.date,
            dueDate: invoice.dueDate,
            items: [],
            subtotal: invoice.subtotal,
            tax: invoice.taxAmount,
            discount: invoice.discount,
            total: invoice.total,
            notes: invoice.notes,
            isPaid: invoice.isPaid,
            paymentDate: null, // Not available in the model
            createdAt: invoice.createdAt,
          )).toList();

          // Calculate totals
          double totalAmount = data.fold(0, (sum, item) => sum + (item as Invoice).total);
          double paidAmount = data.fold(0, (sum, item) => sum + ((item as Invoice).isPaid ? (item).total : 0));
          double unpaidAmount = data.fold(0, (sum, item) => sum + ((item as Invoice).isPaid ? 0 : (item).total));

          summary = {
            'total': totalAmount,
            'paid': paidAmount,
            'unpaid': unpaidAmount,
            'count': data.length.toDouble(),
          };

          // If it's a customer report, add additional information
          if (widget.reportType == 'customer' && customerId != null) {
            // Get service requests for this customer
            final serviceRequests = await _serviceRequestRepository.getServiceRequestsByCustomerId(customerId);

            // Calculate service metrics
            double servicesTotal = serviceRequests.length.toDouble();
            double servicesCompleted = serviceRequests
                .where((request) => request.status == app_service.ServiceRequestStatus.completed)
                .length
                .toDouble();

            // Get customer balance
            double balance = await _customerRepository.getCustomerBalance(customerId);

            // Add to summary
            summary['servicesTotal'] = servicesTotal;
            summary['servicesCompleted'] = servicesCompleted;
            summary['balance'] = -balance; // Negative balance means customer owes money
          }
          break;

        case 'services':
          // Get real service requests from database
          List<app_service.ServiceRequest> serviceRequests = [];

          if (customerId != null) {
            // Get service requests for specific customer
            serviceRequests = await _serviceRequestRepository.getServiceRequestsByCustomerId(customerId);
          } else {
            // Get all service requests
            serviceRequests = await _serviceRequestRepository.getAllServiceRequests();
          }

          // Convert to report model
          data = serviceRequests.map((request) => ServiceRequest(
            id: request.id ?? 0,
            requestNumber: request.requestNumber,
            customerId: request.customerId ?? 0,
            customerName: request.customerName,
            serviceType: request.serviceType,
            description: request.description,
            address: request.address,
            scheduledDate: request.scheduledDate,
            status: _convertServiceStatus(request.status),
            assignedTo: request.assignedToName,
            // completionDate is not available in the model, use null
            notes: request.notes,
            createdAt: request.createdAt,
          )).toList();

          // Calculate summary
          summary = {
            'total': data.length.toDouble(),
            'completed': data.where((item) => (item as ServiceRequest).status == ServiceStatus.completed).length.toDouble(),
            'pending': data.where((item) => (item as ServiceRequest).status == ServiceStatus.pending).length.toDouble(),
            'inProgress': data.where((item) => (item as ServiceRequest).status == ServiceStatus.inProgress).length.toDouble(),
          };
          break;

        case 'transactions':
        case 'supplier':
        case 'employee':
          // Get real transactions from database
          List<app_transaction.Transaction> transactions = [];

          if (widget.reportType == 'customer' && customerId != null) {
            // Get transactions for specific customer
            transactions = await _transactionRepository.getTransactionsByCustomer(customerId);
          } else if (widget.reportType == 'supplier' && customerId != null) {
            // Get transactions for specific supplier
            transactions = await _transactionRepository.getTransactionsBySupplier(customerId);
          } else {
            // Get all transactions
            transactions = await _transactionRepository.getAllTransactions();
          }

          // Convert to report model
          data = transactions.map((transaction) => Transaction(
            id: transaction.id ?? 0,
            reference: transaction.reference,
            date: transaction.date,
            amount: transaction.amount,
            type: transaction.type == app_transaction.TransactionType.income
                ? TransactionType.income
                : TransactionType.expense,
            category: transaction.category,
            categoryId: null,
            description: transaction.description ?? '',
            paymentMethod: transaction.paymentMethod == app_transaction.PaymentMethod.cash
                ? PaymentMethod.cash
                : PaymentMethod.bankTransfer,
            bankAccountId: transaction.bankAccountId,
            bankAccountName: transaction.bankAccountName,
            relatedEntityId: transaction.customerId ?? transaction.supplierId,
            relatedEntityName: transaction.customerName ?? transaction.supplierName,
            relatedEntityType: transaction.customerId != null
                ? 'customer'
                : transaction.supplierId != null
                    ? 'supplier'
                    : null,
            employeeId: transaction.employeeId,
            employeeName: transaction.employeeName,
            notes: null,
            isReconciled: false,
            createdAt: transaction.createdAt,
            updatedAt: transaction.updatedAt,
          )).toList();

          // Filter by entity if needed
          if (filterById) {
            if (widget.reportType == 'supplier' && customerId != null) {
              data = data.where((transaction) =>
                (transaction as Transaction).relatedEntityId == customerId &&
                transaction.relatedEntityType == 'supplier'
              ).toList();
            } else if (widget.reportType == 'employee' && customerId != null) {
              data = data.where((transaction) =>
                (transaction as Transaction).employeeId == customerId
              ).toList();
            }
          }

          // Calculate summary
          summary = {
            'income': data.fold(0, (sum, item) => sum + ((item as Transaction).type == TransactionType.income ? (item).amount : 0)),
            'expense': data.fold(0, (sum, item) => sum + ((item as Transaction).type == TransactionType.expense ? (item).amount : 0)),
            'balance': data.fold(0, (sum, item) => sum + ((item as Transaction).type == TransactionType.income ? (item).amount : -(item).amount)),
            'count': data.length.toDouble(),
          };
          break;

        default:
          data = [];
          summary = {};
      }

      if (mounted) {
        setState(() {
          _reportData = data;
          _summaryData = summary;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading report data: $e');
      }

      if (mounted) {
        setState(() {
          _reportData = [];
          _summaryData = {};
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to convert service status
  ServiceStatus _convertServiceStatus(app_service.ServiceRequestStatus status) {
    switch (status) {
      case app_service.ServiceRequestStatus.pending:
        return ServiceStatus.pending;
      case app_service.ServiceRequestStatus.inProgress:
        return ServiceStatus.inProgress;
      case app_service.ServiceRequestStatus.completed:
        return ServiceStatus.completed;
      case app_service.ServiceRequestStatus.cancelled:
        return ServiceStatus.cancelled;
    }
  }

  void showFilterDialog() {
    if (widget.reportType == 'customer') {
      // Mostrar diálogo de filtro de cliente
      showDialog(
        context: context,
        builder: (context) => CustomerFilterDialog(
          selectedCustomerId: _selectedCustomerId,
          onApplyFilter: (customerId) {
            setState(() {
              _selectedCustomerId = customerId;
            });
            _loadReportData();
          },
        ),
      );
    } else {
      // Mostrar diálogo de filtro de fecha
      showDialog(
        context: context,
        builder: (context) => ReportFilterDialog(
          startDate: _startDate,
          endDate: _endDate,
          filterType: _filterType,
          onApplyFilter: (startDate, endDate, filterType) {
            setState(() {
              _startDate = startDate;
              _endDate = endDate;
              _filterType = filterType;
            });
            _loadReportData();
          },
        ),
      );
    }
  }

  // This is now a synchronous method that returns a title based on current state
  // For entity-specific titles that need async data, we'll update the UI after loading
  String getReportTitle() {
    // If it's a customer report with selected customer
    if (widget.reportType == 'customer' && _selectedCustomerId != null) {
      return 'تقرير العميل: ${getCustomerName(_selectedCustomerId!)}';
    }

    switch (widget.reportType) {
      case 'invoices':
        return 'تقرير الفواتير';
      case 'services':
        return 'تقرير طلبات الخدمة';
      case 'transactions':
        return 'تقرير المعاملات المالية';
      case 'customer':
        return widget.id != null
            ? 'تقرير العميل'  // We'll update this with the actual name after loading
            : 'تقرير العملاء';
      case 'employee':
        return 'تقرير الموظف';  // We'll update this with the actual name after loading
      case 'supplier':
        return 'تقرير المورد';  // We'll update this with the actual name after loading
      default:
        return 'تقرير تفصيلي';
    }
  }

  // Title state to be updated after async loading
  String _reportTitle = '';

  // Update report title with entity name
  Future<void> _updateReportTitle() async {
    if (widget.id != null) {
      final entityName = await getEntityName();

      if (mounted) {
        setState(() {
          switch (widget.reportType) {
            case 'invoices':
              _reportTitle = 'تقرير الفواتير - $entityName';
              break;
            case 'services':
              _reportTitle = 'تقرير طلبات الخدمة - $entityName';
              break;
            case 'transactions':
              _reportTitle = 'تقرير المعاملات المالية - $entityName';
              break;
            case 'customer':
              _reportTitle = 'تقرير العميل: $entityName';
              break;
            case 'employee':
              _reportTitle = 'تقرير الموظف: $entityName';
              break;
            case 'supplier':
              _reportTitle = 'تقرير المورد: $entityName';
              break;
            default:
              _reportTitle = 'تقرير تفصيلي';
          }
        });
      }
    } else {
      _reportTitle = getReportTitle();
    }
  }

  String getCustomerName(int customerId) {
    // Find customer in the loaded customers list
    final customer = _customers.firstWhere(
      (c) => c.localId == customerId,
      orElse: () => Customer(
        localId: customerId,
        name: 'عميل $customerId',
        type: CustomerType.individual,
        createdAt: DateTime.now(),
      ),
    );

    return customer.name;
  }

  // Funciones para construir elementos de UI
  Widget buildInvoiceItem(Invoice invoice) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${invoice.invoiceNumber} - ${invoice.customerName}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: invoice.isPaid
                        ? Colors.green.withAlpha(25)
                        : Colors.orange.withAlpha(25),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                    border: Border.all(
                      color: invoice.isPaid ? Colors.green : Colors.orange,
                    ),
                  ),
                  child: Text(
                    invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
                    style: TextStyle(
                      color: invoice.isPaid ? Colors.green : Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            Wrap(
              spacing: AppDimensions.paddingM,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'التاريخ: ${DateFormat('dd/MM/yyyy').format(invoice.date)}',
                      style: const TextStyle(color: AppColors.textSecondary),
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.attach_money,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'المبلغ: ${invoice.total.toStringAsFixed(2)} ر.س',
                      style: const TextStyle(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('عرض التفاصيل'),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('عرض تفاصيل الفاتورة ${invoice.invoiceNumber}'),
                      ),
                    );
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildServiceRequestItem(ServiceRequest serviceRequest) {
    // Define status color
    Color statusColor = Colors.grey; // Default color

    switch (serviceRequest.status) {
      case ServiceStatus.completed:
        statusColor = Colors.green;
        break;
      case ServiceStatus.inProgress:
        statusColor = Colors.orange;
        break;
      case ServiceStatus.pending:
        statusColor = Colors.blue;
        break;
      case ServiceStatus.cancelled:
        statusColor = Colors.red;
        break;
      case ServiceStatus.onHold:
        statusColor = Colors.purple;
        break;
      case ServiceStatus.followUp:
        statusColor = Colors.amber;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: ListTile(
        title: Text(
          '${serviceRequest.requestNumber} - ${serviceRequest.customerName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'النوع: ${serviceRequest.serviceType} | التاريخ: ${DateFormat('dd/MM/yyyy').format(serviceRequest.scheduledDate)}',
        ),
        trailing: Chip(
          label: Text(
            ServiceRequest.getStatusName(serviceRequest.status),
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: statusColor,
        ),
        onTap: () {
          // Navigate to service request details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('عرض تفاصيل طلب الخدمة ${serviceRequest.requestNumber}'),
            ),
          );
        },
      ),
    );
  }

  Widget buildTransactionItem(Transaction transaction) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: ListTile(
        leading: Icon(
          transaction.type == TransactionType.income
              ? Icons.arrow_upward
              : Icons.arrow_downward,
          color: transaction.type == TransactionType.income
              ? Colors.green
              : Colors.red,
        ),
        title: Text(
          transaction.description,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'التصنيف: ${transaction.category} | التاريخ: ${DateFormat('dd/MM/yyyy').format(transaction.date)}',
        ),
        trailing: Text(
          '${transaction.amount.toStringAsFixed(2)} ر.س',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: transaction.type == TransactionType.income
                ? Colors.green
                : Colors.red,
          ),
        ),
        onTap: () {
          // Navigate to transaction details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('عرض تفاصيل الإيراد ${transaction.id}'),
            ),
          );
        },
      ),
    );
  }

  Widget buildSummarySection() {
    if (_summaryData.isEmpty) {
      return const SizedBox.shrink();
    }

    switch (widget.reportType) {
      case 'invoices':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'إجمالي الفواتير',
              value: _summaryData['total'] != null && (_summaryData['total'] as double).isFinite
                ? '${NumberFormat('#,##0.00', 'ar').format(_summaryData['total'])} ر.س'
                : '0.00 ر.س',
              icon: Icons.receipt_long,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'المدفوع',
              value: _summaryData['paid'] != null && (_summaryData['paid'] as double).isFinite
                ? '${NumberFormat('#,##0.00', 'ar').format(_summaryData['paid'])} ر.س'
                : '0.00 ر.س',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'غير المدفوع',
              value: _summaryData['unpaid'] != null && (_summaryData['unpaid'] as double).isFinite
                ? '${NumberFormat('#,##0.00', 'ar').format(_summaryData['unpaid'])} ر.س'
                : '0.00 ر.س',
              icon: Icons.pending,
              color: Colors.orange,
            ),
            SummaryItem(
              title: 'عدد الفواتير',
              value: '${_summaryData['count']?.toInt()}',
              icon: Icons.numbers,
              color: Colors.blue,
            ),
          ],
        );
      case 'customer':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'إجمالي المبالغ',
              value: '${_summaryData['total']?.toStringAsFixed(2)} ر.س',
              icon: Icons.receipt_long,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'المبالغ المدفوعة',
              value: '${_summaryData['paid']?.toStringAsFixed(2)} ر.س',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'الخدمات المستهلكة',
              value: '${_summaryData['servicesTotal']?.toInt() ?? 0}',
              icon: Icons.build,
              color: Colors.orange,
            ),
            SummaryItem(
              title: 'الرصيد المتبقي',
              value: '${_summaryData['balance']?.toStringAsFixed(2)} ر.س',
              icon: Icons.account_balance_wallet,
              color: _summaryData['balance'] != null && _summaryData['balance']! >= 0
                  ? Colors.green
                  : Colors.red,
            ),
          ],
        );
      case 'services':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'إجمالي الطلبات',
              value: '${_summaryData['total']?.toInt()}',
              icon: Icons.home_repair_service,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'المكتملة',
              value: '${_summaryData['completed']?.toInt()}',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'قيد التنفيذ',
              value: '${_summaryData['inProgress']?.toInt()}',
              icon: Icons.pending_actions,
              color: Colors.orange,
            ),
            SummaryItem(
              title: 'في الانتظار',
              value: '${_summaryData['pending']?.toInt()}',
              icon: Icons.schedule,
              color: Colors.blue,
            ),
          ],
        );
      case 'transactions':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'الإيرادات',
              value: '${_summaryData['income']?.toStringAsFixed(2)} ر.س',
              icon: Icons.arrow_upward,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'المصروفات',
              value: '${_summaryData['expense']?.toStringAsFixed(2)} ر.س',
              icon: Icons.arrow_downward,
              color: Colors.red,
            ),
            SummaryItem(
              title: 'الرصيد',
              value: '${_summaryData['balance']?.toStringAsFixed(2)} ر.س',
              icon: Icons.account_balance,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'عدد المعاملات',
              value: '${_summaryData['count']?.toInt()}',
              icon: Icons.swap_horiz,
              color: Colors.blue,
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget buildReportList() {
    if (_reportData.isEmpty) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: Text(
            'لا توجد بيانات للعرض',
            style: AppTextStyles.heading3,
          ),
        ),
      );
    }

    // Calcular la altura basada en el número de elementos, con un máximo
    final itemHeight = 100.0; // Altura aproximada de cada elemento
    final maxHeight = 500.0; // Altura máxima para la lista
    final calculatedHeight = min(_reportData.length * itemHeight, maxHeight);

    switch (widget.reportType) {
      case 'invoices':
      case 'customer':
        return SizedBox(
          height: calculatedHeight,
          child: ListView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemCount: _reportData.length,
            itemBuilder: (context, index) {
              final invoice = _reportData[index] as Invoice;
              return buildInvoiceItem(invoice);
            },
          ),
        );
      case 'services':
        return SizedBox(
          height: calculatedHeight,
          child: ListView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemCount: _reportData.length,
            itemBuilder: (context, index) {
              final serviceRequest = _reportData[index] as ServiceRequest;
              return buildServiceRequestItem(serviceRequest);
            },
          ),
        );
      case 'transactions':
      case 'supplier':
      case 'employee':
        return SizedBox(
          height: calculatedHeight,
          child: ListView.builder(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemCount: _reportData.length,
            itemBuilder: (context, index) {
              final transaction = _reportData[index] as Transaction;
              return buildTransactionItem(transaction);
            },
          ),
        );
      default:
        return const SizedBox(
          height: 200,
          child: Center(
            child: Text(
              'نوع تقرير غير معروف',
              style: AppTextStyles.heading3,
            ),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_reportTitle.isNotEmpty ? _reportTitle : getReportTitle()),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: showFilterDialog,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'export_pdf') {
                _exportToPdf();
              } else if (value == 'export_excel') {
                _exportToExcel();
              } else if (value == 'print') {
                _printReport();
              } else if (value == 'share') {
                _shareReport();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red),
                    SizedBox(width: 8),
                    Text('تصدير PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_excel',
                child: Row(
                  children: [
                    Icon(Icons.table_chart, color: Colors.green),
                    SizedBox(width: 8),
                    Text('تصدير Excel'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share, color: Colors.orange),
                    SizedBox(width: 8),
                    Text('مشاركة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeArea(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: widget.reportType == 'customer'
                      ? Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (_selectedCustomerId != null)
                              Container(
                                padding: const EdgeInsets.all(AppDimensions.paddingM),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(25),
                                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                                  border: Border.all(color: AppColors.primary.withAlpha(76)),
                                ),
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundColor: AppColors.primary,
                                      child: Text(
                                        getCustomerName(_selectedCustomerId!).substring(0, 1),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: AppDimensions.paddingM),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            getCustomerName(_selectedCustomerId!),
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                            ),
                                          ),
                                          Text(
                                            'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                                            style: const TextStyle(
                                              color: AppColors.textSecondary,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.close),
                                      onPressed: () {
                                        setState(() {
                                          _selectedCustomerId = null;
                                        });
                                        _loadReportData();
                                      },
                                      tooltip: 'إزالة الفلتر',
                                    ),
                                  ],
                                ),
                              )
                            else
                              Container(
                                padding: const EdgeInsets.all(AppDimensions.paddingM),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withAlpha(25),
                                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                                  border: Border.all(color: Colors.amber.withAlpha(76)),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.info,
                                      color: Colors.amber,
                                    ),
                                    const SizedBox(width: AppDimensions.paddingM),
                                    const Expanded(
                                      child: Text(
                                        'اضغط على زر الفلتر لاختيار عميل محدد لعرض تفاصيل أكثر',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.filter_list),
                                      onPressed: showFilterDialog,
                                      tooltip: 'اختيار عميل',
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        )
                      : Text(
                          'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                          style: AppTextStyles.heading3,
                        ),
                ),
                buildSummarySection(),
                const Divider(),
                buildReportList(),
                ],
              ),
            ),
          ),
    );
  }

  // Métodos para exportar, imprimir y compartir el informe
  Future<void> _exportToPdf() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تصدير التقرير إلى PDF...'),
        ),
      );

      // استخدام الطريقة الجديدة البسيطة لإنشاء ملف PDF
      final filePath = await SimplePdfExport.createSimpleReport(
        title: getReportTitle(),
        reportType: widget.reportType,
        startDate: _startDate,
        endDate: _endDate,
        data: _reportData,
        summaryData: _cleanSummaryData(),
        context: context,
      );

      if (filePath == null) {
        throw Exception('فشل في إنشاء ملف PDF');
      }

      if (!mounted) return;

      // عرض شاشة معاينة PDF
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PdfPreviewScreen(
            filePath: filePath,
            title: getReportTitle(),
          ),
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تصدير التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al exportar a PDF: $e');
        print('Stack trace: ${StackTrace.current}');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportToExcel() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تصدير التقرير إلى Excel...'),
        ),
      );

      await ExcelExportUtil.exportReport(
        reportType: widget.reportType,
        title: getReportTitle(),
        startDate: _startDate,
        endDate: _endDate,
        data: _reportData,
        summaryData: _cleanSummaryData(),
        context: context,
      );

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تصدير التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al exportar a Excel: $e');
        print('Stack trace: ${StackTrace.current}');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _printReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تحضير الطباعة...'),
        ),
      );

      // استخدام الطريقة الجديدة للطباعة مباشرة
      final success = await SimplePdfExport.printReport(
        title: getReportTitle(),
        reportType: widget.reportType,
        startDate: _startDate,
        endDate: _endDate,
        data: _reportData,
        summaryData: _cleanSummaryData(),
      );

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال التقرير للطباعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء الطباعة أو حدث خطأ'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error printing report: $e');
        print('Stack trace: ${StackTrace.current}');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحضير الطباعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to clean summary data
  Map<String, double> _cleanSummaryData() {
    Map<String, double> cleanSummaryData = {};

    // Ensure all values are finite
    _summaryData.forEach((key, value) {
      if (value.isFinite) {
        cleanSummaryData[key] = value;
      } else {
        cleanSummaryData[key] = 0.0;
        if (kDebugMode) {
          print('Non-finite value found in _summaryData[$key]: $value, replaced with 0.0');
        }
      }
    });

    // Ensure all required keys exist
    final requiredKeys = ['total', 'paid', 'unpaid', 'count', 'balance', 'income', 'expense'];
    for (final key in requiredKeys) {
      if (!cleanSummaryData.containsKey(key)) {
        cleanSummaryData[key] = 0.0;
      }
    }

    return cleanSummaryData;
  }

  Future<void> _shareReport() async {
    try {
      // Mostrar diálogo para elegir el formato
      final format = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('اختر صيغة المشاركة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
                title: const Text('PDF'),
                subtitle: const Text('مستند PDF للقراءة والطباعة'),
                onTap: () => Navigator.pop(context, 'pdf'),
              ),
              ListTile(
                leading: const Icon(Icons.table_chart, color: Colors.green),
                title: const Text('Excel'),
                subtitle: const Text('جدول بيانات Excel للتحليل'),
                onTap: () => Navigator.pop(context, 'excel'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (format == 'pdf') {
        await _exportToPdf();
      } else if (format == 'excel') {
        await _exportToExcel();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء مشاركة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
