import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import './cash_box_repository.dart';
import '../../shared/models/employee_financial.dart';
import '../../shared/models/cash_box.dart';

/// Repository for managing employee financial transactions (salary and advances only)
class EmployeeFinancialRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();

  /// Get all employee financial records
  Future<List<EmployeeFinancial>> getAllEmployeeFinancials() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'employee_financials',
        orderBy: 'payment_date DESC, created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return EmployeeFinancial.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee financials: $e');
      }
      rethrow;
    }
  }

  /// Get employee financial records by employee ID
  Future<List<EmployeeFinancial>> getEmployeeFinancialsByEmployeeId(int employeeId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'employee_financials',
        where: 'employee_id = ?',
        whereArgs: [employeeId],
        orderBy: 'payment_date DESC, created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return EmployeeFinancial.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee financials by employee ID: $e');
      }
      rethrow;
    }
  }

  /// Get employee financial records by type (salary or advance)
  Future<List<EmployeeFinancial>> getEmployeeFinancialsByType(String type) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'employee_financials',
        where: 'type = ?',
        whereArgs: [type],
        orderBy: 'payment_date DESC, created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return EmployeeFinancial.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee financials by type: $e');
      }
      rethrow;
    }
  }

  /// Get pending employee financial records
  Future<List<EmployeeFinancial>> getPendingEmployeeFinancials() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'employee_financials',
        where: 'status = ?',
        whereArgs: ['pending'],
        orderBy: 'payment_date ASC, created_at ASC',
      );

      return List.generate(maps.length, (i) {
        return EmployeeFinancial.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting pending employee financials: $e');
      }
      rethrow;
    }
  }

  /// Insert new employee financial record
  Future<int> insertEmployeeFinancial(EmployeeFinancial employeeFinancial) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert('employee_financials', employeeFinancial.toMap());
      
      if (kDebugMode) {
        print('✅ Employee financial record inserted with ID: $id');
      }
      
      return id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error inserting employee financial: $e');
      }
      rethrow;
    }
  }

  /// Update employee financial record
  Future<int> updateEmployeeFinancial(EmployeeFinancial employeeFinancial) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'employee_financials',
        employeeFinancial.toMap(),
        where: 'id = ?',
        whereArgs: [employeeFinancial.id],
      );
      
      if (kDebugMode) {
        print('✅ Employee financial record updated');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating employee financial: $e');
      }
      rethrow;
    }
  }

  /// Process employee payment (salary or advance) through cash box
  Future<bool> processEmployeePayment({
    required int employeeId,
    required String type, // 'salary' or 'advance'
    required double amount,
    required int cashBoxId,
    String? description,
    String? paymentMethod,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.transaction((txn) async {
        // Validate employee exists
        final employeeData = await txn.query(
          'employees',
          where: 'id = ?',
          whereArgs: [employeeId],
        );
        
        if (employeeData.isEmpty) {
          throw Exception('Employee not found');
        }
        
        final employee = employeeData.first;
        final employeeName = employee['name'] as String;
        final monthlySalary = (employee['salary'] as num).toDouble();
        
        // Validate advance payment limit if it's an advance
        if (type == EmployeeFinancialType.advance) {
          final totalAdvances = await _getTotalAdvances(employeeId, txn);
          final remainingLimit = EmployeeSalaryCalculator.calculateRemainingAdvanceBalance(
            totalAdvances,
            monthlySalary,
          );
          
          if (amount > remainingLimit) {
            throw Exception('Advance amount exceeds limit. Remaining limit: $remainingLimit');
          }
        }
        
        // Create employee financial record
        final employeeFinancial = EmployeeFinancial(
          employeeId: employeeId,
          type: type,
          amount: amount,
          paymentDate: DateTime.now(),
          description: description ?? 
              (type == EmployeeFinancialType.salary 
                  ? 'Salary payment for $employeeName'
                  : 'Advance payment for $employeeName'),
          paymentMethod: paymentMethod ?? 'cash',
          cashBoxId: cashBoxId,
          status: EmployeeFinancialStatus.paid,
          createdAt: DateTime.now(),
        );
        
        // Insert employee financial record
        final financialId = await txn.insert('employee_financials', employeeFinancial.toMap());
        
        // Create cash box transaction
        final cashBoxTransaction = CashBoxTransaction(
          cashBoxId: cashBoxId,
          type: CashBoxTransactionType.expense,
          amount: amount,
          description: employeeFinancial.description!,
          reference: financialId.toString(),
          referenceType: type == EmployeeFinancialType.salary 
              ? CashBoxReferenceType.salary 
              : CashBoxReferenceType.advance,
          transactionDate: DateTime.now(),
          createdAt: DateTime.now(),
        );
        
        // Add transaction to cash box (this will update the balance)
        await _cashBoxRepository.addTransaction(cashBoxTransaction);
        
        if (kDebugMode) {
          print('✅ Employee payment processed: $type payment of $amount for employee $employeeId');
        }
        
        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error processing employee payment: $e');
      }
      rethrow;
    }
  }

  /// Get employee financial summary
  Future<EmployeeFinancialSummary> getEmployeeFinancialSummary(int employeeId) async {
    try {
      final db = await _dbHelper.database;
      
      // Get employee details
      final employeeData = await db.query(
        'employees',
        where: 'id = ?',
        whereArgs: [employeeId],
      );
      
      if (employeeData.isEmpty) {
        throw Exception('Employee not found');
      }
      
      final employee = employeeData.first;
      final employeeName = employee['name'] as String;
      final monthlySalary = (employee['salary'] as num).toDouble();
      
      // Get financial summary
      final summaryData = await db.rawQuery('''
        SELECT 
          SUM(CASE WHEN type = 'salary' AND status = 'paid' THEN amount ELSE 0 END) as total_salary_paid,
          SUM(CASE WHEN type = 'advance' AND status = 'paid' THEN amount ELSE 0 END) as total_advances_paid,
          SUM(CASE WHEN type = 'salary' AND status = 'pending' THEN amount ELSE 0 END) as pending_salary,
          SUM(CASE WHEN type = 'advance' AND status = 'pending' THEN amount ELSE 0 END) as pending_advances,
          MAX(payment_date) as last_payment_date
        FROM employee_financials 
        WHERE employee_id = ?
      ''', [employeeId]);
      
      final data = summaryData.first;
      final totalAdvancesPaid = (data['total_advances_paid'] as num?)?.toDouble() ?? 0.0;
      final remainingAdvanceLimit = EmployeeSalaryCalculator.calculateRemainingAdvanceBalance(
        totalAdvancesPaid,
        monthlySalary,
      );
      
      return EmployeeFinancialSummary(
        employeeId: employeeId,
        employeeName: employeeName,
        monthlySalary: monthlySalary,
        totalSalaryPaid: (data['total_salary_paid'] as num?)?.toDouble() ?? 0.0,
        totalAdvancesPaid: totalAdvancesPaid,
        pendingSalary: (data['pending_salary'] as num?)?.toDouble() ?? 0.0,
        pendingAdvances: (data['pending_advances'] as num?)?.toDouble() ?? 0.0,
        remainingAdvanceLimit: remainingAdvanceLimit,
        lastPaymentDate: data['last_payment_date'] != null 
            ? DateTime.parse(data['last_payment_date'] as String)
            : DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee financial summary: $e');
      }
      rethrow;
    }
  }

  /// Get total advances for an employee (helper method)
  Future<double> _getTotalAdvances(int employeeId, dynamic txn) async {
    final result = await txn.rawQuery('''
      SELECT SUM(amount) as total_advances
      FROM employee_financials 
      WHERE employee_id = ? AND type = 'advance' AND status = 'paid'
    ''', [employeeId]);
    
    return (result.first['total_advances'] as num?)?.toDouble() ?? 0.0;
  }

  /// Delete employee financial record
  Future<int> deleteEmployeeFinancial(int id) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'employee_financials',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (kDebugMode) {
        print('✅ Employee financial record deleted with ID: $id');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting employee financial: $e');
      }
      rethrow;
    }
  }

  /// Get employee financial records by date range
  Future<List<EmployeeFinancial>> getEmployeeFinancialsByDateRange({
    DateTime? startDate,
    DateTime? endDate,
    int? employeeId,
    String? type,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (startDate != null) {
        whereClause += ' AND payment_date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }
      
      if (endDate != null) {
        whereClause += ' AND payment_date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }
      
      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }
      
      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }
      
      final List<Map<String, dynamic>> maps = await db.query(
        'employee_financials',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'payment_date DESC, created_at DESC',
      );

      return List.generate(maps.length, (i) {
        return EmployeeFinancial.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting employee financials by date range: $e');
      }
      rethrow;
    }
  }
}
