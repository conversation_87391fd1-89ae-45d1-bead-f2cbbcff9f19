import 'package:flutter/foundation.dart';
import 'dart:convert'; // Import dart:convert for jsonEncode
import '../shared/models/user.dart';
import '../core/database/database_helper.dart'; // Import DatabaseHelper

enum UserStateStatus {
  initial,
  loading,
  loaded,
  error,
}

class UserState extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper(); // Use DatabaseHelper

  UserStateStatus _status = UserStateStatus.initial;
  List<User> _users = [];
  User? _selectedUser;
  String? _errorMessage;

  // Getters
  UserStateStatus get status => _status;
  List<User> get users => _users;
  User? get selectedUser => _selectedUser;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _status == UserStateStatus.loading;

  // Load all users
  Future<void> loadUsers() async {
    // Only notify if not already loading
    if (_status != UserStateStatus.loading) {
      _status = UserStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = UserStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final users = await _databaseHelper.getUsers(); // Use DatabaseHelper
      _users = users;
      _status = UserStateStatus.loaded;
    } catch (e) {
      _errorMessage = e.toString();
      _status = UserStateStatus.error;
    }

    // Use Future.microtask to avoid calling notifyListeners during build
    await Future.microtask(() => notifyListeners());
  }

  // Load user by ID - Note: DatabaseHelper currently doesn't have get user by ID.
  // We might need to add this or adjust the logic. For now, we'll keep the method
  // but it won't work correctly until implemented in DatabaseHelper or removed.
  // Assuming User model has an 'id' field that matches the database 'id' TEXT field.
  Future<void> loadUserById(String id) async { // Changed id type to String
    // Only notify if not already loading
    if (_status != UserStateStatus.loading) {
      _status = UserStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = UserStateStatus.loading;
      _errorMessage = null;
    }

    try {
      // DatabaseHelper needs a method to get user by ID.
      // For now, we'll simulate or add it later.
      // Example: final user = await _databaseHelper.getUserById(id);
      // Since it's not implemented, we'll just find in the loaded list for now (less efficient)
      final user = _users.firstWhere((u) => u.id == int.parse(id), orElse: () => throw Exception('User not found'));
      _selectedUser = user;
      _status = UserStateStatus.loaded;
    } catch (e) {
      _errorMessage = e.toString();
      _status = UserStateStatus.error;
    }

    // Use Future.microtask to avoid calling notifyListeners during build
    await Future.microtask(() => notifyListeners());
  }

  // Add a new user
  // Note: Password needs to be handled separately, likely in the UI layer before calling this.
  // Based on DatabaseHelper, it expects a User object.
  Future<bool> addUser(User user, String password) async { // Password parameter might be redundant if User model includes it
    // Only notify if not already loading
    if (_status != UserStateStatus.loading) {
      _status = UserStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = UserStateStatus.loading;
      _errorMessage = null;
    }

    try {
      // Assuming User model includes password or it's handled before calling this.
      // DatabaseHelper.insertUser expects a User object.
      final result = await _databaseHelper.insertUser(user); // Use DatabaseHelper

      if (result > 0) { // insert returns the id of the last inserted row, or 0 if no row was inserted
        // To get the full user object with ID from DB, we might need to fetch it after insertion
        // For simplicity, we'll just add the provided user object to the list for now.
        _users.add(user); // This might not have the correct DB-assigned ID if not set before insertion
        _status = UserStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل إضافة المستخدم';
        _status = UserStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = UserStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Update an existing user
  Future<bool> updateUser(User user) async {
    // Only notify if not already loading
    if (_status != UserStateStatus.loading) {
      _status = UserStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = UserStateStatus.loading;
      _errorMessage = null;
    }

    try {
      // DatabaseHelper needs an update method. Let's assume it exists or add it.
      // DatabaseHelper.updateUser expects a User object.
      final result = await _databaseHelper.updateUser(user); // Use DatabaseHelper - Need to add this method in DatabaseHelper

      if (result > 0) { // update returns the number of rows affected
        // Update in the list
        final index = _users.indexWhere((u) => u.id == user.id);
        if (index >= 0) {
          _users[index] = user; // Update with the provided user object
        }

        // Update selected user if it's the same
        if (_selectedUser?.id == user.id) {
          _selectedUser = user; // Update with the provided user object
        }

        _status = UserStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل تحديث المستخدم';
        _status = UserStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = UserStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Update user permissions
  // Assuming permissionsData is a Map<String, dynamic> that needs to be converted to JSON string
  Future<bool> updateUserPermissions(String userId, Map<String, dynamic> permissionsData) async { // Changed userId type to String
    // Only notify if not already loading
    if (_status != UserStateStatus.loading) {
      _status = UserStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = UserStateStatus.loading;
      _errorMessage = null;
    }

    try {
      // Convert permissionsData to JSON string
      final permissionsJson = jsonEncode(permissionsData); // Need to import 'dart:convert'

      final result = await _databaseHelper.updateUserPermissions(int.parse(userId), permissionsJson); // Use DatabaseHelper

      if (result > 0) { // update returns the number of rows affected
        // Find the user in the list and update their permissions
        final index = _users.indexWhere((u) => u.id == int.parse(userId));
        if (index >= 0) {
          // Assuming User model has a way to update permissions from JSON string
          // This might require modifying the User model or how permissions are handled.
          // For now, we'll just update the user object in the list.
          // A better approach might be to refetch the user from the database after update.
          // For simplicity, we'll assume the User model can be updated with the new permissions data.
          _users[index] = _users[index].copyWith(permissions: permissionsData.toString()); // Use copyWith
        }

        // Update selected user if it's the same using copyWith
        if (_selectedUser?.id == int.parse(userId)) {
           _selectedUser = _selectedUser?.copyWith(permissions: permissionsData.toString()); // Use copyWith
        }

        _status = UserStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل تحديث صلاحيات المستخدم';
        _status = UserStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = UserStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Delete a user
  Future<bool> deleteUser(String id) async { // Changed id type to String
    // Only notify if not already loading
    if (_status != UserStateStatus.loading) {
      _status = UserStateStatus.loading;
      _errorMessage = null;
      notifyListeners();
    } else {
      _status = UserStateStatus.loading;
      _errorMessage = null;
    }

    try {
      final result = await _databaseHelper.deleteUser(int.parse(id)); // Use DatabaseHelper

      if (result > 0) { // delete returns the number of rows affected
        // Remove from the list
        _users.removeWhere((user) => user.id == int.parse(id));

        // Clear selected user if it's the same
        if (_selectedUser?.id == int.parse(id)) {
          _selectedUser = null;
        }

        _status = UserStateStatus.loaded;
        await Future.microtask(() => notifyListeners());
        return true;
      } else {
        _errorMessage = 'فشل حذف المستخدم';
        _status = UserStateStatus.error;
        await Future.microtask(() => notifyListeners());
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _status = UserStateStatus.error;
      await Future.microtask(() => notifyListeners());
      return false;
    }
  }

  // Set selected user
  Future<void> setSelectedUser(User? user) async {
    _selectedUser = user;
    await Future.microtask(() => notifyListeners());
  }

  // Clear error
  Future<void> clearError() async {
    _errorMessage = null;
    await Future.microtask(() => notifyListeners());
  }
}
