import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A custom widget that renders a snowflake icon exactly matching the provided image
class ExactSnowflakeIcon extends StatelessWidget {
  final double size;
  final Color color;
  final Color backgroundColor;
  final bool showBackground;
  final bool showShadow;

  const ExactSnowflakeIcon({
    super.key,
    this.size = 48.0,
    this.color = Colors.white,
    this.backgroundColor = const Color(0xFF3498DB),
    this.showBackground = true,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: showBackground
          ? BoxDecoration(
              color: backgroundColor,
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF3498DB),
                  const Color(0xFF2980B9),
                ],
              ),
              boxShadow: showShadow
                  ? [
                      BoxShadow(
                        color: Colors.black.withAlpha(77),
                        blurRadius: size * 0.05,
                        offset: Offset(size * 0.02, size * 0.02),
                      ),
                    ]
                  : null,
            )
          : null,
      child: CustomPaint(
        size: Size(size, size),
        painter: ExactSnowflakePainter(
          color: color,
        ),
      ),
    );
  }
}

/// Custom painter for drawing the snowflake exactly as in the image
class ExactSnowflakePainter extends CustomPainter {
  final Color color;

  ExactSnowflakePainter({
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4; // Slightly smaller to fit in circle with padding

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeCap = StrokeCap.round;

    // Create a path for the filled snowflake
    final path = Path();

    // Draw the six main arms of the snowflake
    for (int i = 0; i < 6; i++) {
      final angle = (i * 60) * (math.pi / 180); // Convert degrees to radians

      // Calculate the main arm points
      final armEndPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle)
      );

      // Draw the main arm (thicker in the middle, tapered at the ends)
      drawFilledArm(path, center, armEndPoint, size.width * 0.1);

      // Calculate the branch points (about 60% along the main arm)
      final branchStartPoint = Offset(
        center.dx + (radius * 0.6) * math.cos(angle),
        center.dy + (radius * 0.6) * math.sin(angle)
      );

      // Draw the two branches
      final branchAngle1 = angle + (40 * math.pi / 180);
      final branchAngle2 = angle - (40 * math.pi / 180);

      final branchLength = radius * 0.4;

      final branch1EndPoint = Offset(
        branchStartPoint.dx + branchLength * math.cos(branchAngle1),
        branchStartPoint.dy + branchLength * math.sin(branchAngle1)
      );

      final branch2EndPoint = Offset(
        branchStartPoint.dx + branchLength * math.cos(branchAngle2),
        branchStartPoint.dy + branchLength * math.sin(branchAngle2)
      );

      drawFilledArm(path, branchStartPoint, branch1EndPoint, size.width * 0.08);
      drawFilledArm(path, branchStartPoint, branch2EndPoint, size.width * 0.08);
    }

    // Draw the center circle
    path.addOval(Rect.fromCircle(center: center, radius: size.width * 0.06));

    // Draw the filled path
    canvas.drawPath(path, paint);
  }

  /// Adds a filled arm to the path
  void drawFilledArm(Path path, Offset start, Offset end, double width) {
    // Calculate the direction vector
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;

    // Calculate the length of the arm
    final length = math.sqrt(dx * dx + dy * dy);

    // Calculate the normalized direction vector
    final dirX = dx / length;
    final dirY = dy / length;

    // Calculate the perpendicular vector
    final perpX = -dirY;
    final perpY = dirX;

    // Add a rounded cap at the end
    path.addOval(Rect.fromCircle(center: end, radius: width * 0.5));

    // Draw the main body of the arm
    final bodyPath = Path();
    bodyPath.moveTo(
      start.dx + perpX * width * 0.4,
      start.dy + perpY * width * 0.4
    );
    bodyPath.lineTo(
      end.dx + perpX * width * 0.4,
      end.dy + perpY * width * 0.4
    );
    bodyPath.lineTo(
      end.dx - perpX * width * 0.4,
      end.dy - perpY * width * 0.4
    );
    bodyPath.lineTo(
      start.dx - perpX * width * 0.4,
      start.dy - perpY * width * 0.4
    );
    bodyPath.close();

    // Add the body path
    path.addPath(bodyPath, Offset.zero);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
