import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/employee.dart';
import '../widgets/status_badge.dart';

class EmployeeDetailsScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailsScreen({
    super.key,
    required this.employee,
  });

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  late Employee _employee;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _employee = widget.employee;
  }

  void _updateEmployeeStatus(EmployeeStatus newStatus) {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _employee = _employee.copyWith(status: newStatus);
          _isLoading = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث حالة الموظف إلى ${Employee.getStatusName(newStatus)}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('بيانات ${_employee.name}'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit') {
                Navigator.pushNamed(
                  context,
                  '/employee-edit',
                  arguments: _employee,
                ).then((updatedEmployee) {
                  if (updatedEmployee != null) {
                    setState(() {
                      _employee = updatedEmployee as Employee;
                    });
                  }
                });
              } else if (value == 'activate') {
                _updateEmployeeStatus(EmployeeStatus.active);
              } else if (value == 'deactivate') {
                _updateEmployeeStatus(EmployeeStatus.inactive);
              } else if (value == 'leave') {
                _updateEmployeeStatus(EmployeeStatus.onLeave);
              } else if (value == 'report') {
                // Navigate to detailed report for this employee
                Navigator.pushNamed(
                  context,
                  AppRoutes.detailedReport,
                  arguments: {
                    'type': 'employee',
                    'id': _employee.id,
                  },
                );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20),
                    SizedBox(width: 8),
                    Text('تعديل البيانات'),
                  ],
                ),
              ),
              if (_employee.status != EmployeeStatus.active)
                const PopupMenuItem(
                  value: 'activate',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, size: 20, color: Colors.green),
                      SizedBox(width: 8),
                      Text('تفعيل الموظف'),
                    ],
                  ),
                ),
              if (_employee.status != EmployeeStatus.inactive)
                const PopupMenuItem(
                  value: 'deactivate',
                  child: Row(
                    children: [
                      Icon(Icons.cancel, size: 20, color: Colors.red),
                      SizedBox(width: 8),
                      Text('تعطيل الموظف'),
                    ],
                  ),
                ),
              if (_employee.status != EmployeeStatus.onLeave)
                const PopupMenuItem(
                  value: 'leave',
                  child: Row(
                    children: [
                      Icon(Icons.beach_access, size: 20, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('إجازة'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.bar_chart, size: 20, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('عرض التقرير التفصيلي'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeaderCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildDetailsCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildFinancialCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildAttendanceCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: AppColors.primary.withAlpha(25),
                  child: Text(
                    _employee.name.substring(0, 1),
                    style: const TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _employee.name,
                        style: AppTextStyles.heading2,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _employee.position,
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      EmployeeStatusBadge(status: _employee.status),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الاتصال',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'البريد الإلكتروني',
              _employee.email ?? 'غير متوفر',
              icon: Icons.email,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'رقم الهاتف',
              _employee.phone ?? 'غير متوفر',
              icon: Icons.phone,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'تاريخ التعيين',
              DateFormat('dd/MM/yyyy').format(_employee.joinDate),
              icon: Icons.calendar_today,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'تاريخ الإضافة',
              DateFormat('dd/MM/yyyy').format(_employee.createdAt),
              icon: Icons.access_time,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات المالية',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الراتب الشهري',
              '${_employee.salary} ر.س',
              icon: Icons.attach_money,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'إجمالي المدفوعات',
              '${_employee.salary * 12} ر.س',
              icon: Icons.payments,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'آخر دفعة',
              DateFormat('dd/MM/yyyy').format(DateTime.now().subtract(const Duration(days: 30))),
              icon: Icons.date_range,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'سجل الحضور',
                  style: AppTextStyles.heading3,
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to attendance history
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('سيتم عرض سجل الحضور الكامل'),
                      ),
                    );
                  },
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildAttendanceRow(
              DateTime.now().subtract(const Duration(days: 1)),
              '08:00 ص',
              '04:00 م',
            ),
            _buildAttendanceRow(
              DateTime.now().subtract(const Duration(days: 2)),
              '08:15 ص',
              '04:30 م',
            ),
            _buildAttendanceRow(
              DateTime.now().subtract(const Duration(days: 3)),
              '07:55 ص',
              '04:05 م',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {IconData? icon}) {
    return Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 8),
        ],
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAttendanceRow(DateTime date, String checkIn, String checkOut) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              DateFormat('dd/MM/yyyy').format(date),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              checkIn,
              style: const TextStyle(
                color: Colors.green,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              checkOut,
              style: const TextStyle(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
