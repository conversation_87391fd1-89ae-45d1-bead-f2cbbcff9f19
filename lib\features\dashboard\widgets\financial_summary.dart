import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/ui_components.dart';
import '../../../shared/animations/counter_animation.dart';

class FinancialSummary extends StatelessWidget {
  const FinancialSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return StyledCard(
      elevation: AppDimensions.elevationS,
      borderRadius: AppDimensions.radiusM,
      title: 'الملخص المالي',
      icon: Icons.insert_chart,
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          const SizedBox(height: AppDimensions.spacingS),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'إجمالي الإيرادات',
                  12500.0,
                  Icons.arrow_upward,
                  AppColors.success,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'إجمالي المصروفات',
                  8200.0,
                  Icons.arrow_downward,
                  AppColors.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'صافي الربح',
                  4300.0,
                  Icons.account_balance,
                  AppColors.primary,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  context,
                  'الفواتير المعلقة',
                  3800.0,
                  Icons.pending_actions,
                  AppColors.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String title,
    double value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.spacingS),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.spacingS),
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              boxShadow: [
                BoxShadow(
                  color: color.withAlpha(15),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                MoneyCounterAnimation(
                  endValue: value,
                  currency: 'ر.س',
                  duration: const Duration(milliseconds: 1500),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  positiveColor: AppColors.textPrimary,
                  negativeColor: AppColors.textPrimary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
