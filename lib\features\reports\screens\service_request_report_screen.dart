import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:icecorner/shared/utils/app_colors.dart';
import 'package:icecorner/shared/utils/app_dimensions.dart';
import 'package:icecorner/shared/utils/app_text_styles.dart';
import 'package:icecorner/shared/models/service_request.dart';
import 'package:icecorner/shared/models/employee.dart';
import 'package:icecorner/shared/models/service_status.dart';
import 'package:icecorner/core/repositories/service_request_repository.dart';
import 'package:icecorner/core/repositories/employee_repository.dart';
import 'package:icecorner/features/reports/utils/report_generator.dart';
import 'package:icecorner/features/reports/widgets/report_widgets.dart';
import 'package:icecorner/features/reports/screens/unified_report_screen.dart';
import 'package:fl_chart/fl_chart.dart';

/// شاشة تقارير طلبات الخدمة
class ServiceRequestReportScreen extends StatefulWidget {
  final int? employeeId; // إذا كان محددًا، سيتم عرض تقرير لفني محدد
  final ServiceStatus? statusFilter; // فلتر الحالة الافتراضي

  const ServiceRequestReportScreen({
    Key? key,
    this.employeeId,
    this.statusFilter,
  }) : super(key: key);

  @override
  State<ServiceRequestReportScreen> createState() => _ServiceRequestReportScreenState();
}

class _ServiceRequestReportScreenState extends State<ServiceRequestReportScreen> {
  // المستودعات
  final _serviceRequestRepository = ServiceRequestRepository();
  final _employeeRepository = EmployeeRepository();

  // البيانات
  List<ServiceRequest> _serviceRequests = [];
  List<Employee> _technicians = [];
  Employee? _selectedTechnician;
  bool _isLoading = true;

  // الفلاتر
  String _searchQuery = '';
  ServiceStatus? _statusFilter;
  String? _serviceTypeFilter;

  @override
  void initState() {
    super.initState();
    _statusFilter = widget.statusFilter;
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل طلبات الخدمة
      final DateTime startDate = DateTime.now().subtract(const Duration(days: 30));
      final DateTime endDate = DateTime.now();

      final requests = await _serviceRequestRepository.getServiceRequestsByDateRange(startDate, endDate);

      // تحميل الموظف الفنيين
      final employees = await _employeeRepository.getAllEmployees();
      final technicians = employees.where((e) => e.position.toLowerCase().contains('فني')).toList();

      // إذا كان هناك معرف فني محدد، ابحث عنه
      Employee? selectedTechnician;
      if (widget.employeeId != null) {
        try {
          selectedTechnician = technicians.firstWhere(
            (t) => t.id == widget.employeeId,
          );
        } catch (e) {
          // إذا لم يتم العثور على الموظف الفني، استخدم الأول إذا كان متاحًا
          if (technicians.isNotEmpty) {
            selectedTechnician = technicians.first;
          }
        }
      }

      // تحديث الحالة
      if (mounted) {
        setState(() {
          _serviceRequests = requests;
          _technicians = technicians;
          _selectedTechnician = selectedTechnician;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// طلبات الخدمة المفلترة
  List<ServiceRequest> get _filteredRequests {
    var filtered = _serviceRequests;

    // تطبيق فلتر الموظف الفني
    if (_selectedTechnician != null) {
      filtered = filtered.where((r) => r.assignedTo == _selectedTechnician!.id).toList();
    }

    // تطبيق فلتر الحالة
    if (_statusFilter != null) {
      filtered = filtered.where((r) => ServiceRequest.toServiceStatus(r.status) == _statusFilter).toList();
    }

    // تطبيق فلتر نوع الخدمة
    if (_serviceTypeFilter != null) {
      filtered = filtered.where((r) => r.serviceType == _serviceTypeFilter).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((r) {
        return r.requestNumber.toLowerCase().contains(query) ||
            r.customerName.toLowerCase().contains(query) ||
            r.description.toLowerCase().contains(query) ||
            r.address.toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }

  /// عدد الطلبات المكتملة
  int get _completedRequests {
    return _filteredRequests.where((r) => ServiceRequest.toServiceStatus(r.status) == ServiceStatus.completed).length;
  }

  /// عدد الطلبات قيد التنفيذ
  int get _inProgressRequests {
    return _filteredRequests.where((r) => ServiceRequest.toServiceStatus(r.status) == ServiceStatus.inProgress).length;
  }

  /// عدد الطلبات المعلقة
  int get _pendingRequests {
    return _filteredRequests.where((r) => ServiceRequest.toServiceStatus(r.status) == ServiceStatus.pending).length;
  }

  /// عدد الطلبات الملغاة
  int get _cancelledRequests {
    return _filteredRequests.where((r) => ServiceRequest.toServiceStatus(r.status) == ServiceStatus.cancelled).length;
  }

  /// متوسط وقت الإنجاز (بالساعات)
  double get _averageCompletionTime {
    final completedRequests = _filteredRequests.where((r) =>
      ServiceRequest.toServiceStatus(r.status) == ServiceStatus.completed).toList();
    if (completedRequests.isEmpty) {
      return 0;
    }

    double totalHours = 0;
    int count = 0;
    for (final request in completedRequests) {
      // استخدام الفرق بين تاريخ الإنشاء وتاريخ الجدولة كبديل
      final difference = request.createdAt.difference(request.scheduledDate).inHours;
      if (difference > 0) {
        totalHours += difference;
        count++;
      }
    }

    return count > 0 ? totalHours / count : 0;
  }

  /// بناء قسم الملخص
  Widget _buildSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // بطاقات الملخص
        SummaryRow(
          cards: [
            SummaryCardData(
              title: 'إجمالي الطلبات',
              value: '${_filteredRequests.length}',
              icon: Icons.assignment,
              color: AppColors.primary,
            ),
            SummaryCardData(
              title: 'مكتملة',
              value: '$_completedRequests',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            SummaryCardData(
              title: 'قيد التنفيذ',
              value: '$_inProgressRequests',
              icon: Icons.pending_actions,
              color: Colors.orange,
            ),
            SummaryCardData(
              title: 'معلقة',
              value: '$_pendingRequests',
              icon: Icons.hourglass_empty,
              color: Colors.blue,
            ),
            SummaryCardData(
              title: 'ملغاة',
              value: '$_cancelledRequests',
              icon: Icons.cancel,
              color: Colors.red,
            ),
          ],
        ),
        const SizedBox(height: 16),

        // مخطط حالة الطلبات
        if (_filteredRequests.isNotEmpty)
          PieChartWidget(
            title: 'توزيع حالة الطلبات',
            sections: [
              PieChartSectionData(
                value: _completedRequests.toDouble(),
                title: 'مكتملة',
                color: Colors.green,
                radius: 60,
                titleStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              PieChartSectionData(
                value: _inProgressRequests.toDouble(),
                title: 'قيد التنفيذ',
                color: Colors.orange,
                radius: 60,
                titleStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              PieChartSectionData(
                value: _pendingRequests.toDouble(),
                title: 'معلقة',
                color: Colors.blue,
                radius: 60,
                titleStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              PieChartSectionData(
                value: _cancelledRequests.toDouble(),
                title: 'ملغاة',
                color: Colors.red,
                radius: 60,
                titleStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        const SizedBox(height: 16),

        // مخطط أنواع الخدمات
        if (_filteredRequests.isNotEmpty)
          _buildServiceTypesChart(),
      ],
    );
  }

  /// بناء مخطط أنواع الخدمات
  Widget _buildServiceTypesChart() {
    // تجميع الطلبات حسب نوع الخدمة
    final Map<String, int> serviceTypeCounts = {};
    for (final request in _filteredRequests) {
      final serviceType = request.serviceType;
      serviceTypeCounts[serviceType] = (serviceTypeCounts[serviceType] ?? 0) + 1;
    }

    // ترتيب أنواع الخدمات حسب العدد
    final sortedTypes = serviceTypeCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // إنشاء بيانات المخطط الشريطي
    final barGroups = <BarChartGroupData>[];
    final xLabels = <String>[];

    // أخذ أعلى 5 أنواع فقط
    final topTypes = sortedTypes.take(5).toList();
    for (int i = 0; i < topTypes.length; i++) {
      final entry = topTypes[i];
      xLabels.add(entry.key);
      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: entry.value.toDouble(),
              color: AppColors.primary,
              width: 20,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
          ],
        ),
      );
    }

    return BarChartWidget(
      title: 'أنواع الخدمات الأكثر طلباً',
      barGroups: barGroups,
      xLabels: xLabels,
    );
  }

  /// بناء قائمة طلبات الخدمة
  Widget _buildServiceRequestsList() {
    if (_filteredRequests.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد طلبات خدمة مطابقة للفلاتر',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return Column(
      children: [
        // عنوان الجدول
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          color: Colors.blue.shade50,
          child: Row(
            children: const [
              Expanded(
                flex: 1,
                child: Text(
                  'الحالة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  'تفاصيل الطلب',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'التاريخ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'المبلغ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),

        // قائمة الطلبات
        Expanded(
          child: ListView.builder(
            itemCount: _filteredRequests.length,
            itemBuilder: (context, index) {
              final request = _filteredRequests[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      // حالة الطلب
                      Expanded(
                        flex: 1,
                        child: CircleAvatar(
                          backgroundColor: _getStatusColor(request.status).withAlpha(50),
                          child: Icon(
                            _getStatusIcon(request.status),
                            color: _getStatusColor(request.status),
                            size: 20,
                          ),
                        ),
                      ),

                      // تفاصيل الطلب
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'طلب #${request.requestNumber}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'العميل: ${request.customerName}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            Text(
                              'النوع: ${request.serviceType}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),

                      // التاريخ
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('dd/MM/yyyy').format(request.scheduledDate),
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'الحالة: ${_getStatusName(request.status)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: _getStatusColor(request.status),
                              ),
                            ),
                            if (request.assignedToName != null)
                              Text(
                                'الموظف الفني: ${request.assignedToName}',
                                style: const TextStyle(fontSize: 12),
                              ),
                          ],
                        ),
                      ),

                      // المبلغ
                      Expanded(
                        flex: 1,
                        child: Text(
                          request.serviceAmount.isFinite
                              ? '${NumberFormat('#,##0.00', 'ar').format(request.serviceAmount)} ر.س'
                              : '0.00 ر.س',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(ServiceRequestStatus status) {
    return ServiceRequest.getStatusColor(status);
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(ServiceRequestStatus status) {
    switch (ServiceRequest.toServiceStatus(status)) {
      case ServiceStatus.completed:
        return Icons.check_circle;
      case ServiceStatus.inProgress:
        return Icons.pending_actions;
      case ServiceStatus.pending:
        return Icons.hourglass_empty;
      case ServiceStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// الحصول على اسم الحالة
  String _getStatusName(ServiceRequestStatus status) {
    return ServiceRequest.getStatusName(status);
  }

  /// بناء قسم الفلاتر
  Widget _buildFiltersSection() {
    // جمع أنواع الخدمات الفريدة
    final serviceTypes = <String>{};
    for (final request in _serviceRequests) {
      serviceTypes.add(request.serviceType);
    }

    return Column(
      children: [
        // فلاتر الصف الأول
        Row(
          children: [
            // فلتر الموظف الفني
            Expanded(
              child: DropdownButtonFormField<Employee?>(
                decoration: const InputDecoration(
                  labelText: 'الموظف الفني',
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                ),
                value: _selectedTechnician,
                items: [
                  const DropdownMenuItem<Employee?>(
                    value: null,
                    child: Text('الكل'),
                  ),
                  ..._technicians.map((technician) {
                    return DropdownMenuItem<Employee>(
                      value: technician,
                      child: Text(technician.name),
                    );
                  }),
                ],
                onChanged: (Employee? value) {
                  setState(() {
                    _selectedTechnician = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 16),

            // فلتر الحالة
            Expanded(
              child: DropdownButtonFormField<ServiceStatus?>(
                decoration: const InputDecoration(
                  labelText: 'الحالة',
                  prefixIcon: Icon(Icons.assignment_turned_in),
                  border: OutlineInputBorder(),
                ),
                value: _statusFilter,
                items: [
                  const DropdownMenuItem<ServiceStatus?>(
                    value: null,
                    child: Text('الكل'),
                  ),
                  const DropdownMenuItem<ServiceStatus>(
                    value: ServiceStatus.completed,
                    child: Text('مكتمل'),
                  ),
                  const DropdownMenuItem<ServiceStatus>(
                    value: ServiceStatus.inProgress,
                    child: Text('قيد التنفيذ'),
                  ),
                  const DropdownMenuItem<ServiceStatus>(
                    value: ServiceStatus.pending,
                    child: Text('معلق'),
                  ),
                  const DropdownMenuItem<ServiceStatus>(
                    value: ServiceStatus.cancelled,
                    child: Text('ملغي'),
                  ),
                ],
                onChanged: (ServiceStatus? value) {
                  setState(() {
                    _statusFilter = value;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // فلاتر الصف الثاني
        Row(
          children: [
            // فلتر نوع الخدمة
            Expanded(
              child: DropdownButtonFormField<String?>(
                decoration: const InputDecoration(
                  labelText: 'نوع الخدمة',
                  prefixIcon: Icon(Icons.category),
                  border: OutlineInputBorder(),
                ),
                value: _serviceTypeFilter,
                items: [
                  const DropdownMenuItem<String?>(
                    value: null,
                    child: Text('الكل'),
                  ),
                  ...serviceTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type),
                    );
                  }),
                ],
                onChanged: (String? value) {
                  setState(() {
                    _serviceTypeFilter = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 16),

            // حقل البحث
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'بحث',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // استخدام شاشة التقارير الموحدة
    return UnifiedReportScreen(
      title: widget.employeeId != null ? 'تقرير أداء الموظف الفني' : 'تقرير طلبات الخدمة',
      reportType: ReportType.serviceRequest,
      entityId: widget.employeeId,
      filterWidget: _buildFiltersSection(),
      summaryWidget: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildSummarySection(),
      dataWidget: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildServiceRequestsList(),
    );
  }
}
