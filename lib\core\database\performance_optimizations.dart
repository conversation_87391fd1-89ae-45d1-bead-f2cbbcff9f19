import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';

/// تحسينات الأداء لقاعدة البيانات
class DatabasePerformanceOptimizer {
  static const String _tag = 'DatabasePerformanceOptimizer';

  /// إضافة فهارس لتحسين الأداء
  static Future<void> createPerformanceIndexes(Database db) async {
    try {
      if (kDebugMode) {
        print('$_tag: إنشاء فهارس الأداء...');
      }

      // فهارس جدول العملاء
      await _createIndexIfNotExists(db, 'idx_customers_name', 'customers', ['name']);
      await _createIndexIfNotExists(db, 'idx_customers_phone', 'customers', ['phone']);
      await _createIndexIfNotExists(db, 'idx_customers_email', 'customers', ['email']);
      await _createIndexIfNotExists(db, 'idx_customers_created_at', 'customers', ['created_at']);
      await _createIndexIfNotExists(db, 'idx_customers_type_status', 'customers', ['type', 'status']);

      // فهارس جدول الموظفين
      await _createIndexIfNotExists(db, 'idx_employees_name', 'employees', ['name']);
      await _createIndexIfNotExists(db, 'idx_employees_position', 'employees', ['position']);
      await _createIndexIfNotExists(db, 'idx_employees_status', 'employees', ['status']);
      await _createIndexIfNotExists(db, 'idx_employees_payment_type', 'employees', ['payment_type']);
      await _createIndexIfNotExists(db, 'idx_employees_join_date', 'employees', ['join_date']);

      // فهارس جدول طلبات الخدمة
      await _createIndexIfNotExists(db, 'idx_service_requests_customer_id', 'service_requests', ['customer_id']);
      await _createIndexIfNotExists(db, 'idx_service_requests_assigned_to', 'service_requests', ['assigned_to']);
      await _createIndexIfNotExists(db, 'idx_service_requests_status', 'service_requests', ['status']);
      await _createIndexIfNotExists(db, 'idx_service_requests_scheduled_date', 'service_requests', ['scheduled_date']);
      await _createIndexIfNotExists(db, 'idx_service_requests_completion_date', 'service_requests', ['completion_date']);
      await _createIndexIfNotExists(db, 'idx_service_requests_created_at', 'service_requests', ['created_at']);
      await _createIndexIfNotExists(db, 'idx_service_requests_reference', 'service_requests', ['request_number']);

      // فهارس جدول الفواتير
      await _createIndexIfNotExists(db, 'idx_invoices_customer_id', 'invoices', ['customer_id']);
      await _createIndexIfNotExists(db, 'idx_invoices_date', 'invoices', ['date']);
      await _createIndexIfNotExists(db, 'idx_invoices_status', 'invoices', ['status']);
      await _createIndexIfNotExists(db, 'idx_invoices_invoice_number', 'invoices', ['invoice_number']);
      await _createIndexIfNotExists(db, 'idx_invoices_created_at', 'invoices', ['created_at']);

      // فهارس جدول الصناديق
      await _createIndexIfNotExists(db, 'idx_cash_boxes_is_active', 'cash_boxes', ['is_active']);
      await _createIndexIfNotExists(db, 'idx_cash_boxes_created_at', 'cash_boxes', ['created_at']);

      // فهارس جدول معاملات الصناديق
      await _createIndexIfNotExists(db, 'idx_cash_box_transactions_cash_box_id', 'cash_box_transactions', ['cash_box_id']);
      await _createIndexIfNotExists(db, 'idx_cash_box_transactions_type', 'cash_box_transactions', ['type']);
      await _createIndexIfNotExists(db, 'idx_cash_box_transactions_date', 'cash_box_transactions', ['transaction_date']);
      await _createIndexIfNotExists(db, 'idx_cash_box_transactions_reference', 'cash_box_transactions', ['reference', 'reference_type']);

      // فهارس جدول المخزون
      await _createIndexIfNotExists(db, 'idx_inventory_name', 'inventory', ['name']);
      await _createIndexIfNotExists(db, 'idx_inventory_category', 'inventory', ['category']);
      await _createIndexIfNotExists(db, 'idx_inventory_quantity', 'inventory', ['quantity']);
      await _createIndexIfNotExists(db, 'idx_inventory_is_active', 'inventory', ['is_active']);

      // فهارس مركبة للاستعلامات الشائعة
      await _createIndexIfNotExists(db, 'idx_service_requests_status_date', 'service_requests', ['status', 'scheduled_date']);
      await _createIndexIfNotExists(db, 'idx_service_requests_assigned_status', 'service_requests', ['assigned_to', 'status']);
      await _createIndexIfNotExists(db, 'idx_cash_transactions_box_date', 'cash_box_transactions', ['cash_box_id', 'transaction_date']);
      await _createIndexIfNotExists(db, 'idx_invoices_customer_date', 'invoices', ['customer_id', 'date']);

      if (kDebugMode) {
        print('$_tag: ✅ تم إنشاء جميع فهارس الأداء بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إنشاء فهارس الأداء: $e');
      }
      rethrow;
    }
  }

  /// إنشاء فهرس إذا لم يكن موجوداً
  static Future<void> _createIndexIfNotExists(
    Database db,
    String indexName,
    String tableName,
    List<String> columns,
  ) async {
    try {
      // التحقق من وجود الفهرس
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index' AND name=?",
        [indexName],
      );

      if (result.isEmpty) {
        // إنشاء الفهرس
        final columnsStr = columns.join(', ');
        await db.execute('CREATE INDEX $indexName ON $tableName ($columnsStr)');
        
        if (kDebugMode) {
          print('$_tag: ✅ تم إنشاء فهرس: $indexName على $tableName($columnsStr)');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ⚠️ خطأ في إنشاء فهرس $indexName: $e');
      }
    }
  }

  /// تحليل أداء الاستعلامات
  static Future<void> analyzeQueryPerformance(Database db) async {
    try {
      if (kDebugMode) {
        print('$_tag: تحليل أداء الاستعلامات...');
      }

      // تحليل الجداول الرئيسية
      final tables = ['customers', 'employees', 'service_requests', 'invoices', 'cash_box_transactions'];
      
      for (final table in tables) {
        final result = await db.rawQuery('ANALYZE $table');
        if (kDebugMode) {
          print('$_tag: تم تحليل جدول $table');
        }
      }

      // إحصائيات قاعدة البيانات
      await _printDatabaseStats(db);

    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تحليل الأداء: $e');
      }
    }
  }

  /// طباعة إحصائيات قاعدة البيانات
  static Future<void> _printDatabaseStats(Database db) async {
    try {
      // حجم قاعدة البيانات
      final sizeResult = await db.rawQuery('PRAGMA page_count');
      final pageSize = await db.rawQuery('PRAGMA page_size');
      
      if (sizeResult.isNotEmpty && pageSize.isNotEmpty) {
        final pages = sizeResult.first['page_count'] as int;
        final size = pageSize.first['page_size'] as int;
        final totalSize = (pages * size / 1024 / 1024).toStringAsFixed(2);
        
        if (kDebugMode) {
          print('$_tag: 📊 حجم قاعدة البيانات: ${totalSize}MB');
        }
      }

      // عدد السجلات في كل جدول
      final tables = ['customers', 'employees', 'service_requests', 'invoices', 'cash_box_transactions'];
      
      for (final table in tables) {
        try {
          final countResult = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
          if (countResult.isNotEmpty) {
            final count = countResult.first['count'] as int;
            if (kDebugMode) {
              print('$_tag: 📋 $table: $count سجل');
            }
          }
        } catch (e) {
          // تجاهل الأخطاء للجداول غير الموجودة
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ⚠️ خطأ في جلب إحصائيات قاعدة البيانات: $e');
      }
    }
  }

  /// تنظيف قاعدة البيانات وتحسين الأداء
  static Future<void> optimizeDatabase(Database db) async {
    try {
      if (kDebugMode) {
        print('$_tag: تحسين قاعدة البيانات...');
      }

      // تنظيف المساحة الفارغة
      await db.execute('VACUUM');
      
      // إعادة تحليل الإحصائيات
      await db.execute('ANALYZE');
      
      // تحسين إعدادات الأداء
      await db.execute('PRAGMA optimize');
      
      if (kDebugMode) {
        print('$_tag: ✅ تم تحسين قاعدة البيانات بنجاح');
      }

    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تحسين قاعدة البيانات: $e');
      }
    }
  }

  /// تكوين إعدادات الأداء
  static Future<void> configurePragmaSettings(Database db) async {
    try {
      if (kDebugMode) {
        print('$_tag: تكوين إعدادات الأداء...');
      }

      // تحسين إعدادات الذاكرة
      await db.execute('PRAGMA cache_size = 10000'); // 10MB cache
      await db.execute('PRAGMA temp_store = MEMORY');
      await db.execute('PRAGMA mmap_size = 268435456'); // 256MB memory map
      
      // تحسين إعدادات الكتابة
      await db.execute('PRAGMA synchronous = NORMAL');
      await db.execute('PRAGMA journal_mode = WAL');
      
      // تحسين إعدادات الاستعلامات
      await db.execute('PRAGMA query_only = OFF');
      await db.execute('PRAGMA automatic_index = ON');
      
      if (kDebugMode) {
        print('$_tag: ✅ تم تكوين إعدادات الأداء بنجاح');
      }

    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تكوين إعدادات الأداء: $e');
      }
    }
  }

  /// تشغيل جميع تحسينات الأداء
  static Future<void> applyAllOptimizations(Database db) async {
    try {
      if (kDebugMode) {
        print('$_tag: 🚀 بدء تطبيق جميع تحسينات الأداء...');
      }

      await configurePragmaSettings(db);
      await createPerformanceIndexes(db);
      await analyzeQueryPerformance(db);
      await optimizeDatabase(db);

      if (kDebugMode) {
        print('$_tag: ✅ تم تطبيق جميع تحسينات الأداء بنجاح');
      }

    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تطبيق تحسينات الأداء: $e');
      }
      rethrow;
    }
  }
}
