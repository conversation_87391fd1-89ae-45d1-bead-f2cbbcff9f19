
import 'package:flutter/foundation.dart';

/// Helper class for handling Arabic text in PDF documents
class ArabicTextHelper {
  /// Processes Arabic text for proper display in PDF
  static String processArabicText(String text) {
    if (text.isEmpty) return '';

    try {
      // تحويل الأرقام العربية إلى أرقام إنجليزية إذا كانت موجودة
      String processedText = arabicToEnglishNumbers(text);

      // إذا كان النص يحتوي على أحرف عربية، نقوم بمعالجته
      if (containsArabic(processedText)) {
        // تقسيم النص إلى كلمات مع الحفاظ على الفواصل والرموز
        List<String> segments = splitTextPreservingPunctuation(processedText);

        // معالجة كل جزء على حدة
        for (int i = 0; i < segments.length; i++) {
          if (containsArabic(segments[i]) && !isPunctuation(segments[i])) {
            // عكس ترتيب الأحرف في الكلمات العربية فقط
            segments[i] = _reverseArabicWord(segments[i]);
          }
        }

        // عكس ترتيب الأجزاء
        segments = segments.reversed.toList();

        // إعادة تجميع النص
        processedText = segments.join('');
      }

      return processedText;
    } catch (e) {
      debugPrint('Error processing Arabic text: $e');
      // في حالة حدوث خطأ، نعيد النص الأصلي بدون معالجة
      return text;
    }
  }

  /// تقسيم النص مع الحفاظ على علامات الترقيم
  static List<String> splitTextPreservingPunctuation(String text) {
    List<String> result = [];
    String currentSegment = '';

    for (int i = 0; i < text.length; i++) {
      String char = text[i];

      if (isPunctuation(char) || char == ' ') {
        // إذا كان هناك جزء حالي، أضفه إلى النتيجة
        if (currentSegment.isNotEmpty) {
          result.add(currentSegment);
          currentSegment = '';
        }

        // أضف علامة الترقيم أو المسافة كجزء منفصل
        result.add(char);
      } else {
        // أضف الحرف إلى الجزء الحالي
        currentSegment += char;
      }
    }

    // إضافة الجزء الأخير إذا كان موجودًا
    if (currentSegment.isNotEmpty) {
      result.add(currentSegment);
    }

    return result;
  }

  /// التحقق مما إذا كان الحرف علامة ترقيم
  static bool isPunctuation(String text) {
    if (text.isEmpty) return false;

    // قائمة علامات الترقيم الشائعة
    const punctuationChars = '.,;:!?()[]{}#@&*+-/\\|"\'<>=%^_~';

    // إذا كان النص حرفًا واحدًا، تحقق مما إذا كان علامة ترقيم
    if (text.length == 1) {
      return punctuationChars.contains(text);
    }

    // إذا كان النص أطول، تحقق مما إذا كانت جميع الأحرف علامات ترقيم
    for (int i = 0; i < text.length; i++) {
      if (!punctuationChars.contains(text[i])) {
        return false;
      }
    }

    return true;
  }

  /// عكس ترتيب الأحرف في الكلمة العربية مع الحفاظ على الأرقام والرموز الخاصة
  static String _reverseArabicWord(String word) {
    try {
      // تحويل الكلمة إلى قائمة من الرموز
      List<int> runes = word.runes.toList();

      // تحديد الأرقام والرموز الخاصة
      List<MapEntry<int, int>> nonArabicChars = [];
      for (int i = 0; i < runes.length; i++) {
        int rune = runes[i];
        // إذا كان الحرف ليس عربيًا (مثل الأرقام والرموز)
        if (!_isArabicChar(rune)) {
          nonArabicChars.add(MapEntry(i, rune));
        }
      }

      // عكس ترتيب الرموز
      List<int> reversedRunes = runes.reversed.toList();

      // إعادة وضع الأرقام والرموز الخاصة في مواقعها الصحيحة
      for (var entry in nonArabicChars) {
        int originalIndex = entry.key;
        int charCode = entry.value;
        int newIndex = runes.length - 1 - originalIndex;
        reversedRunes[newIndex] = charCode;
      }

      // إعادة تحويل الرموز إلى نص
      return String.fromCharCodes(reversedRunes);
    } catch (e) {
      debugPrint('Error reversing Arabic word: $e');
      return word; // في حالة حدوث خطأ، نعيد الكلمة الأصلية
    }
  }

  /// التحقق مما إذا كان الحرف عربيًا
  static bool _isArabicChar(int charCode) {
    return (charCode >= 0x0600 && charCode <= 0x06FF) ||
        (charCode >= 0x0750 && charCode <= 0x077F) ||
        (charCode >= 0x08A0 && charCode <= 0x08FF) ||
        (charCode >= 0xFB50 && charCode <= 0xFDFF) ||
        (charCode >= 0xFE70 && charCode <= 0xFEFF);
  }

  /// التحقق مما إذا كان النص يحتوي على حروف عربية
  static bool containsArabic(String text) {
    // نطاق الحروف العربية في Unicode
    // من 0x0600 إلى 0x06FF (Arabic)
    // من 0x0750 إلى 0x077F (Arabic Supplement)
    // من 0x08A0 إلى 0x08FF (Arabic Extended-A)
    // من 0xFB50 إلى 0xFDFF (Arabic Presentation Forms-A)
    // من 0xFE70 إلى 0xFEFF (Arabic Presentation Forms-B)

    for (final rune in text.runes) {
      if ((rune >= 0x0600 && rune <= 0x06FF) ||
          (rune >= 0x0750 && rune <= 0x077F) ||
          (rune >= 0x08A0 && rune <= 0x08FF) ||
          (rune >= 0xFB50 && rune <= 0xFDFF) ||
          (rune >= 0xFE70 && rune <= 0xFEFF)) {
        return true;
      }
    }
    return false;
  }

  /// تحويل الأرقام العربية (١٢٣) إلى أرقام إنجليزية (123)
  static String arabicToEnglishNumbers(String input) {
    const Map<String, String> arabicDigits = {
      '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
      '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
    };

    String output = input;
    arabicDigits.forEach((arabic, english) {
      output = output.replaceAll(arabic, english);
    });

    return output;
  }

  /// تحويل الأرقام الإنجليزية (123) إلى أرقام عربية (١٢٣)
  static String englishToArabicNumbers(String input) {
    const Map<String, String> englishDigits = {
      '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
      '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩',
    };

    String output = input;
    englishDigits.forEach((english, arabic) {
      output = output.replaceAll(english, arabic);
    });

    return output;
  }
}
