import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// Migration to add service_categories table
class AddServiceCategoriesTable {
  static const String tableName = 'service_categories';

  /// Check if the migration is needed
  static Future<bool> isNeeded(Database db) async {
    try {
      // Check if the table already exists
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='$tableName'",
      );
      return tables.isEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if service_categories table exists: $e');
      }
      return false;
    }
  }

  /// Apply the migration
  static Future<void> migrate(Database db) async {
    try {
      if (await isNeeded(db)) {
        if (kDebugMode) {
          print('Applying migration: Adding service_categories table');
        }

        // Create service_categories table
        await db.execute('''
          CREATE TABLE $tableName(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            icon_code INTEGER,
            color INTEGER,
            status TEXT NOT NULL DEFAULT 'active',
            created_at TEXT NOT NULL,
            updated_at TEXT
          )
        ''');

        // Create indexes
        await db.execute('CREATE INDEX idx_service_categories_name ON $tableName(name)');
        await db.execute('CREATE INDEX idx_service_categories_status ON $tableName(status)');

        // Insert default service categories
        final now = DateTime.now().toIso8601String();
        
        // Default service categories
        await db.insert(tableName, {
          'name': 'تركيب مكيف',
          'description': 'تركيب وحدات تكييف جديدة',
          'icon_code': 0xe0d8, // ac_unit icon code
          'color': 0xFF2196F3, // blue color
          'status': 'active',
          'created_at': now,
        });

        await db.insert(tableName, {
          'name': 'صيانة دورية',
          'description': 'صيانة دورية لوحدات التكييف',
          'icon_code': 0xe57f, // settings icon code
          'color': 0xFF4CAF50, // green color
          'status': 'active',
          'created_at': now,
        });

        await db.insert(tableName, {
          'name': 'إصلاح أعطال',
          'description': 'إصلاح الأعطال والمشاكل في وحدات التكييف',
          'icon_code': 0xe33e, // build icon code
          'color': 0xFFFF9800, // orange color
          'status': 'active',
          'created_at': now,
        });

        await db.insert(tableName, {
          'name': 'فحص وتشخيص',
          'description': 'فحص وتشخيص مشاكل وحدات التكييف',
          'icon_code': 0xe8b6, // search icon code
          'color': 0xFF9C27B0, // purple color
          'status': 'active',
          'created_at': now,
        });

        await db.insert(tableName, {
          'name': 'تنظيف وتعقيم',
          'description': 'تنظيف وتعقيم وحدات التكييف',
          'icon_code': 0xe284, // cleaning_services icon code
          'color': 0xFF009688, // teal color
          'status': 'active',
          'created_at': now,
        });

        if (kDebugMode) {
          print('Migration completed: Added service_categories table with default categories');
        }
      } else {
        if (kDebugMode) {
          print('Migration skipped: service_categories table already exists');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error applying migration: $e');
      }
      rethrow;
    }
  }
}
