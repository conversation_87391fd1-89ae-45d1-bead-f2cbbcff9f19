import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../security/security_logger.dart';
import '../performance/memory_manager.dart';

/// مدير المزامنة المحسن مع Firebase
class EnhancedSyncManager {
  static final EnhancedSyncManager _instance = EnhancedSyncManager._internal();
  factory EnhancedSyncManager() => _instance;
  EnhancedSyncManager._internal();

  static const String _tag = 'EnhancedSyncManager';
  
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final SecurityLogger _securityLogger = SecurityLogger();
  final MemoryManager _memoryManager = MemoryManager();
  
  StreamSubscription? _connectivitySubscription;
  Timer? _periodicSyncTimer;
  
  bool _isSyncing = false;
  bool _isInitialized = false;
  
  final List<String> _tablesToSync = [
    'customers',
    'employees',
    'service_requests',
    'invoices',
    'cash_boxes',
    'cash_box_transactions',
    'inventory',
    'suppliers',
  ];
  
  final Map<String, int> _syncStats = {
    'uploaded': 0,
    'downloaded': 0,
    'conflicts': 0,
    'errors': 0,
  };

  /// تهيئة مدير المزامنة
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('$_tag: تهيئة مدير المزامنة المحسن...');
      }
      
      // مراقبة حالة الاتصال
      _connectivitySubscription = Connectivity().onConnectivityChanged.listen(_handleConnectivityChange);
      
      // جدولة المزامنة الدورية
      _periodicSyncTimer = Timer.periodic(const Duration(minutes: 15), (_) => syncIfConnected());
      
      // تسجيل التهيئة
      _memoryManager.registerTimer(_periodicSyncTimer!);
      _memoryManager.registerSubscription(_connectivitySubscription!);
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('$_tag: ✅ تم تهيئة مدير المزامنة بنجاح');
      }
      
      // التحقق من الاتصال الحالي
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        // مزامنة أولية
        syncIfConnected();
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تهيئة مدير المزامنة: $e');
      }
      
      await _securityLogger.logSystemError(
        error: 'فشل تهيئة مدير المزامنة',
        context: 'EnhancedSyncManager.initialize',
        stackTrace: e.toString(),
      );
    }
  }

  /// معالجة تغيير حالة الاتصال
  void _handleConnectivityChange(ConnectivityResult result) {
    if (result != ConnectivityResult.none) {
      if (kDebugMode) {
        print('$_tag: تم استعادة الاتصال، بدء المزامنة...');
      }
      syncIfConnected();
    } else {
      if (kDebugMode) {
        print('$_tag: تم فقدان الاتصال، توقف المزامنة');
      }
    }
  }

  /// مزامنة البيانات إذا كان هناك اتصال
  Future<void> syncIfConnected() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        await synchronizeData();
      } else {
        if (kDebugMode) {
          print('$_tag: لا يوجد اتصال، تم تأجيل المزامنة');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في التحقق من الاتصال: $e');
      }
    }
  }

  /// مزامنة البيانات مع Firebase
  Future<Map<String, int>> synchronizeData() async {
    if (_isSyncing) {
      if (kDebugMode) {
        print('$_tag: المزامنة قيد التنفيذ بالفعل');
      }
      return _syncStats;
    }
    
    _isSyncing = true;
    _resetSyncStats();
    
    try {
      if (kDebugMode) {
        print('$_tag: بدء مزامنة البيانات...');
      }
      
      await _securityLogger.logSyncOperation(
        operation: 'start',
        table: 'all',
        success: true,
      );
      
      // مزامنة كل جدول
      for (final table in _tablesToSync) {
        await _synchronizeTable(table);
      }
      
      // مزامنة عمليات الحذف
      await _synchronizeDeletes();
      
      // تنظيف عمليات المزامنة القديمة
      await _cleanupSyncQueue();
      
      if (kDebugMode) {
        print('$_tag: ✅ تمت المزامنة بنجاح');
        print('$_tag: 📊 إحصائيات المزامنة:');
        print('  - تم رفع: ${_syncStats['uploaded']} سجل');
        print('  - تم تنزيل: ${_syncStats['downloaded']} سجل');
        print('  - تعارضات: ${_syncStats['conflicts']} سجل');
        print('  - أخطاء: ${_syncStats['errors']} سجل');
      }
      
      await _securityLogger.logSyncOperation(
        operation: 'complete',
        table: 'all',
        success: true,
        recordCount: _syncStats['uploaded']! + _syncStats['downloaded']!,
      );
      
      return Map.from(_syncStats);
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في المزامنة: $e');
      }
      
      await _securityLogger.logSyncOperation(
        operation: 'error',
        table: 'all',
        success: false,
        error: e.toString(),
      );
      
      return Map.from(_syncStats);
    } finally {
      _isSyncing = false;
    }
  }

  /// مزامنة جدول محدد
  Future<void> _synchronizeTable(String tableName) async {
    try {
      if (kDebugMode) {
        print('$_tag: مزامنة جدول $tableName...');
      }
      
      // رفع السجلات المعلقة
      await _uploadPendingRecords(tableName);
      
      // تنزيل السجلات الجديدة
      await _downloadNewRecords(tableName);
      
      if (kDebugMode) {
        print('$_tag: ✅ تمت مزامنة جدول $tableName بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في مزامنة جدول $tableName: $e');
      }
      
      _syncStats['errors'] = (_syncStats['errors'] ?? 0) + 1;
      
      await _securityLogger.logSyncOperation(
        operation: 'sync_table',
        table: tableName,
        success: false,
        error: e.toString(),
      );
    }
  }

  /// رفع السجلات المعلقة
  Future<void> _uploadPendingRecords(String tableName) async {
    try {
      // جلب السجلات المعلقة للرفع
      final pendingRecords = await _dbHelper.getRecordsNeedingSync(tableName);
      
      if (pendingRecords.isEmpty) {
        return;
      }
      
      if (kDebugMode) {
        print('$_tag: رفع ${pendingRecords.length} سجل من $tableName');
      }
      
      for (final record in pendingRecords) {
        try {
          // تحويل البيانات للتخزين في Firestore
          final data = _prepareDataForFirestore(record);
          
          // التحقق من وجود معرف Firestore
          String? firestoreId = record['firestore_id'] as String?;
          
          if (firestoreId == null || firestoreId.isEmpty) {
            // إنشاء سجل جديد
            final docRef = await _firestore.collection(tableName).add(data);
            firestoreId = docRef.id;
            
            // تحديث المعرف المحلي
            await _dbHelper.updateFirestoreId(tableName, record['id'] as int, firestoreId);
          } else {
            // تحديث سجل موجود
            await _firestore.collection(tableName).doc(firestoreId).set(data, SetOptions(merge: true));
          }
          
          // تحديث حالة المزامنة
          await _dbHelper.updateSyncStatus(
            tableName,
            record['id'] as int,
            'synced',
            lastSyncTime: DateTime.now(),
          );
          
          _syncStats['uploaded'] = (_syncStats['uploaded'] ?? 0) + 1;
        } catch (e) {
          if (kDebugMode) {
            print('$_tag: ❌ خطأ في رفع سجل من $tableName: $e');
          }
          
          // تحديث حالة المزامنة
          await _dbHelper.updateSyncStatus(
            tableName,
            record['id'] as int,
            'failed',
            lastSyncTime: DateTime.now(),
          );
          
          _syncStats['errors'] = (_syncStats['errors'] ?? 0) + 1;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في رفع السجلات المعلقة من $tableName: $e');
      }
      rethrow;
    }
  }

  /// تنزيل السجلات الجديدة
  Future<void> _downloadNewRecords(String tableName) async {
    try {
      // جلب آخر وقت مزامنة
      final lastSyncTime = await _getLastSyncTime(tableName);
      
      // جلب السجلات الجديدة من Firestore
      QuerySnapshot snapshot;
      if (lastSyncTime != null) {
        snapshot = await _firestore
            .collection(tableName)
            .where('updated_at', isGreaterThan: lastSyncTime.toIso8601String())
            .get();
      } else {
        snapshot = await _firestore.collection(tableName).get();
      }
      
      if (snapshot.docs.isEmpty) {
        return;
      }
      
      if (kDebugMode) {
        print('$_tag: تنزيل ${snapshot.docs.length} سجل من $tableName');
      }
      
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          
          // التحقق من وجود السجل محلياً
          final localRecord = await _dbHelper.getRecordByFirestoreId(tableName, doc.id);
          
          if (localRecord == null) {
            // إنشاء سجل جديد
            final newData = {
              ...data,
              'firestore_id': doc.id,
              'sync_status': 'synced',
              'last_sync_time': DateTime.now().toIso8601String(),
            };
            
            final db = await _dbHelper.database;
            await db.insert(tableName, newData);
            _syncStats['downloaded'] = (_syncStats['downloaded'] ?? 0) + 1;
          } else {
            // التحقق من التعارضات
            if (localRecord['sync_status'] == 'pendingUpload') {
              // تعارض - السجل تم تعديله محلياً ولم يتم مزامنته بعد
              await _handleSyncConflict(tableName, localRecord, data, doc.id);
              _syncStats['conflicts'] = (_syncStats['conflicts'] ?? 0) + 1;
            } else {
              // تحديث السجل المحلي
              final updatedData = {
                ...data,
                'firestore_id': doc.id,
                'sync_status': 'synced',
                'last_sync_time': DateTime.now().toIso8601String(),
              };
              
              final db = await _dbHelper.database;
              await db.update(
                tableName,
                updatedData,
                where: 'id = ?',
                whereArgs: [localRecord['id']],
              );
              _syncStats['downloaded'] = (_syncStats['downloaded'] ?? 0) + 1;
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('$_tag: ❌ خطأ في تنزيل سجل من $tableName: $e');
          }
          
          _syncStats['errors'] = (_syncStats['errors'] ?? 0) + 1;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تنزيل السجلات الجديدة من $tableName: $e');
      }
      rethrow;
    }
  }

  /// معالجة تعارضات المزامنة
  Future<void> _handleSyncConflict(
    String tableName,
    Map<String, dynamic> localRecord,
    Map<String, dynamic> remoteRecord,
    String firestoreId,
  ) async {
    try {
      if (kDebugMode) {
        print('$_tag: معالجة تعارض في جدول $tableName');
      }
      
      // تسجيل التعارض
      await _dbHelper.addSyncConflict({
        'table_name': tableName,
        'record_id': localRecord['id'],
        'firestore_id': firestoreId,
        'local_data': json.encode(localRecord),
        'remote_data': json.encode(remoteRecord),
        'detected_at': DateTime.now().toIso8601String(),
        'is_resolved': 0,
      });
      
      // تحديث حالة المزامنة
      await _dbHelper.updateSyncStatus(
        tableName,
        localRecord['id'] as int,
        'conflict',
        lastSyncTime: DateTime.now(),
      );
      
      await _securityLogger.logSyncOperation(
        operation: 'conflict',
        table: tableName,
        success: false,
        error: 'تعارض بيانات',
      );
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في معالجة تعارض المزامنة: $e');
      }
    }
  }

  /// مزامنة عمليات الحذف
  Future<void> _synchronizeDeletes() async {
    try {
      for (final table in _tablesToSync) {
        // جلب السجلات المحذوفة محلياً
        final db = await _dbHelper.database;
        final deletedRecords = await db.query(
          table,
          where: 'is_deleted = ? AND sync_status = ?',
          whereArgs: [1, 'pendingUpload'],
        );
        
        if (deletedRecords.isEmpty) {
          continue;
        }
        
        if (kDebugMode) {
          print('$_tag: مزامنة ${deletedRecords.length} سجل محذوف من $table');
        }
        
        for (final record in deletedRecords) {
          try {
            final firestoreId = record['firestore_id'] as String?;
            
            if (firestoreId != null && firestoreId.isNotEmpty) {
              // حذف السجل من Firestore
              await _firestore.collection(table).doc(firestoreId).delete();
            }
            
            // تحديث حالة المزامنة
            await _dbHelper.updateSyncStatus(
              table,
              record['id'] as int,
              'synced',
              lastSyncTime: DateTime.now(),
            );
            
            _syncStats['uploaded'] = (_syncStats['uploaded'] ?? 0) + 1;
          } catch (e) {
            if (kDebugMode) {
              print('$_tag: ❌ خطأ في مزامنة سجل محذوف من $table: $e');
            }
            
            _syncStats['errors'] = (_syncStats['errors'] ?? 0) + 1;
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في مزامنة عمليات الحذف: $e');
      }
      rethrow;
    }
  }

  /// تنظيف عمليات المزامنة القديمة
  Future<void> _cleanupSyncQueue() async {
    try {
      // حذف عمليات المزامنة القديمة
      final db = await _dbHelper.database;
      await db.delete(
        'sync_queue',
        where: 'created_at < ?',
        whereArgs: [DateTime.now().subtract(const Duration(days: 7)).toIso8601String()],
      );
      
      if (kDebugMode) {
        print('$_tag: تم تنظيف عمليات المزامنة القديمة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تنظيف عمليات المزامنة القديمة: $e');
      }
    }
  }

  /// الحصول على آخر وقت مزامنة
  Future<DateTime?> _getLastSyncTime(String tableName) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.rawQuery('''
        SELECT MAX(last_sync_time) as last_sync_time
        FROM $tableName
        WHERE sync_status = 'synced'
      ''');
      
      if (result.isNotEmpty && result.first['last_sync_time'] != null) {
        return DateTime.parse(result.first['last_sync_time'] as String);
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في الحصول على آخر وقت مزامنة: $e');
      }
      return null;
    }
  }

  /// تحضير البيانات للتخزين في Firestore
  Map<String, dynamic> _prepareDataForFirestore(Map<String, dynamic> data) {
    final result = Map<String, dynamic>.from(data);
    
    // حذف الحقول الخاصة بالمزامنة
    result.remove('id');
    result.remove('firestore_id');
    result.remove('sync_status');
    result.remove('last_sync_time');
    
    // تحويل التواريخ
    for (final key in result.keys.toList()) {
      final value = result[key];
      if (value is String && _isIsoDate(value)) {
        try {
          final date = DateTime.parse(value);
          result[key] = Timestamp.fromDate(date);
        } catch (e) {
          // تجاهل الخطأ - الاحتفاظ بالقيمة كما هي
        }
      }
    }
    
    // إضافة وقت التحديث
    result['updated_at'] = Timestamp.fromDate(DateTime.now());
    
    return result;
  }

  /// التحقق من أن النص هو تاريخ ISO
  bool _isIsoDate(String text) {
    return RegExp(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}').hasMatch(text);
  }

  /// إعادة تعيين إحصائيات المزامنة
  void _resetSyncStats() {
    _syncStats['uploaded'] = 0;
    _syncStats['downloaded'] = 0;
    _syncStats['conflicts'] = 0;
    _syncStats['errors'] = 0;
  }

  /// الحصول على إحصائيات المزامنة
  Map<String, int> getSyncStats() {
    return Map.from(_syncStats);
  }

  /// التحقق من حالة المزامنة
  bool get isSyncing => _isSyncing;

  /// إيقاف مدير المزامنة
  Future<void> dispose() async {
    try {
      if (kDebugMode) {
        print('$_tag: إيقاف مدير المزامنة...');
      }
      
      _connectivitySubscription?.cancel();
      _periodicSyncTimer?.cancel();
      
      _isInitialized = false;
      
      if (kDebugMode) {
        print('$_tag: ✅ تم إيقاف مدير المزامنة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إيقاف مدير المزامنة: $e');
      }
    }
  }
}

/// مدير النسخ الاحتياطي التلقائي
class AutoBackupManager {
  static final AutoBackupManager _instance = AutoBackupManager._internal();
  factory AutoBackupManager() => _instance;
  AutoBackupManager._internal();

  static const String _tag = 'AutoBackupManager';

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final SecurityLogger _securityLogger = SecurityLogger();

  Timer? _backupTimer;
  bool _isBackingUp = false;

  /// تهيئة النسخ الاحتياطي التلقائي
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('$_tag: تهيئة النسخ الاحتياطي التلقائي...');
      }

      // جدولة النسخ الاحتياطي اليومي
      _scheduleBackup();

      if (kDebugMode) {
        print('$_tag: ✅ تم تهيئة النسخ الاحتياطي التلقائي');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تهيئة النسخ الاحتياطي: $e');
      }
    }
  }

  /// جدولة النسخ الاحتياطي
  void _scheduleBackup() {
    // النسخ الاحتياطي كل 24 ساعة
    _backupTimer = Timer.periodic(const Duration(hours: 24), (_) {
      createBackup();
    });

    // نسخة احتياطية فورية عند التهيئة
    createBackup();
  }

  /// إنشاء نسخة احتياطية
  Future<bool> createBackup() async {
    if (_isBackingUp) {
      if (kDebugMode) {
        print('$_tag: النسخ الاحتياطي قيد التنفيذ بالفعل');
      }
      return false;
    }

    _isBackingUp = true;

    try {
      if (kDebugMode) {
        print('$_tag: بدء إنشاء نسخة احتياطية...');
      }

      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final backupFileName = 'hvac_backup_$timestamp.json';

      // جلب جميع البيانات
      final backupData = await _exportAllData();

      // حفظ النسخة الاحتياطية
      final success = await _saveBackupFile(backupFileName, backupData);

      if (success) {
        await _securityLogger.logBackupOperation(
          operation: 'create',
          success: true,
          filePath: backupFileName,
          fileSize: json.encode(backupData).length,
        );

        if (kDebugMode) {
          print('$_tag: ✅ تم إنشاء النسخة الاحتياطية: $backupFileName');
        }

        // تنظيف النسخ القديمة
        await _cleanupOldBackups();

        return true;
      } else {
        throw Exception('فشل في حفظ ملف النسخة الاحتياطية');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      }

      await _securityLogger.logBackupOperation(
        operation: 'create',
        success: false,
        error: e.toString(),
      );

      return false;
    } finally {
      _isBackingUp = false;
    }
  }

  /// تصدير جميع البيانات
  Future<Map<String, dynamic>> _exportAllData() async {
    final backupData = <String, dynamic>{
      'metadata': {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'app_version': '1.0.0',
      },
      'data': {},
    };

    final tables = [
      'customers',
      'employees',
      'service_requests',
      'invoices',
      'cash_boxes',
      'cash_box_transactions',
      'inventory',
      'suppliers',
    ];

    for (final table in tables) {
      try {
        final db = await _dbHelper.database;
        final records = await db.query(table);
        backupData['data'][table] = records;

        if (kDebugMode) {
          print('$_tag: تم تصدير ${records.length} سجل من جدول $table');
        }
      } catch (e) {
        if (kDebugMode) {
          print('$_tag: ❌ خطأ في تصدير جدول $table: $e');
        }
        backupData['data'][table] = [];
      }
    }

    return backupData;
  }

  /// حفظ ملف النسخة الاحتياطية
  Future<bool> _saveBackupFile(String fileName, Map<String, dynamic> data) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupsDir = Directory('${directory.path}/backups');

      if (!await backupsDir.exists()) {
        await backupsDir.create(recursive: true);
      }

      final file = File('${backupsDir.path}/$fileName');
      final jsonString = json.encode(data);

      await file.writeAsString(jsonString);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في حفظ ملف النسخة الاحتياطية: $e');
      }
      return false;
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  Future<void> _cleanupOldBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupsDir = Directory('${directory.path}/backups');

      if (!await backupsDir.exists()) return;

      final backupFiles = await backupsDir
          .list()
          .where((entity) => entity is File && entity.path.contains('hvac_backup_'))
          .cast<File>()
          .toList();

      // ترتيب الملفات حسب تاريخ التعديل
      backupFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // الاحتفاظ بآخر 7 نسخ احتياطية
      const maxBackups = 7;
      if (backupFiles.length > maxBackups) {
        for (int i = maxBackups; i < backupFiles.length; i++) {
          await backupFiles[i].delete();
          if (kDebugMode) {
            print('$_tag: تم حذف النسخة الاحتياطية القديمة: ${backupFiles[i].path}');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
      }
    }
  }

  /// استعادة البيانات من نسخة احتياطية
  Future<bool> restoreFromBackup(String backupFileName) async {
    try {
      if (kDebugMode) {
        print('$_tag: بدء استعادة البيانات من: $backupFileName');
      }

      final directory = await getApplicationDocumentsDirectory();
      final backupFile = File('${directory.path}/backups/$backupFileName');

      if (!await backupFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      final jsonString = await backupFile.readAsString();
      final backupData = json.decode(jsonString) as Map<String, dynamic>;

      // التحقق من صحة البيانات
      if (!_validateBackupData(backupData)) {
        throw Exception('ملف النسخة الاحتياطية تالف أو غير صحيح');
      }

      // استعادة البيانات
      await _restoreData(backupData['data'] as Map<String, dynamic>);

      await _securityLogger.logBackupOperation(
        operation: 'restore',
        success: true,
        filePath: backupFileName,
      );

      if (kDebugMode) {
        print('$_tag: ✅ تم استعادة البيانات بنجاح من: $backupFileName');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في استعادة البيانات: $e');
      }

      await _securityLogger.logBackupOperation(
        operation: 'restore',
        success: false,
        filePath: backupFileName,
        error: e.toString(),
      );

      return false;
    }
  }

  /// التحقق من صحة بيانات النسخة الاحتياطية
  bool _validateBackupData(Map<String, dynamic> backupData) {
    try {
      // التحقق من وجود البيانات الأساسية
      if (!backupData.containsKey('metadata') || !backupData.containsKey('data')) {
        return false;
      }

      final metadata = backupData['metadata'] as Map<String, dynamic>;
      if (!metadata.containsKey('version') || !metadata.containsKey('created_at')) {
        return false;
      }

      final data = backupData['data'] as Map<String, dynamic>;
      if (data.isEmpty) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// استعادة البيانات
  Future<void> _restoreData(Map<String, dynamic> data) async {
    final db = await _dbHelper.database;

    await db.transaction((txn) async {
      for (final tableName in data.keys) {
        try {
          final records = data[tableName] as List<dynamic>;

          // مسح البيانات الحالية
          await txn.delete(tableName);

          // إدراج البيانات المستعادة
          for (final record in records) {
            await txn.insert(tableName, Map<String, dynamic>.from(record));
          }

          if (kDebugMode) {
            print('$_tag: تم استعادة ${records.length} سجل في جدول $tableName');
          }
        } catch (e) {
          if (kDebugMode) {
            print('$_tag: ❌ خطأ في استعادة جدول $tableName: $e');
          }
        }
      }
    });
  }

  /// الحصول على قائمة النسخ الاحتياطية المتاحة
  Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupsDir = Directory('${directory.path}/backups');

      if (!await backupsDir.exists()) {
        return [];
      }

      final backupFiles = await backupsDir
          .list()
          .where((entity) => entity is File && entity.path.contains('hvac_backup_'))
          .cast<File>()
          .toList();

      final backups = <BackupInfo>[];

      for (final file in backupFiles) {
        try {
          final stat = await file.stat();
          final fileName = file.path.split('/').last;

          backups.add(BackupInfo(
            fileName: fileName,
            filePath: file.path,
            size: stat.size,
            createdAt: stat.modified,
          ));
        } catch (e) {
          if (kDebugMode) {
            print('$_tag: ❌ خطأ في قراءة معلومات ملف النسخة الاحتياطية: $e');
          }
        }
      }

      // ترتيب حسب تاريخ الإنشاء
      backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return backups;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في الحصول على قائمة النسخ الاحتياطية: $e');
      }
      return [];
    }
  }

  /// إيقاف مدير النسخ الاحتياطي
  void dispose() {
    _backupTimer?.cancel();
    _backupTimer = null;
  }
}

/// معلومات النسخة الاحتياطية
class BackupInfo {
  final String fileName;
  final String filePath;
  final int size;
  final DateTime createdAt;

  BackupInfo({
    required this.fileName,
    required this.filePath,
    required this.size,
    required this.createdAt,
  });

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String get formattedDate {
    return DateFormat('dd/MM/yyyy HH:mm').format(createdAt);
  }
}
