import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/account_transfer.dart';
import '../../../shared/models/bank_account.dart';
import '../../../shared/models/employee.dart';
import '../../../core/repositories/account_transfer_repository.dart';
import '../../../core/repositories/bank_account_repository.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../core/repositories/employee_balance_repository.dart';


class AccountTransferScreen extends StatefulWidget {
  const AccountTransferScreen({super.key});

  @override
  State<AccountTransferScreen> createState() => _AccountTransferScreenState();
}

class _AccountTransferScreenState extends State<AccountTransferScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountTransferRepository = AccountTransferRepository();
  final _bankAccountRepository = BankAccountRepository();
  final _employeeRepository = EmployeeRepository();
  final _employeeBalanceRepository = EmployeeBalanceRepository();

  bool _isLoading = true;
  List<BankAccount> _bankAccounts = [];
  List<Employee> _employees = [];

  // نوع التحويل
  String _transferType = 'employee_to_employee';

  // المصدر
  TransferEntityType _sourceType = TransferEntityType.employee;
  int? _sourceEmployeeId;
  int? _sourceBankAccountId;
  String _sourceBalance = '0.0';

  // الوجهة
  TransferEntityType _destinationType = TransferEntityType.employee;
  int? _destinationEmployeeId;
  int? _destinationBankAccountId;
  String _destinationBalance = '0.0';

  // تفاصيل التحويل
  final _amountController = TextEditingController();
  final _reasonController = TextEditingController();
  DateTime _transferDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  // تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحسابات البنكية
      final bankAccounts = await _bankAccountRepository.getAllBankAccounts();

      // تحميل الموظفين
      final employees = await _employeeRepository.getAllEmployees();

      if (mounted) {
        setState(() {
          _bankAccounts = bankAccounts;
          _employees = employees;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات: $e');
      }
    }
  }

  // تحديث نوع التحويل
  void _updateTransferType(String? value) {
    if (value == null) return;

    setState(() {
      _transferType = value;

      // تحديث نوع المصدر والوجهة بناءً على نوع التحويل
      switch (value) {
        case 'employee_to_employee':
          _sourceType = TransferEntityType.employee;
          _destinationType = TransferEntityType.employee;
          break;
        case 'employee_to_bank':
          _sourceType = TransferEntityType.employee;
          _destinationType = TransferEntityType.bankAccount;
          break;
        case 'bank_to_employee':
          _sourceType = TransferEntityType.bankAccount;
          _destinationType = TransferEntityType.employee;
          break;
        case 'bank_to_bank':
          _sourceType = TransferEntityType.bankAccount;
          _destinationType = TransferEntityType.bankAccount;
          break;
      }

      // إعادة تعيين القيم
      _sourceEmployeeId = null;
      _sourceBankAccountId = null;
      _destinationEmployeeId = null;
      _destinationBankAccountId = null;
      _sourceBalance = '0.0';
      _destinationBalance = '0.0';
    });
  }

  // تحديث المصدر
  Future<void> _updateSource(int? value) async {
    if (value == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_sourceType == TransferEntityType.employee) {
        _sourceEmployeeId = value;
        // استرجاع رصيد الموظف (العهدة)
        final balance = await _employeeBalanceRepository.getEmployeeBalance(value);
        setState(() {
          _sourceBalance = balance.toStringAsFixed(2);
          _isLoading = false;
        });
      } else {
        _sourceBankAccountId = value;
        // استرجاع رصيد الحساب البنكي
        final account = _bankAccounts.firstWhere(
          (a) => a.id == value,
          orElse: () => BankAccount(
            bankName: '',
            accountNumber: '',
            accountName: '',
            type: BankAccountType.checking,
            isActive: true,
            balance: 0.0,
            createdAt: DateTime.now(),
          ),
        );
        setState(() {
          _sourceBalance = account.balance.toStringAsFixed(2);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _sourceBalance = '0.0';
          _isLoading = false;
        });
        _showErrorSnackBar('حدث خطأ أثناء استرجاع الرصيد: $e');
      }
    }
  }

  // تحديث الوجهة
  Future<void> _updateDestination(int? value) async {
    if (value == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_destinationType == TransferEntityType.employee) {
        _destinationEmployeeId = value;
        // استرجاع رصيد الموظف (العهدة)
        final balance = await _employeeBalanceRepository.getEmployeeBalance(value);
        setState(() {
          _destinationBalance = balance.toStringAsFixed(2);
          _isLoading = false;
        });
      } else {
        _destinationBankAccountId = value;
        // استرجاع رصيد الحساب البنكي
        final account = _bankAccounts.firstWhere(
          (a) => a.id == value,
          orElse: () => BankAccount(
            bankName: '',
            accountNumber: '',
            accountName: '',
            type: BankAccountType.checking,
            isActive: true,
            balance: 0.0,
            createdAt: DateTime.now(),
          ),
        );
        setState(() {
          _destinationBalance = account.balance.toStringAsFixed(2);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _destinationBalance = '0.0';
          _isLoading = false;
        });
        _showErrorSnackBar('حدث خطأ أثناء استرجاع الرصيد: $e');
      }
    }
  }

  // تنفيذ التحويل
  Future<void> _executeTransfer() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من اختيار المصدر والوجهة
    if (_sourceType == TransferEntityType.employee && _sourceEmployeeId == null) {
      _showErrorSnackBar('الرجاء اختيار الموظف المصدر');
      return;
    }
    if (_sourceType == TransferEntityType.bankAccount && _sourceBankAccountId == null) {
      _showErrorSnackBar('الرجاء اختيار الحساب البنكي المصدر');
      return;
    }
    if (_destinationType == TransferEntityType.employee && _destinationEmployeeId == null) {
      _showErrorSnackBar('الرجاء اختيار الموظف الوجهة');
      return;
    }
    if (_destinationType == TransferEntityType.bankAccount && _destinationBankAccountId == null) {
      _showErrorSnackBar('الرجاء اختيار الحساب البنكي الوجهة');
      return;
    }

    // التحقق من أن المصدر والوجهة مختلفان
    if (_sourceType == _destinationType) {
      if (_sourceType == TransferEntityType.employee && _sourceEmployeeId == _destinationEmployeeId) {
        _showErrorSnackBar('لا يمكن التحويل إلى نفس الموظف');
        return;
      }
      if (_sourceType == TransferEntityType.bankAccount && _sourceBankAccountId == _destinationBankAccountId) {
        _showErrorSnackBar('لا يمكن التحويل إلى نفس الحساب البنكي');
        return;
      }
    }

    // التحقق من المبلغ
    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      _showErrorSnackBar('الرجاء إدخال مبلغ صحيح');
      return;
    }

    // التحقق من كفاية الرصيد
    final sourceBalance = double.tryParse(_sourceBalance) ?? 0.0;
    if (amount > sourceBalance) {
      _showErrorSnackBar('الرصيد غير كافٍ لإجراء التحويل');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء كائن التحويل
      final transfer = AccountTransfer(
        reference: _generateReference(),
        sourceType: _sourceType,
        sourceId: _sourceType == TransferEntityType.employee
            ? _sourceEmployeeId!
            : _sourceBankAccountId!,
        sourceName: _getSourceName(),
        destinationType: _destinationType,
        destinationId: _destinationType == TransferEntityType.employee
            ? _destinationEmployeeId!
            : _destinationBankAccountId!,
        destinationName: _getDestinationName(),
        amount: amount,
        date: _transferDate,
        reason: _reasonController.text,
        status: TransferStatus.completed,
        createdAt: DateTime.now(),
      );

      // تنفيذ التحويل
      final result = await _accountTransferRepository.insertTransfer(transfer);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (result > 0) {
          // إعادة تحميل البيانات
          await _loadData();

          // إعادة تعيين النموذج
          _resetForm();

          // عرض رسالة نجاح
          _showSuccessSnackBar('تم تنفيذ التحويل بنجاح');
        } else {
          _showErrorSnackBar('فشل تنفيذ التحويل');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('حدث خطأ أثناء تنفيذ التحويل: $e');
      }
    }
  }

  // إنشاء مرجع فريد للتحويل
  String _generateReference() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString().substring(6);
    return 'TRF-$timestamp';
  }

  // الحصول على اسم المصدر
  String _getSourceName() {
    if (_sourceType == TransferEntityType.employee) {
      final employee = _employees.firstWhere(
        (e) => e.id == _sourceEmployeeId,
        orElse: () => Employee(
          name: 'غير معروف',
          position: '',
          joinDate: DateTime.now(),
          salary: 0,
          status: EmployeeStatus.active,
          createdAt: DateTime.now(),
        ),
      );
      return employee.name;
    } else {
      final account = _bankAccounts.firstWhere(
        (a) => a.id == _sourceBankAccountId,
        orElse: () => BankAccount(
          bankName: '',
          accountNumber: '',
          accountName: 'غير معروف',
          type: BankAccountType.checking,
          isActive: true,
          balance: 0.0,
          createdAt: DateTime.now(),
        ),
      );
      return account.accountName;
    }
  }

  // الحصول على اسم الوجهة
  String _getDestinationName() {
    if (_destinationType == TransferEntityType.employee) {
      final employee = _employees.firstWhere(
        (e) => e.id == _destinationEmployeeId,
        orElse: () => Employee(
          name: 'غير معروف',
          position: '',
          joinDate: DateTime.now(),
          salary: 0,
          status: EmployeeStatus.active,
          createdAt: DateTime.now(),
        ),
      );
      return employee.name;
    } else {
      final account = _bankAccounts.firstWhere(
        (a) => a.id == _destinationBankAccountId,
        orElse: () => BankAccount(
          bankName: '',
          accountNumber: '',
          accountName: 'غير معروف',
          type: BankAccountType.checking,
          isActive: true,
          balance: 0.0,
          createdAt: DateTime.now(),
        ),
      );
      return account.accountName;
    }
  }

  // إعادة تعيين النموذج
  void _resetForm() {
    _formKey.currentState?.reset();
    _amountController.clear();
    _reasonController.clear();
    setState(() {
      _transferType = 'employee_to_employee';
      _sourceType = TransferEntityType.employee;
      _destinationType = TransferEntityType.employee;
      _sourceEmployeeId = null;
      _sourceBankAccountId = null;
      _destinationEmployeeId = null;
      _destinationBankAccountId = null;
      _sourceBalance = '0.0';
      _destinationBalance = '0.0';
      _transferDate = DateTime.now();
    });
  }

  // عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  // بناء قسم نوع التحويل
  Widget _buildTransferTypeSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نوع التحويل',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                hintText: 'اختر نوع التحويل',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
              value: _transferType,
              items: const [
                DropdownMenuItem(
                  value: 'employee_to_employee',
                  child: Text('من موظف إلى موظف'),
                ),
                DropdownMenuItem(
                  value: 'employee_to_bank',
                  child: Text('من موظف إلى حساب بنكي'),
                ),
                DropdownMenuItem(
                  value: 'bank_to_employee',
                  child: Text('من حساب بنكي إلى موظف'),
                ),
                DropdownMenuItem(
                  value: 'bank_to_bank',
                  child: Text('من حساب بنكي إلى حساب بنكي'),
                ),
              ],
              onChanged: _updateTransferType,
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم المصدر
  Widget _buildSourceSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'المصدر',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'الرصيد: $_sourceBalance ر.س',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: double.parse(_sourceBalance) > 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            if (_sourceType == TransferEntityType.employee)
              DropdownButtonFormField<int>(
                decoration: InputDecoration(
                  hintText: 'اختر الموظف المصدر',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                value: _sourceEmployeeId,
                items: _employees
                    .map((e) => DropdownMenuItem(
                          value: e.id,
                          child: Text(e.name),
                        ))
                    .toList(),
                onChanged: _updateSource,
              )
            else
              DropdownButtonFormField<int>(
                decoration: InputDecoration(
                  hintText: 'اختر الحساب البنكي المصدر',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                value: _sourceBankAccountId,
                items: _bankAccounts
                    .map((a) => DropdownMenuItem(
                          value: a.id,
                          child: Text('${a.bankName} - ${a.accountName}'),
                        ))
                    .toList(),
                onChanged: _updateSource,
              ),
          ],
        ),
      ),
    );
  }

  // بناء قسم الوجهة
  Widget _buildDestinationSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الوجهة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'الرصيد: $_destinationBalance ر.س',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: double.parse(_destinationBalance) > 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            if (_destinationType == TransferEntityType.employee)
              DropdownButtonFormField<int>(
                decoration: InputDecoration(
                  hintText: 'اختر الموظف الوجهة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                value: _destinationEmployeeId,
                items: _employees
                    .map((e) => DropdownMenuItem(
                          value: e.id,
                          child: Text(e.name),
                        ))
                    .toList(),
                onChanged: _updateDestination,
              )
            else
              DropdownButtonFormField<int>(
                decoration: InputDecoration(
                  hintText: 'اختر الحساب البنكي الوجهة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                value: _destinationBankAccountId,
                items: _bankAccounts
                    .map((a) => DropdownMenuItem(
                          value: a.id,
                          child: Text('${a.bankName} - ${a.accountName}'),
                        ))
                    .toList(),
                onChanged: _updateDestination,
              ),
          ],
        ),
      ),
    );
  }

  // بناء قسم تفاصيل التحويل
  Widget _buildTransferDetailsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل التحويل',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            // حقل المبلغ
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText: 'المبلغ',
                hintText: 'أدخل المبلغ',
                prefixIcon: const Icon(Icons.attach_money),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال المبلغ';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'الرجاء إدخال مبلغ صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: AppDimensions.paddingM),
            // حقل التاريخ
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _transferDate,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now().add(const Duration(days: 1)),
                );
                if (date != null) {
                  setState(() {
                    _transferDate = date;
                  });
                }
              },
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'تاريخ التحويل',
                  prefixIcon: const Icon(Icons.calendar_today),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: Text(
                  DateFormat('yyyy/MM/dd').format(_transferDate),
                ),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            // حقل السبب
            TextFormField(
              controller: _reasonController,
              decoration: InputDecoration(
                labelText: 'سبب التحويل',
                hintText: 'أدخل سبب التحويل',
                prefixIcon: const Icon(Icons.description),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  // بناء زر التنفيذ
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _executeTransfer,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: const Text(
          'تنفيذ التحويل',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التحويلات المالية'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTransferTypeSection(),
                    const SizedBox(height: AppDimensions.paddingL),
                    _buildSourceSection(),
                    const SizedBox(height: AppDimensions.paddingL),
                    _buildDestinationSection(),
                    const SizedBox(height: AppDimensions.paddingL),
                    _buildTransferDetailsSection(),
                    const SizedBox(height: AppDimensions.paddingXL),
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
    );
  }
}
