import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/account_transfer.dart';
import './bank_account_repository.dart';
import './employee_repository.dart';

class AccountTransferRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final BankAccountRepository _bankAccountRepository = BankAccountRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();

  // Ensure the account_transfers table exists
  Future<void> ensureTableExists() async {
    try {
      final db = await _dbHelper.database;

      // Check if the table exists
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='account_transfers'"
      );

      if (tables.isEmpty) {
        debugPrint('Creating account_transfers table as it does not exist');

        // Create the table if it doesn't exist
        await db.execute('''
          CREATE TABLE IF NOT EXISTS account_transfers(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reference TEXT NOT NULL,
            source_type TEXT NOT NULL,
            source_id INTEGER NOT NULL,
            destination_type TEXT NOT NULL,
            destination_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            date TEXT NOT NULL,
            reason TEXT,
            status TEXT NOT NULL,
            notes TEXT,
            created_by INTEGER,
            created_at TEXT NOT NULL,
            updated_at TEXT
          )
        ''');

        debugPrint('account_transfers table created successfully');
      } else {
        debugPrint('account_transfers table already exists');
      }
    } catch (e) {
      debugPrint('Error ensuring account_transfers table exists: $e');
    }
  }

  // Get all transfers
  Future<List<AccountTransfer>> getAllTransfers() async {
    try {
      await ensureTableExists();
      
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('account_transfers');

      return await Future.wait(maps.map((map) async {
        final transfer = AccountTransfer.fromMap(map);
        
        // Get source name
        String sourceName = 'غير معروف';
        if (transfer.sourceType == TransferEntityType.bankAccount) {
          final account = await _bankAccountRepository.getBankAccountById(transfer.sourceId);
          if (account != null) {
            sourceName = account.accountName;
          }
        } else if (transfer.sourceType == TransferEntityType.employee) {
          final employee = await _employeeRepository.getEmployeeById(transfer.sourceId);
          if (employee != null) {
            sourceName = employee.name;
          }
        }
        
        // Get destination name
        String destinationName = 'غير معروف';
        if (transfer.destinationType == TransferEntityType.bankAccount) {
          final account = await _bankAccountRepository.getBankAccountById(transfer.destinationId);
          if (account != null) {
            destinationName = account.accountName;
          }
        } else if (transfer.destinationType == TransferEntityType.employee) {
          final employee = await _employeeRepository.getEmployeeById(transfer.destinationId);
          if (employee != null) {
            destinationName = employee.name;
          }
        }
        
        return transfer.copyWith(
          sourceName: sourceName,
          destinationName: destinationName,
        );
      }).toList());
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transfers: $e');
      }
      return [];
    }
  }

  // Get transfer by ID
  Future<AccountTransfer?> getTransferById(int id) async {
    try {
      await ensureTableExists();
      
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'account_transfers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        final transfer = AccountTransfer.fromMap(maps.first);
        
        // Get source name
        String sourceName = 'غير معروف';
        if (transfer.sourceType == TransferEntityType.bankAccount) {
          final account = await _bankAccountRepository.getBankAccountById(transfer.sourceId);
          if (account != null) {
            sourceName = account.accountName;
          }
        } else if (transfer.sourceType == TransferEntityType.employee) {
          final employee = await _employeeRepository.getEmployeeById(transfer.sourceId);
          if (employee != null) {
            sourceName = employee.name;
          }
        }
        
        // Get destination name
        String destinationName = 'غير معروف';
        if (transfer.destinationType == TransferEntityType.bankAccount) {
          final account = await _bankAccountRepository.getBankAccountById(transfer.destinationId);
          if (account != null) {
            destinationName = account.accountName;
          }
        } else if (transfer.destinationType == TransferEntityType.employee) {
          final employee = await _employeeRepository.getEmployeeById(transfer.destinationId);
          if (employee != null) {
            destinationName = employee.name;
          }
        }
        
        return transfer.copyWith(
          sourceName: sourceName,
          destinationName: destinationName,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transfer by ID: $e');
      }
      return null;
    }
  }

  // Get transfers by date range
  Future<List<AccountTransfer>> getTransfersByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      await ensureTableExists();
      
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'account_transfers',
        where: 'date BETWEEN ? AND ?',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      );

      return await Future.wait(maps.map((map) async {
        final transfer = AccountTransfer.fromMap(map);
        
        // Get source name
        String sourceName = 'غير معروف';
        if (transfer.sourceType == TransferEntityType.bankAccount) {
          final account = await _bankAccountRepository.getBankAccountById(transfer.sourceId);
          if (account != null) {
            sourceName = account.accountName;
          }
        } else if (transfer.sourceType == TransferEntityType.employee) {
          final employee = await _employeeRepository.getEmployeeById(transfer.sourceId);
          if (employee != null) {
            sourceName = employee.name;
          }
        }
        
        // Get destination name
        String destinationName = 'غير معروف';
        if (transfer.destinationType == TransferEntityType.bankAccount) {
          final account = await _bankAccountRepository.getBankAccountById(transfer.destinationId);
          if (account != null) {
            destinationName = account.accountName;
          }
        } else if (transfer.destinationType == TransferEntityType.employee) {
          final employee = await _employeeRepository.getEmployeeById(transfer.destinationId);
          if (employee != null) {
            destinationName = employee.name;
          }
        }
        
        return transfer.copyWith(
          sourceName: sourceName,
          destinationName: destinationName,
        );
      }).toList());
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transfers by date range: $e');
      }
      return [];
    }
  }

  // Insert a new transfer
  Future<int> insertTransfer(AccountTransfer transfer) async {
    try {
      await ensureTableExists();
      
      final db = await _dbHelper.database;
      
      int transferId = -1;
      
      await db.transaction((txn) async {
        // Insert the transfer
        transferId = await txn.insert(
          'account_transfers',
          {
            'reference': transfer.reference,
            'source_type': transfer.sourceType.toString().split('.').last,
            'source_id': transfer.sourceId,
            'destination_type': transfer.destinationType.toString().split('.').last,
            'destination_id': transfer.destinationId,
            'amount': transfer.amount,
            'date': transfer.date.toIso8601String(),
            'reason': transfer.reason,
            'status': transfer.status.toString().split('.').last,
            'notes': transfer.notes,
            'created_by': transfer.createdBy,
            'created_at': DateTime.now().toIso8601String(),
          },
        );
        
        // Update source balance
        if (transfer.sourceType == TransferEntityType.bankAccount) {
          await txn.rawUpdate('''
            UPDATE bank_accounts
            SET balance = balance - ?
            WHERE id = ?
          ''', [transfer.amount, transfer.sourceId]);
        }
        
        // Update destination balance
        if (transfer.destinationType == TransferEntityType.bankAccount) {
          await txn.rawUpdate('''
            UPDATE bank_accounts
            SET balance = balance + ?
            WHERE id = ?
          ''', [transfer.amount, transfer.destinationId]);
        }
      });
      
      return transferId;
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting transfer: $e');
      }
      return -1;
    }
  }
}
