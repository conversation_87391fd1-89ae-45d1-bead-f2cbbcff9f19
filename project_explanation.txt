# شرح مشروع نظام إدارة خدمات التكييف والتبريد - ركن الجليد

## 1. نظرة عامة على المشروع

### ما هو التطبيق؟
تطبيق "ركن الجليد" هو نظام إدارة شامل لخدمات التكييف والتبريد مطور باستخدام Flutter. يهدف التطبيق إلى تسهيل إدارة جميع جوانب أعمال شركات التكييف والتبريد من خلال واجهة موحدة وسهلة الاستخدام.

### الهدف من التطبيق:
- إدارة طلبات الخدمة والصيانة
- متابعة العملاء وبياناتهم
- إدارة المخزون وقطع الغيار
- إنشاء الفواتير والتقارير المالية
- جدولة المواعيد والإشعارات
- إدارة الموظفين والصلاحيات

## 2. البنية التقنية للمشروع

### التقنيات المستخدمة:
- **Flutter**: إطار العمل الأساسي لتطوير التطبيق
- **Dart**: لغة البرمجة المستخدمة
- **SQLite**: قاعدة البيانات المحلية
- **Provider**: إدارة الحالة (State Management)
- **PDF Generation**: لإنشاء التقارير والفواتير
- **Local Notifications**: للإشعارات والتذكيرات

### هيكل المجلدات الرئيسية:

#### lib/ - المجلد الرئيسي للكود:
```
lib/
├── main.dart                 # نقطة البداية للتطبيق
├── app.dart                  # إعداد التطبيق الرئيسي
├── config/                   # ملفات الإعدادات
│   ├── constants.dart        # الثوابت والقيم الثابتة
│   ├── routes.dart          # مسارات التنقل
│   ├── theme.dart           # تصميم التطبيق
│   └── localization.dart    # إعدادات اللغة
├── core/                    # الوظائف الأساسية
│   ├── database/            # إدارة قاعدة البيانات
│   ├── services/            # الخدمات الأساسية
│   ├── repositories/        # طبقة الوصول للبيانات
│   └── api/                 # خدمات الشبكة
├── features/                # الميزات الرئيسية
│   ├── auth/                # المصادقة وتسجيل الدخول
│   ├── dashboard/           # لوحة التحكم
│   ├── service_requests/    # إدارة طلبات الخدمة
│   ├── customers/           # إدارة العملاء
│   ├── inventory/           # إدارة المخزون
│   ├── invoices/            # إدارة الفواتير
│   ├── employees/           # إدارة الموظفين
│   ├── reports/             # التقارير
│   └── settings/            # الإعدادات
├── shared/                  # المكونات المشتركة
│   ├── models/              # نماذج البيانات
│   ├── widgets/             # عناصر واجهة المستخدم
│   └── utils/               # الأدوات المساعدة
└── state/                   # إدارة حالة التطبيق
    ├── auth_state.dart      # حالة المصادقة
    ├── user_state.dart      # حالة المستخدم
    └── category_state.dart  # حالة الفئات
```

## 3. الميزات الرئيسية للتطبيق

### أ) لوحة التحكم (Dashboard)
- عرض إحصائيات سريعة عن الأعمال
- ملخص مالي للإيرادات والمصروفات
- أزرار الوصول السريع للوظائف الأساسية
- عرض النشاطات الأخيرة
- رسوم بيانية لتحليل الأداء

### ب) إدارة طلبات الخدمة (Service Requests)
- إنشاء طلبات خدمة جديدة
- تصنيف الطلبات حسب النوع (تركيب، صيانة، إصلاح)
- تحديد الأولوية (عالية، متوسطة، منخفضة)
- جدولة المواعيد والتذكيرات
- تتبع حالة الطلب (معلق، قيد التنفيذ، مكتمل، ملغي)
- إضافة صور للمشاكل والحلول
- تسجيل تفاصيل الجهاز (النوع، الماركة، الموديل، الرقم التسلسلي)
- حساب تكلفة الخدمة

### ج) إدارة العملاء (Customers)
- إضافة وتعديل بيانات العملاء
- تصنيف العملاء (أفراد، شركات)
- تتبع تاريخ الخدمات المقدمة
- إدارة الاشتراكات الشهرية
- حفظ طرق الدفع المفضلة
- تسجيل الملاحظات الخاصة بكل عميل

### د) إدارة المخزون (Inventory)
- تتبع قطع الغيار والمواد
- إدارة مستويات المخزون
- تسجيل حركات الدخول والخروج
- تنبيهات عند انخفاض المخزون
- ربط المخزون بطلبات الخدمة

### هـ) إدارة الفواتير (Invoices)
- إنشاء فواتير احترافية
- حساب الضرائب والخصومات
- تصدير الفواتير بصيغة PDF
- تتبع حالة الدفع
- ربط الفواتير بطلبات الخدمة

### و) التقارير (Reports)
- تقارير مالية شاملة
- تقارير أداء الموظفين
- تقارير العملاء والخدمات
- تصدير التقارير بصيغ مختلفة (PDF, Excel)
- رسوم بيانية تفاعلية

### ز) إدارة المستخدمين والصلاحيات
- إنشاء حسابات للموظفين
- تحديد الصلاحيات لكل مستخدم
- تتبع نشاطات المستخدمين
- إدارة كلمات المرور والأمان

## 4. تدفق البيانات في التطبيق

### مسار إنشاء طلب خدمة:
1. المستخدم يدخل إلى شاشة إضافة طلب خدمة
2. يختار العميل أو ينشئ عميل جديد
3. يدخل تفاصيل الخدمة المطلوبة
4. يحدد موعد الخدمة ووقت التذكير
5. يضيف صور المشكلة إن وجدت
6. يحفظ الطلب في قاعدة البيانات
7. يتم جدولة إشعار تذكيري
8. يظهر الطلب في قائمة الطلبات والتقويم

### مسار إنجاز طلب الخدمة:
1. الموظف الفني يستلم الطلب
2. يبدأ العمل ويغير الحالة إلى "قيد التنفيذ"
3. يسجل الحل والقطع المستخدمة
4. يضيف صور الحل
5. يكمل الطلب ويغير الحالة إلى "مكتمل"
6. يتم إنشاء فاتورة تلقائياً
7. يتم تحديث المخزون بالقطع المستخدمة

## 5. قاعدة البيانات

### الجداول الرئيسية:
- **customers**: بيانات العملاء
- **service_requests**: طلبات الخدمة
- **users**: المستخدمين والموظفين
- **inventory_items**: عناصر المخزون
- **invoices**: الفواتير
- **service_categories**: فئات الخدمات
- **transactions**: المعاملات المالية
- **employees**: بيانات الموظفين

### العلاقات بين الجداول:
- كل طلب خدمة مرتبط بعميل واحد
- كل طلب خدمة يمكن أن يكون له فاتورة واحدة
- كل فاتورة مرتبطة بعميل واحد
- المستخدمون لهم صلاحيات محددة
- عناصر المخزون مرتبطة بطلبات الخدمة

## 6. المكونات الرئيسية للكود

### أ) النماذج (Models):
- **ServiceRequest**: نموذج طلب الخدمة
- **Customer**: نموذج العميل
- **User**: نموذج المستخدم
- **Invoice**: نموذج الفاتورة
- **InventoryItem**: نموذج عنصر المخزون

### ب) إدارة الحالة (State Management):
- **AuthState**: إدارة حالة المصادقة
- **UserState**: إدارة حالة المستخدمين
- **CategoryState**: إدارة حالة الفئات

### ج) الخدمات (Services):
- **NotificationService**: خدمة الإشعارات
- **AuthService**: خدمة المصادقة
- **DatabaseHelper**: مساعد قاعدة البيانات
- **ReportGenerator**: مولد التقارير

### د) المستودعات (Repositories):
- **ServiceRequestRepository**: مستودع طلبات الخدمة
- **CustomerRepository**: مستودع العملاء
- **UserRepository**: مستودع المستخدمين

## 7. كيفية عمل التطبيق

### تشغيل التطبيق:
1. يبدأ التطبيق من main.dart
2. يتم تهيئة قاعدة البيانات
3. يتم تهيئة خدمات الإشعارات
4. يتم فحص حالة المصادقة
5. يتم توجيه المستخدم إلى الشاشة المناسبة

### سير العمل اليومي:
1. تسجيل الدخول
2. عرض لوحة التحكم مع الإحصائيات
3. مراجعة المواعيد اليومية
4. إدارة طلبات الخدمة الجديدة
5. متابعة الطلبات قيد التنفيذ
6. إنشاء الفواتير
7. مراجعة التقارير

### الإشعارات والتذكيرات:
- تذكيرات المواعيد قبل الوقت المحدد
- إشعارات عند انخفاض المخزون
- تنبيهات عند استحقاق الفواتير
- إشعارات تحديث حالة الطلبات

## 8. الأمان والصلاحيات

### نظام الصلاحيات:
- صلاحيات مختلفة لكل وحدة (عرض، إضافة، تعديل، حذف)
- تشفير كلمات المرور
- حفظ آمن للبيانات الحساسة
- تسجيل نشاطات المستخدمين

### حماية البيانات:
- تشفير قاعدة البيانات المحلية
- نسخ احتياطية دورية
- التحقق من صحة البيانات المدخلة
- حماية من الوصول غير المصرح

هذا التطبيق يوفر حلاً شاملاً لإدارة أعمال شركات التكييف والتبريد بطريقة احترافية ومنظمة، مما يساعد على تحسين الكفاءة وزيادة الأرباح.
