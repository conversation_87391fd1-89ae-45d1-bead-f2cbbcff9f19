import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/statistics_card.dart';
import '../widgets/statistics_chart.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({Key? key}) : super(key: key);

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  bool _isLoading = true;
  String _selectedPeriod = 'monthly';
  final List<Map<String, dynamic>> _statisticsData = [];

  @override
  void initState() {
    super.initState();
    _loadStatisticsData();
  }

  Future<void> _loadStatisticsData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Mock data
    final mockData = [
      {
        'title': 'إجمالي المبيعات',
        'value': 45600.0,
        'change': 12.5,
        'isPositive': true,
        'icon': Icons.attach_money,
        'color': AppColors.primary,
      },
      {
        'title': 'عدد العملاء',
        'value': 128.0,
        'change': 8.3,
        'isPositive': true,
        'icon': Icons.people,
        'color': AppColors.secondary,
      },
      {
        'title': 'طلبات الخدمة',
        'value': 87.0,
        'change': 5.2,
        'isPositive': true,
        'icon': Icons.build,
        'color': AppColors.accent,
      },
      {
        'title': 'المصروفات',
        'value': 28400.0,
        'change': 3.7,
        'isPositive': false,
        'icon': Icons.shopping_cart,
        'color': Colors.red,
      },
    ];

    if (mounted) {
      setState(() {
        _statisticsData.clear();
        _statisticsData.addAll(mockData);
        _isLoading = false;
      });
    }
  }

  void _changePeriod(String period) {
    if (_selectedPeriod != period) {
      setState(() {
        _selectedPeriod = period;
      });
      _loadStatisticsData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإحصائيات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatisticsData,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: _changePeriod,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'daily',
                child: Text('يومي'),
              ),
              const PopupMenuItem(
                value: 'weekly',
                child: Text('أسبوعي'),
              ),
              const PopupMenuItem(
                value: 'monthly',
                child: Text('شهري'),
              ),
              const PopupMenuItem(
                value: 'yearly',
                child: Text('سنوي'),
              ),
            ],
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Period selector
                  Container(
                    margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildPeriodButton('يومي', 'daily'),
                        _buildPeriodButton('أسبوعي', 'weekly'),
                        _buildPeriodButton('شهري', 'monthly'),
                        _buildPeriodButton('سنوي', 'yearly'),
                      ],
                    ),
                  ),

                  // Statistics cards
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 1.5,
                      crossAxisSpacing: AppDimensions.paddingM,
                      mainAxisSpacing: AppDimensions.paddingM,
                    ),
                    itemCount: _statisticsData.length,
                    itemBuilder: (context, index) {
                      final data = _statisticsData[index];
                      return StatisticsCard(
                        title: data['title'],
                        value: data['value'],
                        change: data['change'],
                        isPositive: data['isPositive'],
                        icon: data['icon'],
                        color: data['color'],
                      );
                    },
                  ),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Sales chart
                  const Text(
                    'تحليل المبيعات',
                    style: AppTextStyles.heading2,
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  const SizedBox(
                    height: 250,
                    child: StatisticsChart(),
                  ),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Service requests chart
                  const Text(
                    'تحليل طلبات الخدمة',
                    style: AppTextStyles.heading2,
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  const SizedBox(
                    height: 250,
                    child: StatisticsChart(isServiceRequests: true),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildPeriodButton(String title, String period) {
    final isSelected = _selectedPeriod == period;
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton(
          onPressed: () => _changePeriod(period),
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected ? AppColors.primary : Colors.white,
            foregroundColor: isSelected ? Colors.white : AppColors.textPrimary,
            elevation: isSelected ? 2 : 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              side: BorderSide(
                color: isSelected ? AppColors.primary : AppColors.border,
              ),
            ),
          ),
          child: Text(title),
        ),
      ),
    );
  }
}
