import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/models/invoice.dart';
import '../../../shared/models/transaction.dart' as app_transaction;
import '../../../shared/models/service_request.dart';
import '../../../core/repositories/customer_repository.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../core/repositories/service_request_repository.dart';

class CustomerDetailsScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailsScreen({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen> {
  late Customer _customer;
  final CustomerRepository _customerRepository = CustomerRepository();
  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final TransactionRepository _transactionRepository = TransactionRepository();
  final ServiceRequestRepository _serviceRequestRepository = ServiceRequestRepository();

  bool _isLoading = true;
  double _balance = 0;
  List<Invoice> _invoices = [];
  List<app_transaction.Transaction> _transactions = [];
  List<ServiceRequest> _serviceRequests = [];

  // Lista combinada para la tabla unificada
  List<Map<String, dynamic>> _unifiedData = [];

  @override
  void initState() {
    super.initState();
    _customer = widget.customer;
    _loadCustomerData();
  }

  Future<void> _loadCustomerData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (_customer.localId != null) {
        // Cargar saldo del cliente
        final balance = await _customerRepository.getCustomerBalance(_customer.localId!);

        // Cargar facturas del cliente
        final invoices = await _invoiceRepository.getInvoicesByCustomerId(_customer.localId!);

        // Cargar transacciones del cliente
        final transactions = await _transactionRepository.getTransactionsByCustomer(_customer.localId!);

        // Cargar solicitudes de servicio del cliente
        final serviceRequests = await _serviceRequestRepository.getServiceRequestsByCustomerId(_customer.localId!);

        // Actualizar el estado
        if (mounted) {
          setState(() {
            _balance = balance;
            _invoices = invoices;
            _transactions = transactions;
            _serviceRequests = serviceRequests;
            _isLoading = false;

            // Combinar todos los datos en una lista unificada
            _prepareUnifiedData();
          });
        }
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل بيانات العميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Preparar datos unificados para la tabla
  void _prepareUnifiedData() {
    _unifiedData = [];

    // Agregar facturas
    for (final invoice in _invoices) {
      _unifiedData.add({
        'type': 'invoice',
        'reference': invoice.invoiceNumber,
        'date': invoice.date,
        'description': 'فاتورة ${invoice.invoiceNumber}',
        'amount': invoice.total,
        'status': invoice.status,
        'data': invoice,
      });
    }

    // Agregar transacciones
    for (final transaction in _transactions) {
      _unifiedData.add({
        'type': transaction.type.toString().split('.').last,
        'reference': transaction.reference,
        'date': transaction.date,
        'description': transaction.description ?? 'إيراد مالية',
        'amount': transaction.amount,
        'status': transaction.type == app_transaction.TransactionType.income ? 'income' : 'expense',
        'data': transaction,
      });
    }

    // Agregar solicitudes de servicio
    for (final request in _serviceRequests) {
      // Calcular el monto según el tipo de pago del cliente
      double amount = 0;
      if (_customer.paymentMethod == CustomerPaymentMethod.perService) {
        // Para clientes que pagan por servicio, usar el monto del servicio
        amount = request.serviceAmount;
      } else if (_customer.paymentMethod == CustomerPaymentMethod.monthlySubscription) {
        // Para clientes con suscripción mensual, usar el monto de suscripción mensual
        amount = _customer.monthlySubscriptionAmount ?? 0;
      }

      _unifiedData.add({
        'type': 'service',
        'reference': request.reference,
        'date': request.scheduledDate,
        'description': request.description.isNotEmpty ? request.description : 'طلب خدمة',
        'amount': amount,
        'status': request.status,
        'data': request,
      });
    }

    // Ordenar por fecha (más reciente primero)
    _unifiedData.sort((a, b) => b['date'].compareTo(a['date']));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('بيانات ${_customer.name}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل البيانات',
            onPressed: () {
              // Implementar edición de cliente
            },
          ),
          IconButton(
            icon: const Icon(Icons.bar_chart),
            tooltip: 'تقرير العميل',
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRoutes.customerReport,
                arguments: _customer.id,
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCustomerInfoCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildFinancialSummaryCard(),
                  const SizedBox(height: AppDimensions.paddingM),
                  _buildUnifiedDataCard(),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Implementar creación de nueva factura o transacción
        },
        icon: const Icon(Icons.add),
        label: const Text('إضافة فاتورة جديدة'),
      ),
    );
  }

  // Tarjeta de información del cliente
  Widget _buildCustomerInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات العميل',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow('النوع', Customer.getTypeName(_customer.type)),
            if (_customer.email != null)
              _buildDetailRow('البريد الإلكتروني', _customer.email!),
            if (_customer.phone != null)
              _buildDetailRow('رقم الهاتف', _customer.phone!),
            if (_customer.address != null)
              _buildDetailRow('العنوان', _customer.address!),
            if (_customer.contactPerson != null)
              _buildDetailRow('الشخص المسؤول', _customer.contactPerson!),
            _buildDetailRow('الحالة', _customer.isActive ? 'نشط' : 'غير نشط'),
            _buildDetailRow('تاريخ الإنشاء', DateFormat('dd/MM/yyyy').format(_customer.createdAt)),
          ],
        ),
      ),
    );
  }

  // Tarjeta de resumen financiero
  Widget _buildFinancialSummaryCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملخص المالي',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            _buildDetailRow(
              'الرصيد الحالي',
              '${_balance.toStringAsFixed(2)} ر.س',
              valueColor: _balance >= 0 ? Colors.green : Colors.red,
            ),
            _buildDetailRow(
              'عدد الفواتير',
              '${_invoices.length}',
            ),
            _buildDetailRow(
              'عدد طلبات الخدمة',
              '${_serviceRequests.length}',
            ),
            if (_customer.paymentMethod != null)
              _buildDetailRow(
                'طريقة الدفع',
                _customer.paymentMethod == CustomerPaymentMethod.perService
                    ? 'دفع لكل خدمة'
                    : 'اشتراك شهري',
              ),
            if (_customer.paymentMethod == CustomerPaymentMethod.monthlySubscription) ...[
              if (_customer.monthlySubscriptionAmount != null)
                _buildDetailRow(
                  'مبلغ الاشتراك الشهري',
                  '${_customer.monthlySubscriptionAmount!.toStringAsFixed(2)} ر.س',
                ),
              if (_customer.subscriptionStartDate != null)
                _buildDetailRow(
                  'تاريخ بداية الاشتراك',
                  DateFormat('dd/MM/yyyy').format(_customer.subscriptionStartDate!),
                ),
              if (_customer.subscriptionEndDate != null)
                _buildDetailRow(
                  'تاريخ نهاية الاشتراك',
                  DateFormat('dd/MM/yyyy').format(_customer.subscriptionEndDate!),
                ),
              if (_customer.isSubscriptionActive != null)
                _buildDetailRow(
                  'حالة الاشتراك',
                  _customer.isSubscriptionActive! ? 'نشط' : 'منتهي',
                  valueColor: _customer.isSubscriptionActive! ? Colors.green : Colors.red,
                ),
            ],
          ],
        ),
      ),
    );
  }

  // Tarjeta de datos unificados
  Widget _buildUnifiedDataCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'سجل العميل',
                  style: AppTextStyles.heading3,
                ),
                TextButton.icon(
                  icon: const Icon(Icons.filter_list, size: 16),
                  label: const Text('تصفية'),
                  onPressed: () {
                    // Implementar filtrado
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            SizedBox(
              height: 500, // Altura fija para la tabla
              child: _buildUnifiedDataTable(),
            ),
          ],
        ),
      ),
    );
  }

  // Construir tabla unificada
  Widget _buildUnifiedDataTable() {
    if (_unifiedData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return Column(
      children: [
        // عنوان الجدول
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: const Row(
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  'النوع',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'الرقم المرجعي',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'التاريخ',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'الوصف',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'المبلغ',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'الحالة',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),

        // محتوى الجدول
        Expanded(
          child: ListView.builder(
            itemCount: _unifiedData.length,
            itemBuilder: (context, index) {
              final item = _unifiedData[index];

              return Container(
                decoration: BoxDecoration(
                  color: index % 2 == 0 ? Colors.white : Colors.grey.shade50,
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Row(
                    children: [
                      // النوع
                      Expanded(
                        flex: 1,
                        child: Center(
                          child: _getTypeIcon(item['type']),
                        ),
                      ),

                      // الرقم المرجعي
                      Expanded(
                        flex: 2,
                        child: Text(
                          item['reference'],
                          textAlign: TextAlign.center,
                        ),
                      ),

                      // التاريخ
                      Expanded(
                        flex: 2,
                        child: Text(
                          DateFormat('dd/MM/yyyy').format(item['date']),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      // الوصف
                      Expanded(
                        flex: 2,
                        child: Text(
                          item['description'],
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // المبلغ
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${item['amount'].toStringAsFixed(2)} ر.س',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getAmountColor(item['type'], item['status']),
                          ),
                        ),
                      ),

                      // الحالة
                      Expanded(
                        flex: 2,
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(item['type'], item['status']),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getStatusText(item['type'], item['status']),
                              style: const TextStyle(color: Colors.white),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // Obtener icono según el tipo de elemento
  Widget _getTypeIcon(String type) {
    switch (type) {
      case 'invoice':
        return Icon(Icons.receipt, color: Colors.blue);
      case 'income':
        return Icon(Icons.arrow_downward, color: Colors.green);
      case 'expense':
        return Icon(Icons.arrow_upward, color: Colors.red);
      case 'service':
        return Icon(Icons.home_repair_service, color: Colors.orange);
      default:
        return Icon(Icons.circle, color: Colors.grey);
    }
  }

  // Obtener color para el monto según el tipo y estado
  Color _getAmountColor(String type, dynamic status) {
    if (type == 'invoice') {
      final invoiceStatus = status as InvoiceStatus;
      return invoiceStatus == InvoiceStatus.paid ? Colors.green : Colors.red;
    } else if (type == 'income') {
      return Colors.green;
    } else if (type == 'expense') {
      return Colors.red;
    } else if (type == 'service') {
      final String statusStr = status.toString();
      return statusStr.contains('completed') ? Colors.green : Colors.blue;
    }
    return Colors.black;
  }

  // Obtener color para el estado según el tipo y estado
  Color _getStatusColor(String type, dynamic status) {
    if (type == 'invoice') {
      final invoiceStatus = status as InvoiceStatus;
      switch (invoiceStatus) {
        case InvoiceStatus.paid:
          return Colors.green;
        case InvoiceStatus.partiallyPaid:
          return Colors.orange;
        case InvoiceStatus.overdue:
          return Colors.red;
        case InvoiceStatus.cancelled:
          return Colors.grey;
        default:
          return Colors.blue;
      }
    } else if (type == 'income') {
      return Colors.green;
    } else if (type == 'expense') {
      return Colors.red;
    } else if (type == 'service') {
      final String statusStr = status.toString();
      if (statusStr.contains('completed')) {
        return Colors.green;
      } else if (statusStr.contains('progress')) {
        return Colors.blue;
      } else if (statusStr.contains('pending')) {
        return Colors.orange;
      } else if (statusStr.contains('cancelled')) {
        return Colors.grey;
      } else {
        return Colors.purple;
      }
    }
    return Colors.grey;
  }

  // Obtener texto para el estado según el tipo y estado
  String _getStatusText(String type, dynamic status) {
    if (type == 'invoice') {
      final invoiceStatus = status as InvoiceStatus;
      return Invoice.getStatusName(invoiceStatus);
    } else if (type == 'income') {
      return 'إيراد';
    } else if (type == 'expense') {
      return 'مصروف';
    } else if (type == 'service') {
      // Convertir el estado a texto
      final String statusStr = status.toString();
      if (statusStr.contains('completed')) {
        return 'مكتمل';
      } else if (statusStr.contains('progress')) {
        return 'قيد التنفيذ';
      } else if (statusStr.contains('pending')) {
        return 'معلق';
      } else if (statusStr.contains('cancelled')) {
        return 'ملغي';
      } else {
        return 'غير معروف';
      }
    }
    return 'غير معروف';
  }

  // Construir fila de detalle
  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
