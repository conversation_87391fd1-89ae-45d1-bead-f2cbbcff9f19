import 'package:sqflite/sqflite.dart';
import '../../shared/models/profitability_analysis.dart';
import '../database/database_helper.dart';

/// مستودع تحليل الربحية
class ProfitabilityAnalysisRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع تحليلات الربحية
  Future<List<ProfitabilityAnalysis>> getAllProfitabilityAnalyses() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ProfitabilityAnalysis.fromMap(maps[i]);
    });
  }

  /// الحصول على تحليلات الربحية حسب نوع الكيان
  Future<List<ProfitabilityAnalysis>> getProfitabilityAnalysesByEntityType(
    String entityType,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      where: 'entity_type = ?',
      whereArgs: [entityType],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ProfitabilityAnalysis.fromMap(maps[i]);
    });
  }

  /// الحصول على تحليلات الربحية لكيان محدد
  Future<List<ProfitabilityAnalysis>> getProfitabilityAnalysesByEntity(
    String entityType,
    int entityId,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      where: 'entity_type = ? AND entity_id = ?',
      whereArgs: [entityType, entityId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ProfitabilityAnalysis.fromMap(maps[i]);
    });
  }

  /// الحصول على تحليلات الربحية حسب الفترة
  Future<List<ProfitabilityAnalysis>> getProfitabilityAnalysesByPeriod(
    String period,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      where: 'period = ? AND start_date >= ? AND end_date <= ?',
      whereArgs: [
        period,
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return ProfitabilityAnalysis.fromMap(maps[i]);
    });
  }

  /// الحصول على تحليل ربحية بواسطة المعرف
  Future<ProfitabilityAnalysis?> getProfitabilityAnalysisById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return ProfitabilityAnalysis.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة تحليل ربحية جديد
  Future<int> insertProfitabilityAnalysis(
    ProfitabilityAnalysis profitabilityAnalysis,
  ) async {
    final db = await _databaseHelper.database;
    return await db.insert(
      'profitability_analysis',
      profitabilityAnalysis.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث تحليل ربحية
  Future<int> updateProfitabilityAnalysis(
    ProfitabilityAnalysis profitabilityAnalysis,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'profitability_analysis',
      profitabilityAnalysis.toMap(),
      where: 'id = ?',
      whereArgs: [profitabilityAnalysis.id],
    );
  }

  /// حذف تحليل ربحية
  Future<int> deleteProfitabilityAnalysis(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'profitability_analysis',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على أكثر الخدمات ربحية
  Future<List<ProfitabilityAnalysis>> getMostProfitableServices(
    int limit,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      where: 'entity_type = ?',
      whereArgs: ['service'],
      orderBy: 'profit_margin DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return ProfitabilityAnalysis.fromMap(maps[i]);
    });
  }

  /// الحصول على أكثر العملاء ربحية
  Future<List<ProfitabilityAnalysis>> getMostProfitableCustomers(
    int limit,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'profitability_analysis',
      where: 'entity_type = ?',
      whereArgs: ['customer'],
      orderBy: 'profit_margin DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return ProfitabilityAnalysis.fromMap(maps[i]);
    });
  }
}
