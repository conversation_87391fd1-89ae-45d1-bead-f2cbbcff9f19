import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../core/repositories/customer_repository.dart';
import '../../core/repositories/employee_repository.dart';
import '../../core/repositories/service_request_repository.dart';
import '../../core/repositories/service_category_repository.dart';
import '../../core/services/notification_service.dart';
import '../../shared/models/customer.dart';
import '../../shared/models/employee.dart';
import '../../shared/models/service_request.dart';
import '../../shared/models/service_category.dart';
import '../../shared/utils/app_colors.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/simple_loading_indicator.dart';

class ServiceRequestFormScreen extends StatefulWidget {
  final ServiceRequest? serviceRequest;

  const ServiceRequestFormScreen({super.key, this.serviceRequest});

  @override
  State<ServiceRequestFormScreen> createState() => _ServiceRequestFormScreenState();
}

class _ServiceRequestFormScreenState extends State<ServiceRequestFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serviceRequestRepository = ServiceRequestRepository();
  final _customerRepository = CustomerRepository();
  final _employeeRepository = EmployeeRepository();
  final _serviceCategoryRepository = ServiceCategoryRepository();
  final _notificationService = NotificationService();

  bool _isLoading = true;
  bool _isSubmitting = false;
  List<Customer> _customers = [];
  List<Employee> _employees = [];
  List<ServiceCategory> _categories = [];

  // Form fields
  String _reference = '';
  int? _customerId;
  String _customerName = '';
  ServiceRequestType _requestType = ServiceRequestType.maintenance;
  String _description = '';
  ServiceRequestStatus _status = ServiceRequestStatus.pending;
  String? _location;
  int? _assignedTo;
  Employee? _selectedTechnician;
  double? _technicianDailyRate;
  DateTime _scheduledDate = DateTime.now();
  String? _deviceType;
  String? _deviceBrand;
  String? _deviceModel;
  String? _serialNumber;
  String? _installationDate;
  String? _warrantyInfo;
  String? _technicalNotes;
  int? _categoryId;
  String? _categoryName;
  String? _notes;
  int _reminderMinutes = 60; // القيمة الافتراضية هي 60 دقيقة (ساعة واحدة)

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load customers, employees and categories
      final customers = await _customerRepository.getAllCustomers();
      final employees = await _employeeRepository.getAllEmployees();
      final categories = await _serviceCategoryRepository.getActiveServiceCategories();

      setState(() {
        _customers = customers;
        _employees = employees;
        _categories = categories;
      });

      // If editing, populate form fields
      if (widget.serviceRequest != null) {
        final request = widget.serviceRequest!;
        setState(() {
          _reference = request.reference;
          _customerId = request.customerId;
          _customerName = request.customerName;
          _requestType = request.requestType;
          _description = request.description;
          _status = request.status;
          _location = request.location;
          _assignedTo = request.assignedTo;
          _technicianDailyRate = request.technicianDailyRate;

          // Find selected technician if assigned
          if (_assignedTo != null) {
            try {
              _selectedTechnician = _employees.firstWhere((emp) => emp.id == _assignedTo);
            } catch (e) {
              // Technician not found in current list
            }
          }
          _scheduledDate = request.scheduledDate;
          _deviceType = request.deviceType;
          _deviceBrand = request.deviceBrand;
          _deviceModel = request.deviceModel;
          _serialNumber = request.serialNumber;
          _installationDate = request.installationDate;
          _warrantyInfo = request.warrantyInfo;
          _technicalNotes = request.technicalNotes;
          _categoryId = request.categoryId;
          _categoryName = request.categoryName;
          _notes = request.notes;
          _reminderMinutes = request.reminderMinutes;
        });
      } else {
        // Generate a new reference number for new requests
        _reference = 'SR-${DateTime.now().millisecondsSinceEpoch.toString().substring(6)}';
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _saveServiceRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _formKey.currentState!.save();

    setState(() {
      _isSubmitting = true;
    });

    try {
      final serviceRequest = ServiceRequest(
        id: widget.serviceRequest?.id,
        reference: _reference,
        customerId: _customerId,
        customerName: _customerName,
        requestType: _requestType,
        description: _description,
        priority: ServiceRequestPriority.medium, // Default priority
        status: _status,
        location: _location,
        assignedTo: _assignedTo,
        scheduledDate: _scheduledDate,
        deviceType: _deviceType,
        deviceBrand: _deviceBrand,
        deviceModel: _deviceModel,
        serialNumber: _serialNumber,
        installationDate: _installationDate,
        warrantyInfo: _warrantyInfo,
        technicalNotes: _technicalNotes,
        categoryId: _categoryId,
        categoryName: _categoryName,
        notes: _notes,
        reminderMinutes: _reminderMinutes, // إضافة وقت التذكير
        createdAt: widget.serviceRequest?.createdAt ?? DateTime.now(),
      );

      int result;
      if (widget.serviceRequest == null) {
        result = await _serviceRequestRepository.insertServiceRequest(serviceRequest);
      } else {
        result = await _serviceRequestRepository.updateServiceRequest(serviceRequest);
      }

      if (result > 0) {
        // إلغاء الإشعارات القديمة إذا كان هذا تعديلًا لطلب موجود
        if (widget.serviceRequest != null) {
          await _notificationService.cancelServiceRequestNotifications(widget.serviceRequest!);
        }

        // جدولة الإشعارات الجديدة
        await _notificationService.scheduleServiceRequestReminder(serviceRequest);

        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        _showErrorSnackBar('حدث خطأ أثناء حفظ طلب الخدمة');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء حفظ طلب الخدمة');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: widget.serviceRequest == null ? 'إضافة طلب خدمة جديد' : 'تعديل طلب الخدمة',
      ),
      body: _isLoading
          ? const SimpleLoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Reference number (read-only)
                    TextFormField(
                      initialValue: _reference,
                      readOnly: true,
                      decoration: const InputDecoration(
                        labelText: 'رقم الطلب',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Customer dropdown
                    DropdownButtonFormField<int>(
                      value: _customerId,
                      decoration: const InputDecoration(
                        labelText: 'العميل',
                        border: OutlineInputBorder(),
                      ),
                      items: _customers.map((customer) {
                        return DropdownMenuItem<int>(
                          value: customer.localId,
                          child: Text(customer.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _customerId = value;
                          if (value != null) {
                            final customer = _customers.firstWhere((c) => c.localId == value);
                            _customerName = customer.name;
                          }
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار العميل';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Service type dropdown
                    DropdownButtonFormField<ServiceRequestType>(
                      value: _requestType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الخدمة',
                        border: OutlineInputBorder(),
                      ),
                      items: ServiceRequestType.values.map((type) {
                        return DropdownMenuItem<ServiceRequestType>(
                          value: type,
                          child: Text(ServiceRequest.getRequestTypeName(type)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _requestType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Service category dropdown
                    DropdownButtonFormField<int>(
                      value: _categoryId,
                      decoration: const InputDecoration(
                        labelText: 'فئة الخدمة',
                        border: OutlineInputBorder(),
                      ),
                      items: _categories.map((category) {
                        return DropdownMenuItem<int>(
                          value: category.id,
                          child: Row(
                            children: [
                              Icon(category.icon, color: category.color, size: 20),
                              const SizedBox(width: 8),
                              Text(category.name),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _categoryId = value;
                          if (value != null) {
                            final category = _categories.firstWhere((c) => c.id == value);
                            _categoryName = category.name;
                          } else {
                            _categoryName = null;
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Description
                    TextFormField(
                      initialValue: _description,
                      decoration: const InputDecoration(
                        labelText: 'وصف المشكلة',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال وصف المشكلة';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _description = value!;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Location
                    TextFormField(
                      initialValue: _location,
                      decoration: const InputDecoration(
                        labelText: 'الموقع',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _location = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Scheduled date
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _scheduledDate,
                          firstDate: DateTime.now().subtract(const Duration(days: 365)),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        );
                        if (date != null) {
                          setState(() {
                            _scheduledDate = date;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الزيارة',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          DateFormat('yyyy-MM-dd').format(_scheduledDate),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Assigned to dropdown
                    DropdownButtonFormField<int>(
                      value: _assignedTo,
                      decoration: const InputDecoration(
                        labelText: 'تعيين إلى',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem<int>(
                          value: null,
                          child: Text('غير معين'),
                        ),
                        ..._employees.map((employee) {
                          return DropdownMenuItem<int>(
                            value: employee.id,
                            child: Text(employee.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _assignedTo = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // Status dropdown
                    DropdownButtonFormField<ServiceRequestStatus>(
                      value: _status,
                      decoration: const InputDecoration(
                        labelText: 'الحالة',
                        border: OutlineInputBorder(),
                      ),
                      items: ServiceRequestStatus.values.map((status) {
                        return DropdownMenuItem<ServiceRequestStatus>(
                          value: status,
                          child: Text(ServiceRequest.getStatusName(status)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _status = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // وقت التذكير
                    DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: 'التذكير قبل الموعد بـ',
                        prefixIcon: Icon(Icons.notifications_active),
                        border: OutlineInputBorder(),
                      ),
                      value: _reminderMinutes,
                      items: [
                        DropdownMenuItem<int>(
                          value: 5,
                          child: Text('5 دقائق'),
                        ),
                        DropdownMenuItem<int>(
                          value: 10,
                          child: Text('10 دقائق'),
                        ),
                        DropdownMenuItem<int>(
                          value: 15,
                          child: Text('15 دقيقة'),
                        ),
                        DropdownMenuItem<int>(
                          value: 30,
                          child: Text('30 دقيقة'),
                        ),
                        DropdownMenuItem<int>(
                          value: 60,
                          child: Text('ساعة واحدة'),
                        ),
                        DropdownMenuItem<int>(
                          value: 120,
                          child: Text('ساعتين'),
                        ),
                        DropdownMenuItem<int>(
                          value: 180,
                          child: Text('3 ساعات'),
                        ),
                        DropdownMenuItem<int>(
                          value: 360,
                          child: Text('6 ساعات'),
                        ),
                        DropdownMenuItem<int>(
                          value: 720,
                          child: Text('12 ساعة'),
                        ),
                        DropdownMenuItem<int>(
                          value: 1440,
                          child: Text('يوم كامل'),
                        ),
                        DropdownMenuItem<int>(
                          value: 2880,
                          child: Text('يومين'),
                        ),
                      ],
                      onChanged: (int? value) {
                        if (value != null) {
                          setState(() {
                            _reminderMinutes = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 8),

                    // نص توضيحي لنظام التذكيرات
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.amber.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.amber.withAlpha(50)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: const [
                              Icon(Icons.info_outline, color: Colors.amber, size: 16),
                              SizedBox(width: 8),
                              Text(
                                'نظام التذكيرات المتعدد',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'سيتم إرسال التذكيرات التالية:',
                            style: TextStyle(fontSize: 12),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            '• تذكير رئيسي في الوقت المحدد أعلاه',
                            style: TextStyle(fontSize: 12),
                          ),
                          const Text(
                            '• تذكير قبل الموعد بساعة (إذا كان التذكير الرئيسي أكثر من ساعة)',
                            style: TextStyle(fontSize: 12),
                          ),
                          const Text(
                            '• تذكير قبل الموعد بـ 15 دقيقة (إذا كان التذكير الرئيسي أكثر من 15 دقيقة)',
                            style: TextStyle(fontSize: 12),
                          ),
                          const Text(
                            '• تذكير في وقت الموعد تمامًا',
                            style: TextStyle(fontSize: 12),
                          ),
                          const Text(
                            '• تذكير بعد الموعد بـ 15 دقيقة لتسجيل النتائج',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Device information section
                    const Text(
                      'معلومات الجهاز',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Device type
                    TextFormField(
                      initialValue: _deviceType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الجهاز',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _deviceType = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Device brand
                    TextFormField(
                      initialValue: _deviceBrand,
                      decoration: const InputDecoration(
                        labelText: 'ماركة الجهاز',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _deviceBrand = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Device model
                    TextFormField(
                      initialValue: _deviceModel,
                      decoration: const InputDecoration(
                        labelText: 'موديل الجهاز',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _deviceModel = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Serial number
                    TextFormField(
                      initialValue: _serialNumber,
                      decoration: const InputDecoration(
                        labelText: 'الرقم التسلسلي',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _serialNumber = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Installation date
                    TextFormField(
                      initialValue: _installationDate,
                      decoration: const InputDecoration(
                        labelText: 'تاريخ التركيب',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _installationDate = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Warranty info
                    TextFormField(
                      initialValue: _warrantyInfo,
                      decoration: const InputDecoration(
                        labelText: 'معلومات الضمان',
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _warrantyInfo = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Technical notes
                    TextFormField(
                      initialValue: _technicalNotes,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات فنية',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onSaved: (value) {
                        _technicalNotes = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Notes
                    TextFormField(
                      initialValue: _notes,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onSaved: (value) {
                        _notes = value;
                      },
                    ),
                    const SizedBox(height: 24),

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : _saveServiceRequest,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                        ),
                        child: _isSubmitting
                            ? const CircularProgressIndicator(color: Colors.white)
                            : Text(
                                widget.serviceRequest == null ? 'إضافة طلب الخدمة' : 'تحديث طلب الخدمة',
                                style: const TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
