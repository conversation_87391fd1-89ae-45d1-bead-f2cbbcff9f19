import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/inventory_item.dart';

class InventoryRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all inventory items
  Future<List<InventoryItem>> getAllInventoryItems() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query('inventory_items');

      return List.generate(maps.length, (i) {
        return InventoryItem(
          id: maps[i]['id'] as int,
          code: maps[i]['code'] as String,
          name: maps[i]['name'] as String,
          description: maps[i]['description'] as String?,
          category: maps[i]['category'] as String?,
          quantity: (maps[i]['quantity'] as num).toDouble(),
          minQuantity: (maps[i]['min_quantity'] as num).toDouble(),
          unit: maps[i]['unit'] as String?,
          costPrice: maps[i]['cost_price'] as double,
          sellingPrice: maps[i]['selling_price'] as double,
          supplierName: maps[i]['supplier_name'] as String?,
          location: maps[i]['location'] as String?,
          isActive: maps[i]['is_active'] == 1,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory items: $e');
      }
      return [];
    }
  }

  // Get inventory item by ID
  Future<InventoryItem?> getInventoryItemById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_items',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return InventoryItem(
          id: maps[0]['id'] as int,
          code: maps[0]['code'] as String,
          name: maps[0]['name'] as String,
          description: maps[0]['description'] as String?,
          category: maps[0]['category'] as String?,
          quantity: (maps[0]['quantity'] as num).toDouble(),
          minQuantity: (maps[0]['min_quantity'] as num).toDouble(),
          unit: maps[0]['unit'] as String?,
          costPrice: maps[0]['cost_price'] as double,
          sellingPrice: maps[0]['selling_price'] as double,
          supplierName: maps[0]['supplier_name'] as String?,
          location: maps[0]['location'] as String?,
          isActive: maps[0]['is_active'] == 1,
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory item by ID: $e');
      }
      return null;
    }
  }

  // Get inventory items by category
  Future<List<InventoryItem>> getInventoryItemsByCategory(String category) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_items',
        where: 'category = ?',
        whereArgs: [category],
      );

      return List.generate(maps.length, (i) {
        return InventoryItem(
          id: maps[i]['id'] as int,
          code: maps[i]['code'] as String,
          name: maps[i]['name'] as String,
          description: maps[i]['description'] as String?,
          category: maps[i]['category'] as String?,
          quantity: (maps[i]['quantity'] as num).toDouble(),
          minQuantity: (maps[i]['min_quantity'] as num).toDouble(),
          unit: maps[i]['unit'] as String?,
          costPrice: maps[i]['cost_price'] as double,
          sellingPrice: maps[i]['selling_price'] as double,
          supplierName: maps[i]['supplier_name'] as String?,
          location: maps[i]['location'] as String?,
          isActive: maps[i]['is_active'] == 1,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory items by category: $e');
      }
      return [];
    }
  }

  // Get low stock inventory items
  Future<List<InventoryItem>> getLowStockInventoryItems() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
        'SELECT * FROM inventory_items WHERE quantity <= min_quantity',
      );

      return List.generate(maps.length, (i) {
        return InventoryItem(
          id: maps[i]['id'] as int,
          code: maps[i]['code'] as String,
          name: maps[i]['name'] as String,
          description: maps[i]['description'] as String?,
          category: maps[i]['category'] as String?,
          quantity: (maps[i]['quantity'] as num).toDouble(),
          minQuantity: (maps[i]['min_quantity'] as num).toDouble(),
          unit: maps[i]['unit'] as String?,
          costPrice: maps[i]['cost_price'] as double,
          sellingPrice: maps[i]['selling_price'] as double,
          supplierName: maps[i]['supplier_name'] as String?,
          location: maps[i]['location'] as String?,
          isActive: maps[i]['is_active'] == 1,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting low stock inventory items: $e');
      }
      return [];
    }
  }

  // Insert a new inventory item
  Future<int> insertInventoryItem(InventoryItem item) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'inventory_items',
        {
          'code': item.code,
          'name': item.name,
          'description': item.description,
          'category_id': null, // Using null for now, will need to be updated when categories are implemented
          'quantity': item.quantity,
          'min_quantity': item.minQuantity,
          'unit': item.unit,
          'cost_price': item.costPrice,
          'selling_price': item.sellingPrice,
          'supplier_id': null, // Using null for now, will need to be updated when supplier selection is implemented
          'location': item.location,
          'status': item.isActive ? 'available' : 'unavailable',
          'notes': null,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting inventory item: $e');
      }
      return -1;
    }
  }

  // Update an existing inventory item
  Future<int> updateInventoryItem(InventoryItem item) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'inventory_items',
        {
          'code': item.code,
          'name': item.name,
          'description': item.description,
          'category_id': null, // Using null for now, will need to be updated when categories are implemented
          'quantity': item.quantity,
          'min_quantity': item.minQuantity,
          'unit': item.unit,
          'cost_price': item.costPrice,
          'selling_price': item.sellingPrice,
          'supplier_id': null, // Using null for now, will need to be updated when supplier selection is implemented
          'location': item.location,
          'status': item.isActive ? 'available' : 'unavailable',
          'notes': null,
        },
        where: 'id = ?',
        whereArgs: [item.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating inventory item: $e');
      }
      return 0;
    }
  }

  // Update inventory item quantity
  Future<int> updateInventoryItemQuantity(int? id, double quantity) async {
    if (id == null) {
      if (kDebugMode) {
        print('Cannot update inventory item with null ID');
      }
      return 0;
    }

    try {
      final db = await _dbHelper.database;
      return await db.update(
        'inventory_items',
        {'quantity': quantity},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating inventory item quantity: $e');
      }
      return 0;
    }
  }

  // Delete an inventory item
  Future<int> deleteInventoryItem(int? id) async {
    if (id == null) {
      if (kDebugMode) {
        print('Cannot delete inventory item with null ID');
      }
      return 0;
    }

    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'inventory_items',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting inventory item: $e');
      }
      return 0;
    }
  }

  // Get all unique categories
  Future<List<String>> getAllCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
        'SELECT DISTINCT category FROM inventory_items WHERE category IS NOT NULL',
      );

      return List.generate(maps.length, (i) {
        return maps[i]['category'] as String;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting categories: $e');
      }
      return [];
    }
  }

  // Get inventory transactions by date range
  Future<List<InventoryItem>> getInventoryTransactionsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT i.* FROM inventory_items i
        JOIN inventory_transactions t ON i.id = t.item_id
        WHERE t.transaction_date BETWEEN ? AND ?
        GROUP BY i.id
      ''', [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ]);

      return List.generate(maps.length, (i) {
        return InventoryItem(
          id: maps[i]['id'] as int,
          code: maps[i]['code'] as String,
          name: maps[i]['name'] as String,
          description: maps[i]['description'] as String?,
          category: maps[i]['category'] as String?,
          quantity: (maps[i]['quantity'] as num).toDouble(),
          minQuantity: (maps[i]['min_quantity'] as num).toDouble(),
          unit: maps[i]['unit'] as String?,
          costPrice: maps[i]['cost_price'] as double,
          sellingPrice: maps[i]['selling_price'] as double,
          supplierName: maps[i]['supplier_name'] as String?,
          location: maps[i]['location'] as String?,
          isActive: maps[i]['is_active'] == 1,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting inventory transactions by date range: $e');
      }
      return [];
    }
  }
}
