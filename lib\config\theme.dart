import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'constants.dart';

/// تعريف ثيم التطبيق الموحد
class AppTheme {
  /// ثيم النمط الفاتح
  static ThemeData lightTheme() {
    return ThemeData(
      // ألوان أساسية
      primaryColor: AppColors.primary,
      primaryColorLight: AppColors.primaryLight,
      primaryColorDark: AppColors.primaryDark,
      colorScheme: const ColorScheme(
        primary: AppColors.primary,
        primaryContainer: AppColors.primaryDark,
        secondary: AppColors.primaryLight,
        secondaryContainer: AppColors.primaryLight,
        surface: AppColors.surface,
        background: AppColors.background,
        error: AppColors.error,
        onPrimary: AppColors.textOnPrimary,
        onPrimaryContainer: AppColors.textOnPrimary,
        onSecondary: AppColors.textOnPrimary,
        onSecondaryContainer: AppColors.textOnPrimary,
        onSurface: AppColors.textPrimary,
        onBackground: AppColors.textPrimary,
        onError: AppColors.textOnPrimary,
        brightness: Brightness.light,
      ),

      // خلفيات
      scaffoldBackgroundColor: AppColors.background,
      cardColor: AppColors.card,
      dialogTheme: DialogTheme(
        backgroundColor: AppColors.surface,
        elevation: AppDimensions.elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),

      // نصوص
      fontFamily: 'Cairo',
      textTheme: const TextTheme(
        displayLarge: AppTextStyles.headingLarge,
        displayMedium: AppTextStyles.headingMedium,
        displaySmall: AppTextStyles.headingSmall,
        headlineLarge: AppTextStyles.headingLarge,
        headlineMedium: AppTextStyles.headingMedium,
        headlineSmall: AppTextStyles.headingSmall,
        titleLarge: AppTextStyles.headingSmall,
        titleMedium: AppTextStyles.labelLarge,
        titleSmall: AppTextStyles.labelMedium,
        bodyLarge: AppTextStyles.bodyLarge,
        bodyMedium: AppTextStyles.bodyMedium,
        bodySmall: AppTextStyles.bodySmall,
        labelLarge: AppTextStyles.labelLarge,
        labelMedium: AppTextStyles.labelMedium,
        labelSmall: AppTextStyles.labelSmall,
      ),

      // أزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonPrimary,
          foregroundColor: AppColors.textOnPrimary,
          textStyle: AppTextStyles.buttonMedium,
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacingM,
            vertical: AppDimensions.spacingS,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          ),
          elevation: AppDimensions.elevationS,
          minimumSize: const Size(120, AppDimensions.buttonHeightM),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: AppTextStyles.buttonMedium.copyWith(color: AppColors.primary),
          side: const BorderSide(color: AppColors.primary, width: AppDimensions.borderWidthRegular),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacingM,
            vertical: AppDimensions.spacingS,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          ),
          minimumSize: const Size(120, AppDimensions.buttonHeightM),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: AppTextStyles.buttonMedium.copyWith(color: AppColors.primary),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacingM,
            vertical: AppDimensions.spacingS,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          ),
          minimumSize: const Size(120, AppDimensions.buttonHeightM),
        ),
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.spacingM,
          vertical: AppDimensions.spacingS,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          borderSide: const BorderSide(
            color: AppColors.border,
            width: AppDimensions.borderWidthRegular,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          borderSide: const BorderSide(
            color: AppColors.border,
            width: AppDimensions.borderWidthRegular,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          borderSide: const BorderSide(
            color: AppColors.primary,
            width: AppDimensions.borderWidthRegular,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          borderSide: const BorderSide(
            color: AppColors.error,
            width: AppDimensions.borderWidthRegular,
          ),
        ),
        labelStyle: AppTextStyles.labelLarge,
        hintStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.textHint),
        errorStyle: AppTextStyles.labelMedium.copyWith(color: AppColors.error),
      ),

      // بطاقات
      cardTheme: CardTheme(
        color: AppColors.card,
        elevation: AppDimensions.elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        margin: const EdgeInsets.all(AppDimensions.cardMargin),
      ),

      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppDimensions.elevationS,
        centerTitle: true,
        titleTextStyle: AppTextStyles.headingSmall,
      ),

      // تبويبات
      tabBarTheme: const TabBarTheme(
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        labelStyle: AppTextStyles.labelLarge,
        unselectedLabelStyle: AppTextStyles.labelLarge,
      ),

      // قوائم
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.spacingM,
          vertical: AppDimensions.spacingXS,
        ),
        minLeadingWidth: 24,
        iconColor: AppColors.primary,
      ),

      // فواصل
      dividerTheme: const DividerThemeData(
        color: AppColors.divider,
        thickness: AppDimensions.borderWidthRegular,
        space: AppDimensions.spacingM,
      ),

      // أيقونات
      iconTheme: const IconThemeData(
        color: AppColors.primary,
        size: AppDimensions.iconSizeM,
      ),

      // تأثيرات النقر
      splashColor: AppColors.primaryLight.withAlpha(76), // ~30% opacity
      highlightColor: AppColors.primaryLight.withAlpha(25), // ~10% opacity

      // ملاحظة: اتجاه النص يتم تحديده في MaterialApp
    );
  }

  /// ثيم النمط الداكن
  static ThemeData darkTheme() {
    return ThemeData(
      // ألوان أساسية
      primaryColor: AppColors.primary,
      primaryColorLight: AppColors.primaryLight,
      primaryColorDark: AppColors.primaryDark,
      colorScheme: const ColorScheme(
        primary: AppColors.primary,
        primaryContainer: AppColors.primaryDark,
        secondary: AppColors.primaryLight,
        secondaryContainer: AppColors.primaryLight,
        surface: AppColors.darkSurface,
        background: AppColors.darkBackground,
        error: AppColors.error,
        onPrimary: AppColors.textOnPrimary,
        onPrimaryContainer: AppColors.textOnPrimary,
        onSecondary: AppColors.textOnPrimary,
        onSecondaryContainer: AppColors.textOnPrimary,
        onSurface: AppColors.darkTextPrimary,
        onBackground: AppColors.darkTextPrimary,
        onError: AppColors.textOnPrimary,
        brightness: Brightness.dark,
      ),

      // خلفيات
      scaffoldBackgroundColor: AppColors.darkBackground,
      cardColor: AppColors.darkCard,
      dialogTheme: DialogTheme(
        backgroundColor: AppColors.darkSurface,
        elevation: AppDimensions.elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),

      // نصوص
      fontFamily: 'Cairo',
      textTheme: TextTheme(
        displayLarge: AppTextStyles.headingLarge.copyWith(color: AppColors.darkTextPrimary),
        displayMedium: AppTextStyles.headingMedium.copyWith(color: AppColors.darkTextPrimary),
        displaySmall: AppTextStyles.headingSmall.copyWith(color: AppColors.darkTextPrimary),
        headlineLarge: AppTextStyles.headingLarge.copyWith(color: AppColors.darkTextPrimary),
        headlineMedium: AppTextStyles.headingMedium.copyWith(color: AppColors.darkTextPrimary),
        headlineSmall: AppTextStyles.headingSmall.copyWith(color: AppColors.darkTextPrimary),
        titleLarge: AppTextStyles.headingSmall.copyWith(color: AppColors.darkTextPrimary),
        titleMedium: AppTextStyles.labelLarge.copyWith(color: AppColors.darkTextPrimary),
        titleSmall: AppTextStyles.labelMedium.copyWith(color: AppColors.darkTextPrimary),
        bodyLarge: AppTextStyles.bodyLarge.copyWith(color: AppColors.darkTextPrimary),
        bodyMedium: AppTextStyles.bodyMedium.copyWith(color: AppColors.darkTextPrimary),
        bodySmall: AppTextStyles.bodySmall.copyWith(color: AppColors.darkTextSecondary),
        labelLarge: AppTextStyles.labelLarge.copyWith(color: AppColors.darkTextPrimary),
        labelMedium: AppTextStyles.labelMedium.copyWith(color: AppColors.darkTextPrimary),
        labelSmall: AppTextStyles.labelSmall.copyWith(color: AppColors.darkTextSecondary),
      ),

      // ملاحظة: اتجاه النص يتم تحديده في MaterialApp
    );
  }
}
