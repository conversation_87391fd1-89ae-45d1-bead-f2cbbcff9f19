import 'dart:convert';
import 'permission.dart';

class User {
  final int id;
  final String name;
  final String? email;
  final String? phone;
  final String? avatar;
  final List<String> roles;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final String? permissions;
  final String? password;

  User({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.avatar,
    required this.roles,
    required this.isActive,
    required this.createdAt,
    this.lastLogin,
    this.permissions,
    this.password,
  });

  factory User.fromMap(Map<String, dynamic> map) {
    // Handle roles from database
    List<String> parsedRoles = [];

    // If roles is a direct list (from memory)
    if (map['roles'] is List) {
      parsedRoles = (map['roles'] as List).map((role) => role.toString()).toList();
    }
    // If roles come from user_roles join query (as a string list)
    else if (map['roles'] is String) {
      try {
        // Try to parse as JSON
        final rolesList = json.decode(map['roles'] as String);
        if (rolesList is List) {
          parsedRoles = rolesList.map((role) => role.toString()).toList();
        }
      } catch (e) {
        // If not JSON, might be comma-separated
        parsedRoles = (map['roles'] as String).split(',').map((s) => s.trim()).toList();
      }
    }

    return User(
      id: map['id'] is int ? map['id'] as int : int.parse(map['id'].toString()),
      name: map['name'] as String,
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      avatar: map['avatar'] as String?,
      roles: parsedRoles,
      isActive: map['is_active'] is bool
          ? map['is_active'] as bool
          : map['is_active'] is int
              ? (map['is_active'] as int) == 1
              : map['status'] == 'active',
      createdAt: map['created_at'] is DateTime
          ? map['created_at'] as DateTime
          : DateTime.parse(map['created_at'] as String),
      lastLogin: map['last_login'] != null
          ? map['last_login'] is DateTime
              ? map['last_login'] as DateTime
              : DateTime.parse(map['last_login'] as String)
          : null,
      permissions: map['permissions'] as String?,
      password: map['password'] as String?,
    );
  }

  factory User.fromJson(String source) =>
      User.fromMap(json.decode(source) as Map<String, dynamic>);

  factory User.fromFirestore(Map<String, dynamic> data, String firestoreId) {
    return User(
      id: 0, // Firestore users don't have integer IDs
      name: data['name'] as String? ?? '',
      email: data['email'] as String?,
      phone: data['phone'] as String?,
      avatar: data['avatar'] as String?,
      roles: data['role'] != null ? [data['role'] as String] : ['technician'],
      isActive: data['isActive'] as bool? ?? true,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'].toString())
          : DateTime.now(),
      lastLogin: data['lastLoginAt'] != null
          ? DateTime.parse(data['lastLoginAt'].toString())
          : null,
      permissions: data['permissions'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'roles': roles, // Store roles as a list (will need to handle this in DB insertion/retrieval)
      'status': isActive ? 'active' : 'inactive', // Store as status string
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'permissions': permissions, // Store permissions as JSON string
    };
  }

  String toJson() => json.encode(toMap());

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? avatar,
    List<String>? roles, // Changed to List<String>
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
    String? permissions,
    String? password, // Add password to copyWith
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      roles: roles ?? this.roles, // Copy the list of roles
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      permissions: permissions ?? this.permissions,
      password: password ?? this.password, // Copy password
    );
  }

  // Helper to check if the user has a specific role
  bool hasRole(String roleName) {
    return roles.contains(roleName);
  }

  // Keeping permission methods for now, assuming they are separate from database roles
  bool hasPermission(String permission) {
    // This logic might need adjustment based on how permissions relate to roles
    if (hasRole('admin')) return true; // Admins have all permissions
    if (permissions == null) return false;

    try {
      final permissionsMap = json.decode(permissions!) as Map<String, dynamic>;
      return permissionsMap[permission] == true;
    } catch (e) {
      return false;
    }
  }

  bool hasModulePermission(String module, String operation) {
    // This logic might need adjustment based on how permissions relate to roles
    if (hasRole('admin')) return true; // Admins have all permissions
    if (permissions == null) return false;

    try {
      final permissionsMap = json.decode(permissions!) as Map<String, dynamic>;
      final permissionKey = '${module}_$operation';
      return permissionsMap[permissionKey] == true;
    } catch (e) {
      return false;
    }
  }

  Map<String, dynamic> getPermissionsMap() {
    if (hasRole('admin')) {
      // Admin has all permissions
      final Map<String, dynamic> allPermissions = {};
      for (final module in PermissionModule.values) {
        final moduleName = module.toString().split('.').last;
        for (final operation in PermissionOperation.values) {
          final operationName = operation.toString().split('.').last;
          allPermissions['${moduleName}_$operationName'] = true;
        }
      }
      return allPermissions;
    }

    if (permissions == null) return {};

    try {
      return json.decode(permissions!) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  List<Permission> getPermissionsList() {
    final List<Permission> result = [];
    final permissionsMap = getPermissionsMap();

    for (final module in PermissionModule.values) {
      final moduleName = module.toString().split('.').last;
      for (final operation in PermissionOperation.values) {
        final operationName = operation.toString().split('.').last;
        final permissionKey = '${moduleName}_$operationName';

        result.add(Permission(
          module: module,
          operation: operation,
          granted: permissionsMap[permissionKey] == true,
        ));
      }
    }

    return result;
  }

  User updatePermissions(List<Permission> permissionsList) {
    final Map<String, dynamic> updatedPermissions = {};

    for (final permission in permissionsList) {
      final moduleName = permission.module.toString().split('.').last;
      final operationName = permission.operation.toString().split('.').last;
      updatedPermissions['${moduleName}_$operationName'] = permission.granted;
    }

    return copyWith(permissions: json.encode(updatedPermissions));
  }


  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, roles: $roles, password: ${password != null ? "[HIDDEN]" : "null"})'; // Updated toString to hide password
  }
}
