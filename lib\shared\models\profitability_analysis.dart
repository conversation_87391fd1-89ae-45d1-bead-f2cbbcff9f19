import 'dart:convert';
import 'package:flutter/material.dart';

/// نموذج تحليل الربحية
class ProfitabilityAnalysis {
  /// المعرف الفريد للتحليل
  final int? id;
  
  /// نوع العنصر المحلل (خدمة، منتج، عميل، موظف)
  final String entityType;
  
  /// معرف العنصر المحلل
  final int entityId;
  
  /// اسم العنصر المحلل
  final String entityName;
  
  /// الفترة الزمنية للتحليل (شهر، ربع سنة، سنة)
  final String period;
  
  /// تاريخ بداية الفترة
  final DateTime startDate;
  
  /// تاريخ نهاية الفترة
  final DateTime endDate;
  
  /// إجمالي الإيرادات
  final double totalRevenue;
  
  /// إجمالي التكاليف المباشرة
  final double directCosts;
  
  /// إجمالي التكاليف غير المباشرة
  final double indirectCosts;
  
  /// إجمالي الربح
  final double grossProfit;
  
  /// صافي الربح
  final double netProfit;
  
  /// هامش الربح (%)
  final double profitMargin;
  
  /// العائد على الاستثمار (%)
  final double roi;
  
  /// ملاحظات
  final String? notes;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime? updatedAt;

  /// إنشاء نموذج تحليل ربحية جديد
  ProfitabilityAnalysis({
    this.id,
    required this.entityType,
    required this.entityId,
    required this.entityName,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.totalRevenue,
    required this.directCosts,
    required this.indirectCosts,
    required this.grossProfit,
    required this.netProfit,
    required this.profitMargin,
    required this.roi,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نموذج من خريطة بيانات
  factory ProfitabilityAnalysis.fromMap(Map<String, dynamic> map) {
    return ProfitabilityAnalysis(
      id: map['id'] as int?,
      entityType: map['entity_type'] as String,
      entityId: map['entity_id'] as int,
      entityName: map['entity_name'] as String,
      period: map['period'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      totalRevenue: map['total_revenue'] as double,
      directCosts: map['direct_costs'] as double,
      indirectCosts: map['indirect_costs'] as double,
      grossProfit: map['gross_profit'] as double,
      netProfit: map['net_profit'] as double,
      profitMargin: map['profit_margin'] as double,
      roi: map['roi'] as double,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  /// تحويل النموذج إلى خريطة بيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entity_type': entityType,
      'entity_id': entityId,
      'entity_name': entityName,
      'period': period,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_revenue': totalRevenue,
      'direct_costs': directCosts,
      'indirect_costs': indirectCosts,
      'gross_profit': grossProfit,
      'net_profit': netProfit,
      'profit_margin': profitMargin,
      'roi': roi,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// تحويل النموذج إلى سلسلة JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من سلسلة JSON
  factory ProfitabilityAnalysis.fromJson(String source) =>
      ProfitabilityAnalysis.fromMap(json.decode(source) as Map<String, dynamic>);

  /// إنشاء نسخة معدلة من النموذج
  ProfitabilityAnalysis copyWith({
    int? id,
    String? entityType,
    int? entityId,
    String? entityName,
    String? period,
    DateTime? startDate,
    DateTime? endDate,
    double? totalRevenue,
    double? directCosts,
    double? indirectCosts,
    double? grossProfit,
    double? netProfit,
    double? profitMargin,
    double? roi,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProfitabilityAnalysis(
      id: id ?? this.id,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      entityName: entityName ?? this.entityName,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      directCosts: directCosts ?? this.directCosts,
      indirectCosts: indirectCosts ?? this.indirectCosts,
      grossProfit: grossProfit ?? this.grossProfit,
      netProfit: netProfit ?? this.netProfit,
      profitMargin: profitMargin ?? this.profitMargin,
      roi: roi ?? this.roi,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على اسم نوع الكيان
  static String getEntityTypeName(String entityType) {
    switch (entityType) {
      case 'service':
        return 'خدمة';
      case 'product':
        return 'منتج';
      case 'customer':
        return 'عميل';
      case 'employee':
        return 'موظف';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على اسم الفترة
  static String getPeriodName(String period) {
    switch (period) {
      case 'month':
        return 'شهر';
      case 'quarter':
        return 'ربع سنة';
      case 'year':
        return 'سنة';
      default:
        return 'غير معروف';
    }
  }
}
