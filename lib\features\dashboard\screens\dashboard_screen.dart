import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../state/auth_state.dart';
import '../../../shared/widgets/ui_components.dart';
import '../widgets/financial_summary.dart';
import '../widgets/quick_access_buttons.dart';
import '../widgets/charts_overview.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    // Simulate loading data
    await Future.delayed(const Duration(seconds: 1));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<AuthState>(context).user;

    if (_isLoading) {
      return const StyledScaffold(
        title: 'ركن الجليد - لوحة التحكم',
        body: Center(
          child: LinearProgressIndicator(
            backgroundColor: AppColors.background,
            color: AppColors.primary,
            minHeight: 4,
          ),
        ),
      );
    }

    return StyledScrollScaffold(
      title: 'ركن الجليد - لوحة التحكم',
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {
            Navigator.pushNamed(context, AppRoutes.notifications);
          },
        ),
        IconButton(
          icon: const Icon(Icons.settings_outlined),
          onPressed: () {
            Navigator.pushNamed(context, AppRoutes.settings);
          },
        ),
      ],
      onRefresh: _loadDashboardData,
      floatingActionButton: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withAlpha(100),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(
            Icons.add,
            color: Colors.white,
            size: 30,
          ),
          onPressed: () => _showQuickActionsMenu(context),
        ),
      ),
      children: [
        // رسالة الترحيب
        StyledCard(
          elevation: AppDimensions.elevationS,
          borderRadius: AppDimensions.radiusM,
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          child: Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppColors.primary,
                child: Text(
                  user?.name.substring(0, 1) ?? 'U',
                  style: const TextStyle(
                    fontSize: 24,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً، ${user?.name ?? "المستخدم"}',
                      style: AppTextStyles.headingMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'آخر تسجيل دخول: ${user?.lastLogin != null ? _formatDate(user!.lastLogin!) : "غير معروف"}',
                      style: AppTextStyles.labelSmall,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // ملخص مالي
        const FinancialSummary(),

        // أزرار الوصول السريع
        const QuickAccessButtons(),

        // نظرة عامة على الرسوم البيانية
        const ChartsOverview(),

        // النشاطات الأخيرة
        StyledCard(
          elevation: AppDimensions.elevationS,
          borderRadius: AppDimensions.radiusM,
          title: 'النشاطات الأخيرة',
          icon: Icons.history,
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Divider(),
              SizedBox(height: AppDimensions.spacingS),
              // قائمة النشاطات الأخيرة ستظهر هنا
              Center(
                child: Padding(
                  padding: EdgeInsets.all(AppDimensions.spacingL),
                  child: Text(
                    'لا توجد نشاطات حديثة',
                    style: AppTextStyles.bodyMedium,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    // Format date to a readable string
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showQuickActionsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.surface,
      elevation: AppDimensions.elevationL,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppDimensions.radiusL)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppDimensions.spacingL,
            horizontal: AppDimensions.spacingM,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.add_circle,
                    color: AppColors.primary,
                    size: 28,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'إضافة سريعة',
                    style: AppTextStyles.headingMedium,
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                    color: AppColors.textSecondary,
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: AppDimensions.spacingM),
              Wrap(
                spacing: AppDimensions.spacingM,
                runSpacing: AppDimensions.spacingM,
                alignment: WrapAlignment.center,
                children: [
                  _buildQuickActionItem(
                    context,
                    'فاتورة جديدة',
                    Icons.receipt_long,
                    AppColors.primary,
                    () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRoutes.addInvoice);
                    },
                  ),
                  _buildQuickActionItem(
                    context,
                    'إيراد جديد',
                    Icons.account_balance_wallet,
                    AppColors.primaryLight,
                    () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRoutes.transactions);
                    },
                  ),
                  _buildQuickActionItem(
                    context,
                    'عميل جديد',
                    Icons.person_add,
                    AppColors.accent,
                    () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRoutes.customers);
                    },
                  ),
                  _buildQuickActionItem(
                    context,
                    'طلب خدمة',
                    Icons.build,
                    Colors.purple,
                    () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRoutes.serviceRequests);
                    },
                  ),
                  _buildQuickActionItem(
                    context,
                    'تقرير الفواتير',
                    Icons.receipt,
                    Colors.deepOrange,
                    () {
                      Navigator.pop(context);
                      Navigator.pushNamed(context, AppRoutes.invoiceReport);
                    },
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacingL),
              StyledButton(
                label: 'إغلاق',
                icon: Icons.close,
                type: StyledButtonType.secondary,
                expanded: true,
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickActionItem(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ScaleOnTap(
      onTap: onTap,
      scaleFactor: 0.95,
      duration: AnimationStyles.shortDuration,
      child: Container(
        width: 100,
        padding: const EdgeInsets.all(AppDimensions.spacingM),
        decoration: BoxDecoration(
          color: color.withAlpha(25),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: color.withAlpha(76), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(15),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
