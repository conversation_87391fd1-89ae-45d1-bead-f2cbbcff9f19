import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:icecorner/shared/models/service_status.dart';

enum ServiceRequestStatus {
  pending,
  inProgress,
  completed,
  cancelled,
}

enum ServiceRequestPriority {
  low,
  medium,
  high,
  urgent,
}

enum ServiceRequestType {
  installation,
  maintenance,
  repair,
  inspection,
  other,
}

class ServiceRequest {
  final int? id;
  final String reference;
  final int? customerId;
  final String customerName;
  final String? customerPhone;
  final ServiceRequestType requestType;
  final String description;
  final ServiceRequestPriority priority;
  final ServiceRequestStatus status;
  final String? location;
  final int? assignedTo;
  final String? assignedToName;
  final double? technicianDailyRate;
  final DateTime scheduledDate;
  final DateTime? completedDate;
  final String? solution;
  final List<String>? usedParts;
  final List<int>? usedInventoryIds;
  final List<int>? usedInventoryQuantities;
  final List<String>? problemImages;
  final List<String>? solutionImages;
  final String? deviceType;
  final String? deviceBrand;
  final String? deviceModel;
  final String? serialNumber;
  final String? installationDate;
  final String? warrantyInfo;
  final String? technicalNotes;
  final int? categoryId;
  final String? categoryName;
  final int? invoiceId;
  final String? notes;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int reminderMinutes; // وقت التذكير بالدقائق قبل الموعد
  final double serviceAmount; // مبلغ الخدمة

  // الخصائص المضافة للتقارير
  String get requestNumber => reference;
  String get address => location ?? '';
  String get serviceType => getRequestTypeName(requestType);
  DateTime get date => scheduledDate;

  ServiceRequest({
    this.id,
    required this.reference,
    this.customerId,
    required this.customerName,
    this.customerPhone,
    required this.requestType,
    required this.description,
    required this.priority,
    required this.status,
    this.location,
    this.assignedTo,
    this.assignedToName,
    this.technicianDailyRate,
    required this.scheduledDate,
    this.completedDate,
    this.solution,
    this.usedParts,
    this.usedInventoryIds,
    this.usedInventoryQuantities,
    this.problemImages,
    this.solutionImages,
    this.deviceType,
    this.deviceBrand,
    this.deviceModel,
    this.serialNumber,
    this.installationDate,
    this.warrantyInfo,
    this.technicalNotes,
    this.categoryId,
    this.categoryName,
    this.invoiceId,
    this.notes,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.reminderMinutes = 60, // القيمة الافتراضية هي 60 دقيقة (ساعة واحدة)
    this.serviceAmount = 0, // القيمة الافتراضية هي 0
  });

  factory ServiceRequest.fromMap(Map<String, dynamic> map) {
    return ServiceRequest(
      id: map['id'] as int?,
      reference: map['reference'] as String,
      customerId: map['customer_id'] as int?,
      customerName: map['customer_name'] as String,
      customerPhone: map['customer_phone'] as String?,
      requestType: _parseRequestType(map['request_type'] as String),
      description: map['description'] as String,
      priority: _parsePriority(map['priority'] as String),
      status: _parseStatus(map['status'] as String),
      location: map['location'] as String?,
      assignedTo: map['assigned_to'] as int?,
      assignedToName: map['assigned_to_name'] as String?,
      technicianDailyRate: map['technician_daily_rate'] != null
          ? (map['technician_daily_rate'] as num).toDouble()
          : null,
      scheduledDate: DateTime.parse(map['scheduled_date'] as String),
      completedDate: map['completed_date'] != null
          ? DateTime.parse(map['completed_date'] as String)
          : null,
      solution: map['solution'] as String?,
      usedParts: map['used_parts'] != null
          ? List<String>.from(json.decode(map['used_parts'] as String))
          : null,
      usedInventoryIds: map['used_inventory_ids'] != null
          ? List<int>.from(json.decode(map['used_inventory_ids'] as String))
          : null,
      usedInventoryQuantities: map['used_inventory_quantities'] != null
          ? List<int>.from(json.decode(map['used_inventory_quantities'] as String))
          : null,
      problemImages: map['problem_images'] != null
          ? List<String>.from(json.decode(map['problem_images'] as String))
          : null,
      solutionImages: map['solution_images'] != null
          ? List<String>.from(json.decode(map['solution_images'] as String))
          : null,
      deviceType: map['device_type'] as String?,
      deviceBrand: map['device_brand'] as String?,
      deviceModel: map['device_model'] as String?,
      serialNumber: map['serial_number'] as String?,
      installationDate: map['installation_date'] as String?,
      warrantyInfo: map['warranty_info'] as String?,
      technicalNotes: map['technical_notes'] as String?,
      categoryId: map['category_id'] as int?,
      categoryName: map['category_name'] as String?,
      invoiceId: map['invoice_id'] as int?,
      notes: map['notes'] as String?,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      reminderMinutes: map['reminder_minutes'] != null
          ? map['reminder_minutes'] as int
          : 60, // القيمة الافتراضية هي 60 دقيقة (ساعة واحدة)
      serviceAmount: map['service_amount'] != null
          ? (map['service_amount'] as num).toDouble()
          : 0, // القيمة الافتراضية هي 0
    );
  }

  factory ServiceRequest.fromJson(String source) =>
      ServiceRequest.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reference': reference,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'request_type': requestType.toString().split('.').last,
      'description': description,
      'priority': priority.toString().split('.').last,
      'status': status.toString().split('.').last,
      'location': location,
      'assigned_to': assignedTo,
      'assigned_to_name': assignedToName,
      'technician_daily_rate': technicianDailyRate,
      'scheduled_date': scheduledDate.toIso8601String(),
      'completed_date': completedDate?.toIso8601String(),
      'solution': solution,
      'used_parts': usedParts != null ? json.encode(usedParts) : null,
      'used_inventory_ids': usedInventoryIds != null ? json.encode(usedInventoryIds) : null,
      'used_inventory_quantities': usedInventoryQuantities != null ? json.encode(usedInventoryQuantities) : null,
      'problem_images': problemImages != null ? json.encode(problemImages) : null,
      'solution_images': solutionImages != null ? json.encode(solutionImages) : null,
      'device_type': deviceType,
      'device_brand': deviceBrand,
      'device_model': deviceModel,
      'serial_number': serialNumber,
      'installation_date': installationDate,
      'warranty_info': warrantyInfo,
      'technical_notes': technicalNotes,
      'category_id': categoryId,
      'category_name': categoryName,
      'invoice_id': invoiceId,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'reminder_minutes': reminderMinutes,
      'service_amount': serviceAmount,
    };
  }

  String toJson() => json.encode(toMap());

  ServiceRequest copyWith({
    int? id,
    String? reference,
    int? customerId,
    String? customerName,
    String? customerPhone,
    ServiceRequestType? requestType,
    String? description,
    ServiceRequestPriority? priority,
    ServiceRequestStatus? status,
    String? location,
    int? assignedTo,
    String? assignedToName,
    double? technicianDailyRate,
    DateTime? scheduledDate,
    DateTime? completedDate,
    String? solution,
    List<String>? usedParts,
    List<int>? usedInventoryIds,
    List<int>? usedInventoryQuantities,
    List<String>? problemImages,
    List<String>? solutionImages,
    String? deviceType,
    String? deviceBrand,
    String? deviceModel,
    String? serialNumber,
    String? installationDate,
    String? warrantyInfo,
    String? technicalNotes,
    int? categoryId,
    String? categoryName,
    int? invoiceId,
    String? notes,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? reminderMinutes,
    double? serviceAmount,
  }) {
    return ServiceRequest(
      id: id ?? this.id,
      reference: reference ?? this.reference,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      requestType: requestType ?? this.requestType,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      location: location ?? this.location,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedToName: assignedToName ?? this.assignedToName,
      technicianDailyRate: technicianDailyRate ?? this.technicianDailyRate,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      completedDate: completedDate ?? this.completedDate,
      solution: solution ?? this.solution,
      usedParts: usedParts ?? this.usedParts,
      usedInventoryIds: usedInventoryIds ?? this.usedInventoryIds,
      usedInventoryQuantities: usedInventoryQuantities ?? this.usedInventoryQuantities,
      problemImages: problemImages ?? this.problemImages,
      solutionImages: solutionImages ?? this.solutionImages,
      deviceType: deviceType ?? this.deviceType,
      deviceBrand: deviceBrand ?? this.deviceBrand,
      deviceModel: deviceModel ?? this.deviceModel,
      serialNumber: serialNumber ?? this.serialNumber,
      installationDate: installationDate ?? this.installationDate,
      warrantyInfo: warrantyInfo ?? this.warrantyInfo,
      technicalNotes: technicalNotes ?? this.technicalNotes,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      invoiceId: invoiceId ?? this.invoiceId,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
      serviceAmount: serviceAmount ?? this.serviceAmount,
    );
  }

  static ServiceRequestStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ServiceRequestStatus.pending;
      case 'in_progress':
      case 'inprogress':
        return ServiceRequestStatus.inProgress;
      case 'completed':
        return ServiceRequestStatus.completed;
      case 'cancelled':
        return ServiceRequestStatus.cancelled;
      default:
        return ServiceRequestStatus.pending;
    }
  }

  static ServiceRequestPriority _parsePriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return ServiceRequestPriority.low;
      case 'medium':
        return ServiceRequestPriority.medium;
      case 'high':
        return ServiceRequestPriority.high;
      case 'urgent':
        return ServiceRequestPriority.urgent;
      default:
        return ServiceRequestPriority.medium;
    }
  }

  static ServiceRequestType _parseRequestType(String type) {
    switch (type.toLowerCase()) {
      case 'installation':
        return ServiceRequestType.installation;
      case 'maintenance':
        return ServiceRequestType.maintenance;
      case 'repair':
        return ServiceRequestType.repair;
      case 'inspection':
        return ServiceRequestType.inspection;
      default:
        return ServiceRequestType.other;
    }
  }

  static String getStatusName(ServiceRequestStatus status) {
    switch (status) {
      case ServiceRequestStatus.pending:
        return 'معلق';
      case ServiceRequestStatus.inProgress:
        return 'قيد التنفيذ';
      case ServiceRequestStatus.completed:
        return 'مكتمل';
      case ServiceRequestStatus.cancelled:
        return 'ملغى';
    }
  }

  static Color getStatusColor(ServiceRequestStatus status) {
    switch (status) {
      case ServiceRequestStatus.pending:
        return Colors.amber;
      case ServiceRequestStatus.inProgress:
        return Colors.blue;
      case ServiceRequestStatus.completed:
        return Colors.green;
      case ServiceRequestStatus.cancelled:
        return Colors.red;
    }
  }

  static String getPriorityName(ServiceRequestPriority priority) {
    switch (priority) {
      case ServiceRequestPriority.low:
        return 'منخفضة';
      case ServiceRequestPriority.medium:
        return 'متوسطة';
      case ServiceRequestPriority.high:
        return 'عالية';
      case ServiceRequestPriority.urgent:
        return 'عاجلة';
    }
  }

  static Color getPriorityColor(ServiceRequestPriority priority) {
    switch (priority) {
      case ServiceRequestPriority.low:
        return Colors.green;
      case ServiceRequestPriority.medium:
        return Colors.blue;
      case ServiceRequestPriority.high:
        return Colors.orange;
      case ServiceRequestPriority.urgent:
        return Colors.red;
    }
  }

  static String getRequestTypeName(ServiceRequestType type) {
    switch (type) {
      case ServiceRequestType.installation:
        return 'تركيب';
      case ServiceRequestType.maintenance:
        return 'صيانة';
      case ServiceRequestType.repair:
        return 'إصلاح';
      case ServiceRequestType.inspection:
        return 'فحص';
      case ServiceRequestType.other:
        return 'أخرى';
    }
  }

  static Color getRequestTypeColor(ServiceRequestType type) {
    switch (type) {
      case ServiceRequestType.installation:
        return Colors.blue;
      case ServiceRequestType.maintenance:
        return Colors.green;
      case ServiceRequestType.repair:
        return Colors.red;
      case ServiceRequestType.inspection:
        return Colors.purple;
      case ServiceRequestType.other:
        return Colors.grey;
    }
  }

  // تحويل حالة طلب الخدمة إلى ServiceStatus
  static ServiceStatus toServiceStatus(ServiceRequestStatus status) {
    switch (status) {
      case ServiceRequestStatus.pending:
        return ServiceStatus.pending;
      case ServiceRequestStatus.inProgress:
        return ServiceStatus.inProgress;
      case ServiceRequestStatus.completed:
        return ServiceStatus.completed;
      case ServiceRequestStatus.cancelled:
        return ServiceStatus.cancelled;
    }
  }

  // تحويل ServiceStatus إلى حالة طلب الخدمة
  static ServiceRequestStatus fromServiceStatus(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.pending:
        return ServiceRequestStatus.pending;
      case ServiceStatus.inProgress:
        return ServiceRequestStatus.inProgress;
      case ServiceStatus.completed:
        return ServiceRequestStatus.completed;
      case ServiceStatus.cancelled:
        return ServiceRequestStatus.cancelled;
    }
  }
}
