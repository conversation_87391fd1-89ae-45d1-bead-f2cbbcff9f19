import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';

/// Migration to add cash_boxes and cash_box_transactions tables
class AddCashBoxesTable {
  static Future<void> migrate(Database db) async {
    try {
      // Check if cash_boxes table exists
      final List<Map<String, dynamic>> tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='cash_boxes'"
      );

      if (tables.isEmpty) {
        if (kDebugMode) {
          print('إنشاء جدول الصناديق (cash_boxes)...');
        }

        // Create cash_boxes table
        await db.execute('''
          CREATE TABLE cash_boxes(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            opening_balance REAL NOT NULL DEFAULT 0.0,
            current_balance REAL NOT NULL DEFAULT 0.0,
            is_active INTEGER NOT NULL DEFAULT 1,
            location TEXT,
            responsible_person TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT,
            firestore_id TEXT
          )
        ''');

        // Create cash_box_transactions table
        await db.execute('''
          CREATE TABLE cash_box_transactions(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cash_box_id INTEGER NOT NULL,
            type TEXT NOT NULL,
            amount REAL NOT NULL,
            description TEXT NOT NULL,
            reference TEXT,
            reference_type TEXT,
            transaction_date TEXT NOT NULL,
            created_at TEXT NOT NULL,
            firestore_id TEXT,
            FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE CASCADE
          )
        ''');

        // Create indexes for cash boxes
        await db.execute('CREATE INDEX idx_cash_boxes_name ON cash_boxes(name)');
        await db.execute('CREATE INDEX idx_cash_boxes_is_active ON cash_boxes(is_active)');
        await db.execute('CREATE INDEX idx_cash_boxes_firestore_id ON cash_boxes(firestore_id)');

        // Create indexes for cash box transactions
        await db.execute('CREATE INDEX idx_cash_box_transactions_cash_box_id ON cash_box_transactions(cash_box_id)');
        await db.execute('CREATE INDEX idx_cash_box_transactions_type ON cash_box_transactions(type)');
        await db.execute('CREATE INDEX idx_cash_box_transactions_date ON cash_box_transactions(transaction_date)');

        // Insert default cash box
        await db.insert('cash_boxes', {
          'name': 'الصندوق الرئيسي',
          'description': 'الصندوق الرئيسي للشركة',
          'opening_balance': 0.0,
          'current_balance': 0.0,
          'is_active': 1,
          'location': 'المكتب الرئيسي',
          'responsible_person': 'المدير',
          'created_at': DateTime.now().toIso8601String(),
        });

        if (kDebugMode) {
          print('✅ تم إنشاء جدول الصناديق بنجاح');
        }
      } else {
        if (kDebugMode) {
          print('تم تخطي الهجرة: جدول الصناديق موجود بالفعل');
        }
      }

      // Check if cash_box_transactions table exists
      final List<Map<String, dynamic>> transactionTables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='cash_box_transactions'"
      );

      if (transactionTables.isEmpty) {
        if (kDebugMode) {
          print('إنشاء جدول معاملات الصناديق (cash_box_transactions)...');
        }

        // Create cash_box_transactions table if it doesn't exist
        await db.execute('''
          CREATE TABLE cash_box_transactions(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cash_box_id INTEGER NOT NULL,
            type TEXT NOT NULL,
            amount REAL NOT NULL,
            description TEXT NOT NULL,
            reference TEXT,
            reference_type TEXT,
            transaction_date TEXT NOT NULL,
            created_at TEXT NOT NULL,
            firestore_id TEXT,
            FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE CASCADE
          )
        ''');

        // Create indexes for cash box transactions
        await db.execute('CREATE INDEX idx_cash_box_transactions_cash_box_id ON cash_box_transactions(cash_box_id)');
        await db.execute('CREATE INDEX idx_cash_box_transactions_type ON cash_box_transactions(type)');
        await db.execute('CREATE INDEX idx_cash_box_transactions_date ON cash_box_transactions(transaction_date)');

        if (kDebugMode) {
          print('✅ تم إنشاء جدول معاملات الصناديق بنجاح');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء جدول الصناديق: $e');
      }
      rethrow;
    }
  }
}
