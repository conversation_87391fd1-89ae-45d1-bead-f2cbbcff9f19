import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:share_plus/share_plus.dart';
import 'package:printing/printing.dart';

/// شاشة عرض ملفات PDF داخل التطبيق
class PdfViewerScreen extends StatefulWidget {
  final String pdfPath;
  final String title;

  const PdfViewerScreen({
    super.key,
    required this.pdfPath,
    required this.title,
  });

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  late PDFViewController _pdfViewController;
  int _totalPages = 0;
  int _currentPage = 0;
  bool _isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        centerTitle: true,
        actions: [
          // زر لمشاركة الملف
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _sharePdf,
            tooltip: 'مشاركة',
          ),
          // زر للطباعة
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printPdf,
            tooltip: 'طباعة',
          ),
        ],
      ),
      body: Stack(
        children: [
          // عرض ملف PDF
          PDFView(
            filePath: widget.pdfPath,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageFling: true,
            pageSnap: true,
            defaultPage: _currentPage,
            fitPolicy: FitPolicy.BOTH,
            preventLinkNavigation: false,
            onRender: (pages) {
              setState(() {
                _totalPages = pages!;
                _isLoading = false;
              });
            },
            onError: (error) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('حدث خطأ أثناء تحميل الملف: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            onPageError: (page, error) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('حدث خطأ في الصفحة $page: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            onViewCreated: (PDFViewController pdfViewController) {
              _pdfViewController = pdfViewController;
            },
            onPageChanged: (int? page, int? total) {
              if (page != null) {
                setState(() {
                  _currentPage = page;
                });
              }
            },
          ),
          // مؤشر التحميل
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
          // شريط التنقل بين الصفحات
          if (!_isLoading && _totalPages > 0)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Container(
                height: 50,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الصفحة السابقة
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: _currentPage > 0
                          ? () {
                              _pdfViewController.setPage(_currentPage - 1);
                            }
                          : null,
                    ),
                    // عرض رقم الصفحة الحالية وإجمالي الصفحات
                    Text(
                      'صفحة ${_currentPage + 1} من $_totalPages',
                      style: const TextStyle(fontSize: 16),
                    ),
                    // زر الصفحة التالية
                    IconButton(
                      icon: const Icon(Icons.arrow_forward_ios),
                      onPressed: _currentPage < _totalPages - 1
                          ? () {
                              _pdfViewController.setPage(_currentPage + 1);
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// مشاركة ملف PDF
  void _sharePdf() async {
    try {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري مشاركة الملف...'),
          backgroundColor: Colors.green,
        ),
      );

      final file = File(widget.pdfPath);
      if (await file.exists()) {
        // استخدام مكتبة share_plus لمشاركة الملف
        await Share.shareXFiles(
          [XFile(widget.pdfPath)],
          text: 'مشاركة تقرير',
          subject: widget.title,
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('الملف غير موجود'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء مشاركة الملف: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// طباعة ملف PDF
  void _printPdf() async {
    try {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري إرسال الملف للطباعة...'),
          backgroundColor: Colors.green,
        ),
      );

      final file = File(widget.pdfPath);
      if (await file.exists()) {
        final pdfData = await file.readAsBytes();
        await Printing.layoutPdf(
          onLayout: (_) => pdfData,
          name: widget.title,
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('الملف غير موجود'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء طباعة الملف: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
