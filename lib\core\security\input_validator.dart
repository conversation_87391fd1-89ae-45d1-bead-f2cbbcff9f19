import 'package:flutter/foundation.dart';

/// نظام التحقق من صحة البيانات المدخلة
class InputValidator {
  static const String _tag = 'InputValidator';

  /// التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }

    final email = value.trim();
    
    // التحقق من الطول
    if (email.length > 254) {
      return 'البريد الإلكتروني طويل جداً';
    }

    // التحقق من النمط
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(email)) {
      return 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من الأحرف الخطيرة
    if (_containsDangerousCharacters(email)) {
      return 'البريد الإلكتروني يحتوي على أحرف غير مسموحة';
    }

    return null;
  }

  /// التحقق من صحة رقم الهاتف
  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'رقم الهاتف مطلوب';
    }

    final phone = value.trim().replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // التحقق من الطول
    if (phone.length < 10 || phone.length > 15) {
      return 'رقم الهاتف يجب أن يكون بين 10-15 رقم';
    }

    // التحقق من أن جميع الأحرف أرقام أو علامة +
    final phoneRegex = RegExp(r'^\+?[0-9]+$');
    if (!phoneRegex.hasMatch(phone)) {
      return 'رقم الهاتف يجب أن يحتوي على أرقام فقط';
    }

    // التحقق من الأرقام السعودية
    if (phone.startsWith('05') || phone.startsWith('+9665')) {
      return null;
    }

    // قبول الأرقام الدولية
    if (phone.startsWith('+')) {
      return null;
    }

    return 'رقم الهاتف غير صحيح';
  }

  /// التحقق من صحة كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }

    // التحقق من الطول الأدنى
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    // التحقق من الطول الأقصى
    if (value.length > 128) {
      return 'كلمة المرور طويلة جداً';
    }

    // التحقق من وجود أحرف خطيرة
    if (_containsDangerousCharacters(value)) {
      return 'كلمة المرور تحتوي على أحرف غير مسموحة';
    }

    return null;
  }

  /// التحقق من صحة الاسم
  static String? validateName(String? value, {int minLength = 2, int maxLength = 100}) {
    if (value == null || value.trim().isEmpty) {
      return 'الاسم مطلوب';
    }

    final name = value.trim();

    // التحقق من الطول
    if (name.length < minLength) {
      return 'الاسم يجب أن يكون $minLength أحرف على الأقل';
    }

    if (name.length > maxLength) {
      return 'الاسم طويل جداً (الحد الأقصى $maxLength حرف)';
    }

    // التحقق من الأحرف المسموحة (عربي وإنجليزي ومسافات)
    final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
    if (!nameRegex.hasMatch(name)) {
      return 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط';
    }

    // التحقق من عدم وجود مسافات متتالية
    if (name.contains(RegExp(r'\s{2,}'))) {
      return 'الاسم لا يجب أن يحتوي على مسافات متتالية';
    }

    return null;
  }

  /// التحقق من صحة المبلغ المالي
  static String? validateAmount(String? value, {double? minAmount, double? maxAmount}) {
    if (value == null || value.trim().isEmpty) {
      return 'المبلغ مطلوب';
    }

    final amount = double.tryParse(value.trim());
    if (amount == null) {
      return 'المبلغ غير صحيح';
    }

    if (amount < 0) {
      return 'المبلغ لا يمكن أن يكون سالباً';
    }

    if (minAmount != null && amount < minAmount) {
      return 'المبلغ يجب أن يكون $minAmount على الأقل';
    }

    if (maxAmount != null && amount > maxAmount) {
      return 'المبلغ يجب أن يكون $maxAmount كحد أقصى';
    }

    // التحقق من عدد الخانات العشرية
    final decimalPlaces = value.split('.').length > 1 ? value.split('.')[1].length : 0;
    if (decimalPlaces > 2) {
      return 'المبلغ لا يجب أن يحتوي على أكثر من خانتين عشريتين';
    }

    return null;
  }

  /// التحقق من صحة النص العام
  static String? validateText(String? value, {
    required String fieldName,
    int minLength = 1,
    int maxLength = 500,
    bool allowEmpty = false,
  }) {
    if (value == null || value.trim().isEmpty) {
      return allowEmpty ? null : '$fieldName مطلوب';
    }

    final text = value.trim();

    // التحقق من الطول
    if (text.length < minLength) {
      return '$fieldName يجب أن يكون $minLength أحرف على الأقل';
    }

    if (text.length > maxLength) {
      return '$fieldName طويل جداً (الحد الأقصى $maxLength حرف)';
    }

    // التحقق من الأحرف الخطيرة
    if (_containsDangerousCharacters(text)) {
      return '$fieldName يحتوي على أحرف غير مسموحة';
    }

    return null;
  }

  /// التحقق من صحة الرقم الصحيح
  static String? validateInteger(String? value, {
    required String fieldName,
    int? minValue,
    int? maxValue,
    bool allowEmpty = false,
  }) {
    if (value == null || value.trim().isEmpty) {
      return allowEmpty ? null : '$fieldName مطلوب';
    }

    final number = int.tryParse(value.trim());
    if (number == null) {
      return '$fieldName يجب أن يكون رقماً صحيحاً';
    }

    if (minValue != null && number < minValue) {
      return '$fieldName يجب أن يكون $minValue على الأقل';
    }

    if (maxValue != null && number > maxValue) {
      return '$fieldName يجب أن يكون $maxValue كحد أقصى';
    }

    return null;
  }

  /// التحقق من صحة التاريخ
  static String? validateDate(String? value, {
    required String fieldName,
    DateTime? minDate,
    DateTime? maxDate,
    bool allowEmpty = false,
  }) {
    if (value == null || value.trim().isEmpty) {
      return allowEmpty ? null : '$fieldName مطلوب';
    }

    final date = DateTime.tryParse(value.trim());
    if (date == null) {
      return '$fieldName غير صحيح';
    }

    if (minDate != null && date.isBefore(minDate)) {
      return '$fieldName لا يمكن أن يكون قبل ${_formatDate(minDate)}';
    }

    if (maxDate != null && date.isAfter(maxDate)) {
      return '$fieldName لا يمكن أن يكون بعد ${_formatDate(maxDate)}';
    }

    return null;
  }

  /// التحقق من وجود أحرف خطيرة
  static bool _containsDangerousCharacters(String value) {
    // قائمة الأحرف والرموز الخطيرة
    final dangerousPatterns = [
      RegExp(r'<script', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),
      RegExp(r'<iframe', caseSensitive: false),
      RegExp(r'<object', caseSensitive: false),
      RegExp(r'<embed', caseSensitive: false),
      RegExp(r'<link', caseSensitive: false),
      RegExp(r'<meta', caseSensitive: false),
      RegExp(r'<style', caseSensitive: false),
      RegExp(r'expression\s*\(', caseSensitive: false),
      RegExp(r'url\s*\(', caseSensitive: false),
      RegExp(r'@import', caseSensitive: false),
      RegExp(r'vbscript:', caseSensitive: false),
      RegExp(r'data:', caseSensitive: false),
    ];

    for (final pattern in dangerousPatterns) {
      if (pattern.hasMatch(value)) {
        if (kDebugMode) {
          print('$_tag: تم اكتشاف نمط خطير: ${pattern.pattern}');
        }
        return true;
      }
    }

    return false;
  }

  /// تنسيق التاريخ للعرض
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تنظيف النص من الأحرف الخطيرة
  static String sanitizeText(String text) {
    // إزالة الأحرف الخطيرة
    String sanitized = text
        .replaceAll(RegExp(r'<[^>]*>'), '') // إزالة HTML tags
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'vbscript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), '')
        .replaceAll(RegExp(r'expression\s*\(', caseSensitive: false), '')
        .replaceAll(RegExp(r'url\s*\(', caseSensitive: false), '')
        .replaceAll(RegExp(r'@import', caseSensitive: false), '')
        .replaceAll(RegExp(r'data:', caseSensitive: false), '');

    // تنظيف المسافات الزائدة
    sanitized = sanitized.replaceAll(RegExp(r'\s+'), ' ').trim();

    return sanitized;
  }

  /// التحقق من صحة URL
  static String? validateUrl(String? value, {bool allowEmpty = true}) {
    if (value == null || value.trim().isEmpty) {
      return allowEmpty ? null : 'الرابط مطلوب';
    }

    final url = value.trim();
    
    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http') && !uri.scheme.startsWith('https'))) {
        return 'الرابط يجب أن يبدأ بـ http:// أو https://';
      }
      
      if (!uri.hasAuthority) {
        return 'الرابط غير صحيح';
      }
      
      return null;
    } catch (e) {
      return 'الرابط غير صحيح';
    }
  }

  /// التحقق من صحة الرقم القومي السعودي
  static String? validateSaudiNationalId(String? value, {bool allowEmpty = true}) {
    if (value == null || value.trim().isEmpty) {
      return allowEmpty ? null : 'رقم الهوية مطلوب';
    }

    final id = value.trim();
    
    // التحقق من الطول
    if (id.length != 10) {
      return 'رقم الهوية يجب أن يكون 10 أرقام';
    }

    // التحقق من أن جميع الأحرف أرقام
    if (!RegExp(r'^\d{10}$').hasMatch(id)) {
      return 'رقم الهوية يجب أن يحتوي على أرقام فقط';
    }

    // التحقق من الرقم الأول (يجب أن يكون 1 أو 2)
    if (!id.startsWith('1') && !id.startsWith('2')) {
      return 'رقم الهوية يجب أن يبدأ بـ 1 أو 2';
    }

    return null;
  }
}
