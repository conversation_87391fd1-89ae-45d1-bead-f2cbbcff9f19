import 'dart:convert';

/// Employee financial model for salary and advance payments only
class EmployeeFinancial {
  final int? id;
  final int employeeId;
  final String type; // 'salary' or 'advance'
  final double amount;
  final DateTime paymentDate;
  final String? description;
  final String? paymentMethod;
  final int? cashBoxId;
  final String status; // 'pending', 'paid', 'cancelled'
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? firestoreId;

  const EmployeeFinancial({
    this.id,
    required this.employeeId,
    required this.type,
    required this.amount,
    required this.paymentDate,
    this.description,
    this.paymentMethod,
    this.cashBoxId,
    this.status = 'pending',
    required this.createdAt,
    this.updatedAt,
    this.firestoreId,
  });

  /// Check if this is a salary payment
  bool get isSalary => type == EmployeeFinancialType.salary;

  /// Check if this is an advance payment
  bool get isAdvance => type == EmployeeFinancialType.advance;

  /// Check if payment is completed
  bool get isPaid => status == 'paid';

  EmployeeFinancial copyWith({
    int? id,
    int? employeeId,
    String? type,
    double? amount,
    DateTime? paymentDate,
    String? description,
    String? paymentMethod,
    int? cashBoxId,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? firestoreId,
  }) {
    return EmployeeFinancial(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      description: description ?? this.description,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      cashBoxId: cashBoxId ?? this.cashBoxId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      firestoreId: firestoreId ?? this.firestoreId,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'employee_id': employeeId,
      'type': type,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'description': description,
      'payment_method': paymentMethod,
      'cash_box_id': cashBoxId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'firestore_id': firestoreId,
    };
  }

  Map<String, dynamic> toFirestoreMap() {
    return <String, dynamic>{
      'employee_id': employeeId,
      'type': type,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'description': description,
      'payment_method': paymentMethod,
      'cash_box_id': cashBoxId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory EmployeeFinancial.fromMap(Map<String, dynamic> map) {
    return EmployeeFinancial(
      id: map['id'] != null ? map['id'] as int : null,
      employeeId: map['employee_id'] as int,
      type: map['type'] as String,
      amount: (map['amount'] as num).toDouble(),
      paymentDate: DateTime.parse(map['payment_date'] as String),
      description: map['description'] != null ? map['description'] as String : null,
      paymentMethod: map['payment_method'] != null ? map['payment_method'] as String : null,
      cashBoxId: map['cash_box_id'] != null ? map['cash_box_id'] as int : null,
      status: map['status'] as String? ?? 'pending',
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String) 
          : null,
      firestoreId: map['firestore_id'] != null ? map['firestore_id'] as String : null,
    );
  }

  factory EmployeeFinancial.fromFirestore(Map<String, dynamic> data, String firestoreId) {
    return EmployeeFinancial(
      firestoreId: firestoreId,
      employeeId: data['employee_id'] as int,
      type: data['type'] as String,
      amount: (data['amount'] as num).toDouble(),
      paymentDate: DateTime.parse(data['payment_date'] as String),
      description: data['description'] as String?,
      paymentMethod: data['payment_method'] as String?,
      cashBoxId: data['cash_box_id'] as int?,
      status: data['status'] as String? ?? 'pending',
      createdAt: data['created_at'] != null
          ? DateTime.parse(data['created_at'] as String)
          : DateTime.now(),
      updatedAt: data['updated_at'] != null
          ? DateTime.parse(data['updated_at'] as String)
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory EmployeeFinancial.fromJson(String source) =>
      EmployeeFinancial.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'EmployeeFinancial(id: $id, employeeId: $employeeId, type: $type, amount: $amount, status: $status)';
  }

  @override
  bool operator ==(covariant EmployeeFinancial other) {
    if (identical(this, other)) return true;
  
    return 
      other.id == id &&
      other.employeeId == employeeId &&
      other.type == type &&
      other.amount == amount &&
      other.paymentDate == paymentDate &&
      other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      employeeId.hashCode ^
      type.hashCode ^
      amount.hashCode ^
      paymentDate.hashCode ^
      status.hashCode;
  }
}

/// Employee financial transaction types
class EmployeeFinancialType {
  static const String salary = 'salary';
  static const String advance = 'advance';
}

/// Employee financial status
class EmployeeFinancialStatus {
  static const String pending = 'pending';
  static const String paid = 'paid';
  static const String cancelled = 'cancelled';
}

/// Employee salary calculation helper
class EmployeeSalaryCalculator {
  /// Calculate monthly salary for an employee
  static double calculateMonthlySalary(double annualSalary) {
    return annualSalary / 12;
  }

  /// Calculate daily salary for an employee
  static double calculateDailySalary(double monthlySalary, int workingDaysPerMonth) {
    return monthlySalary / workingDaysPerMonth;
  }

  /// Calculate hourly salary for an employee
  static double calculateHourlySalary(double monthlySalary, int workingHoursPerMonth) {
    return monthlySalary / workingHoursPerMonth;
  }

  /// Calculate overtime pay
  static double calculateOvertimePay(double hourlyRate, double overtimeHours, double overtimeMultiplier) {
    return hourlyRate * overtimeHours * overtimeMultiplier;
  }

  /// Calculate advance payment limit (typically 50% of monthly salary)
  static double calculateAdvanceLimit(double monthlySalary, {double percentage = 0.5}) {
    return monthlySalary * percentage;
  }

  /// Calculate remaining advance balance for an employee
  static double calculateRemainingAdvanceBalance(
    double totalAdvances,
    double monthlySalary, {
    double maxPercentage = 0.5,
  }) {
    final maxAdvance = calculateAdvanceLimit(monthlySalary, percentage: maxPercentage);
    return maxAdvance - totalAdvances;
  }
}

/// Employee financial summary
class EmployeeFinancialSummary {
  final int employeeId;
  final String employeeName;
  final double monthlySalary;
  final double totalSalaryPaid;
  final double totalAdvancesPaid;
  final double pendingSalary;
  final double pendingAdvances;
  final double remainingAdvanceLimit;
  final DateTime lastPaymentDate;

  const EmployeeFinancialSummary({
    required this.employeeId,
    required this.employeeName,
    required this.monthlySalary,
    required this.totalSalaryPaid,
    required this.totalAdvancesPaid,
    required this.pendingSalary,
    required this.pendingAdvances,
    required this.remainingAdvanceLimit,
    required this.lastPaymentDate,
  });

  /// Get total amount paid to employee
  double get totalPaid => totalSalaryPaid + totalAdvancesPaid;

  /// Get total pending payments
  double get totalPending => pendingSalary + pendingAdvances;

  /// Check if employee has pending payments
  bool get hasPendingPayments => totalPending > 0;

  /// Check if employee can receive more advances
  bool get canReceiveAdvance => remainingAdvanceLimit > 0;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'employee_id': employeeId,
      'employee_name': employeeName,
      'monthly_salary': monthlySalary,
      'total_salary_paid': totalSalaryPaid,
      'total_advances_paid': totalAdvancesPaid,
      'pending_salary': pendingSalary,
      'pending_advances': pendingAdvances,
      'remaining_advance_limit': remainingAdvanceLimit,
      'last_payment_date': lastPaymentDate.toIso8601String(),
    };
  }

  factory EmployeeFinancialSummary.fromMap(Map<String, dynamic> map) {
    return EmployeeFinancialSummary(
      employeeId: map['employee_id'] as int,
      employeeName: map['employee_name'] as String,
      monthlySalary: (map['monthly_salary'] as num).toDouble(),
      totalSalaryPaid: (map['total_salary_paid'] as num).toDouble(),
      totalAdvancesPaid: (map['total_advances_paid'] as num).toDouble(),
      pendingSalary: (map['pending_salary'] as num).toDouble(),
      pendingAdvances: (map['pending_advances'] as num).toDouble(),
      remainingAdvanceLimit: (map['remaining_advance_limit'] as num).toDouble(),
      lastPaymentDate: DateTime.parse(map['last_payment_date'] as String),
    );
  }
}
