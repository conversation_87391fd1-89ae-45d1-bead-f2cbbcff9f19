import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../database/database_helper.dart';
import '../services/connectivity_service.dart';
import '../utils/firebase_utils.dart';
import '../../shared/models/customer.dart';

class CustomerRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final ConnectivityService _connectivityService = ConnectivityService();

  /// Get Firestore instance (lazy initialization to avoid Firebase errors)
  FirebaseFirestore? get _firestore {
    return FirebaseUtils.safeFirebaseOperationSync<FirebaseFirestore>(
      () => FirebaseFirestore.instance,
      operationName: 'Get Firestore instance',
      fallbackValue: null,
    );
  }

  // Get all customers
  Future<List<Customer>> getAllCustomers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        orderBy: 'name ASC'
      );

      return List.generate(maps.length, (i) {
        return Customer(
          localId: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          email: maps[i]['email'] as String?,
          phone: maps[i]['phone'] as String?,
          address: maps[i]['address'] as String?,
          contactPerson: maps[i]['contact_person'] as String?,
          type: _parseType(maps[i]['type'] as String?),
          notes: maps[i]['notes'] as String?,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          updatedAt: null,
          // الحقول الجديدة
          preferredServiceTypes: maps[i]['preferred_service_types'] != null
              ? List<String>.from(json.decode(maps[i]['preferred_service_types'] as String))
              : null,
          paymentMethod: maps[i]['payment_method'] != null
              ? _parsePaymentMethod(maps[i]['payment_method'] as String)
              : null,
          monthlySubscriptionAmount: maps[i]['monthly_subscription_amount'] != null
              ? (maps[i]['monthly_subscription_amount'] as num).toDouble()
              : null,
          subscriptionStartDate: maps[i]['subscription_start_date'] != null
              ? DateTime.parse(maps[i]['subscription_start_date'] as String)
              : null,
          subscriptionEndDate: maps[i]['subscription_end_date'] != null
              ? DateTime.parse(maps[i]['subscription_end_date'] as String)
              : null,
          isSubscriptionActive: maps[i]['is_subscription_active'] != null
              ? maps[i]['is_subscription_active'] == 1
              : null,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting customers: $e');
      }
      return [];
    }
  }

  // Get active customers
  Future<List<Customer>> getActiveCustomers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'status = ?',
        whereArgs: ['active'],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return Customer(
          localId: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          email: maps[i]['email'] as String?,
          phone: maps[i]['phone'] as String?,
          address: maps[i]['address'] as String?,
          contactPerson: maps[i]['contact_person'] as String?,
          type: _parseType(maps[i]['type'] as String?),
          notes: maps[i]['notes'] as String?,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          updatedAt: null,
          // الحقول الجديدة
          preferredServiceTypes: maps[i]['preferred_service_types'] != null
              ? List<String>.from(json.decode(maps[i]['preferred_service_types'] as String))
              : null,
          paymentMethod: maps[i]['payment_method'] != null
              ? _parsePaymentMethod(maps[i]['payment_method'] as String)
              : null,
          monthlySubscriptionAmount: maps[i]['monthly_subscription_amount'] != null
              ? (maps[i]['monthly_subscription_amount'] as num).toDouble()
              : null,
          subscriptionStartDate: maps[i]['subscription_start_date'] != null
              ? DateTime.parse(maps[i]['subscription_start_date'] as String)
              : null,
          subscriptionEndDate: maps[i]['subscription_end_date'] != null
              ? DateTime.parse(maps[i]['subscription_end_date'] as String)
              : null,
          isSubscriptionActive: maps[i]['is_subscription_active'] != null
              ? maps[i]['is_subscription_active'] == 1
              : null,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting active customers: $e');
      }
      return [];
    }
  }

  // Get customer by ID
  Future<Customer?> getCustomerById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Customer(
          localId: maps[0]['id'] as int,
          name: maps[0]['name'] as String,
          email: maps[0]['email'] as String?,
          phone: maps[0]['phone'] as String?,
          address: maps[0]['address'] as String?,
          contactPerson: maps[0]['contact_person'] as String?,
          type: _parseType(maps[0]['type'] as String?),
          notes: maps[0]['notes'] as String?,
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
          updatedAt: null,
          // الحقول الجديدة
          preferredServiceTypes: maps[0]['preferred_service_types'] != null
              ? List<String>.from(json.decode(maps[0]['preferred_service_types'] as String))
              : null,
          paymentMethod: maps[0]['payment_method'] != null
              ? _parsePaymentMethod(maps[0]['payment_method'] as String)
              : null,
          monthlySubscriptionAmount: maps[0]['monthly_subscription_amount'] != null
              ? (maps[0]['monthly_subscription_amount'] as num).toDouble()
              : null,
          subscriptionStartDate: maps[0]['subscription_start_date'] != null
              ? DateTime.parse(maps[0]['subscription_start_date'] as String)
              : null,
          subscriptionEndDate: maps[0]['subscription_end_date'] != null
              ? DateTime.parse(maps[0]['subscription_end_date'] as String)
              : null,
          isSubscriptionActive: maps[0]['is_subscription_active'] != null
              ? maps[0]['is_subscription_active'] == 1
              : null,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting customer by ID: $e');
      }
      return null;
    }
  }

  // Insert a new customer
  Future<int> insertCustomer(Customer customer) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert(
        'customers',
        {
          'name': customer.name,
          'email': customer.email,
          'phone': customer.phone,
          'address': customer.address,
          'contact_person': customer.contactPerson,
          'type': customer.type.toString().split('.').last,
          'status': customer.isActive ? 'active' : 'inactive',
          'notes': customer.notes,
          'created_at': DateTime.now().toIso8601String(),
          // الحقول الجديدة
          'preferred_service_types': customer.preferredServiceTypes != null
              ? json.encode(customer.preferredServiceTypes)
              : null,
          'payment_method': customer.paymentMethod?.toString().split('.').last,
          'monthly_subscription_amount': customer.monthlySubscriptionAmount,
          'subscription_start_date': customer.subscriptionStartDate?.toIso8601String(),
          'subscription_end_date': customer.subscriptionEndDate?.toIso8601String(),
          'is_subscription_active': customer.isSubscriptionActive != null
              ? (customer.isSubscriptionActive! ? 1 : 0)
              : null,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting customer: $e');
      }
      return -1;
    }
  }

  // Update an existing customer
  Future<int> updateCustomer(Customer customer) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'customers',
        {
          'name': customer.name,
          'email': customer.email,
          'phone': customer.phone,
          'address': customer.address,
          'contact_person': customer.contactPerson,
          'type': customer.type.toString().split('.').last,
          'status': customer.isActive ? 'active' : 'inactive',
          'notes': customer.notes,
          // الحقول الجديدة
          'preferred_service_types': customer.preferredServiceTypes != null
              ? json.encode(customer.preferredServiceTypes)
              : null,
          'payment_method': customer.paymentMethod?.toString().split('.').last,
          'monthly_subscription_amount': customer.monthlySubscriptionAmount,
          'subscription_start_date': customer.subscriptionStartDate?.toIso8601String(),
          'subscription_end_date': customer.subscriptionEndDate?.toIso8601String(),
          'is_subscription_active': customer.isSubscriptionActive != null
              ? (customer.isSubscriptionActive! ? 1 : 0)
              : null,
        },
        where: 'id = ?',
        whereArgs: [customer.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating customer: $e');
      }
      return 0;
    }
  }

  // Delete a customer
  Future<int> deleteCustomer(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting customer: $e');
      }
      return 0;
    }
  }

  // Get customer by ID (alias for getCustomerById for sync compatibility)
  Future<Customer?> getById(String id) async {
    try {
      // Convert string ID to int for existing method
      final intId = int.tryParse(id);
      if (intId == null) {
        return null;
      }
      return await getCustomerById(intId);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting customer by ID: $e');
      }
      return null;
    }
  }

  // Update customer (alias for updateCustomer for sync compatibility)
  Future<Customer> update(Customer customer) async {
    try {
      await updateCustomer(customer);
      // Return the updated customer
      if (customer.localId != null) {
        final updated = await getCustomerById(customer.localId!);
        return updated ?? customer;
      }
      return customer;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating customer: $e');
      }
      rethrow;
    }
  }



  // Get customer balance (total invoices - total payments)
  Future<double> getCustomerBalance(int customerId) async {
    try {
      final db = await _dbHelper.database;

      // Get total invoices amount
      final invoicesResult = await db.rawQuery('''
        SELECT COALESCE(SUM(total), 0) as total_invoices
        FROM invoices
        WHERE customer_id = ? AND status != 'cancelled'
      ''', [customerId]);

      final totalInvoices = invoicesResult.isNotEmpty
          ? (invoicesResult.first['total_invoices'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Get total payments
      final paymentsResult = await db.rawQuery('''
        SELECT COALESCE(SUM(amount), 0) as total_payments
        FROM transactions
        WHERE customer_id = ? AND type = 'income'
      ''', [customerId]);

      final totalPayments = paymentsResult.isNotEmpty
          ? (paymentsResult.first['total_payments'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // Calculate balance
      return totalInvoices - totalPayments;
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating customer balance: $e');
      }
      return 0.0;
    }
  }

  // Helper method to parse type string to enum
  CustomerType _parseType(String? type) {
    if (type == null) return CustomerType.individual;

    switch (type.toLowerCase()) {
      case 'company':
        return CustomerType.company;
      case 'individual':
      default:
        return CustomerType.individual;
    }
  }

  // Helper method to parse payment method string to enum
  CustomerPaymentMethod _parsePaymentMethod(String? method) {
    if (method == null) return CustomerPaymentMethod.perService;

    switch (method.toLowerCase()) {
      case 'monthlysubscription':
        return CustomerPaymentMethod.monthlySubscription;
      case 'perservice':
      default:
        return CustomerPaymentMethod.perService;
    }
  }

  /// Sync customers with Firebase
  Future<void> sync() async {
    try {
      if (!await _connectivityService.shouldSync()) {
        if (kDebugMode) {
          print('⚠️ Skipping customer sync - no connectivity');
        }
        return;
      }

      if (kDebugMode) {
        print('🔄 Starting customer sync...');
      }

      // Sync local changes to Firebase
      await _syncLocalToFirebase();

      // Sync Firebase changes to local
      await _syncFirebaseToLocal();

      if (kDebugMode) {
        print('✅ Customer sync completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing customers: $e');
      }
      rethrow;
    }
  }

  /// Sync local changes to Firebase
  Future<void> _syncLocalToFirebase() async {
    try {
      final localCustomers = await getAllCustomers();

      for (final customer in localCustomers) {
        if (customer.firestoreId == null) {
          // Create new customer in Firebase
          await _createCustomerInFirebase(customer);
        } else {
          // Update existing customer in Firebase
          await _updateCustomerInFirebase(customer);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing local to Firebase: $e');
      }
      rethrow;
    }
  }

  /// Sync Firebase changes to local
  Future<void> _syncFirebaseToLocal() async {
    return await FirebaseUtils.safeFirebaseOperation<void>(
      () async {
        final firestore = _firestore;
        if (firestore == null) {
          throw Exception('Firestore not available');
        }

        final snapshot = await firestore.collection('customers').get();

        for (final doc in snapshot.docs) {
          final data = doc.data();
          data['id'] = doc.id;

          final customer = Customer.fromFirestore(data, doc.id);
          await _upsertLocalCustomer(customer);
        }
      },
      operationName: 'Sync Firebase to local customers',
    );
  }

  /// Create customer in Firebase
  Future<void> _createCustomerInFirebase(Customer customer) async {
    return await FirebaseUtils.safeFirebaseOperation<void>(
      () async {
        final firestore = _firestore;
        if (firestore == null) {
          throw Exception('Firestore not available');
        }

        final data = customer.toFirestoreMap();
        final docRef = await firestore.collection('customers').add(data);

        // Update local customer with Firebase ID
        await _updateCustomerFirestoreId(customer.localId!, docRef.id);

        if (kDebugMode) {
          print('✅ Created customer in Firebase: ${docRef.id}');
        }
      },
      operationName: 'Create customer in Firebase',
    );
  }

  /// Update customer in Firebase
  Future<void> _updateCustomerInFirebase(Customer customer) async {
    return await FirebaseUtils.safeFirebaseOperation<void>(
      () async {
        if (customer.firestoreId == null) return;

        final firestore = _firestore;
        if (firestore == null) {
          throw Exception('Firestore not available');
        }

        final data = customer.toFirestoreMap();
        data['updated_at'] = FieldValue.serverTimestamp();

        await firestore
            .collection('customers')
            .doc(customer.firestoreId)
            .update(data);

        if (kDebugMode) {
          print('✅ Updated customer in Firebase: ${customer.firestoreId}');
        }
      },
      operationName: 'Update customer in Firebase',
    );
  }

  /// Upsert customer in local database
  Future<void> _upsertLocalCustomer(Customer customer) async {
    try {
      final db = await _dbHelper.database;

      // Check if customer exists by Firebase ID
      final existing = await db.query(
        'customers',
        where: 'firestore_id = ?',
        whereArgs: [customer.firestoreId],
      );

      final data = {
        'firestore_id': customer.firestoreId,
        'name': customer.name,
        'email': customer.email,
        'phone': customer.phone,
        'address': customer.address,
        'contact_person': customer.contactPerson,
        'type': customer.type.toString(),
        'notes': customer.notes,
        'preferred_service_types': customer.preferredServiceTypes != null
            ? json.encode(customer.preferredServiceTypes)
            : null,
        'payment_method': customer.paymentMethod?.toString(),
        'monthly_subscription_amount': customer.monthlySubscriptionAmount,
        'subscription_start_date': customer.subscriptionStartDate?.toIso8601String(),
        'subscription_end_date': customer.subscriptionEndDate?.toIso8601String(),
        'is_subscription_active': customer.isSubscriptionActive == true ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (existing.isNotEmpty) {
        // Update existing
        await db.update(
          'customers',
          data,
          where: 'firestore_id = ?',
          whereArgs: [customer.firestoreId],
        );
      } else {
        // Insert new
        data['created_at'] = DateTime.now().toIso8601String();
        await db.insert('customers', data);
      }

      if (kDebugMode) {
        print('✅ Upserted customer locally: ${customer.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error upserting customer locally: $e');
      }
      rethrow;
    }
  }

  /// Update customer's Firebase ID in local database
  Future<void> _updateCustomerFirestoreId(int localId, String firestoreId) async {
    try {
      final db = await _dbHelper.database;
      await db.update(
        'customers',
        {'firestore_id': firestoreId},
        where: 'id = ?',
        whereArgs: [localId],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating customer Firebase ID: $e');
      }
      rethrow;
    }
  }
}
