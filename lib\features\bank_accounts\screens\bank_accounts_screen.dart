import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../core/repositories/bank_account_repository.dart';
import '../../../shared/models/bank_account.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/bank_account_card.dart';

class BankAccountsScreen extends StatefulWidget {
  const BankAccountsScreen({super.key});

  @override
  State<BankAccountsScreen> createState() => _BankAccountsScreenState();
}

class _BankAccountsScreenState extends State<BankAccountsScreen> {
  final BankAccountRepository _bankAccountRepository = BankAccountRepository();
  bool _isLoading = true;
  List<BankAccount> _bankAccounts = [];
  String _searchQuery = '';
  String _typeFilter = 'all';

  // Helper method to show snackbar safely
  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _loadBankAccounts();
  }

  Future<void> _loadBankAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = await _bankAccountRepository.getAllBankAccounts();

      if (mounted) {
        setState(() {
          _bankAccounts = accounts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تحميل الحسابات البنكية: $e', isError: true);
      }
    }
  }

  List<BankAccount> get _filteredBankAccounts {
    List<BankAccount> filtered = _bankAccounts;

    // Apply type filter
    if (_typeFilter != 'all') {
      filtered = filtered.where((account) {
        return account.type.toString().split('.').last == _typeFilter;
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((account) {
        final query = _searchQuery.toLowerCase();
        return account.bankName.toLowerCase().contains(query) ||
            account.accountName.toLowerCase().contains(query) ||
            account.accountNumber.toLowerCase().contains(query) ||
            (account.iban?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الحسابات البنكية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن حساب بنكي...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          _buildTypeFilterChips(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredBankAccounts.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد حسابات بنكية',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadBankAccounts,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredBankAccounts.length,
                          itemBuilder: (context, index) {
                            final bankAccount = _filteredBankAccounts[index];
                            return BankAccountCard(
                              bankAccount: bankAccount,
                              onTap: () {
                                _showBankAccountActions(context, bankAccount);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddBankAccountDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTypeFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Row(
        children: [
          _buildFilterChip('all', 'الكل'),
          const SizedBox(width: 8),
          _buildFilterChip('checking', BankAccount.getTypeName(BankAccountType.checking)),
          const SizedBox(width: 8),
          _buildFilterChip('savings', BankAccount.getTypeName(BankAccountType.savings)),
          const SizedBox(width: 8),
          _buildFilterChip('business', BankAccount.getTypeName(BankAccountType.business)),
          const SizedBox(width: 8),
          _buildFilterChip('other', BankAccount.getTypeName(BankAccountType.other)),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    return FilterChip(
      label: Text(label),
      selected: _typeFilter == value,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _typeFilter = value;
          });
        }
      },
      selectedColor: AppColors.primary.withAlpha(51), // 0.2 opacity is approximately alpha 51
      checkmarkColor: AppColors.primary,
    );
  }

  void _showBankAccountActions(BuildContext context, BankAccount bankAccount) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text(
                  bankAccount.bankName,
                  style: AppTextStyles.heading3,
                  textAlign: TextAlign.center,
                ),
                subtitle: Text(
                  '${bankAccount.accountNumber} - ${bankAccount.accountName}',
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.visibility),
                title: const Text('عرض التفاصيل'),
                onTap: () {
                  Navigator.pop(context);
                  _showBankAccountDetails(context, bankAccount);
                },
              ),
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('تعديل البيانات'),
                onTap: () {
                  Navigator.pop(context);
                  _showEditBankAccountDialog(context, bankAccount);
                },
              ),
              ListTile(
                leading: Icon(
                  bankAccount.isActive ? Icons.block : Icons.check_circle,
                  color: bankAccount.isActive ? Colors.red : Colors.green,
                ),
                title: Text(
                  bankAccount.isActive ? 'تعطيل الحساب' : 'تفعيل الحساب',
                ),
                onTap: () {
                  Navigator.pop(context);
                  _toggleBankAccountStatus(bankAccount);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('حذف الحساب'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(context, bankAccount);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showBankAccountDetails(BuildContext context, BankAccount bankAccount) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(bankAccount.bankName),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('اسم الحساب', bankAccount.accountName),
                _buildDetailRow('رقم الحساب', bankAccount.accountNumber),
                _buildDetailRow('نوع الحساب', BankAccount.getTypeName(bankAccount.type)),
                if (bankAccount.iban != null)
                  _buildDetailRow('رقم الآيبان', bankAccount.iban!),
                if (bankAccount.swiftCode != null)
                  _buildDetailRow('رمز السويفت', bankAccount.swiftCode!),
                if (bankAccount.branchName != null)
                  _buildDetailRow('اسم الفرع', bankAccount.branchName!),
                _buildDetailRow('الرصيد', '${bankAccount.balance} ر.س'),
                _buildDetailRow('الحالة', bankAccount.isActive ? 'نشط' : 'غير نشط'),
                _buildDetailRow(
                  'تاريخ الإنشاء',
                  DateFormat('dd/MM/yyyy').format(bankAccount.createdAt),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Implementaciones de los métodos para agregar, editar y eliminar cuentas bancarias
  void _showAddBankAccountDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String bankName = '';
    String accountNumber = '';
    String accountName = '';
    BankAccountType accountType = BankAccountType.checking;
    String? iban;
    String? swiftCode;
    String? branchName;
    String? notes;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إضافة حساب بنكي جديد'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم البنك',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم البنك';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      bankName = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'رقم الحساب',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم الحساب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      accountNumber = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم الحساب',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم الحساب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      accountName = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  DropdownButtonFormField<BankAccountType>(
                    decoration: const InputDecoration(
                      labelText: 'نوع الحساب',
                      border: OutlineInputBorder(),
                    ),
                    value: accountType,
                    items: BankAccountType.values.map((type) {
                      return DropdownMenuItem<BankAccountType>(
                        value: type,
                        child: Row(
                          children: [
                            Icon(
                              BankAccount.getTypeIcon(type),
                              size: 20,
                              color: BankAccount.getTypeColor(type),
                            ),
                            const SizedBox(width: 8),
                            Text(BankAccount.getTypeName(type)),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      accountType = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'رقم الآيبان (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    onSaved: (value) {
                      iban = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'رمز السويفت (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    onSaved: (value) {
                      swiftCode = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم الفرع (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    onSaved: (value) {
                      branchName = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                    onSaved: (value) {
                      notes = value;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();

                  try {
                    // Create new bank account
                    final newBankAccount = BankAccount(
                      id: null,
                      bankName: bankName,
                      accountNumber: accountNumber,
                      accountName: accountName,
                      type: accountType,
                      iban: iban,
                      swiftCode: swiftCode,
                      branchName: branchName,
                      notes: notes,
                      isActive: true,
                      balance: 0.0,
                      createdAt: DateTime.now(),
                    );

                    // Close dialog first
                    Navigator.pop(context);

                    // Insert into database
                    _bankAccountRepository.insertBankAccount(newBankAccount).then((id) {
                      if (id > 0 && mounted) {
                        // Then reload bank accounts from database
                        _loadBankAccounts();

                        // Show success message
                        _showSnackBar('تم إضافة الحساب البنكي بنجاح');
                      } else if (mounted) {
                        _showSnackBar('فشل إضافة الحساب البنكي', isError: true);
                      }
                    });
                  } catch (e) {
                    if (mounted) {
                      _showSnackBar('حدث خطأ أثناء إضافة الحساب البنكي: $e', isError: true);
                    }
                  }
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  void _showEditBankAccountDialog(BuildContext context, BankAccount bankAccount) {
    final formKey = GlobalKey<FormState>();
    String bankName = bankAccount.bankName;
    String accountNumber = bankAccount.accountNumber;
    String accountName = bankAccount.accountName;
    BankAccountType accountType = bankAccount.type;
    String? iban = bankAccount.iban;
    String? swiftCode = bankAccount.swiftCode;
    String? branchName = bankAccount.branchName;
    String? notes = bankAccount.notes;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل الحساب البنكي'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم البنك',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: bankName,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم البنك';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      bankName = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'رقم الحساب',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: accountNumber,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم الحساب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      accountNumber = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم الحساب',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: accountName,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم الحساب';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      accountName = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  DropdownButtonFormField<BankAccountType>(
                    decoration: const InputDecoration(
                      labelText: 'نوع الحساب',
                      border: OutlineInputBorder(),
                    ),
                    value: accountType,
                    items: BankAccountType.values.map((type) {
                      return DropdownMenuItem<BankAccountType>(
                        value: type,
                        child: Row(
                          children: [
                            Icon(
                              BankAccount.getTypeIcon(type),
                              size: 20,
                              color: BankAccount.getTypeColor(type),
                            ),
                            const SizedBox(width: 8),
                            Text(BankAccount.getTypeName(type)),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      accountType = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'رقم الآيبان (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: iban,
                    onSaved: (value) {
                      iban = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'رمز السويفت (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: swiftCode,
                    onSaved: (value) {
                      swiftCode = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'اسم الفرع (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: branchName,
                    onSaved: (value) {
                      branchName = value;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات (اختياري)',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: notes,
                    maxLines: 3,
                    onSaved: (value) {
                      notes = value;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();

                  try {
                    // Update bank account
                    final updatedBankAccount = bankAccount.copyWith(
                      bankName: bankName,
                      accountNumber: accountNumber,
                      accountName: accountName,
                      type: accountType,
                      iban: iban,
                      swiftCode: swiftCode,
                      branchName: branchName,
                      notes: notes,
                    );

                    // Close dialog first
                    Navigator.pop(context);

                    // Update in database
                    _bankAccountRepository.updateBankAccount(updatedBankAccount).then((result) {
                      if (result > 0 && mounted) {
                        // Then reload bank accounts from database
                        _loadBankAccounts();

                        // Show success message
                        _showSnackBar('تم تحديث الحساب البنكي بنجاح');
                      } else if (mounted) {
                        _showSnackBar('فشل تحديث الحساب البنكي', isError: true);
                      }
                    });
                  } catch (e) {
                    if (mounted) {
                      _showSnackBar('حدث خطأ أثناء تحديث الحساب البنكي: $e', isError: true);
                    }
                  }
                }
              },
              child: const Text('حفظ التغييرات'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _toggleBankAccountStatus(BankAccount bankAccount) async {
    try {
      final updatedBankAccount = bankAccount.copyWith(
        isActive: !bankAccount.isActive,
      );

      // Update in database
      final result = await _bankAccountRepository.updateBankAccount(updatedBankAccount);

      if (result > 0 && mounted) {
        // Reload bank accounts from database
        _loadBankAccounts();

        // Show success message
        _showSnackBar(
          bankAccount.isActive
              ? 'تم تعطيل الحساب البنكي بنجاح'
              : 'تم تفعيل الحساب البنكي بنجاح',
        );
      } else if (mounted) {
        _showSnackBar('فشل تغيير حالة الحساب البنكي', isError: true);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('حدث خطأ أثناء تغيير حالة الحساب البنكي: $e', isError: true);
      }
    }
  }

  void _showDeleteConfirmation(BuildContext context, BankAccount bankAccount) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('حذف الحساب البنكي'),
          content: Text(
            'هل أنت متأكد من حذف الحساب البنكي "${bankAccount.bankName} - ${bankAccount.accountNumber}"؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                // Close confirmation dialog first
                Navigator.pop(context);

                try {
                  // Delete from database
                  _bankAccountRepository.deleteBankAccount(bankAccount.id!).then((result) {
                    if (result > 0 && mounted) {
                      // Reload bank accounts from database
                      _loadBankAccounts();

                      // Show success message
                      _showSnackBar('تم حذف الحساب البنكي بنجاح');
                    } else if (mounted) {
                      _showSnackBar('فشل حذف الحساب البنكي', isError: true);
                    }
                  });
                } catch (e) {
                  if (mounted) {
                    _showSnackBar('حدث خطأ أثناء حذف الحساب البنكي: $e', isError: true);
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تصفية الحسابات البنكية'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'نوع الحساب',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Wrap(
                spacing: 8,
                children: [
                  _buildDialogFilterChip('all', 'الكل'),
                  _buildDialogFilterChip('checking', BankAccount.getTypeName(BankAccountType.checking)),
                  _buildDialogFilterChip('savings', BankAccount.getTypeName(BankAccountType.savings)),
                  _buildDialogFilterChip('business', BankAccount.getTypeName(BankAccountType.business)),
                  _buildDialogFilterChip('other', BankAccount.getTypeName(BankAccountType.other)),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDialogFilterChip(String value, String label) {
    return FilterChip(
      label: Text(label),
      selected: _typeFilter == value,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _typeFilter = value;
          });
          Navigator.pop(context);
        }
      },
      selectedColor: AppColors.primary.withAlpha(51), // 0.2 opacity is approximately alpha 51
      checkmarkColor: AppColors.primary,
    );
  }
}
