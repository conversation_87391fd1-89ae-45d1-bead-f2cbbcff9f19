import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:rxdart/subjects.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:icecorner/shared/models/service_request.dart';
import 'package:icecorner/core/services/audio_service.dart';

class ReceivedNotification {
  final int id;
  final String? title;
  final String? body;
  final String? payload;

  ReceivedNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.payload,
  });
}

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  final BehaviorSubject<ReceivedNotification> onNotificationClick =
      BehaviorSubject<ReceivedNotification>();

  // خدمة الصوت
  final AudioService _audioService = AudioService();

  // حالة تفعيل الصوت في الإشعارات
  bool _playSound = true;

  NotificationService._internal();

  Future<void> init() async {
    // تهيئة المناطق الزمنية
    tz_data.initializeTimeZones();

    // تهيئة خدمة الصوت
    await _audioService.init();

    // إنشاء قنوات الإشعارات لنظام Android
    await _createNotificationChannels();

    // تهيئة إعدادات الإشعارات لنظام Android
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // تهيئة إعدادات الإشعارات لنظام iOS
    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      notificationCategories: [
        DarwinNotificationCategory(
          'serviceRequestCategory',
          actions: [
            DarwinNotificationAction.plain(
              'VIEW',
              'عرض التفاصيل',
              options: {DarwinNotificationActionOption.foreground},
            ),
            DarwinNotificationAction.plain(
              'DISMISS',
              'تجاهل',
              options: {DarwinNotificationActionOption.destructive},
            ),
          ],
          options: {
            DarwinNotificationCategoryOption.hiddenPreviewShowTitle,
          },
        ),
      ],
    );

    // تهيئة إعدادات الإشعارات
    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    // تهيئة مكتبة الإشعارات
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );

    // طلب الإذن للإشعارات
    await requestPermissions();

    if (kDebugMode) {
      print('تم تهيئة خدمة الإشعارات والصوت بنجاح');
    }
  }

  // إنشاء قنوات الإشعارات لنظام Android
  Future<void> _createNotificationChannels() async {
    // قناة الإشعارات العادية
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'icecorner_channel',
      'ركن الجليد',
      description: 'إشعارات تطبيق ركن الجليد',
      importance: Importance.max,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('ringtonesettings'),
      enableVibration: true,
      enableLights: true,
      ledColor: Color.fromARGB(255, 0, 150, 255),
    );

    // قناة إشعارات المواعيد
    const AndroidNotificationChannel scheduledChannel = AndroidNotificationChannel(
      'icecorner_scheduled_channel',
      'مواعيد ركن الجليد',
      description: 'إشعارات مواعيد تطبيق ركن الجليد',
      importance: Importance.high,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('ringtonesettings'),
      enableVibration: true,
      enableLights: true,
      ledColor: Color.fromARGB(255, 255, 150, 0),
    );

    // قناة الإشعارات العاجلة
    const AndroidNotificationChannel urgentChannel = AndroidNotificationChannel(
      'icecorner_urgent_channel',
      'إشعارات عاجلة',
      description: 'إشعارات عاجلة لتطبيق ركن الجليد',
      importance: Importance.max,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('ringtonesettings'),
      enableVibration: true,
      enableLights: true,
      ledColor: Color.fromARGB(255, 255, 0, 0),
    );

    // إنشاء القنوات
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(scheduledChannel);

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(urgentChannel);
  }

  Future<void> requestPermissions() async {
    if (Platform.isIOS) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    } else if (Platform.isAndroid) {
      // في الإصدارات الحديثة من Flutter Local Notifications، يتم استخدام طريقة مختلفة لطلب الإذن
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation != null) {
        // استخدام الطريقة الصحيحة لطلب الإذن
        await androidImplementation.requestNotificationsPermission();
      }
    }
  }

  void onDidReceiveLocalNotification(
      int id, String? title, String? body, String? payload) {
    onNotificationClick.add(
      ReceivedNotification(
        id: id,
        title: title,
        body: body,
        payload: payload,
      ),
    );
  }

  void onDidReceiveNotificationResponse(NotificationResponse response) {
    if (response.payload != null && response.payload!.isNotEmpty) {
      onNotificationClick.add(
        ReceivedNotification(
          id: response.id ?? 0,
          title: response.notificationResponseType.toString(),
          body: response.payload,
          payload: response.payload,
        ),
      );
    }
  }

  // إرسال إشعار فوري
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    bool playSound = true,
    bool isUrgent = false,
    bool useFullScreenIntent = false,
  }) async {
    // تشغيل الصوت إذا كان مفعلاً
    if (_playSound && playSound) {
      await _audioService.playNotificationSound();
    }

    // تحديد قناة الإشعارات المناسبة
    String channelId = isUrgent ? 'icecorner_urgent_channel' : 'icecorner_channel';

    // إعدادات الإشعارات لنظام Android
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      channelId,
      isUrgent ? 'إشعارات عاجلة' : 'ركن الجليد',
      channelDescription: isUrgent ? 'إشعارات عاجلة لتطبيق ركن الجليد' : 'إشعارات تطبيق ركن الجليد',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
      playSound: true,
      sound: const RawResourceAndroidNotificationSound('ringtonesettings'),
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 500, 200, 500, 200, 500]),
      fullScreenIntent: useFullScreenIntent, // استخدام الإشعارات المنبثقة على كامل الشاشة
      icon: '@mipmap/ic_launcher',
      largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      color: Colors.blue,
      category: isUrgent ? AndroidNotificationCategory.alarm : AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      styleInformation: BigTextStyleInformation(
        body,
        htmlFormatBigText: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: 'ركن الجليد',
        htmlFormatSummaryText: true,
      ),
    );

    // إعدادات الإشعارات لنظام iOS
    final DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'ringtonesettings.mp3',
      interruptionLevel: isUrgent ? InterruptionLevel.timeSensitive : InterruptionLevel.active,
      categoryIdentifier: 'serviceRequestCategory',
    );

    // إعدادات الإشعارات لجميع المنصات
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // عرض الإشعار
    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );

    if (kDebugMode) {
      print('تم عرض إشعار: $title');
      print('نوع الإشعار: ${isUrgent ? 'عاجل' : 'عادي'}');
      print('إشعار منبثق: ${useFullScreenIntent ? 'نعم' : 'لا'}');
    }
  }

  // جدولة إشعار في وقت محدد
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    bool playSound = true,
    bool isUrgent = false,
    bool useFullScreenIntent = false,
  }) async {
    // تشغيل الصوت للتأكيد على جدولة الإشعار (اختياري)
    if (_playSound && playSound && scheduledDate.difference(DateTime.now()).inMinutes < 1) {
      await _audioService.playNotificationSound();
    }

    // تحديد قناة الإشعارات المناسبة
    String channelId = isUrgent ? 'icecorner_urgent_channel' : 'icecorner_scheduled_channel';

    // إعدادات الإشعارات لنظام Android
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      channelId,
      isUrgent ? 'إشعارات عاجلة' : 'مواعيد ركن الجليد',
      channelDescription: isUrgent
          ? 'إشعارات عاجلة لتطبيق ركن الجليد'
          : 'إشعارات مواعيد تطبيق ركن الجليد',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
      playSound: true,
      sound: const RawResourceAndroidNotificationSound('ringtonesettings'),
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 500, 200, 500, 200, 500]),
      fullScreenIntent: useFullScreenIntent, // استخدام الإشعارات المنبثقة على كامل الشاشة
      icon: '@mipmap/ic_launcher',
      largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      color: isUrgent ? Colors.red : Colors.orange,
      category: isUrgent ? AndroidNotificationCategory.alarm : AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      styleInformation: BigTextStyleInformation(
        body,
        htmlFormatBigText: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: 'موعد زيارة',
        htmlFormatSummaryText: true,
      ),
    );

    // إعدادات الإشعارات لنظام iOS
    final DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'ringtonesettings.mp3',
      interruptionLevel: isUrgent ? InterruptionLevel.timeSensitive : InterruptionLevel.active,
      categoryIdentifier: 'serviceRequestCategory',
    );

    // إعدادات الإشعارات لجميع المنصات
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // جدولة الإشعار
    await flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      payload: payload,
    );

    if (kDebugMode) {
      print('تم جدولة إشعار: $title في ${scheduledDate.toString()}');
      print('نوع الإشعار: ${isUrgent ? 'عاجل' : 'عادي'}');
      print('إشعار منبثق: ${useFullScreenIntent ? 'نعم' : 'لا'}');
    }
  }

  // جدولة إشعار قبل موعد الزيارة بوقت محدد
  Future<void> scheduleServiceRequestReminder(ServiceRequest serviceRequest) async {
    // إنشاء معرف فريد للإشعار باستخدام معرف طلب الخدمة
    final int notificationId = serviceRequest.id ?? serviceRequest.reference.hashCode;

    // تحديد وقت الإشعار الرئيسي (قبل الموعد بالوقت المحدد في reminderMinutes)
    final DateTime reminderTime = serviceRequest.scheduledDate.subtract(Duration(minutes: serviceRequest.reminderMinutes));

    // التحقق من أن وقت الإشعار لم يمر بعد
    if (reminderTime.isAfter(DateTime.now())) {
      // تحويل الدقائق إلى نص مناسب للعرض
      String reminderTimeText = '';
      if (serviceRequest.reminderMinutes >= 60) {
        final int hours = serviceRequest.reminderMinutes ~/ 60;
        final int minutes = serviceRequest.reminderMinutes % 60;
        if (minutes == 0) {
          reminderTimeText = 'بعد $hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
        } else {
          reminderTimeText = 'بعد $hours ${hours == 1 ? 'ساعة' : 'ساعات'} و $minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
        }
      } else {
        reminderTimeText = 'بعد ${serviceRequest.reminderMinutes} ${serviceRequest.reminderMinutes == 1 ? 'دقيقة' : 'دقائق'}';
      }

      // الإشعار الرئيسي (حسب وقت التذكير المحدد)
      await scheduleNotification(
        id: notificationId,
        title: 'تذكير بموعد زيارة',
        body: 'لديك موعد زيارة للعميل ${serviceRequest.customerName} $reminderTimeText',
        scheduledDate: reminderTime,
        payload: 'service_request_${serviceRequest.reference}',
        isUrgent: false,
        useFullScreenIntent: false,
      );

      // إشعار إضافي قبل الموعد بساعة (إذا كان وقت التذكير الرئيسي أكثر من ساعة)
      if (serviceRequest.reminderMinutes > 60) {
        final DateTime oneHourBefore = serviceRequest.scheduledDate.subtract(const Duration(hours: 1));
        if (oneHourBefore.isAfter(DateTime.now()) && oneHourBefore.isAfter(reminderTime)) {
          await scheduleNotification(
            id: notificationId + 10,
            title: 'تذكير بموعد زيارة',
            body: 'لديك موعد زيارة للعميل ${serviceRequest.customerName} بعد ساعة واحدة',
            scheduledDate: oneHourBefore,
            payload: 'service_request_${serviceRequest.reference}',
            isUrgent: false,
            useFullScreenIntent: false,
          );
        }
      }

      // إشعار إضافي قبل الموعد بـ 15 دقيقة (إذا كان وقت التذكير الرئيسي أكثر من 15 دقيقة)
      if (serviceRequest.reminderMinutes > 15) {
        final DateTime fifteenMinutesBefore = serviceRequest.scheduledDate.subtract(const Duration(minutes: 15));
        if (fifteenMinutesBefore.isAfter(DateTime.now()) && fifteenMinutesBefore.isAfter(reminderTime)) {
          await scheduleNotification(
            id: notificationId + 20,
            title: 'تذكير هام بموعد زيارة',
            body: 'لديك موعد زيارة للعميل ${serviceRequest.customerName} بعد 15 دقيقة',
            scheduledDate: fifteenMinutesBefore,
            payload: 'service_request_${serviceRequest.reference}',
            isUrgent: true,
            useFullScreenIntent: true, // استخدام إشعار منبثق قبل الموعد بـ 15 دقيقة
          );
        }
      }

      // إشعار في وقت الموعد تمامًا (إشعار منبثق)
      await scheduleNotification(
        id: notificationId + 1,
        title: 'موعد الزيارة الآن',
        body: 'حان الآن موعد زيارة العميل ${serviceRequest.customerName}',
        scheduledDate: serviceRequest.scheduledDate,
        payload: 'service_request_${serviceRequest.reference}',
        isUrgent: true,
        useFullScreenIntent: true, // استخدام إشعار منبثق في وقت الموعد تمامًا
      );

      // إشعار بعد الموعد بـ 15 دقيقة (للتذكير بتسجيل نتائج الزيارة)
      final DateTime fifteenMinutesAfter = serviceRequest.scheduledDate.add(const Duration(minutes: 15));
      await scheduleNotification(
        id: notificationId + 30,
        title: 'تذكير بتسجيل نتائج الزيارة',
        body: 'تذكير بتسجيل نتائج زيارة العميل ${serviceRequest.customerName}',
        scheduledDate: fifteenMinutesAfter,
        payload: 'service_request_result_${serviceRequest.reference}',
        isUrgent: false,
        useFullScreenIntent: false,
      );
    }
  }

  // إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    await flutterLocalNotificationsPlugin.cancel(id);
  }

  // إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  // إلغاء جميع الإشعارات المتعلقة بطلب خدمة معين
  Future<void> cancelServiceRequestNotifications(ServiceRequest serviceRequest) async {
    final int baseNotificationId = serviceRequest.id ?? serviceRequest.reference.hashCode;

    // إلغاء الإشعار الرئيسي
    await cancelNotification(baseNotificationId);

    // إلغاء إشعار وقت الموعد
    await cancelNotification(baseNotificationId + 1);

    // إلغاء إشعار قبل الموعد بساعة
    await cancelNotification(baseNotificationId + 10);

    // إلغاء إشعار قبل الموعد بـ 15 دقيقة
    await cancelNotification(baseNotificationId + 20);

    // إلغاء إشعار بعد الموعد بـ 15 دقيقة
    await cancelNotification(baseNotificationId + 30);
  }

  // تفعيل/تعطيل صوت الإشعارات
  void setSoundEnabled(bool enabled) {
    _playSound = enabled;
    _audioService.setSoundEnabled(enabled);

    if (kDebugMode) {
      print('تم ${enabled ? 'تفعيل' : 'تعطيل'} صوت الإشعارات');
    }
  }

  // الحصول على حالة تفعيل الصوت
  bool get isSoundEnabled => _playSound;

  // تشغيل صوت الإشعار (للاختبار)
  Future<void> playNotificationSound() async {
    await _audioService.playNotificationSound();
  }

  // إيقاف صوت الإشعار
  Future<void> stopNotificationSound() async {
    await _audioService.stopNotificationSound();
  }
}
