import 'package:flutter/material.dart';
import '../../../config/constants.dart';

class SummaryItem {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  SummaryItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });
}

class ReportSummaryCard extends StatelessWidget {
  final List<SummaryItem> items;

  const ReportSummaryCard({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(AppDimensions.paddingM),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص التقرير',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Divider(),
            const SizedBox(height: AppDimensions.paddingS),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 2,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
              ),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                return _buildSummaryItem(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(SummaryItem item) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: item.color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: item.color.withAlpha(77),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                item.icon,
                color: item.color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  item.title,
                  style: TextStyle(
                    color: item.color,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              item.value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: item.color,
              ),
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
