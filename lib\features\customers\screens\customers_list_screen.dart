import 'package:flutter/material.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../core/repositories/customer_repository.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/widgets/enhanced_card.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../../../shared/widgets/rtl_aware_widget.dart';
import '../../../core/error_handling/error_handler.dart';
import '../../../config/routes.dart';

/// Enhanced customers list screen with comprehensive Arabic support
class CustomersListScreen extends StatefulWidget {
  const CustomersListScreen({super.key});

  @override
  State<CustomersListScreen> createState() => _CustomersListScreenState();
}

class _CustomersListScreenState extends State<CustomersListScreen> {
  final CustomerRepository _customerRepository = CustomerRepository();
  final TextEditingController _searchController = TextEditingController();
  
  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  bool _isLoading = true;
  CustomerType? _selectedType;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    try {
      setState(() => _isLoading = true);
      
      final customers = await _customerRepository.getAllCustomers();
      
      setState(() {
        _customers = customers;
        _filteredCustomers = customers;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ErrorHandler.handleError(context, e);
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterCustomers() {
    final query = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredCustomers = _customers.where((customer) {
        final matchesSearch = customer.name.toLowerCase().contains(query) ||
            (customer.email?.toLowerCase().contains(query) ?? false) ||
            (customer.phone?.contains(query) ?? false);
        
        final matchesType = _selectedType == null || customer.type == _selectedType;
        
        return matchesSearch && matchesType;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return RTLAwareWidget(
      child: Scaffold(
        appBar: AppBar(
          title: Text(localizations.translate('customers')),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _addCustomer(),
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadCustomers,
            ),
          ],
        ),
        body: ResponsiveContainer(
          child: Column(
            children: [
              // Search and Filter Section
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Search Field
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: localizations.translate('search_customers'),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onChanged: (_) => _filterCustomers(),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Type Filter
                    Row(
                      children: [
                        Text(
                          localizations.translate('customer_type'),
                          style: theme.textTheme.labelMedium,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: DropdownButton<CustomerType?>(
                            value: _selectedType,
                            isExpanded: true,
                            hint: Text(localizations.translate('all_types')),
                            items: [
                              DropdownMenuItem<CustomerType?>(
                                value: null,
                                child: Text(localizations.translate('all_types')),
                              ),
                              DropdownMenuItem<CustomerType?>(
                                value: CustomerType.individual,
                                child: Text(localizations.translate('individual')),
                              ),
                              DropdownMenuItem<CustomerType?>(
                                value: CustomerType.company,
                                child: Text(localizations.translate('company')),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() => _selectedType = value);
                              _filterCustomers();
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Customers List
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredCustomers.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.people_outline,
                                  size: 64,
                                  color: theme.colorScheme.outline,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _customers.isEmpty
                                      ? localizations.translate('no_customers')
                                      : localizations.translate('no_customers_found'),
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: theme.colorScheme.outline,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton.icon(
                                  onPressed: _addCustomer,
                                  icon: const Icon(Icons.add),
                                  label: Text(localizations.translate('add_customer')),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: _filteredCustomers.length,
                            itemBuilder: (context, index) {
                              final customer = _filteredCustomers[index];
                              return _buildCustomerCard(customer, localizations, theme);
                            },
                          ),
              ),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _addCustomer,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildCustomerCard(Customer customer, AppLocalizations localizations, ThemeData theme) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _viewCustomerDetails(customer),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: theme.colorScheme.primaryContainer,
                child: Icon(
                  customer.type == CustomerType.company
                      ? Icons.business
                      : Icons.person,
                  color: theme.colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customer.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (customer.email != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        customer.email!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: customer.type == CustomerType.company
                          ? Colors.blue.withOpacity(0.1)
                          : Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      customer.type == CustomerType.company
                          ? localizations.translate('company')
                          : localizations.translate('individual'),
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: customer.type == CustomerType.company
                            ? Colors.blue
                            : Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (customer.phone != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      customer.phone!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
          
          if (customer.address != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    customer.address!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
          
          // Subscription Status
          if (customer.paymentMethod == CustomerPaymentMethod.monthlySubscription) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: customer.isSubscriptionActive == true
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    customer.isSubscriptionActive == true
                        ? Icons.check_circle
                        : Icons.warning,
                    size: 14,
                    color: customer.isSubscriptionActive == true
                        ? Colors.green
                        : Colors.orange,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    customer.isSubscriptionActive == true
                        ? localizations.translate('active_subscription')
                        : localizations.translate('inactive_subscription'),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: customer.isSubscriptionActive == true
                          ? Colors.green
                          : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _addCustomer() {
    // Navigate to add customer screen
    // This would be implemented based on your existing customer form
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context)!.translate('feature_coming_soon')),
      ),
    );
  }

  void _viewCustomerDetails(Customer customer) {
    Navigator.pushNamed(
      context,
      AppRoutes.customerDetails,
      arguments: customer,
    );
  }
}
