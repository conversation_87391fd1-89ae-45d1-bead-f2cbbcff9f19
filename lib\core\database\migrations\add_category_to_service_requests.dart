import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// هجرة لإضافة حقول فئة الخدمة إلى جدول طلبات الخدمة
class AddCategoryToServiceRequests {
  static const String tableName = 'service_requests';

  /// التحقق مما إذا كانت الهجرة مطلوبة
  static Future<bool> isNeeded(Database db) async {
    try {
      // التحقق من وجود الجدول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='$tableName'",
      );
      
      if (tables.isEmpty) {
        return false; // الجدول غير موجود
      }

      // التحقق من وجود العمود
      final columns = await db.rawQuery("PRAGMA table_info($tableName)");
      final hasCategoryId = columns.any((column) => column['name'] == 'category_id');
      
      return !hasCategoryId; // الهجرة مطلوبة إذا كان العمود غير موجود
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء التحقق من حقول جدول طلبات الخدمة: $e');
      }
      return false;
    }
  }

  /// تطبيق الهجرة
  static Future<void> migrate(Database db) async {
    try {
      if (await isNeeded(db)) {
        if (kDebugMode) {
          print('تطبيق هجرة: إضافة حقول فئة الخدمة إلى جدول طلبات الخدمة');
        }

        // إضافة عمود category_id
        await db.execute('''
          ALTER TABLE $tableName ADD COLUMN category_id INTEGER
        ''');

        // إضافة عمود category_name
        await db.execute('''
          ALTER TABLE $tableName ADD COLUMN category_name TEXT
        ''');

        if (kDebugMode) {
          print('تم تطبيق الهجرة بنجاح');
        }
      } else {
        if (kDebugMode) {
          print('تم تخطي الهجرة: حقول فئة الخدمة موجودة بالفعل في جدول طلبات الخدمة');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء تطبيق الهجرة: $e');
      }
      rethrow;
    }
  }
}
