import 'package:flutter/material.dart';

/// حالات طلبات الخدمة
enum ServiceStatus {
  pending,
  inProgress,
  completed,
  cancelled,
}

/// امتدادات لتسهيل التعامل مع حالات الخدمة
extension ServiceStatusExtension on ServiceStatus {
  /// الحصول على اسم الحالة بالعربية
  String get name {
    switch (this) {
      case ServiceStatus.pending:
        return 'معلق';
      case ServiceStatus.inProgress:
        return 'قيد التنفيذ';
      case ServiceStatus.completed:
        return 'مكتمل';
      case ServiceStatus.cancelled:
        return 'ملغي';
    }
  }

  /// الحصول على لون الحالة
  Color get color {
    switch (this) {
      case ServiceStatus.pending:
        return Colors.amber;
      case ServiceStatus.inProgress:
        return Colors.blue;
      case ServiceStatus.completed:
        return Colors.green;
      case ServiceStatus.cancelled:
        return Colors.red;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get icon {
    switch (this) {
      case ServiceStatus.pending:
        return Icons.hourglass_empty;
      case ServiceStatus.inProgress:
        return Icons.pending_actions;
      case ServiceStatus.completed:
        return Icons.check_circle;
      case ServiceStatus.cancelled:
        return Icons.cancel;
    }
  }
}

/// دوال مساعدة للتعامل مع حالات الخدمة
class ServiceStatusHelper {
  /// تحويل النص إلى حالة خدمة
  static ServiceStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ServiceStatus.pending;
      case 'in_progress':
      case 'inprogress':
        return ServiceStatus.inProgress;
      case 'completed':
        return ServiceStatus.completed;
      case 'cancelled':
        return ServiceStatus.cancelled;
      default:
        return ServiceStatus.pending;
    }
  }

  /// الحصول على اسم الحالة بالعربية
  static String getStatusName(ServiceStatus status) {
    return status.name;
  }

  /// الحصول على لون الحالة
  static Color getStatusColor(ServiceStatus status) {
    return status.color;
  }

  /// الحصول على أيقونة الحالة
  static IconData getStatusIcon(ServiceStatus status) {
    return status.icon;
  }
}
