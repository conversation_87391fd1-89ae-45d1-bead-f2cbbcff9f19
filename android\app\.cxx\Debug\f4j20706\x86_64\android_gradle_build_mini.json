{"buildFiles": ["C:\\flutter_windows_3.29.1-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\food\\HVAC Service Manager\\android\\app\\.cxx\\Debug\\f4j20706\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\food\\HVAC Service Manager\\android\\app\\.cxx\\Debug\\f4j20706\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}