name: icecorner
description: "ركن الجليد - للتكييف والتبريد "
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.4.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  google_fonts: ^6.1.0
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^10.0.0
  data_table_2: ^2.5.10
  fl_chart: ^0.66.0
  intl: ^0.19.0
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.0
  table_calendar: ^3.0.9

  # State Management
  provider: ^6.1.1
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # Firebase
  firebase_core: ^2.32.0
  cloud_firestore: ^4.17.5
  firebase_auth: ^4.20.0
  firebase_storage: ^11.7.7

  # Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  path: ^1.8.3
  flutter_secure_storage: ^9.0.0

  # Networking
  http: ^1.1.2
  dio: ^5.4.0
  connectivity_plus: ^5.0.2

  # PDF & Printing
  pdf: ^3.10.7
  printing: ^5.11.1

  # Others
  uuid: ^4.2.2
  logger: ^2.0.2+1
  image_picker: ^1.0.7
  url_launcher: ^6.2.2
  package_info_plus: ^8.3.0
  share_plus: ^7.2.2
  flutter_pdfview: ^1.4.0+1
  excel: ^4.0.6
  flutter_colorpicker: ^1.1.0
  flutter_iconpicker: ^4.0.1
  crypto: ^3.0.3

  # Notifications
  flutter_local_notifications: ^18.0.1
  timezone: ^0.9.2
  rxdart: ^0.27.7

  # Audio
  audioplayers: ^5.2.1
  just_audio: ^0.9.36
  go_router: ^14.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.7
  mockito: ^5.4.4
  flutter_launcher_icons: ^0.13.1
  sqflite_common_ffi: ^2.3.0
  test: ^1.25.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/i18n/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
