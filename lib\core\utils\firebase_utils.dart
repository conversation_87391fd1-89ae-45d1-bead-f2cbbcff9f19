import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import '../../firebase_options.dart';

/// Utility class for Firebase operations and initialization checks
class FirebaseUtils {
  /// Check if Firebase is properly initialized
  static bool get isInitialized {
    try {
      // Try to access Firebase.apps to see if Firebase is initialized
      return Firebase.apps.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Firebase not initialized: $e');
      }
      return false;
    }
  }

  /// Get the default Firebase app if available
  static FirebaseApp? get defaultApp {
    try {
      if (isInitialized) {
        return Firebase.app();
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Cannot get default Firebase app: $e');
      }
      return null;
    }
  }

  /// Execute a Firebase operation with proper error handling
  static Future<T?> safeFirebaseOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
    T? fallbackValue,
  }) async {
    try {
      if (!isInitialized) {
        if (kDebugMode) {
          print('⚠️ Skipping ${operationName ?? 'Firebase operation'} - Firebase not initialized');
        }
        return fallbackValue;
      }

      return await operation();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in ${operationName ?? 'Firebase operation'}: $e');
      }
      return fallbackValue;
    }
  }

  /// Execute a Firebase operation synchronously with proper error handling
  static T? safeFirebaseOperationSync<T>(
    T Function() operation, {
    String? operationName,
    T? fallbackValue,
  }) {
    try {
      if (!isInitialized) {
        if (kDebugMode) {
          print('⚠️ Skipping ${operationName ?? 'Firebase operation'} - Firebase not initialized');
        }
        return fallbackValue;
      }

      return operation();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in ${operationName ?? 'Firebase operation'}: $e');
      }
      return fallbackValue;
    }
  }

  /// Wait for Firebase to be initialized with timeout
  static Future<bool> waitForInitialization({
    Duration timeout = const Duration(seconds: 10),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    while (stopwatch.elapsed < timeout) {
      if (isInitialized) {
        if (kDebugMode) {
          print('✅ Firebase initialized after ${stopwatch.elapsed.inMilliseconds}ms');
        }
        return true;
      }
      
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    if (kDebugMode) {
      print('⏰ Firebase initialization timeout after ${timeout.inSeconds}s');
    }
    return false;
  }

  /// Initialize Firebase with proper error handling
  static Future<bool> initializeFirebase() async {
    try {
      if (isInitialized) {
        if (kDebugMode) {
          print('✅ Firebase already initialized');
        }
        return true;
      }

      if (kDebugMode) {
        print('🔧 تهيئة Firebase باستخدام الإعدادات المحدثة...');
        print('📱 Platform: ${DefaultFirebaseOptions.currentPlatform.toString()}');
      }

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      if (kDebugMode) {
        print('✅ Firebase initialized successfully');
        print('🔥 Project ID: ${Firebase.app().options.projectId}');
        print('🔑 API Key: ${Firebase.app().options.apiKey.substring(0, 10)}...');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
        print('🔍 تحقق من:');
        print('   - ملف google-services.json في android/app/');
        print('   - إعدادات firebase_options.dart');
        print('   - اتصال الإنترنت');
      }
      return false;
    }
  }

  /// Get Firebase initialization status message
  static String getInitializationStatus() {
    if (isInitialized) {
      final app = defaultApp;
      if (app != null) {
        return 'Firebase initialized successfully (${app.name})';
      } else {
        return 'Firebase initialized but app not accessible';
      }
    } else {
      return 'Firebase not initialized';
    }
  }

  /// Log Firebase status for debugging
  static void logStatus() {
    if (kDebugMode) {
      print('🔥 Firebase Status: ${getInitializationStatus()}');
      if (isInitialized) {
        try {
          final apps = Firebase.apps;
          print('📱 Available Firebase apps: ${apps.map((app) => app.name).join(', ')}');
        } catch (e) {
          print('❌ Error getting Firebase apps: $e');
        }
      }
    }
  }
}
