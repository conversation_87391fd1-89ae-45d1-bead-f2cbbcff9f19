import 'package:flutter/material.dart';
import 'package:icecorner/config/constants.dart';
import 'package:intl/intl.dart';
import '../../../shared/models/user.dart';

class UserInfoCard extends StatelessWidget {
  final User user;

  const UserInfoCard({
    super.key,
    required this.user,
  });

  String _getRoleName(List<String> roles) {
    if (roles.isEmpty) return 'غير محدد'; // Default
    final roleName = roles.first;
    switch (roleName) {
      case 'admin':
        return 'مدير النظام';
      case 'manager':
        return 'مدير';
      case 'accountant':
        return 'محاسب';
      case 'technician':
        return 'فني';
      case 'receptionist':
        return 'موظف استقبال';
      case 'employee':
        return 'موظف';
      default:
        return 'غير محدد';
    }
  }

  Color _getRoleColor(List<String> roles) {
    if (roles.isEmpty) return Colors.grey; // Default
    final roleName = roles.first;
    switch (roleName) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.blue;
      case 'accountant':
        return Colors.green;
      case 'technician':
        return Colors.orange;
      case 'receptionist':
        return Colors.purple;
      case 'employee':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd - hh:mm a').format(date);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with avatar
            Row(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: _getRoleColor(user.roles).withAlpha(51),
                  child: Text(
                    user.name.substring(0, 1),
                    style: TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.bold,
                      color: _getRoleColor(user.roles),
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              user.name,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (!user.isActive)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.red.withAlpha(26),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'غير نشط',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getRoleColor(user.roles).withAlpha(26),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getRoleName(user.roles),
                          style: TextStyle(
                            fontSize: 14,
                            color: _getRoleColor(user.roles),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Divider(),

            // User details
            if (user.email != null)
              _buildInfoRow(
                icon: Icons.email,
                title: 'البريد الإلكتروني',
                value: user.email!,
              ),
            if (user.phone != null)
              _buildInfoRow(
                icon: Icons.phone,
                title: 'رقم الهاتف',
                value: user.phone!,
              ),
            _buildInfoRow(
              icon: Icons.calendar_today,
              title: 'تاريخ الإنشاء',
              value: _formatDate(user.createdAt),
            ),
            if (user.lastLogin != null)
              _buildInfoRow(
                icon: Icons.access_time,
                title: 'آخر تسجيل دخول',
                value: _formatDate(user.lastLogin!),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
