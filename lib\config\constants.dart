import 'package:flutter/material.dart';

/// Application Colors
class AppColors {
  // Primary Colors - مستوحاة من شعار الثلج
  static const Color primary = Color(0xFF1E88E5); // اللون الأزرق الأساسي
  static const Color primaryLight = Color(0xFF64B5F6); // اللون الأزرق الثانوي
  static const Color primaryDark = Color(0xFF1565C0); // اللون الأزرق الداكن

  // للتوافق مع الكود القديم
  static const Color secondary = primaryLight;
  static const Color accent = Color(0xFF16A085); // لون مكمل

  // Background Colors
  static const Color background = Color(0xFFF5F5F5); // خلفية رمادية فاتحة
  static const Color surface = Color(0xFFFFFFFF); // خلفية بيضاء
  static const Color card = Color(0xFFFFFFFF); // خلفية البطاقات

  // Status Colors
  static const Color error = Color(0xFFFF5252); // لون الخطأ
  static const Color success = Color(0xFF4CAF50); // لون النجاح
  static const Color warning = Color(0xFFFFC107); // لون التحذير
  static const Color info = Color(0xFF2196F3); // لون المعلومات

  // Text Colors
  static const Color textPrimary = Color(0xFF212121); // نص أساسي داكن
  static const Color textSecondary = Color(0xFF757575); // نص ثانوي
  static const Color textHint = Color(0xFF9E9E9E); // نص تلميحي
  static const Color textOnPrimary = Color(0xFFFFFFFF); // نص على خلفية ملونة

  // Border & Divider
  static const Color border = Color(0xFFE0E0E0); // لون الحدود
  static const Color divider = Color(0xFFEEEEEE); // لون الفواصل

  // Table Colors
  static const Color tableHeader = Color(0xFF1976D2); // لون رأس الجدول
  static const Color tableHeaderText = Color(0xFFFFFFFF); // لون نص رأس الجدول
  static const Color tableRowEven = Color(0xFFFFFFFF); // لون الصف الزوجي
  static const Color tableRowOdd = Color(0xFFF5F5F5); // لون الصف الفردي
  static const Color tableRowHover = Color(0xFFE3F2FD); // لون تمرير المؤشر
  static const Color tableRowSelected = Color(0xFFBBDEFB); // لون الصف المحدد
  static const Color tableBorder = Color(0xFFE0E0E0); // لون حدود الجدول

  // Button Colors
  static const Color buttonPrimary = Color(0xFF1E88E5); // لون الزر الرئيسي
  static const Color buttonSecondary = Color(0xFFFFFFFF); // لون الزر الثانوي
  static const Color buttonDisabled = Color(0xFFBDBDBD); // لون الزر المعطل

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212); // خلفية داكنة
  static const Color darkSurface = Color(0xFF1E1E1E); // سطح داكن
  static const Color darkCard = Color(0xFF2C2C2C); // بطاقة داكنة
  static const Color darkTextPrimary = Color(0xFFFFFFFF); // نص أساسي فاتح
  static const Color darkTextSecondary = Color(0xFFB0B0B0); // نص ثانوي فاتح
  static const Color darkTextHint = Color(0xFF6D6D6D); // نص تلميحي داكن
  static const Color darkBorder = Color(0xFF2C2C2C); // حدود داكنة
  static const Color darkDivider = Color(0xFF323232); // فواصل داكنة
}

/// Application Text Styles
class AppTextStyles {
  // العناوين الرئيسية
  static const TextStyle headingLarge = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 24,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.textPrimary,
    height: 1.3,
  );

  static const TextStyle headingMedium = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 20,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.textPrimary,
    height: 1.3,
  );

  static const TextStyle headingSmall = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 16,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.textPrimary,
    height: 1.3,
  );

  // نصوص الجسم
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 16,
    fontWeight: FontWeight.w400, // Regular
    color: AppColors.textPrimary,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 14,
    fontWeight: FontWeight.w400, // Regular
    color: AppColors.textPrimary,
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 12,
    fontWeight: FontWeight.w400, // Regular
    color: AppColors.textSecondary,
    height: 1.5,
  );

  // نصوص خاصة
  static const TextStyle labelLarge = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 14,
    fontWeight: FontWeight.w500, // Medium
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 12,
    fontWeight: FontWeight.w500, // Medium
    color: AppColors.textPrimary,
    height: 1.4,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 11,
    fontWeight: FontWeight.w500, // Medium
    color: AppColors.textSecondary,
    height: 1.4,
  );

  // أزرار
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 16,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.textOnPrimary,
    height: 1.3,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 14,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.textOnPrimary,
    height: 1.3,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 12,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.textOnPrimary,
    height: 1.3,
  );

  // عناوين الجداول
  static const TextStyle tableHeader = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 14,
    fontWeight: FontWeight.w700, // Bold
    color: AppColors.tableHeaderText,
    height: 1.3,
  );

  // خلايا الجداول
  static const TextStyle tableCell = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 14,
    fontWeight: FontWeight.w400, // Regular
    color: AppColors.textPrimary,
    height: 1.3,
  );

  // عناوين إضافية
  static const TextStyle titleLarge = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 22,
    fontWeight: FontWeight.w600, // Semi-Bold
    color: AppColors.textPrimary,
    height: 1.3,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 18,
    fontWeight: FontWeight.w600, // Semi-Bold
    color: AppColors.textPrimary,
    height: 1.3,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: 'Cairo',
    fontSize: 15,
    fontWeight: FontWeight.w600, // Semi-Bold
    color: AppColors.textPrimary,
    height: 1.3,
  );

  // للتوافق مع الكود القديم
  static const TextStyle heading1 = headingLarge;
  static const TextStyle heading2 = headingMedium;
  static const TextStyle heading3 = headingSmall;
  static const TextStyle body = bodyLarge;
  static const TextStyle body1 = bodyLarge;
  static const TextStyle body2 = bodyMedium;
  static const TextStyle caption = labelSmall;
  static const TextStyle button = buttonMedium;
}

/// Application Dimensions
class AppDimensions {
  // تباعد وهوامش
  static const double spacingXXS = 4.0; // تباعد صغير جداً جداً
  static const double spacingXS = 8.0; // تباعد صغير جداً
  static const double spacingS = 12.0; // تباعد صغير
  static const double spacingM = 16.0; // تباعد متوسط
  static const double spacingL = 24.0; // تباعد كبير
  static const double spacingXL = 32.0; // تباعد كبير جداً
  static const double spacingXXL = 48.0; // تباعد كبير جداً جداً

  // للتوافق مع الكود القديم
  static const double paddingXS = spacingXXS;
  static const double paddingS = spacingXS;
  static const double paddingM = spacingM;
  static const double paddingL = spacingL;
  static const double paddingXL = spacingXL;
  static const double buttonHeight = buttonHeightL;

  // هوامش الشاشة
  static const double screenMargin = 16.0; // هامش الشاشة القياسي
  static const double cardMargin = 16.0; // هامش البطاقات
  static const double cardPadding = 16.0; // تباعد داخل البطاقات
  static const double listItemSpacing = 12.0; // تباعد بين عناصر القائمة
  static const double sectionSpacing = 24.0; // تباعد بين الأقسام

  // نصف قطر الزوايا
  static const double radiusXS = 4.0; // نصف قطر صغير جداً
  static const double radiusS = 8.0; // نصف قطر صغير
  static const double radiusM = 12.0; // نصف قطر متوسط
  static const double radiusL = 16.0; // نصف قطر كبير
  static const double radiusXL = 24.0; // نصف قطر كبير جداً
  static const double radiusCircular = 100.0; // نصف قطر دائري

  // أحجام الأيقونات
  static const double iconSizeXS = 16.0; // أيقونة صغيرة جداً
  static const double iconSizeS = 20.0; // أيقونة صغيرة
  static const double iconSizeM = 24.0; // أيقونة متوسطة
  static const double iconSizeL = 32.0; // أيقونة كبيرة
  static const double iconSizeXL = 48.0; // أيقونة كبيرة جداً

  // ارتفاعات العناصر
  static const double buttonHeightS = 32.0; // ارتفاع الزر الصغير
  static const double buttonHeightM = 40.0; // ارتفاع الزر المتوسط
  static const double buttonHeightL = 48.0; // ارتفاع الزر الكبير
  static const double inputHeight = 56.0; // ارتفاع حقل الإدخال
  static const double appBarHeight = 56.0; // ارتفاع شريط التطبيق
  static const double tabBarHeight = 48.0; // ارتفاع شريط التبويب
  static const double bottomNavBarHeight = 56.0; // ارتفاع شريط التنقل السفلي

  // ظلال
  static const double elevationXS = 1.0; // ظل خفيف جداً
  static const double elevationS = 2.0; // ظل خفيف
  static const double elevationM = 4.0; // ظل متوسط
  static const double elevationL = 8.0; // ظل كبير
  static const double elevationXL = 16.0; // ظل كبير جداً

  // سماكة الحدود
  static const double borderWidthThin = 0.5; // حد رفيع
  static const double borderWidthRegular = 1.0; // حد عادي
  static const double borderWidthThick = 2.0; // حد سميك
}

/// Application Assets
class AppAssets {
  // Images
  static const String logoPath = 'assets/images/logo.png';
  static const String loginBgPath = 'assets/images/login_bg.jpg';
  static const String placeholderPath = 'assets/images/placeholder.png';

  // Icons
  static const String dashboardIcon = 'assets/icons/dashboard.svg';
  static const String invoiceIcon = 'assets/icons/invoice.svg';
  static const String customerIcon = 'assets/icons/customer.svg';
  static const String inventoryIcon = 'assets/icons/inventory.svg';
  static const String employeeIcon = 'assets/icons/employee.svg';
  static const String reportIcon = 'assets/icons/report.svg';
  static const String settingsIcon = 'assets/icons/settings.svg';
}

/// Application Constants
class AppConstants {
  // API URLs
  static const String baseUrl = 'https://api.icecorner.com/api/v1';

  // Shared Preferences Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String themeKey = 'app_theme';
  static const String languageKey = 'app_language';
  static const String rememberMeKey = 'remember_me';
  static const String usernameKey = 'username';
  static const String passwordKey = 'password';

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Pagination
  static const int defaultPageSize = 20;
}
