import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../models/report_models.dart';
import '../widgets/report_filter_dialog.dart';
import '../widgets/report_summary_card.dart';
import '../utils/pdf_export_util.dart';

class DetailedReportScreen extends StatefulWidget {
  final String reportType;
  final int? id;

  const DetailedReportScreen({
    super.key,
    required this.reportType,
    this.id,
  });

  @override
  State<DetailedReportScreen> createState() => _DetailedReportScreenState();
}

class _DetailedReportScreenState extends State<DetailedReportScreen> {
  bool _isLoading = true;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _filterType = 'all';
  List<dynamic> _reportData = [];
  Map<String, double> _summaryData = {};

  // Funciones para generar datos de prueba
  List<Invoice> generateMockInvoices() {
    return List.generate(
      20,
      (index) => Invoice(
        id: index + 1,
        invoiceNumber: 'INV-${2023}${(index + 1).toString().padLeft(4, '0')}',
        customerId: index % 5 + 1,
        customerName: 'عميل ${index % 5 + 1}',
        date: DateTime.now().subtract(Duration(days: index * 2)),
        dueDate: DateTime.now().add(Duration(days: 15 - index)),
        items: [],
        subtotal: 1000.0 + (index * 100),
        tax: (1000.0 + (index * 100)) * 0.15,
        discount: index * 50.0,
        total: (1000.0 + (index * 100)) * 1.15 - (index * 50.0),
        notes: 'ملاحظات الفاتورة ${index + 1}',
        isPaid: index % 3 == 0,
        paymentDate: index % 3 == 0 ? DateTime.now().subtract(Duration(days: index)) : null,
        createdAt: DateTime.now().subtract(Duration(days: index * 2 + 1)),
      ),
    );
  }

  List<ServiceRequest> generateMockServiceRequests() {
    final statuses = [
      ServiceStatus.pending,
      ServiceStatus.inProgress,
      ServiceStatus.completed,
      ServiceStatus.cancelled,
    ];

    return List.generate(
      15,
      (index) => ServiceRequest(
        id: index + 1,
        requestNumber: 'SR-${2023}${(index + 1).toString().padLeft(4, '0')}',
        customerId: index % 5 + 1,
        customerName: 'عميل ${index % 5 + 1}',
        serviceType: index % 2 == 0 ? 'صيانة تكييف' : 'تركيب تكييف',
        description: 'وصف طلب الخدمة ${index + 1}',
        address: 'عنوان العميل ${index % 5 + 1}',
        scheduledDate: DateTime.now().add(Duration(days: index)),
        status: statuses[index % 4],
        assignedTo: index % 4 == 3 ? null : 'فني ${index % 3 + 1}',
        completionDate: statuses[index % 4] == ServiceStatus.completed
            ? DateTime.now().subtract(Duration(days: index % 5))
            : null,
        notes: index % 2 == 0 ? 'ملاحظات إضافية للطلب' : null,
        createdAt: DateTime.now().subtract(Duration(days: index * 3)),
      ),
    );
  }

  List<Transaction> generateMockTransactions() {
    final types = [TransactionType.income, TransactionType.expense];
    final categories = [
      'مبيعات',
      'رواتب',
      'إيجار',
      'مشتريات',
      'صيانة',
      'أخرى',
    ];

    return List.generate(
      25,
      (index) => Transaction(
        id: index + 1,
        reference: index % 4 == 0
            ? 'SUP-${index % 3 + 1}-${index + 1000}'
            : index % 4 == 1
                ? 'EMP-${index % 3 + 1}-${index + 1000}'
                : 'REF-${index + 1000}',
        date: DateTime.now().subtract(Duration(days: index)),
        amount: 500.0 + (index * 100),
        type: types[index % 2],
        category: categories[index % 6],
        description: 'وصف الإيراد ${index + 1}',
        paymentMethod: index % 3 == 0 ? PaymentMethod.cash : PaymentMethod.bankTransfer,
        createdAt: DateTime.now().subtract(Duration(days: index, hours: 2)),
      ),
    );
  }

  // Función para obtener el nombre de la entidad
  String getEntityName() {
    // En una aplicación real, obtendríamos el nombre de la entidad desde la base de datos
    if (widget.id == null) return '';

    switch (widget.reportType) {
      case 'customer':
        return 'عميل ${widget.id}';
      case 'employee':
        return 'موظف ${widget.id}';
      case 'supplier':
        return 'مورد ${widget.id}';
      default:
        return 'عنصر ${widget.id}';
    }
  }

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Generate mock data based on report type
    List<dynamic> data = [];
    Map<String, double> summary = {};

    // Filtrar por ID específico si está disponible
    bool filterById = widget.id != null;

    switch (widget.reportType) {
      case 'invoices':
      case 'customer':
        data = generateMockInvoices();

        // Filtrar por cliente si es necesario
        if (filterById && widget.reportType == 'customer') {
          data = data.where((invoice) => (invoice as Invoice).customerId == widget.id).toList();
        }

        summary = {
          'total': data.fold(0, (sum, item) => sum + (item as Invoice).total),
          'paid': data.fold(0, (sum, item) => sum + ((item as Invoice).isPaid ? (item).total : 0)),
          'unpaid': data.fold(0, (sum, item) => sum + ((item as Invoice).isPaid ? 0 : (item).total)),
          'count': data.length.toDouble(),
        };
        break;

      case 'services':
        data = generateMockServiceRequests();

        // Filtrar por cliente si es necesario
        if (filterById && widget.reportType == 'customer') {
          data = data.where((request) => (request as ServiceRequest).customerId == widget.id).toList();
        }

        summary = {
          'total': data.length.toDouble(),
          'completed': data.where((item) => (item as ServiceRequest).status == ServiceStatus.completed).length.toDouble(),
          'pending': data.where((item) => (item as ServiceRequest).status == ServiceStatus.pending).length.toDouble(),
          'inProgress': data.where((item) => (item as ServiceRequest).status == ServiceStatus.inProgress).length.toDouble(),
        };
        break;

      case 'transactions':
      case 'supplier':
      case 'employee':
        data = generateMockTransactions();

        // Filtrar por entidad específica si es necesario
        if (filterById) {
          if (widget.reportType == 'supplier') {
            // Filtrar transacciones relacionadas con el proveedor
            data = data.where((transaction) {
              final trans = transaction as Transaction;
              return trans.reference.contains('SUP-$widget.id');
            }).toList();
          } else if (widget.reportType == 'employee') {
            // Filtrar transacciones relacionadas con el empleado
            data = data.where((transaction) {
              final trans = transaction as Transaction;
              return trans.reference.contains('EMP-$widget.id');
            }).toList();
          }
        }

        summary = {
          'income': data.fold(0, (sum, item) => sum + ((item as Transaction).type == TransactionType.income ? (item).amount : 0)),
          'expense': data.fold(0, (sum, item) => sum + ((item as Transaction).type == TransactionType.expense ? (item).amount : 0)),
          'balance': data.fold(0, (sum, item) => sum + ((item as Transaction).type == TransactionType.income ? (item).amount : -(item).amount)),
          'count': data.length.toDouble(),
        };
        break;

      default:
        data = [];
        summary = {};
    }

    if (mounted) {
      setState(() {
        _reportData = data;
        _summaryData = summary;
        _isLoading = false;
      });
    }
  }

  void showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => ReportFilterDialog(
        startDate: _startDate,
        endDate: _endDate,
        filterType: _filterType,
        onApplyFilter: (startDate, endDate, filterType) {
          setState(() {
            _startDate = startDate;
            _endDate = endDate;
            _filterType = filterType;
          });
          _loadReportData();
        },
      ),
    );
  }

  String getReportTitle() {
    // Si hay un ID específico, es un informe para una entidad específica
    final specificEntity = widget.id != null ? ' - ${getEntityName()}' : '';

    switch (widget.reportType) {
      case 'invoices':
        return 'تقرير الفواتير$specificEntity';
      case 'services':
        return 'تقرير طلبات الخدمة$specificEntity';
      case 'transactions':
        return 'تقرير الإيرادا المالية$specificEntity';
      case 'customer':
        return 'تقرير العميل: ${getEntityName()}';
      case 'employee':
        return 'تقرير الموظف: ${getEntityName()}';
      case 'supplier':
        return 'تقرير المورد: ${getEntityName()}';
      default:
        return 'تقرير تفصيلي';
    }
  }

  // Funciones para construir elementos de UI
  Widget buildInvoiceItem(Invoice invoice) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: ListTile(
        title: Text(
          '${invoice.invoiceNumber} - ${invoice.customerName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'التاريخ: ${DateFormat('dd/MM/yyyy').format(invoice.date)} | المبلغ: ${invoice.total.toStringAsFixed(2)} ر.س',
        ),
        trailing: Chip(
          label: Text(
            invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: invoice.isPaid ? Colors.green : Colors.orange,
        ),
        onTap: () {
          // Navigate to invoice details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('عرض تفاصيل الفاتورة ${invoice.invoiceNumber}'),
            ),
          );
        },
      ),
    );
  }

  Widget buildServiceRequestItem(ServiceRequest serviceRequest) {
    Color statusColor;
    switch (serviceRequest.status) {
      case ServiceStatus.completed:
        statusColor = Colors.green;
        break;
      case ServiceStatus.inProgress:
        statusColor = Colors.orange;
        break;
      case ServiceStatus.pending:
        statusColor = Colors.blue;
        break;
      case ServiceStatus.cancelled:
        statusColor = Colors.red;
        break;
      case ServiceStatus.onHold:
        statusColor = Colors.purple;
        break;
      case ServiceStatus.followUp:
        statusColor = Colors.amber;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: ListTile(
        title: Text(
          '${serviceRequest.requestNumber} - ${serviceRequest.customerName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'النوع: ${serviceRequest.serviceType} | التاريخ: ${DateFormat('dd/MM/yyyy').format(serviceRequest.scheduledDate)}',
        ),
        trailing: Chip(
          label: Text(
            ServiceRequest.getStatusName(serviceRequest.status),
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: statusColor,
        ),
        onTap: () {
          // Navigate to service request details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('عرض تفاصيل طلب الخدمة ${serviceRequest.requestNumber}'),
            ),
          );
        },
      ),
    );
  }

  Widget buildTransactionItem(Transaction transaction) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: ListTile(
        leading: Icon(
          transaction.type == TransactionType.income
              ? Icons.arrow_upward
              : Icons.arrow_downward,
          color: transaction.type == TransactionType.income
              ? Colors.green
              : Colors.red,
        ),
        title: Text(
          transaction.description,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'التصنيف: ${transaction.category} | التاريخ: ${DateFormat('dd/MM/yyyy').format(transaction.date)}',
        ),
        trailing: Text(
          '${transaction.amount.toStringAsFixed(2)} ر.س',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: transaction.type == TransactionType.income
                ? Colors.green
                : Colors.red,
          ),
        ),
        onTap: () {
          // Navigate to transaction details
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('عرض تفاصيل الإيراد ${transaction.id}'),
            ),
          );
        },
      ),
    );
  }

  Widget buildSummarySection() {
    if (_summaryData.isEmpty) {
      return const SizedBox.shrink();
    }

    switch (widget.reportType) {
      case 'invoices':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'إجمالي الفواتير',
              value: '${_summaryData['total']?.toStringAsFixed(2)} ر.س',
              icon: Icons.receipt_long,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'المدفوع',
              value: '${_summaryData['paid']?.toStringAsFixed(2)} ر.س',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'غير المدفوع',
              value: '${_summaryData['unpaid']?.toStringAsFixed(2)} ر.س',
              icon: Icons.pending,
              color: Colors.orange,
            ),
            SummaryItem(
              title: 'عدد الفواتير',
              value: '${_summaryData['count']?.toInt()}',
              icon: Icons.numbers,
              color: Colors.blue,
            ),
          ],
        );
      case 'services':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'إجمالي الطلبات',
              value: '${_summaryData['total']?.toInt()}',
              icon: Icons.home_repair_service,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'المكتملة',
              value: '${_summaryData['completed']?.toInt()}',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'قيد التنفيذ',
              value: '${_summaryData['inProgress']?.toInt()}',
              icon: Icons.pending_actions,
              color: Colors.orange,
            ),
            SummaryItem(
              title: 'في الانتظار',
              value: '${_summaryData['pending']?.toInt()}',
              icon: Icons.schedule,
              color: Colors.blue,
            ),
          ],
        );
      case 'transactions':
        return ReportSummaryCard(
          items: [
            SummaryItem(
              title: 'الإيرادات',
              value: '${_summaryData['income']?.toStringAsFixed(2)} ر.س',
              icon: Icons.arrow_upward,
              color: Colors.green,
            ),
            SummaryItem(
              title: 'المصروفات',
              value: '${_summaryData['expense']?.toStringAsFixed(2)} ر.س',
              icon: Icons.arrow_downward,
              color: Colors.red,
            ),
            SummaryItem(
              title: 'الرصيد',
              value: '${_summaryData['balance']?.toStringAsFixed(2)} ر.س',
              icon: Icons.account_balance,
              color: AppColors.primary,
            ),
            SummaryItem(
              title: 'عدد الإيرادا',
              value: '${_summaryData['count']?.toInt()}',
              icon: Icons.swap_horiz,
              color: Colors.blue,
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget buildReportList() {
    if (_reportData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: AppTextStyles.heading3,
        ),
      );
    }

    switch (widget.reportType) {
      case 'invoices':
      case 'customer':
        return ListView.builder(
          itemCount: _reportData.length,
          itemBuilder: (context, index) {
            final invoice = _reportData[index] as Invoice;
            return buildInvoiceItem(invoice);
          },
        );
      case 'services':
        return ListView.builder(
          itemCount: _reportData.length,
          itemBuilder: (context, index) {
            final serviceRequest = _reportData[index] as ServiceRequest;
            return buildServiceRequestItem(serviceRequest);
          },
        );
      case 'transactions':
      case 'supplier':
      case 'employee':
        return ListView.builder(
          itemCount: _reportData.length,
          itemBuilder: (context, index) {
            final transaction = _reportData[index] as Transaction;
            return buildTransactionItem(transaction);
          },
        );
      default:
        return const Center(
          child: Text(
            'نوع تقرير غير معروف',
            style: AppTextStyles.heading3,
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(getReportTitle()),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: () async {
              try {
                // No necesitamos convertir _summaryData ya que ya es Map<String, double>
                final Map<String, double> summaryDataDouble = _summaryData;

                // Determinar el tipo de reporte correcto para PDF
                String pdfReportType = widget.reportType;

                // Verificar el tipo de datos para asignar el tipo de reporte correcto
                if (_reportData.isNotEmpty) {
                  if (_reportData.first is Invoice) {
                    pdfReportType = 'invoices';
                  } else if (_reportData.first is ServiceRequest) {
                    pdfReportType = 'services';
                  } else if (_reportData.first is Transaction) {
                    pdfReportType = 'transactions';
                  }
                }

                await PdfExportUtil.exportReport(
                  reportType: pdfReportType,
                  title: getReportTitle(),
                  startDate: _startDate,
                  endDate: _endDate,
                  data: _reportData,
                  summaryData: summaryDataDouble,
                  context: context,
                );
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('جاري طباعة التقرير...'),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('جاري مشاركة التقرير...'),
                ),
              );
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Text(
                    'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                    style: AppTextStyles.heading3,
                  ),
                ),
                buildSummarySection(),
                const Divider(),
                Expanded(
                  child: buildReportList(),
                ),
              ],
            ),
    );
  }
}
