import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/report.dart';

class ReportRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all reports
  Future<List<Report>> getAllReports() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'reports',
        orderBy: 'created_at DESC',
      );
      
      return List.generate(maps.length, (i) {
        return Report.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting reports: $e');
      }
      return [];
    }
  }

  // Get reports by type
  Future<List<Report>> getReportsByType(String type) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'reports',
        where: 'type = ?',
        whereArgs: [type],
        orderBy: 'created_at DESC',
      );
      
      return List.generate(maps.length, (i) {
        return Report.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting reports by type: $e');
      }
      return [];
    }
  }

  // Get report by ID
  Future<Report?> getReportById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'reports',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (maps.isNotEmpty) {
        return Report.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting report by ID: $e');
      }
      return null;
    }
  }

  // Insert a new report
  Future<int> insertReport(Report report) async {
    try {
      final db = await _dbHelper.database;
      
      final Map<String, dynamic> reportMap = {
        'title': report.title,
        'description': report.description,
        'type': report.type,
        'parameters': report.parameters != null ? jsonEncode(report.parameters) : null,
        'created_by': report.createdBy,
        'created_at': DateTime.now().toIso8601String(),
      };
      
      return await db.insert('reports', reportMap);
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting report: $e');
      }
      return -1;
    }
  }

  // Update an existing report
  Future<int> updateReport(Report report) async {
    try {
      final db = await _dbHelper.database;
      
      final Map<String, dynamic> reportMap = {
        'title': report.title,
        'description': report.description,
        'type': report.type,
        'parameters': report.parameters != null ? jsonEncode(report.parameters) : null,
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      return await db.update(
        'reports',
        reportMap,
        where: 'id = ?',
        whereArgs: [report.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating report: $e');
      }
      return 0;
    }
  }

  // Delete a report
  Future<int> deleteReport(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'reports',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting report: $e');
      }
      return 0;
    }
  }
}
