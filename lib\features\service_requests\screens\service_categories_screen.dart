import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/models/service_category.dart';
import '../../../core/repositories/service_category_repository.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../../../shared/widgets/empty_state.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/error_state.dart';
import '../../../shared/widgets/search_bar.dart';
import '../widgets/service_category_card.dart';
import 'service_category_form_screen.dart';

class ServiceCategoriesScreen extends StatefulWidget {
  const ServiceCategoriesScreen({super.key});

  @override
  State<ServiceCategoriesScreen> createState() => _ServiceCategoriesScreenState();
}

class _ServiceCategoriesScreenState extends State<ServiceCategoriesScreen> {
  final ServiceCategoryRepository _repository = ServiceCategoryRepository();
  List<ServiceCategory> _categories = [];
  List<ServiceCategory> _filteredCategories = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final categories = await _repository.getAllServiceCategories();
      
      if (mounted) {
        setState(() {
          _categories = categories;
          _applySearch();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تحميل فئات الخدمة: $e';
          _isLoading = false;
        });
      }
    }
  }

  void _applySearch() {
    if (_searchQuery.isEmpty) {
      _filteredCategories = List.from(_categories);
    } else {
      _filteredCategories = _categories.where((category) {
        return category.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            category.description.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _applySearch();
    });
  }

  Future<void> _toggleCategoryStatus(ServiceCategory category) async {
    try {
      final result = await _repository.toggleServiceCategoryStatus(
        category.id!,
        !category.isActive,
      );

      if (result > 0) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                category.isActive
                    ? 'تم تعطيل الفئة ${category.name}'
                    : 'تم تفعيل الفئة ${category.name}',
              ),
              backgroundColor: Colors.green,
            ),
          );
          _loadCategories();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث حالة الفئة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteCategory(ServiceCategory category) async {
    try {
      final result = await _repository.deleteServiceCategory(category.id!);

      if (result > 0) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف الفئة ${category.name}'),
              backgroundColor: Colors.green,
            ),
          );
          _loadCategories();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء حذف الفئة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteConfirmationDialog(ServiceCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فئة "${category.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteCategory(category);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فئات الخدمة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCategories,
            tooltip: 'تحديث',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const LoadingIndicator(message: 'جاري تحميل فئات الخدمة...')
          : _errorMessage != null
              ? ErrorState(
                  message: _errorMessage!,
                  onRetry: _loadCategories,
                )
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: AppSearchBar(
                        onChanged: _onSearchChanged,
                        hintText: 'البحث في فئات الخدمة...',
                      ),
                    ),
                    Expanded(
                      child: _filteredCategories.isEmpty
                          ? EmptyState(
                              icon: Icons.category,
                              title: 'لا توجد فئات خدمة',
                              message: _searchQuery.isNotEmpty
                                  ? 'لا توجد نتائج تطابق بحثك'
                                  : 'قم بإضافة فئات الخدمة لتظهر هنا',
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              itemCount: _filteredCategories.length,
                              itemBuilder: (context, index) {
                                final category = _filteredCategories[index];
                                return ServiceCategoryCard(
                                  category: category,
                                  onEdit: () async {
                                    final result = await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => ServiceCategoryFormScreen(
                                          category: category,
                                        ),
                                      ),
                                    );
                                    if (result == true) {
                                      _loadCategories();
                                    }
                                  },
                                  onDelete: () => _showDeleteConfirmationDialog(category),
                                  onToggleStatus: () => _toggleCategoryStatus(category),
                                );
                              },
                            ),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ServiceCategoryFormScreen(),
            ),
          );
          if (result == true) {
            _loadCategories();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
