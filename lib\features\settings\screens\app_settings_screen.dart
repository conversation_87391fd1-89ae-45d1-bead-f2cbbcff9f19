import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../state/auth_state.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_switch_tile.dart';
import '../widgets/settings_selection_tile.dart';

class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({super.key});

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  bool _darkMode = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  String _language = 'ar';
  String _currency = 'SAR';

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<AuthState>(context).user;

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      drawer: const AppDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User profile section
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: AppColors.primary,
                      child: Text(
                        user?.name.substring(0, 1) ?? 'U',
                        style: const TextStyle(
                          fontSize: 30,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    Text(
                      user?.name ?? 'المستخدم',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Text(
                      user?.email ?? '<EMAIL>',
                      style: const TextStyle(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    OutlinedButton(
                      onPressed: () {
                        _showEditProfileDialog(context);
                      },
                      child: const Text('تعديل الملف الشخصي'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),

            // Appearance settings
            SettingsSection(
              title: 'المظهر',
              children: [
                SettingsSwitchTile(
                  title: 'الوضع الداكن',
                  subtitle: 'تفعيل المظهر الداكن للتطبيق',
                  value: _darkMode,
                  onChanged: (value) {
                    setState(() {
                      _darkMode = value;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('سيتم تفعيل هذه الميزة قريباً'),
                      ),
                    );
                  },
                ),
                SettingsSelectionTile(
                  title: 'اللغة',
                  value: _getLanguageName(_language),
                  onTap: () {
                    _showLanguageSelectionDialog(context);
                  },
                ),
                SettingsSelectionTile(
                  title: 'العملة',
                  value: _getCurrencyName(_currency),
                  onTap: () {
                    _showCurrencySelectionDialog(context);
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),

            // Notifications settings
            SettingsSection(
              title: 'الإشعارات',
              children: [
                ListTile(
                  title: const Text('إعدادات الإشعارات'),
                  subtitle: const Text('تخصيص إعدادات الإشعارات والتذكيرات'),
                  leading: const Icon(Icons.notifications_active),
                  onTap: () {
                    Navigator.pushNamed(context, AppRoutes.notificationSettings);
                  },
                ),
                const Divider(),
                SettingsSwitchTile(
                  title: 'الإشعارات',
                  subtitle: 'تفعيل إشعارات التطبيق',
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                      if (!value) {
                        _soundEnabled = false;
                        _vibrationEnabled = false;
                      }
                    });
                  },
                ),
                SettingsSwitchTile(
                  title: 'الصوت',
                  subtitle: 'تفعيل صوت الإشعارات',
                  value: _soundEnabled,
                  enabled: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                  },
                ),
                SettingsSwitchTile(
                  title: 'الاهتزاز',
                  subtitle: 'تفعيل اهتزاز الإشعارات',
                  value: _vibrationEnabled,
                  enabled: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _vibrationEnabled = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),

            // Security settings
            SettingsSection(
              title: 'الأمان',
              children: [
                ListTile(
                  title: const Text('تغيير كلمة المرور'),
                  leading: const Icon(Icons.lock_outline),
                  onTap: () {
                    _showChangePasswordDialog(context);
                  },
                ),
                ListTile(
                  title: const Text('تسجيل الخروج'),
                  leading: const Icon(Icons.logout),
                  onTap: () {
                    _showLogoutConfirmation(context);
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),

            // System settings
            SettingsSection(
              title: 'إعدادات النظام',
              children: [
                ListTile(
                  title: const Text('معلومات الشركة'),
                  subtitle: const Text('تعديل بيانات الشركة وشعارها'),
                  leading: const Icon(Icons.business),
                  onTap: () {
                    Navigator.pushNamed(context, AppRoutes.companyInfo);
                  },
                ),
                ListTile(
                  title: const Text('إعدادات قاعدة البيانات'),
                  subtitle: const Text('تحديد مسار قاعدة البيانات'),
                  leading: const Icon(Icons.storage),
                  onTap: () {
                    Navigator.pushNamed(context, AppRoutes.databaseSettings);
                  },
                ),
                ListTile(
                  title: const Text('البيانات التعليمية'),
                  subtitle: const Text('توليد بيانات تعليمية للتطبيق'),
                  leading: const Icon(Icons.data_array),
                  onTap: () {
                    Navigator.pushNamed(context, AppRoutes.demoData);
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),

            // About section
            SettingsSection(
              title: 'حول التطبيق',
              children: [
                ListTile(
                  title: const Text('الإصدار'),
                  subtitle: const Text('1.0.0'),
                  leading: const Icon(Icons.info_outline),
                ),
                ListTile(
                  title: const Text('سياسة الخصوصية'),
                  leading: const Icon(Icons.privacy_tip_outlined),
                  onTap: () {
                    // Navigate to privacy policy
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('سيتم فتح سياسة الخصوصية'),
                      ),
                    );
                  },
                ),
                ListTile(
                  title: const Text('شروط الاستخدام'),
                  leading: const Icon(Icons.description_outlined),
                  onTap: () {
                    // Navigate to terms of service
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('سيتم فتح شروط الاستخدام'),
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingL),
          ],
        ),
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String name = Provider.of<AuthState>(context, listen: false).user?.name ?? '';
    String email = Provider.of<AuthState>(context, listen: false).user?.email ?? '';
    String phone = '';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل الملف الشخصي'),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    initialValue: name,
                    decoration: const InputDecoration(
                      labelText: 'الاسم',
                      prefixIcon: Icon(Icons.person),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الاسم';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      name = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    initialValue: email,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      prefixIcon: Icon(Icons.email),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال البريد الإلكتروني';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      email = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    initialValue: phone,
                    decoration: const InputDecoration(
                      labelText: 'رقم الهاتف',
                      prefixIcon: Icon(Icons.phone),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                    onSaved: (value) {
                      phone = value!;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // Update user profile
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث الملف الشخصي بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  void _showChangePasswordDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    // These variables are used in onSaved callbacks but not used elsewhere
    String currentPassword = '';
    String newPassword = '';
    String confirmPassword = '';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تغيير كلمة المرور'),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور الحالية',
                      prefixIcon: Icon(Icons.lock),
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال كلمة المرور الحالية';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      currentPassword = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور الجديدة',
                      prefixIcon: Icon(Icons.lock_outline),
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال كلمة المرور الجديدة';
                      }
                      if (value.length < 6) {
                        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      newPassword = value!;
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingM),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'تأكيد كلمة المرور الجديدة',
                      prefixIcon: Icon(Icons.lock_outline),
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى تأكيد كلمة المرور الجديدة';
                      }
                      if (value != newPassword) {
                        return 'كلمة المرور غير متطابقة';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      confirmPassword = value!;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // Using the variables to avoid warnings
                  if (kDebugMode) {
                    print('Current: $currentPassword, New: $newPassword, Confirm: $confirmPassword');
                  }

                  // Change password
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تغيير كلمة المرور بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('تغيير'),
            ),
          ],
        );
      },
    );
  }

  void _showLanguageSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر اللغة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('العربية'),
                leading: Radio<String>(
                  value: 'ar',
                  groupValue: _language,
                  onChanged: (value) {
                    setState(() {
                      _language = value!;
                    });
                    Navigator.pop(context);
                  },
                ),
              ),
              ListTile(
                title: const Text('English'),
                leading: Radio<String>(
                  value: 'en',
                  groupValue: _language,
                  onChanged: (value) {
                    setState(() {
                      _language = value!;
                    });
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showCurrencySelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختر العملة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('ريال سعودي (SAR)'),
                leading: Radio<String>(
                  value: 'SAR',
                  groupValue: _currency,
                  onChanged: (value) {
                    setState(() {
                      _currency = value!;
                    });
                    Navigator.pop(context);
                  },
                ),
              ),
              ListTile(
                title: const Text('دولار أمريكي (USD)'),
                leading: Radio<String>(
                  value: 'USD',
                  groupValue: _currency,
                  onChanged: (value) {
                    setState(() {
                      _currency = value!;
                    });
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);

                // Logout
                Provider.of<AuthState>(context, listen: false).logout();

                // Navigate to login screen
                Navigator.pushReplacementNamed(context, '/login');
              },
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return 'العربية';
    }
  }

  String _getCurrencyName(String code) {
    switch (code) {
      case 'SAR':
        return 'ريال سعودي (SAR)';
      case 'USD':
        return 'دولار أمريكي (USD)';
      default:
        return 'ريال سعودي (SAR)';
    }
  }
}
