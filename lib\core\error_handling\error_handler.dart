import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../localization/app_localizations.dart';

/// Comprehensive error handling service with Arabic support
class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  /// Handle and display errors to user
  static void handleError(
    BuildContext context,
    dynamic error, {
    String? customMessage,
    bool showSnackBar = true,
    bool logError = true,
  }) {
    final localizations = AppLocalizations.of(context);
    
    if (logError && kDebugMode) {
      print('❌ Error: $error');
      if (error is Error) {
        print('Stack trace: ${error.stackTrace}');
      }
    }

    final errorMessage = customMessage ?? _getErrorMessage(error, localizations);
    
    if (showSnackBar) {
      _showErrorSnackBar(context, errorMessage);
    }
  }

  /// Show error dialog
  static void showErrorDialog(
    BuildContext context,
    dynamic error, {
    String? title,
    String? customMessage,
    VoidCallback? onRetry,
  }) {
    final localizations = AppLocalizations.of(context);
    final errorMessage = customMessage ?? _getErrorMessage(error, localizations);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? localizations?.translate('error') ?? 'Error'),
        content: Text(errorMessage),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                onRetry();
              },
              child: Text(localizations?.translate('retry') ?? 'Retry'),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations?.translate('ok') ?? 'OK'),
          ),
        ],
      ),
    );
  }

  /// Show success message
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show warning message
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show info message
  static void showInfo(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: duration,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show loading dialog
  static void showLoadingDialog(
    BuildContext context, {
    String? message,
  }) {
    final localizations = AppLocalizations.of(context);
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message ?? localizations?.translate('please_wait') ?? 'Please wait...'),
          ],
        ),
      ),
    );
  }

  /// Hide loading dialog
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context, rootNavigator: true).pop();
  }

  /// Handle async operations with error handling
  static Future<T?> handleAsyncOperation<T>(
    BuildContext context,
    Future<T> operation, {
    String? loadingMessage,
    String? successMessage,
    bool showLoading = false,
    bool showSuccess = false,
  }) async {
    try {
      if (showLoading) {
        showLoadingDialog(context, message: loadingMessage);
      }

      final result = await operation;

      if (showLoading) {
        hideLoadingDialog(context);
      }

      if (showSuccess && successMessage != null) {
        ErrorHandler.showSuccess(context, successMessage);
      }

      return result;
    } catch (error) {
      if (showLoading) {
        hideLoadingDialog(context);
      }

      handleError(context, error);
      return null;
    }
  }

  /// Get user-friendly error message
  static String _getErrorMessage(dynamic error, AppLocalizations? localizations) {
    if (localizations == null) {
      return _getEnglishErrorMessage(error);
    }

    // Firebase Auth errors
    if (error is FirebaseAuthException) {
      return _getFirebaseAuthErrorMessage(error, localizations);
    }

    // Firestore errors
    if (error is FirebaseException) {
      return _getFirebaseErrorMessage(error, localizations);
    }

    // Network errors
    if (error is SocketException) {
      return localizations.translate('network_error');
    }

    if (error is TimeoutException) {
      return localizations.translate('timeout_error');
    }

    // HTTP errors
    if (error is HttpException) {
      return localizations.translate('server_error');
    }

    // Format errors
    if (error is FormatException) {
      return localizations.translate('data_format_error');
    }

    // Generic errors
    if (error is ArgumentError) {
      return localizations.translate('invalid_input');
    }

    if (error is StateError) {
      return localizations.translate('app_state_error');
    }

    // Default error message
    return localizations.translate('unexpected_error');
  }

  /// Get Firebase Auth error message
  static String _getFirebaseAuthErrorMessage(FirebaseAuthException error, AppLocalizations localizations) {
    switch (error.code) {
      case 'user-not-found':
        return localizations.translate('user_not_found');
      case 'wrong-password':
        return localizations.translate('wrong_password');
      case 'email-already-in-use':
        return localizations.translate('email_already_in_use');
      case 'weak-password':
        return localizations.translate('weak_password');
      case 'invalid-email':
        return localizations.translate('invalid_email');
      case 'user-disabled':
        return localizations.translate('user_disabled');
      case 'too-many-requests':
        return localizations.translate('too_many_requests');
      case 'operation-not-allowed':
        return localizations.translate('operation_not_allowed');
      case 'network-request-failed':
        return localizations.translate('network_error');
      default:
        return localizations.translate('auth_error');
    }
  }

  /// Get Firebase error message
  static String _getFirebaseErrorMessage(FirebaseException error, AppLocalizations localizations) {
    switch (error.code) {
      case 'permission-denied':
        return localizations.translate('permission_denied');
      case 'not-found':
        return localizations.translate('data_not_found');
      case 'already-exists':
        return localizations.translate('data_already_exists');
      case 'resource-exhausted':
        return localizations.translate('quota_exceeded');
      case 'failed-precondition':
        return localizations.translate('operation_failed');
      case 'aborted':
        return localizations.translate('operation_aborted');
      case 'out-of-range':
        return localizations.translate('invalid_range');
      case 'unimplemented':
        return localizations.translate('feature_not_available');
      case 'internal':
        return localizations.translate('server_error');
      case 'unavailable':
        return localizations.translate('service_unavailable');
      case 'data-loss':
        return localizations.translate('data_corruption');
      case 'unauthenticated':
        return localizations.translate('authentication_required');
      default:
        return localizations.translate('firebase_error');
    }
  }

  /// Get English error message (fallback)
  static String _getEnglishErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'No user found with this email address';
        case 'wrong-password':
          return 'Incorrect password';
        case 'email-already-in-use':
          return 'An account already exists with this email address';
        case 'weak-password':
          return 'Password is too weak';
        case 'invalid-email':
          return 'Invalid email address';
        case 'user-disabled':
          return 'This account has been disabled';
        case 'too-many-requests':
          return 'Too many failed attempts. Please try again later';
        default:
          return 'Authentication error occurred';
      }
    }

    if (error is SocketException) {
      return 'Network connection error';
    }

    if (error is TimeoutException) {
      return 'Request timeout';
    }

    if (error is HttpException) {
      return 'Server error';
    }

    if (error is FormatException) {
      return 'Data format error';
    }

    return 'An unexpected error occurred';
  }

  /// Show error snackbar
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}

/// Custom exception classes
class ValidationException implements Exception {
  final String message;
  final String? field;

  ValidationException(this.message, {this.field});

  @override
  String toString() => 'ValidationException: $message';
}

class BusinessLogicException implements Exception {
  final String message;
  final String? code;

  BusinessLogicException(this.message, {this.code});

  @override
  String toString() => 'BusinessLogicException: $message';
}

class DataSyncException implements Exception {
  final String message;
  final String? entityType;

  DataSyncException(this.message, {this.entityType});

  @override
  String toString() => 'DataSyncException: $message';
}
