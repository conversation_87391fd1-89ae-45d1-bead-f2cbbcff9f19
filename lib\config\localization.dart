import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'constants.dart';

class AppLocalization {
  static const List<Locale> supportedLocales = [
    Locale('ar', ''), // Arabic
    Locale('en', ''), // English
  ];

  static const Locale fallbackLocale = Locale('ar', '');

  static Future<Locale> getLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(AppConstants.languageKey);

    if (languageCode != null) {
      return Locale(languageCode, '');
    }

    return fallbackLocale;
  }

  static Future<void> setLocale(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.languageKey, languageCode);
  }
}

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const Map<String, Map<String, String>> _localizedValues = {
    'ar': {
      // Auth
      'login': 'تسجيل الدخول',
      'register': 'إنشاء حساب',
      'forgotPassword': 'نسيت كلمة المرور؟',
      'username': 'اسم المستخدم',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'confirmPassword': 'تأكيد كلمة المرور',
      'rememberMe': 'تذكرني',
      'createAccount': 'إنشاء حساب جديد',
      'alreadyHaveAccount': 'لديك حساب بالفعل؟',
      'dontHaveAccount': 'ليس لديك حساب؟',
      'resetPassword': 'إعادة تعيين كلمة المرور',
      'sendResetLink': 'إرسال رابط إعادة التعيين',

      // Dashboard
      'dashboard': 'لوحة التحكم',
      'totalRevenue': 'إجمالي الإيرادات',
      'totalExpenses': 'إجمالي المصروفات',
      'netProfit': 'صافي الربح',
      'invoicesIssued': 'الفواتير الصادرة',
      'pendingRequests': 'طلبات قيد الانتظار',
      'lowStockItems': 'عناصر المخزون المنخفضة',

      // Invoices
      'invoices': 'الفواتير',
      'newInvoice': 'فاتورة جديدة',
      'invoiceDetails': 'تفاصيل الفاتورة',
      'invoiceNumber': 'رقم الفاتورة',
      'customer': 'العميل',
      'issueDate': 'تاريخ الإصدار',
      'dueDate': 'تاريخ الاستحقاق',
      'status': 'الحالة',
      'items': 'العناصر',
      'quantity': 'الكمية',
      'unitPrice': 'سعر الوحدة',
      'total': 'المجموع',
      'subtotal': 'المجموع الفرعي',
      'tax': 'الضريبة',
      'discount': 'الخصم',
      'grandTotal': 'المجموع الكلي',
      'addItem': 'إضافة عنصر',
      'notes': 'ملاحظات',
      'save': 'حفظ',
      'print': 'طباعة',
      'delete': 'حذف',
      'cancel': 'إلغاء',

      // Transactions
      'transactions': 'المعاملات',
      'income': 'الإيرادات',
      'expense': 'المصروفات',
      'newTransaction': 'إيراد جديدة',
      'transactionType': 'نوع الإيراد',
      'amount': 'المبلغ',
      'date': 'التاريخ',
      'transactionCategory': 'الفئة',
      'description': 'الوصف',
      'paymentMethod': 'طريقة الدفع',

      // Customers
      'customers': 'العملاء',
      'suppliers': 'الموردين',
      'newCustomer': 'عميل جديد',
      'customerDetails': 'تفاصيل العميل',
      'name': 'الاسم',
      'phone': 'الهاتف',
      'address': 'العنوان',
      'contactPerson': 'جهة الاتصال',

      // Service Requests
      'serviceRequests': 'طلبات الخدمة',
      'newRequest': 'طلب جديد',
      'requestDetails': 'تفاصيل الطلب',
      'requestType': 'نوع الطلب',
      'priority': 'الأولوية',
      'assignedTo': 'مسند إلى',
      'location': 'الموقع',
      'problemDescription': 'وصف المشكلة',
      'solution': 'الحل',

      // Inventory
      'inventory': 'المخزون',
      'newItem': 'عنصر جديد',
      'itemDetails': 'تفاصيل العنصر',
      'itemCode': 'رمز العنصر',
      'itemName': 'اسم العنصر',
      'category': 'الفئة',
      'inStock': 'في المخزون',
      'minStock': 'الحد الأدنى للمخزون',
      'supplier': 'المورد',
      'costPrice': 'سعر التكلفة',
      'sellingPrice': 'سعر البيع',

      // Employees
      'employees': 'الموظفين',
      'newEmployee': 'موظف جديد',
      'employeeDetails': 'تفاصيل الموظف',
      'position': 'المنصب',
      'joinDate': 'تاريخ الانضمام',
      'salary': 'الراتب',
      'documents': 'المستندات',

      // Payroll
      'payroll': 'الرواتب',
      'salarySlip': 'قسيمة الراتب',
      'month': 'الشهر',
      'year': 'السنة',
      'basicSalary': 'الراتب الأساسي',
      'allowances': 'البدلات',
      'deductions': 'الاستقطاعات',
      'netSalary': 'صافي الراتب',
      'withdrawals': 'السحوبات',
      'newWithdrawal': 'سحب جديد',

      // Reports
      'reports': 'التقارير',
      'financialReports': 'التقارير المالية',
      'salesReports': 'تقارير المبيعات',
      'inventoryReports': 'تقارير المخزون',
      'employeeReports': 'تقارير الموظفين',
      'generateReport': 'إنشاء تقرير',
      'exportPDF': 'تصدير PDF',
      'exportExcel': 'تصدير Excel',

      // Settings
      'settings': 'الإعدادات',
      'generalSettings': 'الإعدادات العامة',
      'userManagement': 'إدارة المستخدمين',
      'companyProfile': 'ملف الشركة',
      'backupRestore': 'النسخ الاحتياطي والاستعادة',
      'language': 'اللغة',
      'theme': 'المظهر',
      'darkMode': 'الوضع الداكن',
      'lightMode': 'الوضع الفاتح',

      // Notifications
      'notifications': 'الإشعارات',
      'markAllAsRead': 'تعليم الكل كمقروء',
      'clearAll': 'مسح الكل',

      // Validation Messages
      'fieldRequired': 'هذا الحقل مطلوب',
      'invalidEmail': 'البريد الإلكتروني غير صالح',
      'passwordTooShort': 'كلمة المرور قصيرة جدًا',
      'passwordsDontMatch': 'كلمات المرور غير متطابقة',
      'invalidPhone': 'رقم الهاتف غير صالح',

      // Success Messages
      'loginSuccess': 'تم تسجيل الدخول بنجاح',
      'registerSuccess': 'تم إنشاء الحساب بنجاح',
      'resetLinkSent': 'تم إرسال رابط إعادة التعيين',
      'saveSuccess': 'تم الحفظ بنجاح',
      'deleteSuccess': 'تم الحذف بنجاح',

      // Error Messages
      'loginError': 'فشل تسجيل الدخول',
      'registerError': 'فشل إنشاء الحساب',
      'networkError': 'خطأ في الاتصال',
      'unknownError': 'حدث خطأ غير معروف',
    },
    'en': {
      // Auth
      'login': 'Login',
      'register': 'Register',
      'forgotPassword': 'Forgot Password?',
      'username': 'Username',
      'email': 'Email',
      'password': 'Password',
      'confirmPassword': 'Confirm Password',
      'rememberMe': 'Remember Me',
      'createAccount': 'Create New Account',
      'alreadyHaveAccount': 'Already have an account?',
      'dontHaveAccount': 'Don\'t have an account?',
      'resetPassword': 'Reset Password',
      'sendResetLink': 'Send Reset Link',

      // Dashboard
      'dashboard': 'Dashboard',
      'totalRevenue': 'Total Revenue',
      'totalExpenses': 'Total Expenses',
      'netProfit': 'Net Profit',
      'invoicesIssued': 'Invoices Issued',
      'pendingRequests': 'Pending Requests',
      'lowStockItems': 'Low Stock Items',

      // Invoices
      'invoices': 'Invoices',
      'newInvoice': 'New Invoice',
      'invoiceDetails': 'Invoice Details',
      'invoiceNumber': 'Invoice Number',
      'customer': 'Customer',
      'issueDate': 'Issue Date',
      'dueDate': 'Due Date',
      'status': 'Status',
      'items': 'Items',
      'quantity': 'Quantity',
      'unitPrice': 'Unit Price',
      'total': 'Total',
      'subtotal': 'Subtotal',
      'tax': 'Tax',
      'discount': 'Discount',
      'grandTotal': 'Grand Total',
      'addItem': 'Add Item',
      'notes': 'Notes',
      'save': 'Save',
      'print': 'Print',
      'delete': 'Delete',
      'cancel': 'Cancel',

      // Add more translations as needed
    },
  };

  String get(String key) {
    return _localizedValues[locale.languageCode]?[key] ?? key;
  }
}
