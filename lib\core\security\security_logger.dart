import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

/// نظام تسجيل الأحداث الأمنية والحساسة
class SecurityLogger {
  static final SecurityLogger _instance = SecurityLogger._internal();
  factory SecurityLogger() => _instance;
  SecurityLogger._internal();

  static const String _tag = 'SecurityLogger';
  static const int _maxLogFileSize = 5 * 1024 * 1024; // 5MB
  static const int _maxLogFiles = 10;

  File? _currentLogFile;
  final List<LogEntry> _memoryBuffer = [];
  static const int _maxMemoryBuffer = 100;

  /// تهيئة نظام التسجيل
  Future<void> initialize() async {
    try {
      await _initializeLogFile();
      if (kDebugMode) {
        print('$_tag: تم تهيئة نظام التسجيل الأمني');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في تهيئة نظام التسجيل: $e');
      }
    }
  }

  /// تسجيل محاولة تسجيل دخول
  Future<void> logLoginAttempt({
    required String username,
    required bool success,
    String? ipAddress,
    String? userAgent,
    String? failureReason,
  }) async {
    final entry = LogEntry(
      level: success ? LogLevel.info : LogLevel.warning,
      category: LogCategory.authentication,
      message: success ? 'تسجيل دخول ناجح' : 'فشل في تسجيل الدخول',
      details: {
        'username': username,
        'success': success,
        'ip_address': ipAddress,
        'user_agent': userAgent,
        if (!success && failureReason != null) 'failure_reason': failureReason,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// تسجيل العمليات المالية
  Future<void> logFinancialOperation({
    required String operation,
    required double amount,
    required String userId,
    String? reference,
    Map<String, dynamic>? additionalData,
  }) async {
    final entry = LogEntry(
      level: LogLevel.info,
      category: LogCategory.financial,
      message: 'عملية مالية: $operation',
      details: {
        'operation': operation,
        'amount': amount,
        'user_id': userId,
        'reference': reference,
        ...?additionalData,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// تسجيل تغييرات البيانات الحساسة
  Future<void> logDataChange({
    required String table,
    required String operation,
    required String recordId,
    required String userId,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
  }) async {
    final entry = LogEntry(
      level: LogLevel.info,
      category: LogCategory.dataChange,
      message: 'تغيير البيانات: $operation في $table',
      details: {
        'table': table,
        'operation': operation,
        'record_id': recordId,
        'user_id': userId,
        if (oldValues != null) 'old_values': oldValues,
        if (newValues != null) 'new_values': newValues,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// تسجيل محاولات الوصول غير المصرح بها
  Future<void> logUnauthorizedAccess({
    required String resource,
    required String userId,
    required String action,
    String? reason,
  }) async {
    final entry = LogEntry(
      level: LogLevel.error,
      category: LogCategory.security,
      message: 'محاولة وصول غير مصرح بها',
      details: {
        'resource': resource,
        'user_id': userId,
        'action': action,
        'reason': reason,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// تسجيل الأخطاء النظام
  Future<void> logSystemError({
    required String error,
    required String context,
    String? stackTrace,
    Map<String, dynamic>? additionalData,
  }) async {
    final entry = LogEntry(
      level: LogLevel.error,
      category: LogCategory.system,
      message: 'خطأ في النظام: $error',
      details: {
        'error': error,
        'context': context,
        'stack_trace': stackTrace,
        ...?additionalData,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// تسجيل عمليات النسخ الاحتياطي
  Future<void> logBackupOperation({
    required String operation,
    required bool success,
    String? filePath,
    int? fileSize,
    String? error,
  }) async {
    final entry = LogEntry(
      level: success ? LogLevel.info : LogLevel.error,
      category: LogCategory.backup,
      message: 'عملية نسخ احتياطي: $operation',
      details: {
        'operation': operation,
        'success': success,
        'file_path': filePath,
        'file_size': fileSize,
        'error': error,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// تسجيل عمليات المزامنة
  Future<void> logSyncOperation({
    required String operation,
    required String table,
    required bool success,
    int? recordCount,
    String? error,
  }) async {
    final entry = LogEntry(
      level: success ? LogLevel.info : LogLevel.warning,
      category: LogCategory.sync,
      message: 'عملية مزامنة: $operation - $table',
      details: {
        'operation': operation,
        'table': table,
        'success': success,
        'record_count': recordCount,
        'error': error,
      },
      timestamp: DateTime.now(),
    );

    await _writeLog(entry);
  }

  /// كتابة السجل
  Future<void> _writeLog(LogEntry entry) async {
    try {
      // إضافة إلى الذاكرة المؤقتة
      _memoryBuffer.add(entry);
      if (_memoryBuffer.length > _maxMemoryBuffer) {
        _memoryBuffer.removeAt(0);
      }

      // كتابة إلى الملف
      if (_currentLogFile != null) {
        final logLine = _formatLogEntry(entry);
        await _currentLogFile!.writeAsString(
          '$logLine\n',
          mode: FileMode.append,
        );

        // التحقق من حجم الملف
        final fileSize = await _currentLogFile!.length();
        if (fileSize > _maxLogFileSize) {
          await _rotateLogFile();
        }
      }

      // طباعة في وضع التطوير
      if (kDebugMode) {
        print('$_tag: ${entry.level.name.toUpperCase()} - ${entry.message}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في كتابة السجل: $e');
      }
    }
  }

  /// تنسيق السجل
  String _formatLogEntry(LogEntry entry) {
    final timestamp = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(entry.timestamp);
    final logData = {
      'timestamp': timestamp,
      'level': entry.level.name,
      'category': entry.category.name,
      'message': entry.message,
      'details': entry.details,
    };
    return json.encode(logData);
  }

  /// تهيئة ملف السجل
  Future<void> _initializeLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${directory.path}/logs');
      
      if (!await logsDir.exists()) {
        await logsDir.create(recursive: true);
      }

      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final logFileName = 'security_log_$timestamp.log';
      _currentLogFile = File('${logsDir.path}/$logFileName');

      // إنشاء الملف إذا لم يكن موجوداً
      if (!await _currentLogFile!.exists()) {
        await _currentLogFile!.create();
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في تهيئة ملف السجل: $e');
      }
    }
  }

  /// تدوير ملفات السجل
  Future<void> _rotateLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${directory.path}/logs');
      
      // الحصول على قائمة ملفات السجل
      final logFiles = await logsDir
          .list()
          .where((entity) => entity is File && entity.path.contains('security_log_'))
          .cast<File>()
          .toList();

      // ترتيب الملفات حسب تاريخ التعديل
      logFiles.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // حذف الملفات الزائدة
      if (logFiles.length >= _maxLogFiles) {
        for (int i = _maxLogFiles - 1; i < logFiles.length; i++) {
          await logFiles[i].delete();
        }
      }

      // إنشاء ملف جديد
      await _initializeLogFile();
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في تدوير ملفات السجل: $e');
      }
    }
  }

  /// الحصول على السجلات من الذاكرة
  List<LogEntry> getRecentLogs({LogLevel? level, LogCategory? category}) {
    return _memoryBuffer.where((entry) {
      if (level != null && entry.level != level) return false;
      if (category != null && entry.category != category) return false;
      return true;
    }).toList();
  }

  /// الحصول على إحصائيات السجلات
  Map<String, int> getLogStatistics() {
    final stats = <String, int>{};
    
    for (final level in LogLevel.values) {
      stats['${level.name}_count'] = _memoryBuffer
          .where((entry) => entry.level == level)
          .length;
    }

    for (final category in LogCategory.values) {
      stats['${category.name}_count'] = _memoryBuffer
          .where((entry) => entry.category == category)
          .length;
    }

    return stats;
  }

  /// تنظيف السجلات القديمة
  Future<void> cleanupOldLogs({int daysToKeep = 30}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${directory.path}/logs');
      
      if (!await logsDir.exists()) return;

      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      
      await for (final entity in logsDir.list()) {
        if (entity is File && entity.path.contains('security_log_')) {
          final lastModified = await entity.lastModified();
          if (lastModified.isBefore(cutoffDate)) {
            await entity.delete();
            if (kDebugMode) {
              print('$_tag: تم حذف ملف السجل القديم: ${entity.path}');
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: خطأ في تنظيف السجلات القديمة: $e');
      }
    }
  }
}

/// مستويات السجل
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// فئات السجل
enum LogCategory {
  authentication,
  authorization,
  financial,
  dataChange,
  security,
  system,
  backup,
  sync,
  performance,
}

/// إدخال السجل
class LogEntry {
  final LogLevel level;
  final LogCategory category;
  final String message;
  final Map<String, dynamic> details;
  final DateTime timestamp;

  LogEntry({
    required this.level,
    required this.category,
    required this.message,
    required this.details,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'category': category.name,
      'message': message,
      'details': details,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      level: LogLevel.values.firstWhere((e) => e.name == json['level']),
      category: LogCategory.values.firstWhere((e) => e.name == json['category']),
      message: json['message'],
      details: Map<String, dynamic>.from(json['details']),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
