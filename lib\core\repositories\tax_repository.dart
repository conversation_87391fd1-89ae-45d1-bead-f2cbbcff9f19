import 'package:sqflite/sqflite.dart';
import '../../shared/models/tax.dart';
import '../database/database_helper.dart';

/// مستودع الضرائب
class TaxRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع الضرائب
  Future<List<Tax>> getAllTaxes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taxes',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Tax.fromMap(maps[i]);
    });
  }

  /// الحصول على الضرائب النشطة
  Future<List<Tax>> getActiveTaxes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taxes',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Tax.fromMap(maps[i]);
    });
  }

  /// الحصول على ضريبة بواسطة المعرف
  Future<Tax?> getTaxById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taxes',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return Tax.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على الضريبة الافتراضية
  Future<Tax?> getDefaultTax() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taxes',
      where: 'is_default = ? AND is_active = ?',
      whereArgs: [1, 1],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return Tax.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة ضريبة جديدة
  Future<int> insertTax(Tax tax) async {
    final db = await _databaseHelper.database;
    
    // إذا كانت الضريبة الجديدة افتراضية، قم بإلغاء تعيين الضريبة الافتراضية الحالية
    if (tax.isDefault) {
      await db.update(
        'taxes',
        {'is_default': 0},
        where: 'is_default = ?',
        whereArgs: [1],
      );
    }
    
    return await db.insert(
      'taxes',
      tax.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// تحديث ضريبة
  Future<int> updateTax(Tax tax) async {
    final db = await _databaseHelper.database;
    
    // إذا كانت الضريبة المحدثة افتراضية، قم بإلغاء تعيين الضريبة الافتراضية الحالية
    if (tax.isDefault) {
      await db.update(
        'taxes',
        {'is_default': 0},
        where: 'is_default = ? AND id != ?',
        whereArgs: [1, tax.id],
      );
    }
    
    return await db.update(
      'taxes',
      tax.toMap(),
      where: 'id = ?',
      whereArgs: [tax.id],
    );
  }

  /// حذف ضريبة
  Future<int> deleteTax(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'taxes',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تغيير حالة الضريبة (نشطة/غير نشطة)
  Future<int> toggleTaxStatus(int id, bool isActive) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'taxes',
      {'is_active': isActive ? 1 : 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تعيين ضريبة كافتراضية
  Future<int> setDefaultTax(int id) async {
    final db = await _databaseHelper.database;
    
    // إلغاء تعيين الضريبة الافتراضية الحالية
    await db.update(
      'taxes',
      {'is_default': 0},
      where: 'is_default = ?',
      whereArgs: [1],
    );
    
    // تعيين الضريبة الجديدة كافتراضية
    return await db.update(
      'taxes',
      {'is_default': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
