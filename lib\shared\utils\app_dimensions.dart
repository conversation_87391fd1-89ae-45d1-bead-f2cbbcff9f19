/// تعريف أبعاد التطبيق الموحدة
class AppDimensions {
  // الهوامش والحشوات
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;

  // أنصاف أقطار الزوايا
  static const double radiusXS = 2.0;
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 24.0;

  // أحجام الأيقونات
  static const double iconSizeXS = 12.0;
  static const double iconSizeS = 16.0;
  static const double iconSizeM = 24.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXL = 48.0;
  static const double iconSizeXXL = 64.0;

  // ارتفاعات الأزرار
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  // أحجام الخط
  static const double fontSizeXS = 10.0;
  static const double fontSizeS = 12.0;
  static const double fontSizeM = 14.0;
  static const double fontSizeL = 16.0;
  static const double fontSizeXL = 18.0;
  static const double fontSizeXXL = 20.0;
  static const double fontSizeXXXL = 24.0;

  // ارتفاعات الشاشة
  static const double appBarHeight = 56.0;
  static const double tabBarHeight = 48.0;
  static const double bottomNavBarHeight = 56.0;
  static const double bottomSheetHeaderHeight = 64.0;
  static const double bottomSheetMinHeight = 120.0;

  // عرض الشاشة
  static const double maxContentWidth = 1200.0;
  static const double dialogMaxWidth = 400.0;
  static const double cardMaxWidth = 500.0;

  // أبعاد الصور
  static const double avatarSizeS = 32.0;
  static const double avatarSizeM = 48.0;
  static const double avatarSizeL = 64.0;
  static const double avatarSizeXL = 96.0;
  static const double logoSizeS = 48.0;
  static const double logoSizeM = 64.0;
  static const double logoSizeL = 96.0;
  static const double logoSizeXL = 128.0;

  // المسافات بين العناصر
  static const double spacingXS = 2.0;
  static const double spacingS = 4.0;
  static const double spacingM = 8.0;
  static const double spacingL = 16.0;
  static const double spacingXL = 24.0;
  static const double spacingXXL = 32.0;

  // ارتفاعات الحقول
  static const double inputHeightS = 32.0;
  static const double inputHeightM = 40.0;
  static const double inputHeightL = 48.0;
  static const double inputHeightXL = 56.0;

  // عرض الحدود
  static const double borderWidthThin = 0.5;
  static const double borderWidthRegular = 1.0;
  static const double borderWidthThick = 2.0;

  // ظلال
  static const double elevationXS = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 16.0;

  // أبعاد الجداول
  static const double tableRowHeight = 48.0;
  static const double tableHeaderHeight = 56.0;
  static const double tableCellPadding = 16.0;
  static const double tableIconSize = 20.0;

  // أبعاد البطاقات
  static const double cardPadding = 16.0;
  static const double cardMargin = 8.0;
  static const double cardElevation = 2.0;
  static const double cardBorderRadius = 8.0;

  // أبعاد القوائم
  static const double listTileHeight = 56.0;
  static const double listTileDenseHeight = 48.0;
  static const double listItemSpacing = 8.0;
  static const double listSectionSpacing = 16.0;

  // أبعاد الرسوم البيانية
  static const double chartHeight = 200.0;
  static const double chartLegendHeight = 48.0;
  static const double chartAxisLabelSize = 12.0;
  static const double chartTitleSize = 16.0;
  static const double chartBarWidth = 16.0;
  static const double chartLineWidth = 2.0;
  static const double chartPointSize = 6.0;

  // أبعاد التقارير
  static const double reportHeaderHeight = 64.0;
  static const double reportFooterHeight = 48.0;
  static const double reportSectionSpacing = 24.0;
  static const double reportCardHeight = 120.0;
}
