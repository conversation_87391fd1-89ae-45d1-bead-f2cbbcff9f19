import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../core/database/database_reset_helper.dart';
import '../shared/widgets/enhanced_ui_components.dart';

/// شاشة تصحيح قاعدة البيانات
class DatabaseDebugScreen extends StatefulWidget {
  const DatabaseDebugScreen({super.key});

  @override
  State<DatabaseDebugScreen> createState() => _DatabaseDebugScreenState();
}

class _DatabaseDebugScreenState extends State<DatabaseDebugScreen> {
  bool _isLoading = false;
  String _statusMessage = '';
  Map<String, dynamic>? _validationResult;

  @override
  void initState() {
    super.initState();
    _validateDatabase();
  }

  Future<void> _validateDatabase() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري التحقق من صحة قاعدة البيانات...';
    });

    try {
      final result = await DatabaseResetHelper.validateDatabase();
      setState(() {
        _validationResult = result;
        _statusMessage = result['isValid'] 
            ? 'قاعدة البيانات صحيحة ✅'
            : 'توجد مشاكل في قاعدة البيانات ❌';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في التحقق من قاعدة البيانات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _applyUpdates() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري تطبيق التحديثات...';
    });

    try {
      final success = await DatabaseResetHelper.applyDatabaseUpdates();
      setState(() {
        _statusMessage = success 
            ? 'تم تطبيق التحديثات بنجاح ✅'
            : 'فشل في تطبيق التحديثات ❌';
      });
      
      if (success) {
        await _validateDatabase();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في تطبيق التحديثات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetWithBackup() async {
    final confirmed = await _showConfirmationDialog(
      'إعادة تعيين مع نسخ احتياطي',
      'هل أنت متأكد من إعادة تعيين قاعدة البيانات مع الاحتفاظ بالبيانات المهمة؟',
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري إعادة تعيين قاعدة البيانات...';
    });

    try {
      final success = await DatabaseResetHelper.resetDatabaseWithBackup();
      setState(() {
        _statusMessage = success 
            ? 'تم إعادة تعيين قاعدة البيانات بنجاح ✅'
            : 'فشل في إعادة تعيين قاعدة البيانات ❌';
      });
      
      if (success) {
        await _validateDatabase();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في إعادة تعيين قاعدة البيانات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetCompletely() async {
    final confirmed = await _showConfirmationDialog(
      'إعادة تعيين كاملة',
      'تحذير: سيتم حذف جميع البيانات نهائياً. هل أنت متأكد؟',
      isDestructive: true,
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري إعادة تعيين قاعدة البيانات بالكامل...';
    });

    try {
      final success = await DatabaseResetHelper.resetDatabaseCompletely();
      setState(() {
        _statusMessage = success 
            ? 'تم إعادة تعيين قاعدة البيانات بالكامل ✅'
            : 'فشل في إعادة تعيين قاعدة البيانات ❌';
      });
      
      if (success) {
        await _validateDatabase();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في إعادة تعيين قاعدة البيانات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<bool> _showConfirmationDialog(String title, String message, {bool isDestructive = false}) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: isDestructive 
                ? ElevatedButton.styleFrom(backgroundColor: Colors.red)
                : null,
            child: const Text('تأكيد'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تصحيح قاعدة البيانات'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حالة قاعدة البيانات
            EnhancedCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حالة قاعدة البيانات',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  if (_isLoading)
                    const EnhancedLoadingIndicator(
                      message: 'جاري المعالجة...',
                      size: 30,
                    )
                  else
                    Text(
                      _statusMessage,
                      style: TextStyle(
                        fontSize: 16,
                        color: _statusMessage.contains('✅') ? Colors.green : Colors.red,
                      ),
                    ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // معلومات التحقق
            if (_validationResult != null) ...[
              EnhancedCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل التحقق',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    
                    if (_validationResult!['errors'].isNotEmpty) ...[
                      const Text('الأخطاء:', style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold)),
                      for (final error in _validationResult!['errors'])
                        Text('• $error', style: const TextStyle(color: Colors.red)),
                      const SizedBox(height: 8),
                    ],
                    
                    if (_validationResult!['warnings'].isNotEmpty) ...[
                      const Text('التحذيرات:', style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold)),
                      for (final warning in _validationResult!['warnings'])
                        Text('• $warning', style: const TextStyle(color: Colors.orange)),
                      const SizedBox(height: 8),
                    ],
                    
                    const Text('معلومات الجداول:', style: TextStyle(fontWeight: FontWeight.bold)),
                    for (final entry in _validationResult!['tableInfo'].entries)
                      Text(
                        '• ${entry.key}: ${entry.value['exists'] ? '${entry.value['recordCount']} سجل' : 'غير موجود'}',
                        style: TextStyle(
                          color: entry.value['exists'] ? Colors.green : Colors.red,
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
            ],
            
            // الأزرار
            EnhancedButton(
              text: 'إعادة التحقق',
              onPressed: _isLoading ? null : _validateDatabase,
              icon: Icons.refresh,
              type: ButtonType.outlined,
            ),
            
            const SizedBox(height: 8),
            
            EnhancedButton(
              text: 'تطبيق التحديثات',
              onPressed: _isLoading ? null : _applyUpdates,
              icon: Icons.update,
              backgroundColor: Colors.blue,
            ),
            
            const SizedBox(height: 8),
            
            EnhancedButton(
              text: 'إعادة تعيين مع نسخ احتياطي',
              onPressed: _isLoading ? null : _resetWithBackup,
              icon: Icons.restore,
              backgroundColor: Colors.orange,
            ),
            
            const SizedBox(height: 8),
            
            if (kDebugMode)
              EnhancedButton(
                text: 'إعادة تعيين كاملة (خطر)',
                onPressed: _isLoading ? null : _resetCompletely,
                icon: Icons.warning,
                backgroundColor: Colors.red,
              ),
          ],
        ),
      ),
    );
  }
}
