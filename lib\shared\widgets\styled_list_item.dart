import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// عنصر قائمة منسق بتصميم موحد
class StyledListItem extends StatelessWidget {
  /// العنوان الرئيسي
  final String title;
  
  /// النص الفرعي (اختياري)
  final String? subtitle;
  
  /// الأيقونة الرئيسية (اختياري)
  final IconData? leadingIcon;
  
  /// صورة مصغرة (اختياري)
  final Widget? leadingImage;
  
  /// أيقونة التذييل (اختياري)
  final IconData? trailingIcon;
  
  /// محتوى التذييل (اختياري)
  final Widget? trailing;
  
  /// شارات العنصر (اختياري)
  final List<Widget>? badges;
  
  /// دالة تنفذ عند النقر على العنصر
  final VoidCallback? onTap;
  
  /// دالة تنفذ عند النقر المطول على العنصر
  final VoidCallback? onLongPress;
  
  /// لون خلفية العنصر
  final Color? backgroundColor;
  
  /// تباعد داخلي
  final EdgeInsetsGeometry padding;
  
  /// ارتفاع العنصر (اختياري)
  final double? height;
  
  /// إظهار حدود العنصر
  final bool showBorder;
  
  /// لون حدود العنصر
  final Color borderColor;
  
  /// سماكة حدود العنصر
  final double borderWidth;
  
  /// نصف قطر زوايا العنصر
  final double borderRadius;
  
  /// إظهار تأثير تمرير المؤشر
  final bool showHoverEffect;
  
  /// إنشاء عنصر قائمة منسق
  const StyledListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.leadingIcon,
    this.leadingImage,
    this.trailingIcon,
    this.trailing,
    this.badges,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.padding = const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 12.0,
    ),
    this.height,
    this.showBorder = true,
    this.borderColor = AppColors.border,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.showHoverEffect = true,
  }) : assert(leadingIcon == null || leadingImage == null,
            'لا يمكن تحديد كل من leadingIcon و leadingImage معًا');

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        borderRadius: BorderRadius.circular(borderRadius),
        border: showBorder
            ? Border.all(
                color: borderColor,
                width: borderWidth,
              )
            : null,
      ),
      margin: const EdgeInsets.symmetric(
        vertical: 4.0,
        horizontal: 0.0,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(borderRadius),
          hoverColor: showHoverEffect ? AppColors.tableRowHover : Colors.transparent,
          splashColor: AppColors.primaryLight.withOpacity(0.1),
          highlightColor: AppColors.primaryLight.withOpacity(0.05),
          child: Padding(
            padding: padding,
            child: Row(
              children: [
                // أيقونة أو صورة مصغرة
                if (leadingIcon != null || leadingImage != null) ...[
                  Container(
                    width: 40,
                    height: 40,
                    margin: const EdgeInsets.only(left: 12),
                    child: leadingIcon != null
                        ? Icon(
                            leadingIcon,
                            color: AppColors.primary,
                            size: 24,
                          )
                        : leadingImage,
                  ),
                ],
                
                // محتوى العنصر
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // العنوان والشارات
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: AppTextStyles.labelLarge,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (badges != null && badges!.isNotEmpty) ...[
                            const SizedBox(width: 8),
                            ...badges!,
                          ],
                        ],
                      ),
                      
                      // النص الفرعي
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle!,
                          style: AppTextStyles.bodySmall,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ],
                    ],
                  ),
                ),
                
                // أيقونة أو محتوى التذييل
                if (trailingIcon != null || trailing != null) ...[
                  const SizedBox(width: 8),
                  trailing ??
                      Icon(
                        trailingIcon ?? Icons.chevron_right,
                        color: AppColors.textSecondary,
                        size: 20,
                      ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// شارة منسقة للاستخدام مع عناصر القائمة
class StyledBadge extends StatelessWidget {
  /// نص الشارة
  final String label;
  
  /// لون خلفية الشارة
  final Color backgroundColor;
  
  /// لون نص الشارة
  final Color textColor;
  
  /// نصف قطر زوايا الشارة
  final double borderRadius;
  
  /// إنشاء شارة منسقة
  const StyledBadge({
    super.key,
    required this.label,
    this.backgroundColor = AppColors.primary,
    this.textColor = AppColors.textOnPrimary,
    this.borderRadius = 12.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 2.0,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Text(
        label,
        style: AppTextStyles.labelSmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
