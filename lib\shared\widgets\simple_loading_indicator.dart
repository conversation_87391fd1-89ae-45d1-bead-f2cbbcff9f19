import 'package:flutter/material.dart';

/// Widget para mostrar un indicador de carga simplificado
class SimpleLoadingIndicator extends StatelessWidget {
  /// Mensaje a mostrar (opcional)
  final String? message;

  /// Constructor
  const SimpleLoadingIndicator({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
