import 'package:flutter/material.dart';
import 'package:icecorner/config/constants.dart';
import '../../../shared/models/user.dart';

class UserListItem extends StatelessWidget {
  final User user;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onPermissions;

  const UserListItem({
    super.key,
    required this.user,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    required this.onPermissions,
  });

  String _getRoleName(List<String> roles) {
    if (roles.isEmpty) return 'غير محدد'; // Default
    final roleName = roles.first;
    switch (roleName) {
      case 'admin':
        return 'مدير النظام';
      case 'manager':
        return 'مدير';
      case 'accountant':
        return 'محاسب';
      case 'technician':
        return 'فني';
      case 'receptionist':
        return 'موظف استقبال';
      case 'employee':
        return 'موظف';
      default:
        return 'غير محدد';
    }
  }

  Color _getRoleColor(List<String> roles) {
    if (roles.isEmpty) return Colors.grey; // Default
    final roleName = roles.first;
    switch (roleName) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.blue;
      case 'accountant':
        return Colors.green;
      case 'technician':
        return Colors.orange;
      case 'receptionist':
        return Colors.purple;
      case 'employee':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 25,
                backgroundColor: _getRoleColor(user.roles).withAlpha(51),
                child: Text(
                  user.name.substring(0, 1),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _getRoleColor(user.roles),
                  ),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),

              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            user.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (!user.isActive)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red.withAlpha(26),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'غير نشط',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user.email ?? 'لا يوجد بريد إلكتروني',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getRoleColor(user.roles).withAlpha(26),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getRoleName(user.roles),
                            style: TextStyle(
                              fontSize: 12,
                              color: _getRoleColor(user.roles),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (user.phone != null)
                          Expanded(
                            child: Text(
                              user.phone!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.textSecondary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              // Actions
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      onEdit();
                      break;
                    case 'delete':
                      onDelete();
                      break;
                    case 'permissions':
                      onPermissions();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'permissions',
                    child: Row(
                      children: [
                        Icon(Icons.security, size: 20),
                        SizedBox(width: 8),
                        Text('الصلاحيات'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
