import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:icecorner/shared/utils/app_colors.dart';
import 'package:icecorner/shared/utils/app_text_styles.dart';
import 'package:icecorner/shared/models/customer.dart';
import 'package:icecorner/shared/models/invoice.dart';
import 'package:icecorner/shared/models/transaction.dart';
import 'package:icecorner/shared/models/service_request.dart';
import 'package:icecorner/core/repositories/customer_repository.dart';
import 'package:icecorner/core/repositories/invoice_repository.dart';
import 'package:icecorner/core/repositories/transaction_repository.dart';
import 'package:icecorner/core/repositories/service_request_repository.dart';

import 'package:icecorner/features/reports/utils/report_generator.dart' show ReportType;
import 'package:icecorner/features/reports/utils/paginated_pdf_export.dart';
import 'package:icecorner/features/reports/screens/unified_report_screen.dart';
import 'package:icecorner/features/reports/screens/customer_data_preview_screen.dart';
import 'package:icecorner/features/reports/screens/pdf_preview_screen.dart';

/// شاشة تقارير العملاء
class CustomerReportScreen extends StatefulWidget {
  final int? customerId; // إذا كان محددًا، سيتم عرض تقرير لعميل محدد

  const CustomerReportScreen({
    super.key,
    this.customerId,
  });

  @override
  State<CustomerReportScreen> createState() => _CustomerReportScreenState();
}

class _CustomerReportScreenState extends State<CustomerReportScreen> with SingleTickerProviderStateMixin {
  // Dummy TabController to fix the '_ticker' error
  late TabController _tabController;
  // المستودعات
  final _customerRepository = CustomerRepository();
  final _invoiceRepository = InvoiceRepository();
  final _transactionRepository = TransactionRepository();
  final _serviceRequestRepository = ServiceRequestRepository();

  // البيانات
  List<Customer> _customers = [];
  List<Invoice> _invoices = [];
  List<Transaction> _transactions = [];
  List<ServiceRequest> _serviceRequests = [];
  Customer? _selectedCustomer;
  bool _isLoading = true;

  // الفلاتر
  String _searchQuery = '';
  final String _statusFilter = 'all';

  @override
  void initState() {
    super.initState();
    // Initialize the dummy TabController to fix the '_ticker' error
    _tabController = TabController(length: 1, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    // Dispose the TabController to prevent memory leaks
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل العملاء
      final customers = await _customerRepository.getAllCustomers();

      // إذا كان هناك معرف عميل محدد، ابحث عنه
      Customer? selectedCustomer;
      if (widget.customerId != null) {
        selectedCustomer = await _customerRepository.getCustomerById(widget.customerId!);
        if (selectedCustomer == null && customers.isNotEmpty) {
          selectedCustomer = customers.first;
        }
      }

      // تحديث قائمة العملاء
      if (mounted) {
        setState(() {
          _customers = customers;
          _selectedCustomer = selectedCustomer;
        });
      }

      // تحميل البيانات المتعلقة بالعميل المحدد
      if (selectedCustomer != null) {
        await _loadCustomerData(selectedCustomer);
      } else {
        // إذا لم يكن هناك عميل محدد، قم بتحميل جميع البيانات
        await _loadAllData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل البيانات المتعلقة بالعميل المحدد
  Future<void> _loadCustomerData(Customer customer) async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (customer.localId != null) {
        // تحميل الفواتير الخاصة بالعميل
        final invoices = await _invoiceRepository.getInvoicesByCustomerId(customer.localId!);
        debugPrint('تم تحميل ${invoices.length} فاتورة للعميل ${customer.name}');

        // تحميل الإيرادا المالية الخاصة بالعميل
        final transactions = await _transactionRepository.getTransactionsByCustomer(customer.localId!);
        debugPrint('تم تحميل ${transactions.length} إيراد مالية للعميل ${customer.name}');

        // تحميل طلبات الخدمة الخاصة بالعميل
        final serviceRequests = await _serviceRequestRepository.getServiceRequestsByCustomerId(customer.localId!);
        debugPrint('تم تحميل ${serviceRequests.length} طلب خدمة للعميل ${customer.name}');

        // طباعة تفاصيل الإيرادا المالية للتشخيص
        for (var transaction in transactions) {
          debugPrint('إيراد: ${transaction.reference}, النوع: ${transaction.type}, المبلغ: ${transaction.amount}');
        }

        // طباعة تفاصيل طلبات الخدمة للتشخيص
        for (var request in serviceRequests) {
          debugPrint('طلب خدمة: ${request.reference}, الحالة: ${request.status}, التاريخ: ${request.scheduledDate}');
        }

        // تحديث الحالة
        if (mounted) {
          setState(() {
            _selectedCustomer = customer;
            _invoices = invoices;
            _transactions = transactions;
            _serviceRequests = serviceRequests;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات العميل: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل بيانات العميل: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل جميع البيانات
  Future<void> _loadAllData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final DateTime startDate = DateTime.now().subtract(const Duration(days: 365));
      final DateTime endDate = DateTime.now();

      debugPrint('تحميل البيانات للفترة من ${DateFormat('yyyy/MM/dd').format(startDate)} إلى ${DateFormat('yyyy/MM/dd').format(endDate)}');

      final invoices = await _invoiceRepository.getInvoicesByDateRange(startDate, endDate);
      debugPrint('تم تحميل ${invoices.length} فاتورة');

      final transactions = await _transactionRepository.getTransactionsByDateRange(startDate, endDate);
      debugPrint('تم تحميل ${transactions.length} إيراد مالية');

      final serviceRequests = await _serviceRequestRepository.getServiceRequestsByDateRange(startDate, endDate);
      debugPrint('تم تحميل ${serviceRequests.length} طلب خدمة');

      // تحديث الحالة
      if (mounted) {
        setState(() {
          _invoices = invoices;
          _transactions = transactions;
          _serviceRequests = serviceRequests;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// الفواتير المفلترة
  List<Invoice> get _filteredInvoices {
    if (_selectedCustomer == null) {
      return _invoices;
    }

    var filtered = _invoices.where((invoice) => invoice.customerId == _selectedCustomer!.id).toList();

    // تطبيق فلتر الحالة
    if (_statusFilter != 'all') {
      final isPaid = _statusFilter == 'paid';
      filtered = filtered.where((invoice) => invoice.isPaid == isPaid).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((invoice) {
        return invoice.invoiceNumber.toLowerCase().contains(query) ||
            (invoice.notes?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    return filtered;
  }

  /// الإيرادا المفلترة
  List<Transaction> get _filteredTransactions {
    if (_selectedCustomer == null) {
      return [];
    }

    // تأكد من أن الإيرادا مرتبطة بالعميل المحدد
    var filtered = _transactions.where((transaction) =>
      transaction.customerId == _selectedCustomer!.id
    ).toList();

    // تطبيق فلتر البحث إذا كان موجودًا
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((transaction) {
        return transaction.reference.toLowerCase().contains(query) ||
            (transaction.description?.toLowerCase().contains(query) ?? false) ||
            (transaction.category?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // ترتيب الإيرادا حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => b.date.compareTo(a.date));

    return filtered;
  }

  /// طلبات الخدمة المفلترة
  List<ServiceRequest> get _filteredServiceRequests {
    if (_selectedCustomer == null) {
      return [];
    }

    // تأكد من أن طلبات الخدمة مرتبطة بالعميل المحدد
    var filtered = _serviceRequests.where((request) =>
      request.customerId == _selectedCustomer!.id
    ).toList();

    // تطبيق فلتر البحث إذا كان موجودًا
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((request) {
        return request.reference.toLowerCase().contains(query) ||
            (request.description.toLowerCase().contains(query)) ||
            (request.serviceType.toLowerCase().contains(query));
      }).toList();
    }

    // ترتيب طلبات الخدمة حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => b.scheduledDate.compareTo(a.scheduledDate));

    return filtered;
  }

  /// إجمالي المبالغ المدفوعة
  double get _totalPaid {
    return _filteredInvoices
        .where((invoice) => invoice.isPaid)
        .fold(0.0, (sum, invoice) => sum + invoice.total);
  }

  /// إجمالي المبالغ المستحقة
  double get _totalDue {
    return _filteredInvoices
        .where((invoice) => !invoice.isPaid)
        .fold(0.0, (sum, invoice) => sum + invoice.total);
  }

  /// إجمالي المبالغ
  double get _totalAmount {
    return _filteredInvoices.fold(0.0, (sum, invoice) => sum + invoice.total);
  }



  /// نص طريقة الدفع للعميل
  String get _paymentMethodText {
    if (_selectedCustomer == null || _selectedCustomer!.paymentMethod == null) {
      return 'دفع عند طلب الخدمة';
    }

    return Customer.getPaymentMethodName(_selectedCustomer!.paymentMethod!);
  }




  /// الحصول على نص الحالة
  String _getStatusText(ServiceRequestStatus status) {
    switch (status) {
      case ServiceRequestStatus.pending:
        return 'قيد الانتظار';
      case ServiceRequestStatus.inProgress:
        return 'قيد التنفيذ';
      case ServiceRequestStatus.completed:
        return 'مكتمل';
      case ServiceRequestStatus.cancelled:
        return 'ملغي';
    }
  }

  /// بناء قسم الفلاتر
  Widget _buildFiltersSection() {
    return Column(
      children: [
        // اختيار العميل
        if (_customers.isEmpty)
          const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'لا يوجد عملاء مسجلين في النظام',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ),
          )
        else
          DropdownButtonFormField<int>(
            decoration: const InputDecoration(
              labelText: 'اختر العميل',
              prefixIcon: Icon(Icons.person),
              border: OutlineInputBorder(),
            ),
            value: _selectedCustomer?.localId,
            hint: const Text('اختر عميل'),
            items: _customers.map((customer) {
              return DropdownMenuItem<int>(
                value: customer.localId,
                child: Text(customer.name),
              );
            }).toList(),
            onChanged: (int? value) {
              if (value != null) {
                final selectedCustomer = _customers.firstWhere(
                  (customer) => customer.id == value,
                  orElse: () => _customers.first,
                );
                setState(() {
                  _selectedCustomer = selectedCustomer;
                });
                // إعادة تحميل البيانات للعميل المحدد
                _loadCustomerData(selectedCustomer);
              }
            },
          ),
        const SizedBox(height: 16),

        // فلاتر إضافية
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            // فلتر الحالة
            SizedBox(
              width: MediaQuery.of(context).size.width > 600
                  ? (MediaQuery.of(context).size.width - 32) / 2
                  : MediaQuery.of(context).size.width - 16,
              ),

            // حقل البحث
            SizedBox(
              width: MediaQuery.of(context).size.width > 600
                  ? (MediaQuery.of(context).size.width - 32) / 2
                  : MediaQuery.of(context).size.width - 16,
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'بحث',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم البيانات الموحد
  Widget _buildDataSection() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_customers.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد عملاء مسجلين في النظام',
          style: AppTextStyles.heading3,
        ),
      );
    }

    if (_selectedCustomer == null) {
      return const Center(
        child: Text(
          'الرجاء اختيار عميل لعرض البيانات',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return Column(
      children: [
        // عنوان الجدول
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: const Row(
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  'النوع',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'الرقم المرجعي',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'التاريخ',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'الوصف',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'المبلغ',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'الحالة',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),

        // محتوى الجدول
        Expanded(
          child: _buildUnifiedDataTable(),
        ),
      ],
    );
  }

  /// بناء جدول البيانات الموحد
  Widget _buildUnifiedDataTable() {
    // تجميع كل البيانات في قائمة واحدة
    final List<Map<String, dynamic>> allData = [];

    // إضافة الفواتير
    for (final invoice in _filteredInvoices) {
      allData.add({
        'type': 'invoice',
        'icon': Icons.receipt,
        'iconColor': Colors.blue,
        'reference': 'فاتورة #${invoice.invoiceNumber}',
        'date': invoice.date,
        'description': 'فاتورة للعميل ${_selectedCustomer?.name}',
        'amount': invoice.total,
        'status': invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
        'statusColor': invoice.isPaid ? Colors.green : Colors.orange,
        'data': invoice,
      });
    }

    // إضافة الإيرادا المالية
    for (final transaction in _filteredTransactions) {
      allData.add({
        'type': 'transaction',
        'icon': transaction.type == TransactionType.income ? Icons.arrow_downward : Icons.arrow_upward,
        'iconColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
        'reference': transaction.reference,
        'date': transaction.date,
        'description': transaction.description ?? '',
        'amount': transaction.amount,
        'status': transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
        'statusColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
        'data': transaction,
      });
    }

    // إضافة طلبات الخدمة
    for (final request in _filteredServiceRequests) {
      // تحديد لون الحالة
      Color statusColor;
      String statusText = _getStatusText(request.status);

      switch (request.status) {
        case ServiceRequestStatus.pending:
          statusColor = Colors.orange;
          break;
        case ServiceRequestStatus.inProgress:
          statusColor = Colors.blue;
          break;
        case ServiceRequestStatus.completed:
          statusColor = Colors.green;
          break;
        case ServiceRequestStatus.cancelled:
          statusColor = Colors.red;
          break;
      }

      allData.add({
        'type': 'service',
        'icon': Icons.home_repair_service,
        'iconColor': statusColor,
        'reference': 'طلب #${request.reference}',
        'date': request.scheduledDate,
        'description': request.description,
        'amount': _selectedCustomer?.paymentMethod == CustomerPaymentMethod.perService
            ? request.serviceAmount
            : 0.0,
        'status': statusText,
        'statusColor': statusColor,
        'data': request,
      });
    }

    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    allData.sort((a, b) => b['date'].compareTo(a['date']));

    if (allData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات لهذا العميل',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return ListView.builder(
      itemCount: allData.length,
      itemBuilder: (context, index) {
        final item = allData[index];
        final bool isEvenRow = index % 2 == 0;

        return Container(
          color: isEvenRow ? Colors.grey.shade50 : Colors.white,
          child: InkWell(
            onTap: () {
              // عرض تفاصيل العنصر
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('عرض تفاصيل ${item['reference']}'),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Row(
                children: [
                  // نوع العنصر
                  Expanded(
                    flex: 1,
                    child: CircleAvatar(
                      backgroundColor: item['iconColor'].withAlpha(50),
                      child: Icon(
                        item['icon'],
                        color: item['iconColor'],
                        size: 20,
                      ),
                    ),
                  ),

                  // الرقم المرجعي
                  Expanded(
                    flex: 2,
                    child: Text(
                      item['reference'],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // التاريخ
                  Expanded(
                    flex: 2,
                    child: Text(
                      DateFormat('dd/MM/yyyy').format(item['date']),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // الوصف
                  Expanded(
                    flex: 2,
                    child: Text(
                      item['description'],
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // المبلغ
                  Expanded(
                    flex: 2,
                    child: Text(
                      item['type'] == 'service' && _selectedCustomer?.paymentMethod == CustomerPaymentMethod.monthlySubscription
                          ? 'ضمن الاشتراك'
                          : '${item['amount'].toStringAsFixed(2)} ر.س',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: item['type'] == 'transaction' ? item['iconColor'] : Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // الحالة
                  Expanded(
                    flex: 2,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: item['statusColor'].withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        item['status'],
                        style: TextStyle(
                          color: item['statusColor'],
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// الحصول على بيانات التقرير لتصديرها
  List<dynamic> _getReportData() {
    try {
      // تجميع كل البيانات في قائمة واحدة
      final List<Map<String, dynamic>> allData = [];

      // التأكد من وجود بيانات للعميل المحدد
      if (_selectedCustomer == null) {
        return [];
      }

      // إضافة الفواتير
      for (final invoice in _filteredInvoices) {
        allData.add({
          'type': 'invoice',
          'icon': Icons.receipt,
          'iconColor': Colors.blue,
          'reference': 'فاتورة #${invoice.invoiceNumber}',
          'date': invoice.date,
          'description': 'فاتورة للعميل ${_selectedCustomer?.name}',
          'amount': invoice.total,
          'status': invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
          'statusColor': invoice.isPaid ? Colors.green : Colors.orange,
          'data': invoice,
          'dataType': 'invoice',
        });
      }

      // إضافة الإيرادا المالية
      for (final transaction in _filteredTransactions) {
        allData.add({
          'type': 'transaction',
          'icon': transaction.type == TransactionType.income ? Icons.arrow_downward : Icons.arrow_upward,
          'iconColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
          'reference': transaction.reference,
          'date': transaction.date,
          'description': transaction.description ?? '',
          'amount': transaction.amount,
          'status': transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
          'statusColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
          'data': transaction,
          'dataType': 'transaction',
        });
      }

      // إضافة طلبات الخدمة
      for (final request in _filteredServiceRequests) {
        // تحديد لون الحالة
        Color statusColor;
        String statusText = _getStatusText(request.status);

        switch (request.status) {
          case ServiceRequestStatus.pending:
            statusColor = Colors.orange;
            break;
          case ServiceRequestStatus.inProgress:
            statusColor = Colors.blue;
            break;
          case ServiceRequestStatus.completed:
            statusColor = Colors.green;
            break;
          case ServiceRequestStatus.cancelled:
            statusColor = Colors.red;
            break;
        }

        allData.add({
          'type': 'service',
          'icon': Icons.home_repair_service,
          'iconColor': statusColor,
          'reference': 'طلب #${request.reference}',
          'date': request.scheduledDate,
          'description': request.description,
          'amount': _selectedCustomer?.paymentMethod == CustomerPaymentMethod.perService
              ? request.serviceAmount
              : 0.0,
          'status': statusText,
          'statusColor': statusColor,
          'data': request,
          'dataType': 'service_request',
        });
      }

      // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
      allData.sort((a, b) => b['date'].compareTo(a['date']));

      // حساب إجماليات الإيرادا المالية
      final totalIncome = _filteredTransactions
          .where((t) => t.type == TransactionType.income)
          .fold(0.0, (sum, t) => sum + t.amount);

      final totalExpenses = _filteredTransactions
          .where((t) => t.type == TransactionType.expense)
          .fold(0.0, (sum, t) => sum + t.amount);

      final transactionBalance = totalIncome - totalExpenses;

      // حساب إجماليات طلبات الخدمة
      final totalServiceAmount = _filteredServiceRequests
          .fold(0.0, (sum, r) => sum + r.serviceAmount);

      // إنشاء بيانات التقرير
      final Map<String, dynamic> reportData = {
        'title': _selectedCustomer != null ? 'تقرير العميل: ${_selectedCustomer!.name}' : 'تقارير العملاء',
        'customer': _selectedCustomer,
        'summary': {
          'totalAmount': _totalAmount,
          'totalPaid': _totalPaid,
          'totalDue': _totalDue,
          'collectionRate': _totalAmount > 0 ? (_totalPaid / _totalAmount) * 100 : 0,
          'transactionsCount': _filteredTransactions.length,
          'totalIncome': totalIncome,
          'totalExpenses': totalExpenses,
          'transactionBalance': transactionBalance,
          'serviceRequestsCount': _filteredServiceRequests.length,
          'inProgressRequestsCount': _filteredServiceRequests.where((r) => r.status == ServiceRequestStatus.inProgress).length,
          'completedRequestsCount': _filteredServiceRequests.where((r) => r.status == ServiceRequestStatus.completed).length,
          'pendingRequestsCount': _filteredServiceRequests.where((r) => r.status == ServiceRequestStatus.pending).length,
          'cancelledRequestsCount': _filteredServiceRequests.where((r) => r.status == ServiceRequestStatus.cancelled).length,
          'paymentMethod': _paymentMethodText,
          'serviceAmount': totalServiceAmount,
        },
        'items': allData,
        'invoices': _filteredInvoices,
        'transactions': _filteredTransactions,
        'serviceRequests': _filteredServiceRequests,
      };

      debugPrint('Report data prepared with:');
      debugPrint('- ${_filteredInvoices.length} invoices');
      debugPrint('- ${_filteredTransactions.length} transactions');
      debugPrint('- ${_filteredServiceRequests.length} service requests');

      return [reportData];
    } catch (e) {
      debugPrint('Error getting report data: $e');
      return [];
    }
  }



  /// تصدير تقرير PDF للعميل
  void _exportCustomerPdf() async {
    if (_selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار عميل أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // تجميع البيانات للتصدير
    final List<Map<String, dynamic>> tableData = [];

    // إضافة الفواتير
    for (final invoice in _filteredInvoices) {
      tableData.add({
        'type': 'invoice',
        'icon': Icons.receipt,
        'iconColor': Colors.blue,
        'reference': 'فاتورة #${invoice.invoiceNumber}',
        'date': invoice.date,
        'description': 'فاتورة للعميل ${_selectedCustomer?.name}',
        'amount': invoice.total,
        'status': invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
        'statusColor': invoice.isPaid ? Colors.green : Colors.orange,
        'data': invoice,
        'id': invoice.id?.toString() ?? '',
        'invoiceId': invoice.id,
      });
    }

    // إضافة الإيرادات المالية
    for (final transaction in _filteredTransactions) {
      tableData.add({
        'type': 'transaction',
        'icon': transaction.type == TransactionType.income ? Icons.arrow_downward : Icons.arrow_upward,
        'iconColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
        'reference': transaction.reference,
        'date': transaction.date,
        'description': transaction.description ?? '',
        'amount': transaction.amount,
        'status': transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
        'statusColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
        'data': transaction,
        'invoiceId': transaction.invoiceId,
      });
    }

    // إضافة طلبات الخدمة
    for (final request in _filteredServiceRequests) {
      // تحديد لون الحالة
      Color statusColor = Colors.grey;
      String statusText = _getStatusText(request.status);

      switch (request.status) {
        case ServiceRequestStatus.pending:
          statusColor = Colors.orange;
          break;
        case ServiceRequestStatus.inProgress:
          statusColor = Colors.blue;
          break;
        case ServiceRequestStatus.completed:
          statusColor = Colors.green;
          break;
        case ServiceRequestStatus.cancelled:
          statusColor = Colors.red;
          break;
      }

      tableData.add({
        'type': 'service',
        'icon': Icons.home_repair_service,
        'iconColor': statusColor,
        'reference': 'طلب #${request.reference}',
        'date': request.scheduledDate,
        'description': request.description,
        'amount': _selectedCustomer?.paymentMethod == CustomerPaymentMethod.perService
            ? request.serviceAmount
            : 0.0,
        'status': statusText,
        'statusColor': statusColor,
        'data': request,
        'customerPaymentMethod': _selectedCustomer?.paymentMethod,
      });
    }

    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    tableData.sort((a, b) => b['date'].compareTo(a['date']));

    if (tableData.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد بيانات كافية لإنشاء التقرير'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // حساب إجمالي مبالغ الخدمة
    double totalServiceAmount = 0.0;
    if (_selectedCustomer != null) {
      // إذا كان العميل مشترك شهري، نستخدم مبلغ الاشتراك الشهري
      if (_selectedCustomer!.paymentMethod == CustomerPaymentMethod.monthlySubscription) {
        totalServiceAmount = _selectedCustomer!.monthlySubscriptionAmount ?? 0.0;
      } else {
        // إذا كان العميل يدفع عند طلب الخدمة، نجمع مبالغ الخدمات من طلبات الخدمة
        totalServiceAmount = _filteredServiceRequests.fold(0.0, (sum, request) => sum + request.serviceAmount);
      }
    }

    // إنشاء بيانات الملخص
    final Map<String, double> summaryData = {
      'totalAmount': _totalAmount,
      'totalPaid': _totalPaid,
      'totalDue': _totalDue,
      'totalServices': totalServiceAmount,
      'collectionRate': _totalAmount > 0 ? (_totalPaid / _totalAmount) * 100 : 0,
    };

    // إنشاء عنوان التقرير
    final title = 'تقرير العميل: ${_selectedCustomer!.name}';

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // استدعاء دالة تصدير PDF مباشرة مع تعطيل المشاركة التلقائية
      final filePath = await PaginatedPdfExport.exportCustomerTableToPdf(
        context: context,
        title: title,
        customer: _selectedCustomer,
        tableData: tableData,
        summaryData: summaryData,
        shareFile: false, // تعطيل المشاركة التلقائية
      );

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.pop(context);
      }

      // فتح ملف PDF مباشرة داخل التطبيق
      if (filePath != null && mounted) {
        // فتح شاشة عرض PDF
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PdfPreviewScreen(
              filePath: filePath,
              title: title,
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      // التحقق من أن الـ widget ما زال مثبتًا قبل استخدام context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إنشاء التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// فتح شاشة معاينة البيانات
  void _openDataPreview() {
    if (_selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار عميل أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // تجميع البيانات للعرض
    final List<Map<String, dynamic>> allData = [];

    // إضافة الفواتير
    for (final invoice in _filteredInvoices) {
      allData.add({
        'type': 'invoice',
        'icon': Icons.receipt,
        'iconColor': Colors.blue,
        'reference': 'فاتورة #${invoice.invoiceNumber}',
        'date': invoice.date,
        'description': 'فاتورة للعميل ${_selectedCustomer?.name}',
        'amount': invoice.total,
        'status': invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
        'statusColor': invoice.isPaid ? Colors.green : Colors.orange,
        'data': invoice,
      });
    }

    // إضافة الإيرادا المالية
    for (final transaction in _filteredTransactions) {
      allData.add({
        'type': 'transaction',
        'icon': transaction.type == TransactionType.income ? Icons.arrow_downward : Icons.arrow_upward,
        'iconColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
        'reference': transaction.reference,
        'date': transaction.date,
        'description': transaction.description ?? '',
        'amount': transaction.amount,
        'status': transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
        'statusColor': transaction.type == TransactionType.income ? Colors.green : Colors.red,
        'data': transaction,
      });
    }

    // إضافة طلبات الخدمة
    for (final request in _filteredServiceRequests) {
      // تحديد لون الحالة
      Color statusColor;
      String statusText = _getStatusText(request.status);

      switch (request.status) {
        case ServiceRequestStatus.pending:
          statusColor = Colors.orange;
          break;
        case ServiceRequestStatus.inProgress:
          statusColor = Colors.blue;
          break;
        case ServiceRequestStatus.completed:
          statusColor = Colors.green;
          break;
        case ServiceRequestStatus.cancelled:
          statusColor = Colors.red;
          break;
      }

      allData.add({
        'type': 'service',
        'icon': Icons.home_repair_service,
        'iconColor': statusColor,
        'reference': 'طلب #${request.reference}',
        'date': request.scheduledDate,
        'description': request.description,
        'amount': _selectedCustomer?.paymentMethod == CustomerPaymentMethod.perService
            ? request.serviceAmount
            : 0.0,
        'status': statusText,
        'statusColor': statusColor,
        'data': request,
      });
    }

    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    allData.sort((a, b) => b['date'].compareTo(a['date']));

    // فتح شاشة المعاينة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDataPreviewScreen(
          customer: _selectedCustomer,
          allData: allData,
          title: 'معاينة بيانات ${_selectedCustomer?.name}',
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // استخدام شاشة التقارير الموحدة
    return UnifiedReportScreen(
      title: widget.customerId != null ? 'تقرير العميل' : 'تقارير العملاء',
      reportType: ReportType.customer,
      entityId: widget.customerId,
      filterWidget: _buildFiltersSection(),
      dataWidget: _buildDataSection(),
      getReportData: _getReportData, // تمرير دالة الحصول على بيانات التقرير
      actions: [
        // زر المعاينة
        IconButton(
          icon: const Icon(Icons.preview),
          onPressed: _openDataPreview,
          tooltip: 'معاينة',
        ),
        // زر تصدير PDF
        IconButton(
          icon: const Icon(Icons.picture_as_pdf),
          onPressed: _exportCustomerPdf,
          tooltip: 'تصدير PDF',
        ),
      ],
    );
  }
}
