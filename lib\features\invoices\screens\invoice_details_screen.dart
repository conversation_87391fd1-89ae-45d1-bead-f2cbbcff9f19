import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/invoice.dart';
import '../../../shared/models/transaction.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/transaction_repository.dart';
import '../../../features/reports/utils/simple_pdf_export.dart';
import '../../../features/reports/screens/pdf_preview_screen.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final int invoiceId;

  const InvoiceDetailsScreen({
    super.key,
    required this.invoiceId,
  });

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  bool _isLoading = true;
  Invoice? _invoice;
  List<Transaction> _payments = [];

  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final TransactionRepository _transactionRepository = TransactionRepository();

  @override
  void initState() {
    super.initState();
    _loadInvoiceDetails();
  }

  Future<void> _loadInvoiceDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب تفاصيل الفاتورة
      final invoice = await _invoiceRepository.getInvoiceById(widget.invoiceId);

      if (invoice != null) {
        // جلب المدفوعات المرتبطة بالفاتورة
        final payments = await _transactionRepository.getTransactionsByInvoiceId(invoice.id!);

        if (mounted) {
          setState(() {
            _invoice = invoice;
            _payments = payments;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // عرض رسالة خطأ إذا لم يتم العثور على الفاتورة
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على الفاتورة'),
              backgroundColor: Colors.red,
            ),
          );

          // العودة إلى الشاشة السابقة
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading invoice details: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل تفاصيل الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportToPdf() async {
    if (_invoice == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      final filePath = await SimplePdfExport.createInvoicePdf(
        invoice: _invoice!,
        payments: _payments,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (filePath != null) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PdfPreviewScreen(
                filePath: filePath,
                title: 'فاتورة ${_invoice!.invoiceNumber}',
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إنشاء ملف PDF'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error exporting invoice to PDF: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تصدير الفاتورة إلى PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPaymentDialog() {
    if (_invoice == null) return;

    // استخدام الدالة الموجودة في شاشة قائمة الفواتير
    Navigator.pushNamed(
      context,
      AppRoutes.invoices,
      arguments: {'showPaymentDialog': true, 'invoice': _invoice},
    ).then((_) {
      // إعادة تحميل البيانات بعد العودة
      _loadInvoiceDetails();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_invoice != null
          ? 'تفاصيل الفاتورة ${_invoice!.invoiceNumber}'
          : 'تفاصيل الفاتورة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'تصدير PDF',
            onPressed: _exportToPdf,
          ),
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة',
            onPressed: _exportToPdf, // يمكن استخدام نفس الدالة للطباعة
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _invoice == null
              ? const Center(child: Text('لا توجد بيانات للعرض'))
              : _buildInvoiceDetails(),
      bottomNavigationBar: _invoice != null && _invoice!.status != InvoiceStatus.paid
          ? Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomButton(
                text: 'تسجيل دفعة جديدة',
                icon: Icons.payment,
                onPressed: _showPaymentDialog,
              ),
            )
          : null,
    );
  }

  Widget _buildInvoiceDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة معلومات الفاتورة
          _buildInvoiceInfoCard(),

          const SizedBox(height: AppDimensions.paddingM),

          // بطاقة معلومات العميل
          _buildCustomerInfoCard(),

          const SizedBox(height: AppDimensions.paddingM),

          // قائمة عناصر الفاتورة
          _buildInvoiceItemsCard(),

          const SizedBox(height: AppDimensions.paddingM),

          // ملخص مالي
          _buildFinancialSummaryCard(),

          const SizedBox(height: AppDimensions.paddingM),

          // سجل المدفوعات
          _buildPaymentsHistoryCard(),
        ],
      ),
    );
  }

  Widget _buildInvoiceInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الفاتورة',
              style: AppTextStyles.heading2,
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'رقم الفاتورة',
                    _invoice!.invoiceNumber,
                    Icons.receipt,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'الحالة',
                    Invoice.getStatusName(_invoice!.status),
                    Icons.info_outline,
                    valueColor: Invoice.getStatusColor(_invoice!.status),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ الإصدار',
                    DateFormat('dd/MM/yyyy').format(_invoice!.date),
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ الاستحقاق',
                    DateFormat('dd/MM/yyyy').format(_invoice!.dueDate),
                    Icons.event,
                    valueColor: _invoice!.dueDate.isBefore(DateTime.now()) &&
                               _invoice!.status != InvoiceStatus.paid
                        ? Colors.red
                        : null,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات العميل',
              style: AppTextStyles.heading2,
            ),
            const Divider(),
            _buildInfoItem(
              'اسم العميل',
              _invoice!.customerName,
              Icons.person,
            ),
            if (_invoice!.customer.phone != null && _invoice!.customer.phone!.isNotEmpty)
              _buildInfoItem(
                'رقم الهاتف',
                _invoice!.customer.phone!,
                Icons.phone,
              ),
            if (_invoice!.customer.email != null && _invoice!.customer.email!.isNotEmpty)
              _buildInfoItem(
                'البريد الإلكتروني',
                _invoice!.customer.email!,
                Icons.email,
              ),
            if (_invoice!.customer.address != null && _invoice!.customer.address!.isNotEmpty)
              _buildInfoItem(
                'العنوان',
                _invoice!.customer.address!,
                Icons.location_on,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceItemsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'عناصر الفاتورة',
              style: AppTextStyles.heading2,
            ),
            const Divider(),
            // عناوين الأعمدة
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: const [
                  Expanded(
                    flex: 3,
                    child: Text(
                      'الوصف',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      'الكمية',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'السعر',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'الإجمالي',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(),
            // عناصر الفاتورة
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _invoice!.items.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final item = _invoice!.items[index];
                return Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(item.name),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        item.quantity.toString(),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        '${item.unitPrice.toStringAsFixed(2)} ر.س',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        '${item.total.toStringAsFixed(2)} ر.س',
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملخص المالي',
              style: AppTextStyles.heading2,
            ),
            const Divider(),
            _buildSummaryRow('المجموع الفرعي', '${_invoice!.subtotal.toStringAsFixed(2)} ر.س'),
            _buildSummaryRow('الضريبة (${(_invoice!.taxRate * 100).toStringAsFixed(0)}%)', '${_invoice!.taxAmount.toStringAsFixed(2)} ر.س'),
            if (_invoice!.discount > 0)
              _buildSummaryRow('الخصم', '${_invoice!.discount.toStringAsFixed(2)} ر.س'),
            const Divider(),
            _buildSummaryRow(
              'الإجمالي',
              '${_invoice!.total.toStringAsFixed(2)} ر.س',
              isTotal: true,
            ),
            const Divider(),
            _buildSummaryRow(
              'المدفوع',
              '${_calculateTotalPaid().toStringAsFixed(2)} ر.س',
              valueColor: Colors.green,
            ),
            _buildSummaryRow(
              'المتبقي',
              '${(_invoice!.total - _calculateTotalPaid()).toStringAsFixed(2)} ر.س',
              valueColor: _calculateTotalPaid() < _invoice!.total ? Colors.red : Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsHistoryCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'سجل المدفوعات',
              style: AppTextStyles.heading2,
            ),
            const Divider(),
            _payments.isEmpty
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text('لا توجد مدفوعات مسجلة'),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _payments.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final payment = _payments[index];
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.green.shade50,
                          child: Icon(
                            Transaction.getPaymentMethodIcon(payment.paymentMethod),
                            color: Colors.green,
                          ),
                        ),
                        title: Text('${payment.amount.toStringAsFixed(2)} ر.س'),
                        subtitle: Text(
                          '${DateFormat('dd/MM/yyyy').format(payment.date)} - ${Transaction.getPaymentMethodName(payment.paymentMethod)}',
                        ),
                        trailing: _buildPaymentDetails(payment),
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails(Transaction payment) {
    String details = '';

    if (payment.paymentMethod == PaymentMethod.cash && payment.employeeName != null) {
      details = 'استلام: ${payment.employeeName}';
    } else if (payment.paymentMethod == PaymentMethod.bankTransfer && payment.bankAccountName != null) {
      details = 'حساب: ${payment.bankAccountName}';
    }

    return details.isNotEmpty
        ? Chip(
            label: Text(
              details,
              style: const TextStyle(fontSize: 12),
            ),
            backgroundColor: Colors.grey.shade200,
          )
        : const SizedBox.shrink();
  }

  Widget _buildInfoItem(String label, String value, IconData icon, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: AppColors.primary),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: valueColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false, Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  double _calculateTotalPaid() {
    return _payments.fold(0.0, (sum, payment) => sum + payment.amount);
  }
}
