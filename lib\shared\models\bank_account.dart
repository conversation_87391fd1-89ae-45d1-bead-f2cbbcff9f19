import 'dart:convert';
import 'package:flutter/material.dart';

enum BankAccountType {
  checking,
  savings,
  business,
  other,
}

class BankAccount {
  final int? id;
  final String bankName;
  final String accountNumber;
  final String accountName;
  final BankAccountType type;
  final String? iban;
  final String? swiftCode;
  final String? branchName;
  final String? notes;
  final bool isActive;
  final double balance;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  BankAccount({
    this.id,
    required this.bankName,
    required this.accountNumber,
    required this.accountName,
    required this.type,
    this.iban,
    this.swiftCode,
    this.branchName,
    this.notes,
    required this.isActive,
    required this.balance,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      id: map['id'] as int?,
      bankName: map['bank_name'] as String,
      accountNumber: map['account_number'] as String,
      accountName: map['account_name'] as String,
      type: _parseType(map['type'] as String),
      iban: map['iban'] as String?,
      swiftCode: map['swift_code'] as String?,
      branchName: map['branch_name'] as String?,
      notes: map['notes'] as String?,
      isActive: map['status'] == 'active',
      balance: map['balance'] as double,
      createdBy: map['created_by'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory BankAccount.fromJson(String source) =>
      BankAccount.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bank_name': bankName,
      'account_number': accountNumber,
      'account_name': accountName,
      'type': type.toString().split('.').last,
      'iban': iban,
      'swift_code': swiftCode,
      'branch_name': branchName,
      'notes': notes,
      'status': isActive ? 'active' : 'inactive',
      'balance': balance,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  BankAccount copyWith({
    int? id,
    String? bankName,
    String? accountNumber,
    String? accountName,
    BankAccountType? type,
    String? iban,
    String? swiftCode,
    String? branchName,
    String? notes,
    bool? isActive,
    double? balance,
    int? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccount(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      type: type ?? this.type,
      iban: iban ?? this.iban,
      swiftCode: swiftCode ?? this.swiftCode,
      branchName: branchName ?? this.branchName,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      balance: balance ?? this.balance,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static BankAccountType _parseType(String type) {
    switch (type.toLowerCase()) {
      case 'checking':
        return BankAccountType.checking;
      case 'savings':
        return BankAccountType.savings;
      case 'business':
        return BankAccountType.business;
      case 'other':
      default:
        return BankAccountType.other;
    }
  }

  static String getTypeName(BankAccountType type) {
    switch (type) {
      case BankAccountType.checking:
        return 'حساب جاري';
      case BankAccountType.savings:
        return 'حساب توفير';
      case BankAccountType.business:
        return 'حساب أعمال';
      case BankAccountType.other:
        return 'أخرى';
    }
  }

  static IconData getTypeIcon(BankAccountType type) {
    switch (type) {
      case BankAccountType.checking:
        return Icons.account_balance;
      case BankAccountType.savings:
        return Icons.savings;
      case BankAccountType.business:
        return Icons.business;
      case BankAccountType.other:
        return Icons.more_horiz;
    }
  }

  static Color getTypeColor(BankAccountType type) {
    switch (type) {
      case BankAccountType.checking:
        return Colors.blue;
      case BankAccountType.savings:
        return Colors.green;
      case BankAccountType.business:
        return Colors.purple;
      case BankAccountType.other:
        return Colors.grey;
    }
  }

  // Método para generar datos de prueba
  static List<BankAccount> getMockBankAccounts() {
    return [
      BankAccount(
        id: 1,
        bankName: 'البنك الأهلي السعودي',
        accountNumber: '**********',
        accountName: 'شركة تكييف الرياض',
        type: BankAccountType.business,
        iban: '************************',
        swiftCode: 'NCBKSAJE',
        branchName: 'فرع العليا',
        isActive: true,
        balance: 125000.0,
        createdAt: DateTime(2022, 1, 15),
      ),
      BankAccount(
        id: 2,
        bankName: 'مصرف الراجحي',
        accountNumber: '**********',
        accountName: 'شركة تكييف الرياض',
        type: BankAccountType.checking,
        iban: '************************',
        isActive: true,
        balance: 75000.0,
        createdAt: DateTime(2022, 3, 10),
      ),
      BankAccount(
        id: 3,
        bankName: 'بنك الرياض',
        accountNumber: '**********',
        accountName: 'شركة تكييف الرياض',
        type: BankAccountType.savings,
        iban: '************************',
        isActive: false,
        balance: 25000.0,
        createdAt: DateTime(2022, 5, 20),
      ),
    ];
  }
}
