import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';

import '../../../shared/models/customer.dart';
import '../../../features/settings/repositories/company_info_repository.dart';
import 'arabic_text_helper.dart';

/// مساعد لتصدير جدول بيانات العميل إلى ملف PDF
class CustomerTablePdfExport {
  /// تصدير جدول بيانات العميل إلى ملف PDF
  static Future<String?> exportCustomerTableToPdf({
    required BuildContext context,
    required String title,
    Customer? customer, // تم الاحتفاظ بالمعلمة للتوافق مع الاستدعاءات الحالية
    required List<Map<String, dynamic>> tableData,
    required Map<String, double> summaryData,
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // تحميل الخطوط العربية Cairo
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // تحميل معلومات الشركة
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // تحميل صورة الشعار
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // إنشاء صورة بسيطة بديلة
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // استخدام نفس الصورة للعلامة المائية
      final watermarkImageBytes = logoImageBytes;

      // الحصول على اسم المستخدم الحالي
      final String username = 'admin'; // TODO: الحصول من حالة المصادقة

      // إنشاء صفحة PDF بسيطة
      pdf.addPage(
        pw.MultiPage(
          margin: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          pageFormat: PdfPageFormat.a4,
          maxPages: 100, // تحديد الحد الأقصى لعدد الصفحات
          header: (context) => buildHeader(arabicFont, arabicBoldFont, companyInfo, logoImageBytes),
          footer: (context) => buildFooter(arabicFont, username),
          build: (pw.Context context) => [
            pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 8), // Reducir margen
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  'تقرير البيانات',
                  style: pw.TextStyle(font: arabicBoldFont, fontSize: 16, color: PdfColors.blue900), // Reducir tamaño de fuente
                ),
              ),
            ),
            _buildCustomerDataTable(tableData, summaryData, arabicFont, arabicBoldFont),
            pw.SizedBox(height: 15), // Reducir espacio
            // العلامة المائية - تم تصغير الحجم
            pw.Center(
              child: pw.Opacity(
                opacity: 0.05, // Reducir opacidad
                child: pw.Image(pw.MemoryImage(watermarkImageBytes), width: 120, height: 120, fit: pw.BoxFit.contain), // Reducir tamaño
              ),
            ),
          ],
        ),
      );

      // حفظ ملف PDF
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/data_report_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF saved to: ${file.path}');
      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  static pw.Widget buildHeader(pw.Font arabicFont, pw.Font arabicBoldFont, dynamic companyInfo, Uint8List logoImageBytes) {
    return pw.Container(
      width: double.infinity,
      margin: pw.EdgeInsets.zero,
      padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 0), // Reducir padding
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border(bottom: pw.BorderSide(color: PdfColors.blue200, width: 0.8)), // Reducir ancho del borde
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          // بيانات الشركة بالإنجليزي (يسار)
          pw.Expanded(
            flex: 4,
            child: pw.Padding(
              padding: const pw.EdgeInsets.only(left: 6), // Reducir padding
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    companyInfo?.nameEn ?? 'Ice Corner',
                    style: pw.TextStyle(font: arabicBoldFont, fontSize: 10, color: PdfColors.blue900), // Reducir tamaño de fuente
                  ),
                  if (companyInfo?.addressEn != null)
                    pw.Text(companyInfo!.addressEn!, style: pw.TextStyle(font: arabicFont, fontSize: 8)), // Reducir tamaño de fuente
                  if (companyInfo?.phoneEn != null)
                    pw.Text(companyInfo!.phoneEn!, style: pw.TextStyle(font: arabicFont, fontSize: 8)), // Reducir tamaño de fuente
                ],
              ),
            ),
          ),
          // الشعار في المنتصف
          pw.Container(
            width: 50, // Reducir tamaño
            height: 50, // Reducir tamaño
            alignment: pw.Alignment.center,
            margin: const pw.EdgeInsets.symmetric(horizontal: 6), // Reducir margen
            child: pw.Image(pw.MemoryImage(logoImageBytes), fit: pw.BoxFit.contain),
          ),
          // بيانات الشركة بالعربي (يمين)
          pw.Expanded(
            flex: 4,
            child: pw.Padding(
              padding: const pw.EdgeInsets.only(right: 6), // Reducir padding
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    companyInfo?.nameAr ?? 'ركن الجليد',
                    style: pw.TextStyle(font: arabicBoldFont, fontSize: 10, color: PdfColors.blue900), // Reducir tamaño de fuente
                    textDirection: pw.TextDirection.rtl,
                  ),
                  if (companyInfo?.addressAr != null)
                    pw.Text(companyInfo!.addressAr!, style: pw.TextStyle(font: arabicFont, fontSize: 8), textDirection: pw.TextDirection.rtl), // Reducir tamaño de fuente
                  if (companyInfo?.phoneAr != null)
                    pw.Text(companyInfo!.phoneAr!, style: pw.TextStyle(font: arabicFont, fontSize: 8), textDirection: pw.TextDirection.rtl), // Reducir tamaño de fuente
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget buildFooter(pw.Font arabicFont, String username) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 12), // Reducir padding
      decoration: pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300, width: 0.5)), // Reducir ancho del borde
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'المستخدم: $username',
            style: pw.TextStyle(font: arabicFont, fontSize: 8, color: PdfColors.grey700), // Reducir tamaño de fuente
            textDirection: pw.TextDirection.rtl,
          ),
          pw.Text(
            'تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
            style: pw.TextStyle(font: arabicFont, fontSize: 8, color: PdfColors.grey700), // Reducir tamaño de fuente
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  /// بناء جدول بيانات العميل
  static pw.Widget _buildCustomerDataTable(
    List<Map<String, dynamic>> tableData,
    Map<String, double> summaryData,
    pw.Font regularFont,
    pw.Font boldFont
  ) {
    if (tableData.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        ),
        child: pw.Center(
          child: pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              'لا توجد بيانات للعرض',
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 16,
                color: PdfColors.grey700,
              ),
            ),
          ),
        ),
      );
    }

    // تحديد عناوين الأعمدة (إضافة عمود العملة)
    final headers = [
      'النوع',
      'الرقم المرجعي',
      'التاريخ',
      'الوصف',
      'المبلغ',
      'العملة',
      'الحالة',
    ];

    // تحويل بيانات الجدول إلى تنسيق مناسب
    final List<List<String>> rows = [];

    // إضافة صفوف البيانات مع عمود العملة
    double totalServicesPaid = 0.0;
    double? monthlySubscriptionAmount;
    int subscriptionServicesCount = 0;

    // التحقق من وجود عميل ومبلغ اشتراك شهري
    if (summaryData.containsKey('monthlySubscriptionAmount') && summaryData['monthlySubscriptionAmount'] != null) {
      // استخدام مبلغ الاشتراك الشهري من بيانات الملخص
      monthlySubscriptionAmount = summaryData['monthlySubscriptionAmount'];
    }

    // عدّ خدمات الاشتراك الشهري
    subscriptionServicesCount = tableData.where((item) =>
      item['type'] == 'service' &&
      item.containsKey('customerPaymentMethod') &&
      item['customerPaymentMethod'] == CustomerPaymentMethod.monthlySubscription
    ).length;

    for (final item in tableData) {
      String amountText = _getAmountText(item);
      String currencyText = '';
      if (item['type'] == 'service') {
        if (item.containsKey('customerPaymentMethod') &&
            item['customerPaymentMethod'] == CustomerPaymentMethod.monthlySubscription) {
          amountText = 'ضمن الاشتراك';
          currencyText = '';
          // لا تجمع مبلغ الخدمة هنا، سيتم معالجته في ملخص الإحصائيات
        } else {
          amountText = item['amount'].toStringAsFixed(2);
          currencyText = 'ر.س';
          totalServicesPaid += (item['amount'] as double);
        }
      } else {
        amountText = item['amount'].toStringAsFixed(2);
        currencyText = 'ر.س';
      }
      final row = <String>[
        _getTypeText(item['type']),
        item['reference'],
        DateFormat('dd/MM/yyyy').format(item['date']),
        item['description'],
        amountText,
        currencyText,
        item['status'],
      ];
      rows.add(row);
    }

    // منطق محسن: حساب إجمالي مبلغ طلبات الخدمات بناءً على نوع اشتراك العميل
    double totalServicesForSummary = totalServicesPaid;

    // التحقق من وجود خدمات بنظام الاشتراك الشهري
    if (subscriptionServicesCount > 0 && monthlySubscriptionAmount != null) {
      // إذا كان العميل لديه اشتراك شهري، استخدم مبلغ الاشتراك الشهري فقط
      totalServicesForSummary = monthlySubscriptionAmount;
    } else {
      // إذا كان العميل يدفع لكل خدمة، استخدم مجموع مبالغ الخدمات الفردية
      totalServicesForSummary = totalServicesPaid;
    }

    // تحديد عرض الأعمدة (إضافة عمود العملة)
    final columnWidths = [0.1, 0.18, 0.13, 0.22, 0.13, 0.07, 0.17];

    // بناء الجدول مع دعم الخط العربي واتجاه RTL
    final table = pw.Table(
      tableWidth: pw.TableWidth.max,
      border: pw.TableBorder.all(
        color: PdfColors.grey400,
        width: 0.7,
      ),
      columnWidths: {
        for (int i = 0; i < columnWidths.length; i++)
          i: pw.FlexColumnWidth(columnWidths[i] * 100),
      },
      children: [
        // Header row - optimizado
        pw.TableRow(
          decoration: pw.BoxDecoration(
            color: PdfColors.blue700,
          ),
          children: List.generate(
            headers.length,
            (index) => pw.Container(
              height: 25, // Reducir altura
              padding: const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 4), // Reducir padding
              alignment: pw.Alignment.centerRight,
              child: pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Text(
                  headers[index],
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 9, // Reducir tamaño de fuente
                    color: PdfColors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Data rows - optimizado para reducir el tamaño del PDF
        ...rows.asMap().entries.map(
          (entry) {
            final rowIndex = entry.key;
            final row = entry.value;
            final rowColor = rowIndex % 2 == 0 ? PdfColors.white : PdfColors.grey100;
            return pw.TableRow(
              decoration: pw.BoxDecoration(
                color: rowColor,
              ),
              children: List.generate(
                headers.length,
                (colIndex) => pw.Container(
                  height: 25, // Reducir altura de las filas
                  padding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4), // Reducir padding
                  alignment: pw.Alignment.centerRight,
                  child: pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: pw.Text(
                      row[colIndex],
                      style: pw.TextStyle(
                        font: regularFont,
                        fontSize: 9, // Reducir tamaño de fuente
                        color: PdfColors.black,
                      ),
                      maxLines: 1, // Limitar a una línea
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );

    // بناء ملخص الإحصائيات
    double summaryTotalServices = totalServicesForSummary;

    // إعادة حساب الرصيد المتبقي: إجمالي الإيرادات - (إجمالي الفواتير + إجمالي مبلغ طلبات الخدمات)
    double recalculatedRemainingBalance = (summaryData['totalTransactions'] ?? 0.0) -
                                         ((summaryData['totalAmount'] ?? 0.0) + summaryTotalServices);
    final summaryWidget = pw.Container(
      margin: const pw.EdgeInsets.only(top: 10), // Reducir margen
      padding: const pw.EdgeInsets.all(6), // Reducir padding
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)), // Reducir radio
        border: pw.Border.all(color: PdfColors.blue200, width: 0.5), // Reducir ancho del borde
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.stretch,
        children: [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Text(
              'ملخص الإحصائيات',
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 10, // Reducir tamaño de fuente
                color: PdfColors.blue800,
              ),
            ),
          ),
          pw.Divider(color: PdfColors.blue200, thickness: 0.5), // Reducir grosor
          pw.SizedBox(height: 4), // Reducir espacio
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryItem(
                ArabicTextHelper.processArabicText('إجمالي الإيرادات'),
                ArabicTextHelper.processArabicText('+${summaryData['totalTransactions']?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                regularFont,
                boldFont,
                PdfColors.green700,
              ),
              _buildSummaryItem(
                ArabicTextHelper.processArabicText('إجمالي الفواتير'),
                ArabicTextHelper.processArabicText('-${summaryData['totalAmount']?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                regularFont,
                boldFont,
                PdfColors.blue700,
              ),
              _buildSummaryItem(
                ArabicTextHelper.processArabicText('إجمالي مبلغ طلبات الخدمات'),
                ArabicTextHelper.processArabicText('-${summaryTotalServices.toStringAsFixed(2)} ر.س'),
                regularFont,
                boldFont,
                PdfColors.orange,
              ),
              _buildSummaryItem(
                ArabicTextHelper.processArabicText('الرصيد المتبقي'),
                ArabicTextHelper.processArabicText(
                  '${recalculatedRemainingBalance >= 0 ? '+' : ''}${recalculatedRemainingBalance.toStringAsFixed(2)} ر.س'
                ),
                regularFont,
                boldFont,
                recalculatedRemainingBalance >= 0 ? PdfColors.green700 : PdfColors.red700,
              ),
            ],
          ),
        ],
      ),
    );

    // دمج الجدول وملخص الإحصائيات فقط (بدون صف الإجماليات)
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        table,
        summaryWidget,
      ],
    );
  }

  /// الحصول على نص نوع العنصر
  static String _getTypeText(String type) {
    switch (type) {
      case 'invoice':
        return 'فاتورة';
      case 'transaction':
        return 'إيراد';
      case 'service':
        return 'خدمة';
      default:
        return type;
    }
  }

  /// الحصول على نص المبلغ
  static String _getAmountText(Map<String, dynamic> item) {
    // 1. إذا كان نوع العنصر هو "service" (خدمة) والعميل لديه اشتراك شهري
    if (item['type'] == 'service') {
      // التحقق من طلبات الخدمة ضمن الاشتراك الشهري (الطريقة القديمة)
      if (item.containsKey('isSubscription') && item['isSubscription'] == true) {
        return 'ضمن الاشتراك';
      }

      // التحقق من طلبات الخدمة للعملاء ذوي الاشتراك الشهري (الطريقة الجديدة)
      if (item.containsKey('customerPaymentMethod') &&
          item['customerPaymentMethod'] == CustomerPaymentMethod.monthlySubscription) {
        return 'ضمن الاشتراك';
      }

      // 2. إذا كان نوع العنصر هو "service" (خدمة) والعميل يدفع لكل خدمة
      if (item.containsKey('customerPaymentMethod') &&
          item['customerPaymentMethod'] == CustomerPaymentMethod.perService) {
        return '${item['amount'].toStringAsFixed(2)} ر.س';
      }
    }

    // 3. لجميع أنواع العناصر الأخرى (مثل الفواتير والمعاملات)
    return '${item['amount'].toStringAsFixed(2)} ر.س';
  }

  /// بناء عنصر ملخص
  static pw.Widget _buildSummaryItem(
    String title,
    String value,
    pw.Font regularFont,
    pw.Font boldFont,
    PdfColor valueColor,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6), // Reducir padding
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)), // Reducir radio
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5), // Reducir ancho del borde
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            ArabicTextHelper.processArabicText(title),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 8, // Reducir tamaño de fuente
              color: PdfColors.grey800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 3), // Reducir espacio
          pw.Text(
            ArabicTextHelper.processArabicText(value),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 10, // Reducir tamaño de fuente
              color: valueColor,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  // تم إزالة دالة _buildCustomerInfoSection و _buildInfoItem بناءً على طلب المستخدم
}
