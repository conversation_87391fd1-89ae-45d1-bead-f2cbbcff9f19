import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../repositories/cash_box_repository.dart';
import '../repositories/customer_repository.dart';
import '../../shared/models/cash_box.dart';
import '../../shared/models/customer.dart';

/// Service for managing opening balances across the system
class OpeningBalanceService {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CashBoxRepository _cashBoxRepository = CashBoxRepository();
  final CustomerRepository _customerRepository = CustomerRepository();

  /// Set opening balance for a cash box
  Future<bool> setCashBoxOpeningBalance({
    required int cashBoxId,
    required double openingBalance,
    String? description,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.transaction((txn) async {
        // Get current cash box
        final cashBox = await _cashBoxRepository.getCashBoxById(cashBoxId);
        if (cashBox == null) {
          throw Exception('Cash box not found');
        }
        
        // Calculate the difference
        final difference = openingBalance - cashBox.openingBalance;
        
        // Update cash box opening and current balance
        final updatedCashBox = cashBox.copyWith(
          openingBalance: openingBalance,
          currentBalance: cashBox.currentBalance + difference,
          updatedAt: DateTime.now(),
        );
        
        await _cashBoxRepository.updateCashBox(updatedCashBox);
        
        // Create opening balance transaction if there's a difference
        if (difference != 0) {
          final transaction = CashBoxTransaction(
            cashBoxId: cashBoxId,
            type: difference > 0 ? CashBoxTransactionType.income : CashBoxTransactionType.expense,
            amount: difference.abs(),
            description: description ?? 'Opening balance adjustment',
            referenceType: CashBoxReferenceType.opening,
            transactionDate: DateTime.now(),
            createdAt: DateTime.now(),
          );
          
          // Note: We don't use addTransaction here to avoid double balance update
          await txn.insert('cash_box_transactions', transaction.toMap());
        }
        
        if (kDebugMode) {
          print('✅ Cash box opening balance set: $openingBalance');
        }
        
        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting cash box opening balance: $e');
      }
      rethrow;
    }
  }

  /// Set opening balance for a customer
  Future<bool> setCustomerOpeningBalance({
    required int customerId,
    required double openingBalance,
    String? description,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      // Update customer opening balance
      await db.update(
        'customers',
        {
          'opening_balance': openingBalance,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [customerId],
      );
      
      if (kDebugMode) {
        print('✅ Customer opening balance set: $openingBalance');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting customer opening balance: $e');
      }
      rethrow;
    }
  }

  /// Set opening balance for a bank account
  Future<bool> setBankAccountOpeningBalance({
    required int bankAccountId,
    required double openingBalance,
    String? description,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.transaction((txn) async {
        // Get current bank account
        final accountData = await txn.query(
          'bank_accounts',
          where: 'id = ?',
          whereArgs: [bankAccountId],
        );
        
        if (accountData.isEmpty) {
          throw Exception('Bank account not found');
        }
        
        final account = accountData.first;
        final currentOpeningBalance = (account['opening_balance'] as num?)?.toDouble() ?? 0.0;
        final currentBalance = (account['current_balance'] as num?)?.toDouble() ?? 0.0;
        
        // Calculate the difference
        final difference = openingBalance - currentOpeningBalance;
        
        // Update bank account opening and current balance
        await txn.update(
          'bank_accounts',
          {
            'opening_balance': openingBalance,
            'current_balance': currentBalance + difference,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [bankAccountId],
        );
        
        if (kDebugMode) {
          print('✅ Bank account opening balance set: $openingBalance');
        }
        
        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error setting bank account opening balance: $e');
      }
      rethrow;
    }
  }

  /// Get opening balance summary for all entities
  Future<OpeningBalanceSummary> getOpeningBalanceSummary() async {
    try {
      final db = await _dbHelper.database;
      
      // Get cash boxes summary
      final cashBoxData = await db.rawQuery('''
        SELECT 
          COUNT(*) as count,
          SUM(opening_balance) as total_opening,
          SUM(current_balance) as total_current
        FROM cash_boxes 
        WHERE is_active = 1
      ''');
      
      // Get customers summary
      final customerData = await db.rawQuery('''
        SELECT 
          COUNT(*) as count,
          SUM(opening_balance) as total_opening
        FROM customers 
        WHERE status = 'active'
      ''');
      
      // Get bank accounts summary
      final bankAccountData = await db.rawQuery('''
        SELECT 
          COUNT(*) as count,
          SUM(opening_balance) as total_opening,
          SUM(current_balance) as total_current
        FROM bank_accounts 
        WHERE status = 'active'
      ''');
      
      return OpeningBalanceSummary(
        cashBoxes: OpeningBalanceEntitySummary(
          count: cashBoxData.first['count'] as int,
          totalOpeningBalance: (cashBoxData.first['total_opening'] as num?)?.toDouble() ?? 0.0,
          totalCurrentBalance: (cashBoxData.first['total_current'] as num?)?.toDouble() ?? 0.0,
        ),
        customers: OpeningBalanceEntitySummary(
          count: customerData.first['count'] as int,
          totalOpeningBalance: (customerData.first['total_opening'] as num?)?.toDouble() ?? 0.0,
          totalCurrentBalance: 0.0, // Customers don't have current balance
        ),
        bankAccounts: OpeningBalanceEntitySummary(
          count: bankAccountData.first['count'] as int,
          totalOpeningBalance: (bankAccountData.first['total_opening'] as num?)?.toDouble() ?? 0.0,
          totalCurrentBalance: (bankAccountData.first['total_current'] as num?)?.toDouble() ?? 0.0,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting opening balance summary: $e');
      }
      rethrow;
    }
  }

  /// Initialize opening balances for new installation
  Future<bool> initializeOpeningBalances({
    required double defaultCashBoxBalance,
    String? defaultCashBoxName,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.transaction((txn) async {
        // Check if any cash boxes exist
        final existingCashBoxes = await txn.query('cash_boxes');
        
        if (existingCashBoxes.isEmpty) {
          // Create default cash box with opening balance
          final defaultCashBox = CashBox(
            name: defaultCashBoxName ?? 'Main Cash Box',
            description: 'Default cash box created during setup',
            openingBalance: defaultCashBoxBalance,
            currentBalance: defaultCashBoxBalance,
            isActive: true,
            currency: 'SAR',
            createdAt: DateTime.now(),
          );
          
          await txn.insert('cash_boxes', defaultCashBox.toMap());
          
          if (kDebugMode) {
            print('✅ Default cash box created with opening balance: $defaultCashBoxBalance');
          }
        }
        
        return true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing opening balances: $e');
      }
      rethrow;
    }
  }

  /// Validate opening balance data consistency
  Future<List<OpeningBalanceValidationIssue>> validateOpeningBalances() async {
    try {
      final issues = <OpeningBalanceValidationIssue>[];
      final db = await _dbHelper.database;
      
      // Check cash boxes with negative opening balances
      final negativeCashBoxes = await db.query(
        'cash_boxes',
        where: 'opening_balance < 0',
      );
      
      for (final cashBox in negativeCashBoxes) {
        issues.add(OpeningBalanceValidationIssue(
          type: 'cash_box',
          entityId: cashBox['id'] as int,
          entityName: cashBox['name'] as String,
          issue: 'Negative opening balance',
          value: (cashBox['opening_balance'] as num).toDouble(),
        ));
      }
      
      // Check customers with negative opening balances
      final negativeCustomers = await db.query(
        'customers',
        where: 'opening_balance < 0',
      );
      
      for (final customer in negativeCustomers) {
        issues.add(OpeningBalanceValidationIssue(
          type: 'customer',
          entityId: customer['id'] as int,
          entityName: customer['name'] as String,
          issue: 'Negative opening balance',
          value: (customer['opening_balance'] as num).toDouble(),
        ));
      }
      
      // Check bank accounts with negative opening balances
      final negativeBankAccounts = await db.query(
        'bank_accounts',
        where: 'opening_balance < 0',
      );
      
      for (final account in negativeBankAccounts) {
        issues.add(OpeningBalanceValidationIssue(
          type: 'bank_account',
          entityId: account['id'] as int,
          entityName: account['account_name'] as String,
          issue: 'Negative opening balance',
          value: (account['opening_balance'] as num).toDouble(),
        ));
      }
      
      return issues;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating opening balances: $e');
      }
      rethrow;
    }
  }
}

/// Opening balance summary for all entities
class OpeningBalanceSummary {
  final OpeningBalanceEntitySummary cashBoxes;
  final OpeningBalanceEntitySummary customers;
  final OpeningBalanceEntitySummary bankAccounts;

  const OpeningBalanceSummary({
    required this.cashBoxes,
    required this.customers,
    required this.bankAccounts,
  });

  /// Get total opening balance across all entities
  double get totalOpeningBalance =>
      cashBoxes.totalOpeningBalance +
      customers.totalOpeningBalance +
      bankAccounts.totalOpeningBalance;

  /// Get total current balance across all entities
  double get totalCurrentBalance =>
      cashBoxes.totalCurrentBalance +
      customers.totalCurrentBalance +
      bankAccounts.totalCurrentBalance;
}

/// Opening balance summary for a specific entity type
class OpeningBalanceEntitySummary {
  final int count;
  final double totalOpeningBalance;
  final double totalCurrentBalance;

  const OpeningBalanceEntitySummary({
    required this.count,
    required this.totalOpeningBalance,
    required this.totalCurrentBalance,
  });
}

/// Opening balance validation issue
class OpeningBalanceValidationIssue {
  final String type; // 'cash_box', 'customer', 'bank_account'
  final int entityId;
  final String entityName;
  final String issue;
  final double value;

  const OpeningBalanceValidationIssue({
    required this.type,
    required this.entityId,
    required this.entityName,
    required this.issue,
    required this.value,
  });
}
