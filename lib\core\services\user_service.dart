import 'dart:convert'; // Import for json encoding/decoding
import 'package:flutter/foundation.dart';
import '../../shared/models/user.dart';
import '../database/database_helper.dart'; // Import DatabaseHelper

class UserService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all users
  Future<List<User>> getUsers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> userMaps = await db.query('users');

      List<User> users = [];
      for (var userMap in userMaps) {
        // Fetch roles for each user
        final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
          SELECT r.name FROM roles r
          JOIN user_roles ur ON r.id = ur.role_id
          WHERE ur.user_id = ?
        ''', [userMap['id']]);

        final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

        users.add(User.fromMap({...userMap, 'roles': roles}));
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting users: $e');
      }
      return [];
    }
  }

  // Get user by ID
  Future<User?> getUserById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> userMaps = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (userMaps.isNotEmpty) {
        final userMap = userMaps.first;
        // Fetch roles for the user
        final List<Map<String, dynamic>> roleMaps = await db.rawQuery('''
          SELECT r.name FROM roles r
          JOIN user_roles ur ON r.id = ur.role_id
          WHERE ur.user_id = ?
        ''', [userMap['id']]);

        final List<String> roles = roleMaps.map((roleMap) => roleMap['name'] as String).toList();

        return User.fromMap({...userMap, 'roles': roles});
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user by ID: $e');
      }
      return null;
    }
  }

  // Add a new user
  Future<User?> addUser(User user, String password) async {
    try {
      final db = await _dbHelper.database;
      final userId = await db.insert('users', {
        'name': user.name,
        'email': user.email,
        'password': password, // Assuming password is provided separately for insertion
        'phone': user.phone,
        'avatar': user.avatar,
        'status': user.isActive ? 'active' : 'inactive',
        'created_at': DateTime.now().toIso8601String(),
        'last_login': user.lastLogin?.toIso8601String(),
        'permissions': user.permissions != null ? json.encode(user.permissions) : null,
      });

      // Assign roles to the new user
      for (final roleName in user.roles) {
        // Find the role ID
        final List<Map<String, dynamic>> roleMaps = await db.query(
          'roles',
          where: 'name = ?',
          whereArgs: [roleName],
        );
        if (roleMaps.isNotEmpty) {
          final roleId = roleMaps.first['id'];
          await db.insert('user_roles', {
            'user_id': userId,
            'role_id': roleId,
          });
        } else {
          if (kDebugMode) {
            print('Warning: Role "$roleName" not found in roles table.');
          }
        }
      }

      // Return the newly created user with its ID and roles
      return getUserById(userId);
    } catch (e) {
      if (kDebugMode) {
        print('Error adding user: $e');
      }
      return null;
    }
  }

  // Update an existing user
  Future<User?> updateUser(User user) async {
    try {
      final db = await _dbHelper.database;
      await db.update(
        'users',
        {
          'name': user.name,
          'email': user.email,
          'phone': user.phone,
          'avatar': user.avatar,
          'status': user.isActive ? 'active' : 'inactive',
          'last_login': user.lastLogin?.toIso8601String(),
          'permissions': user.permissions != null ? json.encode(user.permissions) : null,
        },
        where: 'id = ?',
        whereArgs: [user.id],
      );

      // Update user roles: remove existing roles and add new ones
      await db.delete('user_roles', where: 'user_id = ?', whereArgs: [user.id]);
      for (final roleName in user.roles) {
         final List<Map<String, dynamic>> roleMaps = await db.query(
          'roles',
          where: 'name = ?',
          whereArgs: [roleName],
        );
        if (roleMaps.isNotEmpty) {
          final roleId = roleMaps.first['id'];
          await db.insert('user_roles', {
            'user_id': user.id,
            'role_id': roleId,
          });
        } else {
           if (kDebugMode) {
            print('Warning: Role "$roleName" not found in roles table.');
          }
        }
      }


      return getUserById(user.id);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user: $e');
      }
      return null;
    }
  }

  // Update user permissions (assuming permissions are stored as JSON in the users table)
  Future<User?> updateUserPermissions(int userId, Map<String, dynamic> permissions) async {
     try {
      final db = await _dbHelper.database;
      await db.update(
        'users',
        {
          'permissions': json.encode(permissions),
        },
        where: 'id = ?',
        whereArgs: [userId],
      );

      return getUserById(userId);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user permissions: $e');
      }
      return null;
    }
  }


  // Delete a user
  Future<bool> deleteUser(int id) async {
    try {
      final db = await _dbHelper.database;
      // Delete user roles first due to foreign key constraint
      await db.delete('user_roles', where: 'user_id = ?', whereArgs: [id]);
      final result = await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );
      return result > 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting user: $e');
      }
      return false;
    }
  }
}
