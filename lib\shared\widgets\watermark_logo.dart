import 'package:flutter/material.dart';
import '../../config/constants.dart';

/// شعار الثلج كعلامة مائية
class WatermarkLogo extends StatelessWidget {
  /// حجم الشعار
  final double size;
  
  /// شفافية الشعار
  final double opacity;
  
  /// دوران الشعار (بالدرجات)
  final double rotation;
  
  /// إنشاء شعار الثلج كعلامة مائية
  const WatermarkLogo({
    super.key,
    this.size = 200.0,
    this.opacity = 0.05,
    this.rotation = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Transform.rotate(
        angle: rotation * 3.14159 / 180, // تحويل الدرجات إلى راديان
        child: Opacity(
          opacity: opacity,
          child: Image.asset(
            'assets/images/snowflake_logo.png',
            width: size,
            height: size,
            fit: BoxFit.contain,
            color: AppColors.primary.withAlpha(50),
            colorBlendMode: BlendMode.srcIn,
          ),
        ),
      ),
    );
  }
}

/// شعار الثلج في شريط التطبيق
class AppBarLogo extends StatelessWidget {
  /// حجم الشعار
  final double size;
  
  /// إنشاء شعار الثلج في شريط التطبيق
  const AppBarLogo({
    super.key,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/images/snowflake_logo.png',
      width: size,
      height: size,
      fit: BoxFit.contain,
    );
  }
}

/// شعار الثلج مع نص التطبيق
class AppLogoWithText extends StatelessWidget {
  /// حجم الشعار
  final double logoSize;
  
  /// حجم النص
  final double fontSize;
  
  /// لون النص
  final Color textColor;
  
  /// اتجاه العرض (أفقي أو رأسي)
  final Axis direction;
  
  /// إنشاء شعار الثلج مع نص التطبيق
  const AppLogoWithText({
    super.key,
    this.logoSize = 48.0,
    this.fontSize = 24.0,
    this.textColor = AppColors.primary,
    this.direction = Axis.horizontal,
  });

  @override
  Widget build(BuildContext context) {
    final logoWidget = Image.asset(
      'assets/images/snowflake_logo.png',
      width: logoSize,
      height: logoSize,
      fit: BoxFit.contain,
    );
    
    final textWidget = Text(
      'ركن الجليد',
      style: TextStyle(
        fontFamily: 'Cairo',
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        color: textColor,
      ),
    );
    
    return direction == Axis.horizontal
        ? Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              logoWidget,
              const SizedBox(width: 12),
              textWidget,
            ],
          )
        : Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              logoWidget,
              const SizedBox(height: 8),
              textWidget,
            ],
          );
  }
}

/// شعار الثلج متحرك للشاشات الانتقالية
class AnimatedSnowflakeLogo extends StatefulWidget {
  /// حجم الشعار
  final double size;
  
  /// مدة الحركة
  final Duration duration;
  
  /// إنشاء شعار الثلج متحرك
  const AnimatedSnowflakeLogo({
    super.key,
    this.size = 120.0,
    this.duration = const Duration(seconds: 2),
  });

  @override
  State<AnimatedSnowflakeLogo> createState() => _AnimatedSnowflakeLogoState();
}

class _AnimatedSnowflakeLogoState extends State<AnimatedSnowflakeLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 360.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.2),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0),
        weight: 1,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value * 3.14159 / 180,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Image.asset(
              'assets/images/snowflake_logo.png',
              width: widget.size,
              height: widget.size,
              fit: BoxFit.contain,
              color: AppColors.primary,
            ),
          ),
        );
      },
    );
  }
}
