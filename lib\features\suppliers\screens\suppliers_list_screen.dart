import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../core/repositories/supplier_repository.dart';
import '../../../shared/models/supplier.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/supplier_card.dart';

class SuppliersListScreen extends StatefulWidget {
  const SuppliersListScreen({super.key});

  @override
  State<SuppliersListScreen> createState() => _SuppliersListScreenState();
}

class _SuppliersListScreenState extends State<SuppliersListScreen> {
  final SupplierRepository _supplierRepository = SupplierRepository();
  bool _isLoading = true;
  List<Supplier> _suppliers = [];
  String _searchQuery = '';
  SupplierCategory? _selectedCategory;

  // Helper method to show snackbar safely
  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _loadSuppliers();
  }

  Future<void> _loadSuppliers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final suppliers = await _supplierRepository.getAllSuppliers();

      if (mounted) {
        setState(() {
          _suppliers = suppliers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تحميل بيانات الموردين: $e', isError: true);
      }
    }
  }

  List<Supplier> get _filteredSuppliers {
    return _suppliers.where((supplier) {
      // Apply search filter
      final matchesSearch = _searchQuery.isEmpty ||
          supplier.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (supplier.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
          (supplier.phone?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

      // Apply category filter
      final matchesCategory = _selectedCategory == null || supplier.category == _selectedCategory;

      return matchesSearch && matchesCategory;
    }).toList();
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية الموردين'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('تصفية حسب الفئة:'),
                  const SizedBox(height: AppDimensions.paddingS),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('الكل'),
                        selected: _selectedCategory == null,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedCategory = null;
                            });
                          }
                        },
                      ),
                      ...SupplierCategory.values.map((category) {
                        return FilterChip(
                          label: Text(Supplier.getCategoryName(category)),
                          selected: _selectedCategory == category,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = selected ? category : null;
                            });
                          },
                        );
                      }),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // Apply the filter
                    this.setState(() {});
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showAddSupplierDialog() {
    Navigator.pushNamed(
      context,
      AppRoutes.supplierEdit,
      arguments: null,
    ).then((newSupplier) async {
      if (newSupplier != null && mounted) {
        try {
          final supplier = newSupplier as Supplier;

          // Insert supplier into database
          final id = await _supplierRepository.insertSupplier(supplier);

          if (id > 0 && mounted) {
            // Reload suppliers from database
            _loadSuppliers();

            // Show success message
            _showSnackBar('تم إضافة المورد بنجاح');
          }
        } catch (e) {
          if (mounted) {
            _showSnackBar('حدث خطأ أثناء إضافة المورد: $e', isError: true);
          }
        }
      }
    });
  }

  void _showSupplierActions(BuildContext context, Supplier supplier) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.4,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return ListView(
              controller: scrollController,
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.paddingL,
                horizontal: AppDimensions.paddingM,
              ),
              children: [
                Text(
                  supplier.name,
                  style: AppTextStyles.heading2,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.paddingS),
                Text(
                  Supplier.getCategoryName(supplier.category),
                  style: TextStyle(
                    color: Supplier.getCategoryColor(supplier.category),
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('عرض التفاصيل'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(
                      context,
                      AppRoutes.supplierDetails,
                      arguments: supplier,
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('تعديل البيانات'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(
                      context,
                      AppRoutes.supplierEdit,
                      arguments: supplier,
                    ).then((updatedSupplier) async {
                      if (updatedSupplier != null && mounted) {
                        try {
                          final updatedSupplierData = updatedSupplier as Supplier;

                          // Update supplier in database
                          final result = await _supplierRepository.updateSupplier(updatedSupplierData);

                          if (result > 0 && mounted) {
                            // Reload suppliers from database
                            _loadSuppliers();

                            // Show success message
                            _showSnackBar('تم تحديث بيانات المورد بنجاح');
                          }
                        } catch (e) {
                          if (mounted) {
                            _showSnackBar('حدث خطأ أثناء تحديث بيانات المورد: $e', isError: true);
                          }
                        }
                      }
                    });
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.receipt_long),
                  title: const Text('عرض المعاملات'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(
                      context,
                      AppRoutes.detailedReport,
                      arguments: {'type': 'supplier', 'id': supplier.id},
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.payment),
                  title: const Text('تسجيل دفعة'),
                  onTap: () {
                    Navigator.pop(context);
                    _showPaymentDialog(context, supplier);
                  },
                ),
                if (supplier.isActive)
                  ListTile(
                    leading: const Icon(Icons.block, color: Colors.orange),
                    title: const Text(
                      'تعطيل الحساب',
                      style: TextStyle(color: Colors.orange),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _showDeactivateConfirmation(context, supplier);
                    },
                  )
                else
                  ListTile(
                    leading: const Icon(Icons.check_circle, color: Colors.green),
                    title: const Text(
                      'تفعيل الحساب',
                      style: TextStyle(color: Colors.green),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _activateSupplier(supplier);
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'حذف المورد',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showDeleteConfirmation(context, supplier);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showPaymentDialog(BuildContext context, Supplier supplier) {
    final formKey = GlobalKey<FormState>();
    double amount = 0;
    String description = ''; // Almacena la descripción para uso futuro

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تسجيل دفعة لـ ${supplier.name}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'المبلغ',
                    prefixIcon: Icon(Icons.money),
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    amount = double.parse(value!);
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  onSaved: (value) {
                    description = value ?? '';
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  try {
                    // Update supplier balance
                    final newBalance = supplier.balance - amount;
                    final result = await _supplierRepository.updateSupplierBalance(supplier.id, newBalance);

                    // En el futuro, podríamos usar la descripción para mostrar detalles adicionales
                    // print('Descripción: $description');

                    if (result > 0 && mounted) {
                      // Reload suppliers from database
                      _loadSuppliers();

                      // Show success message
                      _showSnackBar('تم تسجيل دفعة بمبلغ $amount ريال لـ ${supplier.name}');
                    }
                  } catch (e) {
                    if (mounted) {
                      _showSnackBar('حدث خطأ أثناء تسجيل الدفعة: $e', isError: true);
                    }
                  }
                }
              },
              child: const Text('تسجيل'),
            ),
          ],
        );
      },
    );
  }

  void _showDeactivateConfirmation(BuildContext context, Supplier supplier) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد تعطيل الحساب'),
          content: Text(
            'هل أنت متأكد من رغبتك في تعطيل حساب المورد ${supplier.name}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deactivateSupplier(supplier);
              },
              child: const Text(
                'تعطيل',
                style: TextStyle(color: Colors.orange),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deactivateSupplier(Supplier supplier) async {
    try {
      // Update supplier in database
      final updatedSupplier = supplier.copyWith(isActive: false);
      final result = await _supplierRepository.updateSupplier(updatedSupplier);

      if (result > 0 && mounted) {
        // Reload suppliers from database
        _loadSuppliers();

        // Show success message
        _showSnackBar('تم تعطيل حساب المورد ${supplier.name}');
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('حدث خطأ أثناء تعطيل حساب المورد: $e', isError: true);
      }
    }
  }

  Future<void> _activateSupplier(Supplier supplier) async {
    try {
      // Update supplier in database
      final updatedSupplier = supplier.copyWith(isActive: true);
      final result = await _supplierRepository.updateSupplier(updatedSupplier);

      if (result > 0 && mounted) {
        // Reload suppliers from database
        _loadSuppliers();

        // Show success message
        _showSnackBar('تم تفعيل حساب المورد ${supplier.name}');
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('حدث خطأ أثناء تفعيل حساب المورد: $e', isError: true);
      }
    }
  }

  void _showDeleteConfirmation(BuildContext context, Supplier supplier) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من رغبتك في حذف المورد ${supplier.name}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);

                try {
                  // Delete supplier from database
                  final result = await _supplierRepository.deleteSupplier(supplier.id);

                  if (result > 0 && mounted) {
                    // Reload suppliers from database
                    _loadSuppliers();

                    // Show success message
                    _showSnackBar('تم حذف المورد ${supplier.name}', isError: true);
                  } else if (mounted) {
                    _showSnackBar('فشل حذف المورد', isError: true);
                  }
                } catch (e) {
                  if (mounted) {
                    _showSnackBar('حدث خطأ أثناء حذف المورد: $e', isError: true);
                  }
                }
              },
              child: const Text(
                'حذف',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الموردين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث عن مورد...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              textDirection: TextDirection.rtl,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          if (_selectedCategory != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              child: Row(
                children: [
                  const Text('الفئة: '),
                  Chip(
                    label: Text(Supplier.getCategoryName(_selectedCategory!)),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      setState(() {
                        _selectedCategory = null;
                      });
                    },
                  ),
                ],
              ),
            ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredSuppliers.isEmpty
                    ? const Center(
                        child: Text(
                          'لا يوجد موردين',
                          style: AppTextStyles.heading3,
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadSuppliers,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          itemCount: _filteredSuppliers.length,
                          itemBuilder: (context, index) {
                            final supplier = _filteredSuppliers[index];
                            return SupplierCard(
                              supplier: supplier,
                              onTap: () {
                                _showSupplierActions(context, supplier);
                              },
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddSupplierDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}
