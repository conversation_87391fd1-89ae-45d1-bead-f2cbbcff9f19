import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/constants.dart';
import '../../../shared/models/supplier.dart';

class EditSupplierScreen extends StatefulWidget {
  final Supplier? supplier;

  const EditSupplierScreen({
    super.key,
    this.supplier,
  });

  @override
  State<EditSupplierScreen> createState() => _EditSupplierScreenState();
}

class _EditSupplierScreenState extends State<EditSupplierScreen> {
  final _formKey = GlobalKey<FormState>();
  late String _name;
  late String? _email;
  late String? _phone;
  late String? _address;
  late String? _contactPerson;
  late SupplierCategory _category;
  late bool _isActive;
  late double _balance;
  late String? _notes;
  late String? _taxNumber;
  late String? _website;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.supplier != null) {
      // Edit mode
      _name = widget.supplier!.name;
      _email = widget.supplier!.email;
      _phone = widget.supplier!.phone;
      _address = widget.supplier!.address;
      _contactPerson = widget.supplier!.contactPerson;
      _category = widget.supplier!.category;
      _isActive = widget.supplier!.isActive;
      _balance = widget.supplier!.balance;
      _notes = widget.supplier!.notes;
      _taxNumber = widget.supplier!.taxNumber;
      _website = widget.supplier!.website;
    } else {
      // Add mode
      _name = '';
      _email = '';
      _phone = '';
      _address = '';
      _contactPerson = '';
      _category = SupplierCategory.parts;
      _isActive = true;
      _balance = 0;
      _notes = '';
      _taxNumber = '';
      _website = '';
    }
  }

  void _saveSupplier() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          if (widget.supplier != null) {
            // Update existing supplier
            final updatedSupplier = widget.supplier!.copyWith(
              name: _name,
              email: _email,
              phone: _phone,
              address: _address,
              contactPerson: _contactPerson,
              category: _category,
              isActive: _isActive,
              balance: _balance,
              notes: _notes,
              taxNumber: _taxNumber,
              website: _website,
            );

            Navigator.pop(context, updatedSupplier);
          } else {
            // Create new supplier
            final newSupplier = Supplier(
              id: DateTime.now().millisecondsSinceEpoch,
              name: _name,
              email: _email,
              phone: _phone,
              address: _address,
              contactPerson: _contactPerson,
              category: _category,
              isActive: _isActive,
              balance: _balance,
              createdAt: DateTime.now(),
              notes: _notes,
              taxNumber: _taxNumber,
              website: _website,
            );

            Navigator.pop(context, newSupplier);
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditMode = widget.supplier != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditMode ? 'تعديل بيانات المورد' : 'إضافة مورد جديد'),
        actions: [
          TextButton.icon(
            onPressed: _isLoading ? null : _saveSupplier,
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic information
                    const Text(
                      'المعلومات الأساسية',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      initialValue: _name,
                      decoration: const InputDecoration(
                        labelText: 'اسم المورد',
                        prefixIcon: Icon(Icons.business),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم المورد';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        _name = value!;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    DropdownButtonFormField<SupplierCategory>(
                      decoration: const InputDecoration(
                        labelText: 'الفئة',
                        prefixIcon: Icon(Icons.category),
                        border: OutlineInputBorder(),
                      ),
                      value: _category,
                      items: SupplierCategory.values.map((category) {
                        return DropdownMenuItem<SupplierCategory>(
                          value: category,
                          child: Row(
                            children: [
                              Icon(
                                Supplier.getCategoryIcon(category),
                                size: 16,
                                color: Supplier.getCategoryColor(category),
                              ),
                              const SizedBox(width: 8),
                              Text(Supplier.getCategoryName(category)),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _category = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    SwitchListTile(
                      title: const Text('نشط'),
                      value: _isActive,
                      activeColor: AppColors.primary,
                      contentPadding: EdgeInsets.zero,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _balance.toString(),
                      decoration: const InputDecoration(
                        labelText: 'الرصيد الحالي',
                        prefixIcon: Icon(Icons.account_balance_wallet),
                        suffixText: 'ر.س',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      onSaved: (value) {
                        _balance = double.tryParse(value!) ?? 0;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Contact information
                    const Text(
                      'معلومات الاتصال',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      initialValue: _email,
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        prefixIcon: Icon(Icons.email),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      onSaved: (value) {
                        _email = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _phone,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        prefixIcon: Icon(Icons.phone),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                      onSaved: (value) {
                        _phone = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _address,
                      decoration: const InputDecoration(
                        labelText: 'العنوان',
                        prefixIcon: Icon(Icons.location_on),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      onSaved: (value) {
                        _address = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _contactPerson,
                      decoration: const InputDecoration(
                        labelText: 'الشخص المسؤول',
                        prefixIcon: Icon(Icons.person),
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _contactPerson = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Additional information
                    const Text(
                      'معلومات إضافية',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      initialValue: _website,
                      decoration: const InputDecoration(
                        labelText: 'الموقع الإلكتروني',
                        prefixIcon: Icon(Icons.language),
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _website = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _taxNumber,
                      decoration: const InputDecoration(
                        labelText: 'الرقم الضريبي',
                        prefixIcon: Icon(Icons.receipt),
                        border: OutlineInputBorder(),
                      ),
                      onSaved: (value) {
                        _taxNumber = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    TextFormField(
                      initialValue: _notes,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات',
                        prefixIcon: Icon(Icons.note),
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onSaved: (value) {
                        _notes = value!.isEmpty ? null : value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),

                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveSupplier,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          isEditMode ? 'حفظ التغييرات' : 'إضافة المورد',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
