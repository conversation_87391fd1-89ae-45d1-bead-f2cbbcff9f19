خطة قاعدة بيانات SQLite محسنة لتطبيق إدارة خدمات التكييف والتبريد

تهدف هذه الخطة إلى وصف هيكل قاعدة البيانات المستخدمة في التطبيق، مع التركيز على أفضل الممارسات لضمان سلامة البيانات، تقليل التكرار، وتحسين الأداء. تعتمد قاعدة البيانات على SQLite ويتم إدارتها باستخدام حزمة sqflite في Flutter.

1.  الجداول الرئيسية والعلاقات (هيكل محسّن):

    *   **جدول المستخدمين (users):**
        -   يحتوي على معلومات المستخدمين الذين يمكنهم الوصول إلى التطبيق.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name` (TEXT NOT NULL)
            -   `email` (TEXT UNIQUE)
            -   `password` (TEXT NOT NULL)
            -   `phone` (TEXT)
            -   `avatar` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'active') - يمكن أن يكون 'active', 'inactive', 'suspended'
            -   `created_at` (INTEGER NOT NULL) - Unix timestamp
            -   `last_login` (INTEGER) - Unix timestamp
            -   `permissions` (TEXT) - يمكن تخزينها كـ JSON أو قائمة مفصولة بفواصل
        -   العلاقات:
            -   علاقة متعدد لمتعدد مع جدول الأدوار (roles) من خلال جدول وسيط (user_roles).
            -   مفتاح أجنبي في جداول أخرى (مثل salaries, withdrawals, reports) للإشارة إلى المستخدم الذي قام بإنشاء أو الموافقة على سجل معين.

    *   **جدول الأدوار (roles):**
        -   يحتوي على الأدوار المختلفة للمستخدمين (مثل admin, employee, manager).
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name` (TEXT NOT NULL UNIQUE)
        -   العلاقات:
            -   علاقة متعدد لمتعدد مع جدول المستخدمين (users) من خلال جدول وسيط (user_roles).

    *   **جدول أدوار المستخدمين (user_roles):**
        -   جدول وسيط لربط المستخدمين بالأدوار.
        -   الأعمدة:
            -   `user_id` (INTEGER NOT NULL)
            -   `role_id` (INTEGER NOT NULL)
        -   المفتاح الأساسي مركب من (`user_id`, `role_id`).
        -   القيود:
            -   FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
            -   FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE

    *   **جدول العملاء (customers):**
        -   يحتوي على معلومات العملاء.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name` (TEXT NOT NULL)
            -   `type` (TEXT) - يمكن أن يكون 'individual', 'company'
            -   `email` (TEXT)
            -   `phone` (TEXT)
            -   `address` (TEXT)
            -   `contact_person` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'active')
            -   `created_at` (INTEGER NOT NULL)
            -   `notes` (TEXT)
        -   العلاقات:
            -   مفتاح أجنبي في جدول المعاملات (transactions) للإشارة إلى العميل المرتبط بالإيراد.
            -   مفتاح أجنبي في جدول طلبات الخدمة (service_requests) للإشارة إلى العميل الذي قدم الطلب.
            -   مفتاح أجنبي في جدول الفواتير (invoices) للإشارة إلى العميل المرتبط بالفاتورة.

    *   **جدول الموردين (suppliers):**
        -   يحتوي على معلومات الموردين.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name` (TEXT NOT NULL)
            -   `email` (TEXT)
            -   `phone` (TEXT)
            -   `address` (TEXT)
            -   `contact_person` (TEXT)
            -   `category` (TEXT) - يمكن استبدالها بـ `category_id` إذا كانت هناك فئات موردين محددة
            -   `status` (TEXT NOT NULL DEFAULT 'active')
            -   `balance` (REAL NOT NULL DEFAULT 0.0)
            -   `created_at` (INTEGER NOT NULL)
            -   `tax_number` (TEXT)
            -   `website` (TEXT)
            -   `notes` (TEXT)
        -   العلاقات:
            -   مفتاح أجنبي في جدول المعاملات (transactions) للإشارة إلى المورد المرتبط بالإيراد.
            -   مفتاح أجنبي في جدول عناصر المخزون (inventory_items) للإشارة إلى المورد الذي يوفر العنصر.

    *   **جدول الموظفين (employees):**
        -   يحتوي على معلومات الموظفين.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name` (TEXT NOT NULL)
            -   `email` (TEXT UNIQUE)
            -   `phone` (TEXT)
            -   `address` (TEXT)
            -   `position` (TEXT)
            -   `join_date` (INTEGER) - Unix timestamp
            -   `salary` (REAL)
            -   `status` (TEXT NOT NULL DEFAULT 'active')
            -   `national_id` (TEXT UNIQUE)
            -   `bank_account` (TEXT)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
            -   `notes` (TEXT)
        -   العلاقات:
            -   مفتاح أجنبي في جدول المعاملات (transactions) للإشارة إلى الموظف المرتبط بالإيراد.
            -   مفتاح أجنبي في جدول طلبات الخدمة (service_requests) للإشارة إلى الموظف المكلف بالطلب.
            -   مفتاح أجنبي في جدول الرواتب (salaries) للإشارة إلى الموظف الذي يتقاضى الراتب.
            -   مفتاح أجنبي في جدول السحوبات (withdrawals) للإشارة إلى الموظف الذي قام بالسحب.

    *   **جدول الحسابات البنكية (bank_accounts):**
        -   يحتوي على معلومات الحسابات البنكية للشركة.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `bank_name` (TEXT NOT NULL)
            -   `account_number` (TEXT NOT NULL UNIQUE)
            -   `account_name` (TEXT NOT NULL)
            -   `type` (TEXT) - يمكن أن يكون 'checking', 'savings', 'credit card'
            -   `iban` (TEXT)
            -   `swift_code` (TEXT)
            -   `branch_name` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'active')
            -   `balance` (REAL NOT NULL DEFAULT 0.0)
            -   `created_at` (INTEGER NOT NULL)
        -   العلاقات:
            -   مفتاح أجنبي في جدول المعاملات (transactions) للإشارة إلى الحساب البنكي المستخدم في الإيراد.

    *   **جدول الفئات (categories):**
        -   يحتوي على فئات مختلفة (مثل فئات الدخل، فئات المصروفات، فئات المخزون).
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name` (TEXT NOT NULL UNIQUE)
            -   `type` (TEXT NOT NULL) - يمكن أن يكون 'income', 'expense', 'inventory', 'supplier'
            -   `description` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'active')
            -   `created_at` (INTEGER NOT NULL)
            -   `parent_id` (INTEGER) - للمساعدة في بناء فئات هرمية (اختياري)
        -   القيود:
            -   FOREIGN KEY (`parent_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL (إذا كان هرمياً)
        -   العلاقات:
            -   مفتاح أجنبي في جدول المعاملات (transactions) وجدول عناصر المخزون (inventory_items).

    *   **جدول المعاملات (transactions):**
        -   يسجل جميع المعاملات المالية (دخل ومصروفات).
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `reference` (TEXT UNIQUE) - رقم مرجعي للإيراد
            -   `date` (INTEGER NOT NULL) - Unix timestamp
            -   `amount` (REAL NOT NULL)
            -   `type` (TEXT NOT NULL) - يمكن أن يكون 'income', 'expense'
            -   `category_id` (INTEGER) - مفتاح أجنبي يشير إلى `categories.id`
            -   `description` (TEXT)
            -   `payment_method` (TEXT) - مثل 'cash', 'bank transfer', 'cheque'
            -   `bank_account_id` (INTEGER) - مفتاح أجنبي يشير إلى `bank_accounts.id`
            -   `supplier_id` (INTEGER) - مفتاح أجنبي يشير إلى `suppliers.id` (يمكن أن يكون NULL)
            -   `employee_id` (INTEGER) - مفتاح أجنبي يشير إلى `employees.id` (يمكن أن يكون NULL)
            -   `customer_id` (INTEGER) - مفتاح أجنبي يشير إلى `customers.id` (يمكن أن يكون NULL)
            -   `created_at` (INTEGER NOT NULL)
        -   القيود:
            -   FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL
            -   FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts`(`id`) ON DELETE SET NULL
            -   FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON DELETE SET NULL
            -   FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE SET NULL
            -   FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`date`)
            -   INDEX (`type`)
            -   INDEX (`category_id`)
            -   INDEX (`bank_account_id`)
            -   INDEX (`supplier_id`)
            -   INDEX (`employee_id`)
            -   INDEX (`customer_id`)

    *   **جدول طلبات الخدمة (service_requests):**
        -   يسجل طلبات الخدمة من العملاء.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `request_number` (TEXT NOT NULL UNIQUE)
            -   `customer_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `customers.id`
            -   `service_type` (TEXT)
            -   `description` (TEXT)
            -   `address` (TEXT)
            -   `scheduled_date` (INTEGER) - Unix timestamp
            -   `status` (TEXT NOT NULL DEFAULT 'pending') - مثل 'pending', 'in progress', 'completed', 'cancelled'
            -   `assigned_to` (INTEGER) - مفتاح أجنبي يشير إلى `employees.id` (يمكن أن يكون NULL)
            -   `completion_date` (INTEGER) - Unix timestamp
            -   `notes` (TEXT)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   القيود:
            -   FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE CASCADE
            -   FOREIGN KEY (`assigned_to`) REFERENCES `employees`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`customer_id`)
            -   INDEX (`status`)
            -   INDEX (`assigned_to`)
            -   INDEX (`scheduled_date`)

    *   **جدول الفواتير (invoices):**
        -   يسجل الفواتير الصادرة للعملاء.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `invoice_number` (TEXT NOT NULL UNIQUE)
            -   `customer_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `customers.id`
            -   `date` (INTEGER NOT NULL) - Unix timestamp
            -   `due_date` (INTEGER) - Unix timestamp
            -   `subtotal` (REAL NOT NULL)
            -   `tax` (REAL NOT NULL DEFAULT 0.0)
            -   `discount` (REAL NOT NULL DEFAULT 0.0)
            -   `total` (REAL NOT NULL)
            -   `notes` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'unpaid') - مثل 'unpaid', 'paid', 'partially paid', 'cancelled'
            -   `payment_date` (INTEGER) - Unix timestamp
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   القيود:
            -   FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE CASCADE
        -   العلاقات:
            -   علاقة واحد لمتعدد مع جدول عناصر الفاتورة (invoice_items).
        -   الفهارس:
            -   INDEX (`customer_id`)
            -   INDEX (`date`)
            -   INDEX (`status`)

    *   **جدول عناصر الفاتورة (invoice_items):**
        -   يسجل تفاصيل العناصر أو الخدمات في كل فاتورة.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `invoice_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `invoices.id`
            -   `description` (TEXT NOT NULL)
            -   `quantity` (REAL NOT NULL)
            -   `unit_price` (REAL NOT NULL)
            -   `total` (REAL NOT NULL)
            -   `created_at` (INTEGER NOT NULL)
        -   القيود:
            -   FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`id`) ON DELETE CASCADE
        -   الفهارس:
            -   INDEX (`invoice_id`)

    *   **جدول معلومات الشركة (company_info):**
        -   يحتوي على معلومات الشركة.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `name_ar` (TEXT NOT NULL)
            -   `name_en` (TEXT)
            -   `address_ar` (TEXT)
            -   `address_en` (TEXT)
            -   `phone_ar` (TEXT)
            -   `phone_en` (TEXT)
            -   `email` (TEXT)
            -   `website` (TEXT)
            -   `tax_number` (TEXT)
            -   `commercial_register` (TEXT)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   العلاقات: لا توجد علاقات مفتاح أجنبي مع جداول أخرى (عادةً ما يكون هناك صف واحد فقط في هذا الجدول).

    *   **جدول عناصر المخزون (inventory_items):**
        -   يسجل عناصر المخزون المتوفرة.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `code` (TEXT UNIQUE)
            -   `name` (TEXT NOT NULL)
            -   `description` (TEXT)
            -   `category_id` (INTEGER) - مفتاح أجنبي يشير إلى `categories.id` (لفئات المخزون)
            -   `quantity` (REAL NOT NULL DEFAULT 0.0)
            -   `min_quantity` (REAL NOT NULL DEFAULT 0.0)
            -   `unit` (TEXT) - مثل 'piece', 'meter', 'kg'
            -   `cost_price` (REAL)
            -   `selling_price` (REAL)
            -   `supplier_id` (INTEGER) - مفتاح أجنبي يشير إلى `suppliers.id` (يمكن أن يكون NULL)
            -   `location` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'available')
            -   `notes` (TEXT)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   القيود:
            -   FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL
            -   FOREIGN KEY (`supplier_id`) REFERENCES `suppliers`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`category_id`)
            -   INDEX (`supplier_id`)
            -   INDEX (`name`)

    *   **جدول الرواتب (salaries):**
        -   يسجل سجلات الرواتب للموظفين.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `employee_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `employees.id`
            -   `month` (INTEGER NOT NULL)
            -   `year` (INTEGER NOT NULL)
            -   `basic_salary` (REAL NOT NULL)
            -   `allowances` (REAL NOT NULL DEFAULT 0.0)
            -   `deductions` (REAL NOT NULL DEFAULT 0.0)
            -   `net_salary` (REAL NOT NULL)
            -   `paid_amount` (REAL NOT NULL DEFAULT 0.0)
            -   `status` (TEXT NOT NULL DEFAULT 'pending') - مثل 'pending', 'paid', 'partially paid'
            -   `notes` (TEXT)
            -   `created_by` (INTEGER) - مفتاح أجنبي يشير إلى `users.id` (يمكن أن يكون NULL)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   القيود:
            -   FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
            -   FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
            -   UNIQUE (`employee_id`, `month`, `year`) - لضمان عدم تكرار راتب لنفس الموظف في نفس الشهر والسنة
        -   الفهارس:
            -   INDEX (`employee_id`)
            -   INDEX (`month`, `year`)

    *   **جدول السحوبات (withdrawals):**
        -   يسجل سحوبات الموظفين (مثل السلف).
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `employee_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `employees.id`
            -   `amount` (REAL NOT NULL)
            -   `date` (INTEGER NOT NULL) - Unix timestamp
            -   `reason` (TEXT)
            -   `status` (TEXT NOT NULL DEFAULT 'pending') - مثل 'pending', 'approved', 'rejected'
            -   `notes` (TEXT)
            -   `approved_by` (INTEGER) - مفتاح أجنبي يشير إلى `users.id` (يمكن أن يكون NULL)
            -   `approved_at` (INTEGER) - Unix timestamp
            -   `created_by` (INTEGER) - مفتاح أجنبي يشير إلى `users.id` (يمكن أن يكون NULL)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   القيود:
            -   FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`) ON DELETE CASCADE
            -   FOREIGN KEY (`approved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
            -   FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`employee_id`)
            -   INDEX (`date`)
            -   INDEX (`status`)

    *   **جدول الإشعارات (notifications):**
        -   يسجل الإشعارات للمستخدمين.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `title` (TEXT NOT NULL)
            -   `message` (TEXT NOT NULL)
            -   `type` (TEXT) - مثل 'info', 'warning', 'error', 'success'
            -   `is_read` (INTEGER NOT NULL DEFAULT 0) - 0 for false, 1 for true
            -   `related_id` (INTEGER) - معرف السجل المرتبط (مثل معرف فاتورة، طلب خدمة)
            -   `related_type` (TEXT) - نوع السجل المرتبط (مثل 'invoice', 'service_request')
            -   `user_id` (INTEGER) - مفتاح أجنبي يشير إلى `users.id` (يمكن أن يكون NULL إذا كان إشعاراً عاماً)
            -   `created_at` (INTEGER NOT NULL)
        -   القيود:
            -   FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
        -   الفهارس:
            -   INDEX (`user_id`)
            -   INDEX (`is_read`)
            -   INDEX (`created_at`)

    *   **جدول التقارير (reports):**
        -   يسجل التقارير التي تم إنشاؤها.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `title` (TEXT NOT NULL)
            -   `description` (TEXT)
            -   `type` (TEXT NOT NULL) - نوع التقرير (مثل 'sales', 'expenses', 'inventory')
            -   `parameters` (TEXT) - يمكن تخزين معلمات التقرير كـ JSON
            -   `created_by` (INTEGER) - مفتاح أجنبي يشير إلى `users.id` (يمكن أن يكون NULL)
            -   `created_at` (INTEGER NOT NULL)
            -   `updated_at` (INTEGER)
        -   القيود:
            -   FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`created_by`)
            -   INDEX (`type`)
            -   INDEX (`created_at`)

2.  مكونات النظام المحاسبي المتكامل (إضافات):

    *   **جدول دليل الحسابات (chart_of_accounts):**
        -   يحتوي على هيكل الحسابات المحاسبية (الأصول، الخصوم، حقوق الملكية، الإيرادات، المصروفات).
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `account_code` (TEXT NOT NULL UNIQUE) - رقم الحساب (مثل 1000, 2100, 3000, 4000, 5000)
            -   `account_name_ar` (TEXT NOT NULL)
            -   `account_name_en` (TEXT)
            -   `account_type` (TEXT NOT NULL) - مثل 'asset', 'liability', 'equity', 'revenue', 'expense'
            -   `parent_id` (INTEGER) - مفتاح أجنبي يشير إلى حساب أب (للهيكل الهرمي)
            -   `status` (TEXT NOT NULL DEFAULT 'active')
            -   `created_at` (INTEGER NOT NULL) - Unix timestamp
            -   `updated_at` (INTEGER) - Unix timestamp
        -   القيود:
            -   FOREIGN KEY (`parent_id`) REFERENCES `chart_of_accounts`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`account_code`)
            -   INDEX (`account_type`)
            -   INDEX (`parent_id`)

    *   **جدول قيود اليومية (journal_entries):**
        -   يسجل رؤوس قيود اليومية.
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `entry_number` (TEXT NOT NULL UNIQUE) - رقم القيد
            -   `date` (INTEGER NOT NULL) - Unix timestamp
            -   `description` (TEXT)
            -   `created_by` (INTEGER) - مفتاح أجنبي يشير إلى `users.id`
            -   `created_at` (INTEGER NOT NULL) - Unix timestamp
            -   `updated_at` (INTEGER) - Unix timestamp
            -   `source_type` (TEXT) - نوع المصدر (مثل 'transaction', 'invoice', 'salary', 'withdrawal', 'manual')
            -   `source_id` (INTEGER) - معرف السجل المصدر
        -   القيود:
            -   FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        -   الفهارس:
            -   INDEX (`entry_number`)
            -   INDEX (`date`)
            -   INDEX (`source_type`, `source_id`)

    *   **جدول بنود قيد اليومية (journal_entry_items):**
        -   يسجل بنود كل قيد يومية (الجانب المدين والدائن).
        -   الأعمدة:
            -   `id` (INTEGER PRIMARY KEY AUTOINCREMENT)
            -   `journal_entry_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `journal_entries.id`
            -   `account_id` (INTEGER NOT NULL) - مفتاح أجنبي يشير إلى `chart_of_accounts.id`
            -   `debit` (REAL NOT NULL DEFAULT 0.0) - المبلغ المدين
            -   `credit` (REAL NOT NULL DEFAULT 0.0) - المبلغ الدائن
            -   `description` (TEXT)
            -   `created_at` (INTEGER NOT NULL) - Unix timestamp
        -   القيود:
            -   FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries`(`id`) ON DELETE CASCADE
            -   FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts`(`id`) ON DELETE CASCADE
        -   الفهارس:
            -   INDEX (`journal_entry_id`)
            -   INDEX (`account_id`)

    *   **ربط الجداول الحالية بالنظام المحاسبي:**
        -   عند إنشاء سجل في جداول مثل `transactions`, `invoices`, `salaries`, `withdrawals`, يجب إنشاء قيد يومية مقابل في جدول `journal_entries`.
        -   يجب أن يحتوي قيد اليومية هذا على بنود في جدول `journal_entry_items` تعكس الحركة المحاسبية (مدين ودائن) وتستخدم `account_id` من جدول `chart_of_accounts`.
        -   يمكن استخدام العمودين `source_type` و `source_id` في جدول `journal_entries` لربط القيد بالسجل الأصلي في الجداول الأخرى.

3.  إدارة قاعدة البيانات باستخدام sqflite (ملاحظات إضافية):

    -   يجب تحديث فئة `DatabaseHelper` لتعكس التغييرات في هيكل الجداول والأعمدة والقيود.
    -   يجب التأكد من أن دالة `_onCreate` تنشئ الجداول بالهيكل الجديد والقيود المحددة.
    -   يجب التعامل مع ترقيات قاعدة البيانات في دالة `_onUpgrade` إذا كان هناك إصدار سابق لقاعدة البيانات قيد الاستخدام. يتضمن ذلك إضافة أعمدة جديدة، تعديل أنواع الأعمدة (إذا لزم الأمر)، وإضافة قيود جديدة.
    -   يجب استخدام معاملات قاعدة البيانات (transactions) للعمليات التي تتضمن تعديلات متعددة لضمان سلامة البيانات (ACID properties).
    -   يجب استخدام استعلامات SQL المعدة مسبقاً (prepared statements) لمنع هجمات حقن SQL.

هذه الخطة المحسنة توفر هيكلاً أكثر قوة واحترافية لقاعدة بيانات SQLite، مع تحسينات في التطبيع، القيود، والفهارس لضمان أداء أفضل وسلامة أعلى للبيانات.

    -   يجب تحديث فئة `DatabaseHelper` لتعكس التغييرات في هيكل الجداول والأعمدة والقيود.
    -   يجب التأكد من أن دالة `_onCreate` تنشئ الجداول بالهيكل الجديد والقيود المحددة.
    -   يجب التعامل مع ترقيات قاعدة البيانات في دالة `_onUpgrade` إذا كان هناك إصدار سابق لقاعدة البيانات قيد الاستخدام. يتضمن ذلك إضافة أعمدة جديدة، تعديل أنواع الأعمدة (إذا لزم الأمر)، وإضافة قيود جديدة.
    -   يجب استخدام معاملات قاعدة البيانات (transactions) للعمليات التي تتضمن تعديلات متعددة لضمان سلامة البيانات (ACID properties).
    -   يجب استخدام استعلامات SQL المعدة مسبقاً (prepared statements) لمنع هجمات حقن SQL.

هذه الخطة المحسنة توفر هيكلاً أكثر قوة واحترافية لقاعدة بيانات SQLite، مع تحسينات في التطبيع، القيود، والفهارس لضمان أداء أفضل وسلامة أعلى للبيانات.
