import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageSelectionScreen extends StatefulWidget {
  final Function(Locale) onLanguageChanged;

  const LanguageSelectionScreen({Key? key, required this.onLanguageChanged}) : super(key: key);

  @override
  State<LanguageSelectionScreen> createState() => _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  String? _selectedLanguage;
  

  @override
  void initState() {
    super.initState();
    _loadPreferredLanguage();
  }

  Future<void> _loadPreferredLanguage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedLanguage = prefs.getString('language') ?? 'en';
    });
  }

  Future<void> _savePreferredLanguage(String language) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', language);
    setState(() {
      _selectedLanguage = language;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختيار اللغة'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                if (value != null) {
                  _savePreferredLanguage(value);
                  widget.onLanguageChanged(Locale(value, 'en'));
                }
              },
            ),
            RadioListTile<String>(
              value: 'ar',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                if (value != null) {
                  _savePreferredLanguage(value);
                  widget.onLanguageChanged(Locale(value, 'العربية'));
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
