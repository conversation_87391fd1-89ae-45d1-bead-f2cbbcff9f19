import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/invoice.dart';

class InvoiceCard extends StatelessWidget {
  final Invoice invoice;
  final VoidCallback? onTap;

  const InvoiceCard({
    super.key,
    required this.invoice,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final bool isUnpaid = !invoice.isPaid && invoice.status != InvoiceStatus.cancelled;
    final bool isOverdue = _isOverdue(invoice);

    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: isUnpaid ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        side: isOverdue
            ? const BorderSide(color: Colors.red, width: 2)
            : isUnpaid
                ? const BorderSide(color: AppColors.primary, width: 1.5)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            gradient: isOverdue
                ? LinearGradient(
                    colors: [Colors.red.withOpacity(0.05), Colors.white],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : isUnpaid
                    ? LinearGradient(
                        colors: [AppColors.primary.withOpacity(0.05), Colors.white],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // مؤشر للفواتير غير المدفوعة
                if (isUnpaid) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                    decoration: BoxDecoration(
                      color: isOverdue ? Colors.red : AppColors.primary,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(AppDimensions.radiusM),
                        topRight: Radius.circular(AppDimensions.radiusM),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isOverdue ? Icons.warning : Icons.payment,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isOverdue ? 'متأخرة عن الموعد' : 'غير مدفوعة',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        if (isOverdue) ...[
                          Text(
                            'متأخرة ${_getDaysOverdue()} يوم',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: AppDimensions.paddingS),
                ],
                Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Invoice.getStatusColor(invoice.status).withAlpha(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                    child: Center(
                      child: Icon(
                        _getStatusIcon(invoice.status),
                        color: Invoice.getStatusColor(invoice.status),
                        size: 28,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invoice.invoiceNumber,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          invoice.customer.name,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        if (invoice.items.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            _getItemsDescription(),
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary.withOpacity(0.8),
                              fontStyle: FontStyle.italic,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Invoice.getStatusColor(invoice.status).withAlpha(25),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      border: Border.all(
                        color: Invoice.getStatusColor(invoice.status),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      Invoice.getStatusName(invoice.status),
                      style: TextStyle(
                        fontSize: 12,
                        color: Invoice.getStatusColor(invoice.status),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Divider(height: 1),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'تاريخ الإصدار',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        DateFormat('dd/MM/yyyy').format(invoice.issueDate),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'تاريخ الاستحقاق',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        DateFormat('dd/MM/yyyy').format(invoice.dueDate),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _isOverdue(invoice) ? Colors.red : null,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'الإجمالي',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '${invoice.total} ر.س',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ), ),
    );
  }

  String _getItemsDescription() {
    if (invoice.items.isEmpty) return '';

    if (invoice.items.length == 1) {
      final item = invoice.items.first;
      return item.description?.isNotEmpty == true
          ? item.description!
          : item.name;
    } else {
      final firstItem = invoice.items.first;
      final itemName = firstItem.description?.isNotEmpty == true
          ? firstItem.description!
          : firstItem.name;
      return '$itemName و ${invoice.items.length - 1} عنصر آخر';
    }
  }

  int _getDaysOverdue() {
    if (!_isOverdue(invoice)) return 0;
    return DateTime.now().difference(invoice.dueDate).inDays;
  }

  IconData _getStatusIcon(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Icons.edit_document;
      case InvoiceStatus.issued:
        return Icons.receipt_long;
      case InvoiceStatus.paid:
        return Icons.check_circle;
      case InvoiceStatus.partiallyPaid:
        return Icons.payments;
      case InvoiceStatus.overdue:
        return Icons.warning;
      case InvoiceStatus.cancelled:
        return Icons.cancel;
    }
  }

  bool _isOverdue(Invoice invoice) {
    if (invoice.status == InvoiceStatus.paid ||
        invoice.status == InvoiceStatus.cancelled) {
      return false;
    }

    return invoice.dueDate.isBefore(DateTime.now());
  }
}
