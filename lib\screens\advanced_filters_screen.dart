import 'package:flutter/material.dart';

class AdvancedFiltersScreen extends StatefulWidget {
  const AdvancedFiltersScreen({Key? key}) : super(key: key);

  @override
  State<AdvancedFiltersScreen> createState() => _AdvancedFiltersScreenState();
}

class _AdvancedFiltersScreenState extends State<AdvancedFiltersScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Advanced Filters'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Budget',
                border: OutlineInputBorder(),
              ),
            ),
            const Si<PERSON>Box(height: 16),
            TextForm<PERSON>ield(
              decoration: const InputDecoration(
                labelText: 'Dates',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Implement apply filters logic
              },
              child: const Text('Apply Filters'),
            ),
          ],
        ),
      ),
    );
  }
}
