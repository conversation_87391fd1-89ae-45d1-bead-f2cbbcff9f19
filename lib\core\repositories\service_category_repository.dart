import 'package:flutter/foundation.dart';
import '../../shared/models/service_category.dart';
import '../database/database_helper.dart';

class ServiceCategoryRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all service categories
  Future<List<ServiceCategory>> getAllServiceCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_categories',
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return ServiceCategory.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service categories: $e');
      }
      return [];
    }
  }

  // Get active service categories
  Future<List<ServiceCategory>> getActiveServiceCategories() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_categories',
        where: 'status = ?',
        whereArgs: ['active'],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return ServiceCategory.fromMap(maps[i]);
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting active service categories: $e');
      }
      return [];
    }
  }

  // Get service category by ID
  Future<ServiceCategory?> getServiceCategoryById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'service_categories',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return ServiceCategory.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting service category by ID: $e');
      }
      return null;
    }
  }

  // Insert a new service category
  Future<int> insertServiceCategory(ServiceCategory category) async {
    try {
      final db = await _dbHelper.database;
      final Map<String, dynamic> categoryMap = {
        'name': category.name,
        'description': category.description,
        'icon_code': category.icon.codePoint,
        'color': category.color.value,
        'status': category.isActive ? 'active' : 'inactive',
        'created_at': DateTime.now().toIso8601String(),
      };

      return await db.insert('service_categories', categoryMap);
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting service category: $e');
      }
      return -1;
    }
  }

  // Update an existing service category
  Future<int> updateServiceCategory(ServiceCategory category) async {
    try {
      final db = await _dbHelper.database;
      final Map<String, dynamic> categoryMap = {
        'name': category.name,
        'description': category.description,
        'icon_code': category.icon.codePoint,
        'color': category.color.value,
        'status': category.isActive ? 'active' : 'inactive',
        'updated_at': DateTime.now().toIso8601String(),
      };

      return await db.update(
        'service_categories',
        categoryMap,
        where: 'id = ?',
        whereArgs: [category.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating service category: $e');
      }
      return -1;
    }
  }

  // Delete a service category
  Future<int> deleteServiceCategory(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'service_categories',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting service category: $e');
      }
      return -1;
    }
  }

  // Toggle service category status (active/inactive)
  Future<int> toggleServiceCategoryStatus(int id, bool isActive) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'service_categories',
        {
          'status': isActive ? 'active' : 'inactive',
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error toggling service category status: $e');
      }
      return -1;
    }
  }
}
