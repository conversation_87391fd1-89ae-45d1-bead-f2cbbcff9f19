import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/models/invoice.dart';
import '../../../shared/models/invoice_item.dart';
import '../widgets/invoice_item_form.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/customer_repository.dart';

class AddInvoiceScreen extends StatefulWidget {
  const AddInvoiceScreen({super.key});

  @override
  State<AddInvoiceScreen> createState() => _AddInvoiceScreenState();
}

class _AddInvoiceScreenState extends State<AddInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  Customer? _selectedCustomer;
  final List<InvoiceItem> _items = [];
  DateTime _issueDate = DateTime.now();
  DateTime _dueDate = DateTime.now().add(const Duration(days: 15));
  final double _taxRate = 0.15; // 15% VAT
  double _discount = 0;
  String? _notes;
  bool _isLoading = true;
  List<Customer> _customers = [];

  final CustomerRepository _customerRepository = CustomerRepository();
  final InvoiceRepository _invoiceRepository = InvoiceRepository();

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  Future<void> _loadCustomers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام CustomerRepository للحصول على العملاء من قاعدة البيانات
      final customers = await _customerRepository.getAllCustomers();

      if (mounted) {
        setState(() {
          _customers = customers;
          _isLoading = false;
        });
      }

      // إذا لم يكن هناك عملاء، استخدم بيانات وهمية للعرض
      if (customers.isEmpty && mounted) {
        setState(() {
          _customers = [
            Customer(
              localId: 1,
              name: 'شركة الرياض للتكييف',
              email: '<EMAIL>',
              phone: '0112345678',
              type: CustomerType.company,
              createdAt: DateTime.now(),
            ),
            Customer(
              localId: 2,
              name: 'أحمد محمد',
              email: '<EMAIL>',
              phone: '0501234567',
              type: CustomerType.individual,
              createdAt: DateTime.now(),
            ),
          ];
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading customers: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;

          // في حالة حدوث خطأ، استخدم بيانات وهمية للعرض
          _customers = [
            Customer(
              localId: 1,
              name: 'شركة الرياض للتكييف',
              email: '<EMAIL>',
              phone: '0112345678',
              type: CustomerType.company,
              createdAt: DateTime.now(),
            ),
            Customer(
              localId: 2,
              name: 'أحمد محمد',
              email: '<EMAIL>',
              phone: '0501234567',
              type: CustomerType.individual,
              createdAt: DateTime.now(),
            ),
          ];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل بيانات العملاء: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _addItem() {
    setState(() {
      _items.add(
        InvoiceItem(
          id: _items.length + 1,
          name: '',
          description: '',
          quantity: 1,
          unitPrice: 0,
          taxRate: _taxRate,
          discount: 0,
        ),
      );
    });
  }

  void _updateItem(int index, InvoiceItem updatedItem) {
    setState(() {
      _items[index] = updatedItem;
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  double get _subtotal {
    return _items.fold(0.0, (sum, item) => sum + item.subtotal);
  }

  double get _taxAmount {
    return _items.fold(0.0, (sum, item) => sum + item.taxAmount);
  }

  double get _total {
    return _subtotal + _taxAmount - _discount;
  }

  Future<void> _selectDate(BuildContext context, bool isIssueDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isIssueDate ? _issueDate : _dueDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isIssueDate) {
          _issueDate = picked;
          // Adjust due date if needed
          if (_dueDate.isBefore(_issueDate)) {
            _dueDate = _issueDate.add(const Duration(days: 15));
          }
        } else {
          _dueDate = picked;
        }
      });
    }
  }

  Future<void> _saveInvoice() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      if (_selectedCustomer == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار العميل'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_items.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى إضافة عنصر واحد على الأقل'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Validate all items
      for (int i = 0; i < _items.length; i++) {
        if (_items[i].name.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('يرجى إدخال اسم العنصر ${i + 1}'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
        if (_items[i].unitPrice <= 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('يرجى إدخال سعر صحيح للعنصر ${i + 1}'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      // عرض مؤشر التحميل
      setState(() {
        _isLoading = true;
      });

      try {
        // توليد رقم فاتورة فريد
        final invoiceRepository = InvoiceRepository();
        final invoiceNumber = await invoiceRepository.generateUniqueInvoiceNumber();

        // إنشاء كائن الفاتورة
        final invoice = Invoice(
          invoiceNumber: invoiceNumber,
          customer: _selectedCustomer!,
          issueDate: _issueDate,
          dueDate: _dueDate,
          items: _items,
          status: InvoiceStatus.draft,
          subtotal: _subtotal,
          taxRate: _taxRate,
          taxAmount: _taxAmount,
          discount: _discount,
          total: _total,
          notes: _notes,
          createdAt: DateTime.now(),
        );

        // حفظ الفاتورة في قاعدة البيانات
        final result = await _invoiceRepository.insertInvoice(invoice);

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          if (result > 0) {
            // تم الحفظ بنجاح
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حفظ الفاتورة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );

            // العودة للشاشة السابقة
            Navigator.pop(context);
          } else {
            // فشل في الحفظ
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في حفظ الفاتورة'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error saving invoice: $e');
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء حفظ الفاتورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء فاتورة جديدة'),
        actions: [
          TextButton.icon(
            onPressed: _saveInvoice,
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer selection
                    const Text(
                      'معلومات العميل',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    DropdownButtonFormField<Customer>(
                      decoration: const InputDecoration(
                        labelText: 'اختر العميل',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                      items: _customers.map((customer) {
                        return DropdownMenuItem<Customer>(
                          value: customer,
                          child: Text(customer.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCustomer = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار العميل';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Invoice dates
                    const Text(
                      'تواريخ الفاتورة',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectDate(context, true),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'تاريخ الإصدار',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.calendar_today),
                              ),
                              child: Text(
                                DateFormat('dd/MM/yyyy').format(_issueDate),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingM),
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectDate(context, false),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'تاريخ الاستحقاق',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.event),
                              ),
                              child: Text(
                                DateFormat('dd/MM/yyyy').format(_dueDate),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Invoice items
                    const Text(
                      'عناصر الفاتورة',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Container(
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      child: TextButton.icon(
                        onPressed: _addItem,
                        icon: const Icon(Icons.add, color: AppColors.primary),
                        label: const Text(
                          'إضافة عنصر',
                          style: TextStyle(color: AppColors.primary),
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    if (_items.isEmpty)
                      const Card(
                        child: Padding(
                          padding: EdgeInsets.all(AppDimensions.paddingM),
                          child: Center(
                            child: Text(
                              'لا توجد عناصر. اضغط على "إضافة عنصر" لإضافة عناصر للفاتورة.',
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _items.length,
                        itemBuilder: (context, index) {
                          return InvoiceItemForm(
                            item: _items[index],
                            onUpdate: (item) => _updateItem(index, item),
                            onRemove: () => _removeItem(index),
                          );
                        },
                      ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Invoice totals
                    const Text(
                      'ملخص الفاتورة',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          children: [
                            _buildSummaryRow('المجموع الفرعي', '${_subtotal.toStringAsFixed(2)} ر.س'),
                            const SizedBox(height: AppDimensions.paddingS),
                            _buildSummaryRow('ضريبة القيمة المضافة (${(_taxRate * 100).toStringAsFixed(0)}%)', '${_taxAmount.toStringAsFixed(2)} ر.س'),
                            const SizedBox(height: AppDimensions.paddingS),
                            TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'الخصم',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.discount),
                                suffixText: 'ر.س',
                              ),
                              keyboardType: TextInputType.number,
                              initialValue: _discount.toString(),
                              onChanged: (value) {
                                setState(() {
                                  _discount = double.tryParse(value) ?? 0;
                                });
                              },
                            ),
                            const SizedBox(height: AppDimensions.paddingM),
                            const Divider(),
                            _buildSummaryRow(
                              'الإجمالي',
                              '${_total.toStringAsFixed(2)} ر.س',
                              isTotal: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Notes
                    const Text(
                      'ملاحظات',
                      style: AppTextStyles.heading3,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات الفاتورة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.note),
                      ),
                      maxLines: 3,
                      onChanged: (value) {
                        _notes = value;
                      },
                    ),
                    const SizedBox(height: AppDimensions.paddingL),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: ElevatedButton(
          onPressed: _saveInvoice,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
          ),
          child: const Text(
            'حفظ الفاتورة',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            fontSize: isTotal ? 16 : 14,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: isTotal ? 18 : 14,
            color: isTotal ? AppColors.primary : null,
          ),
        ),
      ],
    );
  }
}
