import 'dart:convert';
import 'sync_model.dart';

enum CustomerType {
  individual,
  company,
}

enum CustomerPaymentMethod {
  perService,
  monthlySubscription,
}

class Customer extends SyncModel {
  final int? _localId;
  final String name;
  final String? email;
  final String? phone;
  final String? address;
  final String? contactPerson;
  final CustomerType type;
  final String? notes;
  final double openingBalance; // الرصيد الافتتاحي

  // الحقول الجديدة
  final List<String>? preferredServiceTypes; // أنواع الخدمات المفضلة
  final CustomerPaymentMethod? paymentMethod; // طريقة الدفع
  final double? monthlySubscriptionAmount; // مبلغ الاشتراك الشهري
  final DateTime? subscriptionStartDate; // تاريخ بداية الاشتراك
  final DateTime? subscriptionEndDate; // تاريخ نهاية الاشتراك
  final bool? isSubscriptionActive; // حالة الاشتراك

  // Sync-related fields
  final String? _firestoreId;
  final DateTime _createdAt;
  final DateTime? _updatedAt;
  final DateTime? _lastSyncTime;
  final SyncStatus _syncStatus;
  final bool _isDeleted;
  final int _version;
  final String? _dataHash;

  Customer({
    int? localId,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.contactPerson,
    required this.type,
    this.notes,
    this.openingBalance = 0.0,
    required DateTime createdAt,
    DateTime? updatedAt,
    this.preferredServiceTypes,
    this.paymentMethod,
    this.monthlySubscriptionAmount,
    this.subscriptionStartDate,
    this.subscriptionEndDate,
    this.isSubscriptionActive,
    // Sync parameters
    String? firestoreId,
    DateTime? lastSyncTime,
    SyncStatus syncStatus = SyncStatus.pendingUpload,
    bool isDeleted = false,
    int version = 1,
    String? dataHash,
  }) : _localId = localId,
       _firestoreId = firestoreId,
       _createdAt = createdAt,
       _updatedAt = updatedAt,
       _lastSyncTime = lastSyncTime,
       _syncStatus = syncStatus,
       _isDeleted = isDeleted,
       _version = version,
       _dataHash = dataHash;

  // Implement SyncModel abstract getters
  @override
  String? get id => _firestoreId ?? localId?.toString();

  @override
  String? get firestoreId => _firestoreId;

  @override
  int? get localId => _localId;

  @override
  DateTime get createdAt => _createdAt;

  @override
  DateTime? get updatedAt => _updatedAt;

  @override
  DateTime? get lastSyncTime => _lastSyncTime;

  @override
  SyncStatus get syncStatus => _syncStatus;

  @override
  bool get isDeleted => _isDeleted;

  @override
  int get version => _version;

  @override
  String? get dataHash => _dataHash;

  // Backward compatibility getter
  bool get isActive => !isDeleted;

  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      localId: map['id'] as int?,
      name: map['name'] as String,
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      address: map['address'] as String?,
      contactPerson: map['contact_person'] as String?,
      type: _parseType(map['type'] as String),
      notes: map['notes'] as String?,
      openingBalance: (map['opening_balance'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      // الحقول الجديدة
      preferredServiceTypes: map['preferred_service_types'] != null
          ? List<String>.from(json.decode(map['preferred_service_types'] as String))
          : null,
      paymentMethod: map['payment_method'] != null
          ? _parsePaymentMethod(map['payment_method'] as String)
          : null,
      monthlySubscriptionAmount: map['monthly_subscription_amount'] != null
          ? (map['monthly_subscription_amount'] as num).toDouble()
          : null,
      subscriptionStartDate: map['subscription_start_date'] != null
          ? DateTime.parse(map['subscription_start_date'] as String)
          : null,
      subscriptionEndDate: map['subscription_end_date'] != null
          ? DateTime.parse(map['subscription_end_date'] as String)
          : null,
      isSubscriptionActive: map['is_subscription_active'] != null
          ? map['is_subscription_active'] as bool
          : null,
      // Sync fields
      firestoreId: map['firestore_id'] as String?,
      lastSyncTime: map['last_sync_time'] != null
          ? DateTime.parse(map['last_sync_time'] as String)
          : null,
      syncStatus: map['sync_status'] != null
          ? SyncStatusExtension.fromString(map['sync_status'] as String)
          : SyncStatus.pendingUpload,
      isDeleted: (map['is_deleted'] as int? ?? 0) == 1,
      version: map['version'] as int? ?? 1,
      dataHash: map['data_hash'] as String?,
    );
  }

  factory Customer.fromJson(String source) =>
      Customer.fromMap(json.decode(source) as Map<String, dynamic>);

  factory Customer.fromFirestore(Map<String, dynamic> data, String firestoreId) {
    return Customer(
      firestoreId: firestoreId,
      name: data['name'] as String,
      email: data['email'] as String?,
      phone: data['phone'] as String?,
      address: data['address'] as String?,
      contactPerson: data['contact_person'] as String?,
      type: _parseType(data['type'] as String? ?? 'individual'),
      notes: data['notes'] as String?,
      openingBalance: (data['opening_balance'] as num?)?.toDouble() ?? 0.0,
      createdAt: data['created_at'] != null
          ? DateTime.parse(data['created_at'] as String)
          : DateTime.now(),
      updatedAt: data['updated_at'] != null
          ? DateTime.parse(data['updated_at'] as String)
          : null,
      preferredServiceTypes: data['preferred_service_types'] != null
          ? List<String>.from(data['preferred_service_types'] as List)
          : null,
      paymentMethod: data['payment_method'] != null
          ? _parsePaymentMethod(data['payment_method'] as String)
          : null,
      monthlySubscriptionAmount: data['monthly_subscription_amount'] != null
          ? (data['monthly_subscription_amount'] as num).toDouble()
          : null,
      subscriptionStartDate: data['subscription_start_date'] != null
          ? DateTime.parse(data['subscription_start_date'] as String)
          : null,
      subscriptionEndDate: data['subscription_end_date'] != null
          ? DateTime.parse(data['subscription_end_date'] as String)
          : null,
      isSubscriptionActive: data['is_subscription_active'] as bool?,
      lastSyncTime: data['last_sync_time'] != null
          ? DateTime.parse(data['last_sync_time'] as String)
          : null,
      syncStatus: SyncStatus.synced,
      version: data['version'] as int? ?? 1,
    );
  }

  // Implement SyncModel abstract methods
  @override
  Map<String, dynamic> toSqliteMap() {
    return {
      'id': localId,
      'firestore_id': firestoreId,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'contact_person': contactPerson,
      'type': type.toString().split('.').last,
      'status': isActive ? 'active' : 'inactive',
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'preferred_service_types': preferredServiceTypes != null
          ? json.encode(preferredServiceTypes)
          : null,
      'payment_method': paymentMethod?.toString().split('.').last,
      'monthly_subscription_amount': monthlySubscriptionAmount,
      'subscription_start_date': subscriptionStartDate?.toIso8601String(),
      'subscription_end_date': subscriptionEndDate?.toIso8601String(),
      'is_subscription_active': isSubscriptionActive,
      // Sync fields
      'last_sync_time': lastSyncTime?.toIso8601String(),
      'sync_status': syncStatus.value,
      'is_deleted': isDeleted ? 1 : 0,
      'version': version,
      'data_hash': dataHash,
    };
  }

  @override
  Map<String, dynamic> toFirestoreMap() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'contact_person': contactPerson,
      'type': type.toString().split('.').last,
      'is_active': isActive,
      'notes': notes,
      'opening_balance': openingBalance,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'preferred_service_types': preferredServiceTypes,
      'payment_method': paymentMethod?.toString().split('.').last,
      'monthly_subscription_amount': monthlySubscriptionAmount,
      'subscription_start_date': subscriptionStartDate?.toIso8601String(),
      'subscription_end_date': subscriptionEndDate?.toIso8601String(),
      'is_subscription_active': isSubscriptionActive,
      // Sync metadata
      'version': version,
      'last_sync_time': lastSyncTime?.toIso8601String(),
    };
  }

  @override
  Customer copyWithSyncData({
    String? firestoreId,
    int? localId,
    DateTime? lastSyncTime,
    SyncStatus? syncStatus,
    bool? isDeleted,
    int? version,
    String? dataHash,
  }) {
    return Customer(
      localId: localId ?? this.localId,
      name: name,
      email: email,
      phone: phone,
      address: address,
      contactPerson: contactPerson,
      type: type,
      notes: notes,
      createdAt: createdAt,
      updatedAt: updatedAt,
      preferredServiceTypes: preferredServiceTypes,
      paymentMethod: paymentMethod,
      monthlySubscriptionAmount: monthlySubscriptionAmount,
      subscriptionStartDate: subscriptionStartDate,
      subscriptionEndDate: subscriptionEndDate,
      isSubscriptionActive: isSubscriptionActive,
      // Sync parameters
      firestoreId: firestoreId ?? this.firestoreId,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      syncStatus: syncStatus ?? this.syncStatus,
      isDeleted: isDeleted ?? this.isDeleted,
      version: version ?? this.version,
      dataHash: dataHash ?? this.dataHash,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'contact_person': contactPerson,
      'type': type.toString().split('.').last,
      'is_active': isActive,
      'notes': notes,
      'opening_balance': openingBalance,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      // الحقول الجديدة
      'preferred_service_types': preferredServiceTypes != null
          ? json.encode(preferredServiceTypes)
          : null,
      'payment_method': paymentMethod != null
          ? paymentMethod.toString().split('.').last
          : null,
      'monthly_subscription_amount': monthlySubscriptionAmount,
      'subscription_start_date': subscriptionStartDate?.toIso8601String(),
      'subscription_end_date': subscriptionEndDate?.toIso8601String(),
      'is_subscription_active': isSubscriptionActive,
    };
  }

  String toJson() => json.encode(toMap());

  Customer copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? contactPerson,
    CustomerType? type,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? preferredServiceTypes,
    CustomerPaymentMethod? paymentMethod,
    double? monthlySubscriptionAmount,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    bool? isSubscriptionActive,
  }) {
    return Customer(
      localId: localId ?? this.localId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      contactPerson: contactPerson ?? this.contactPerson,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      preferredServiceTypes: preferredServiceTypes ?? this.preferredServiceTypes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      monthlySubscriptionAmount: monthlySubscriptionAmount ?? this.monthlySubscriptionAmount,
      subscriptionStartDate: subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      isSubscriptionActive: isSubscriptionActive ?? this.isSubscriptionActive,
    );
  }

  static CustomerPaymentMethod _parsePaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'monthlysubscription':
        return CustomerPaymentMethod.monthlySubscription;
      case 'perservice':
      default:
        return CustomerPaymentMethod.perService;
    }
  }

  static CustomerType _parseType(String type) {
    switch (type.toLowerCase()) {
      case 'company':
        return CustomerType.company;
      case 'individual':
      default:
        return CustomerType.individual;
    }
  }

  static String getTypeName(CustomerType type) {
    switch (type) {
      case CustomerType.company:
        return 'شركة';
      case CustomerType.individual:
        return 'فرد';
    }
  }

  static String getPaymentMethodName(CustomerPaymentMethod method) {
    switch (method) {
      case CustomerPaymentMethod.monthlySubscription:
        return 'اشتراك شهري';
      case CustomerPaymentMethod.perService:
        return 'دفع عند طلب الخدمة';
    }
  }

  // Generate mock customers for testing
  static List<Customer> getMockCustomers() {
    return [
      Customer(
        localId: 1,
        name: 'شركة الأمل للمقاولات',
        type: CustomerType.company,
        email: '<EMAIL>',
        phone: '0512345678',
        address: 'الرياض، حي العليا',
        contactPerson: 'أحمد محمد',
        createdAt: DateTime.now().subtract(const Duration(days: 100)),
      ),
      Customer(
        localId: 2,
        name: 'محمد أحمد',
        type: CustomerType.individual,
        phone: '0598765432',
        address: 'جدة، حي الروضة',
        createdAt: DateTime.now().subtract(const Duration(days: 80)),
      ),
      Customer(
        localId: 3,
        name: 'مؤسسة النور',
        type: CustomerType.company,
        email: '<EMAIL>',
        phone: '0512345678',
        address: 'الدمام، حي الفيصلية',
        contactPerson: 'خالد عبدالله',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
      ),
      Customer(
        localId: 4,
        name: 'سارة محمد',
        type: CustomerType.individual,
        phone: '0587654321',
        createdAt: DateTime.now().subtract(const Duration(days: 40)),
      ),
      Customer(
        localId: 5,
        name: 'شركة البناء الحديث',
        type: CustomerType.company,
        email: '<EMAIL>',
        phone: '0512345678',
        address: 'الرياض، حي الملز',
        contactPerson: 'فهد سعيد',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
    ];
  }
}
