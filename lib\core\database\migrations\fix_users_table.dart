import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// هجرة لإصلاح جدول المستخدمين
class FixUsersTable {
  static const String tableName = 'users';

  /// التحقق مما إذا كانت الهجرة مطلوبة
  static Future<bool> isNeeded(Database db) async {
    try {
      // التحقق من وجود الجدول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='$tableName'",
      );
      
      if (tables.isEmpty) {
        return false; // الجدول غير موجود
      }

      // التحقق من هيكل الجدول
      final columns = await db.rawQuery("PRAGMA table_info($tableName)");
      
      // التحقق من وجود عمود status
      final hasStatus = columns.any((column) => column['name'] == 'status');
      
      // التحقق من عدم وجود عمود is_active
      final hasIsActive = columns.any((column) => column['name'] == 'is_active');
      
      // نحتاج للهجرة إذا كان هناك عمود is_active أو لا يوجد عمود status
      return hasIsActive || !hasStatus;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء التحقق من هيكل جدول المستخدمين: $e');
      }
      return false;
    }
  }

  /// تطبيق الهجرة
  static Future<void> migrate(Database db) async {
    try {
      if (await isNeeded(db)) {
        if (kDebugMode) {
          print('تطبيق هجرة: إصلاح جدول المستخدمين');
        }

        // التحقق من وجود عمود status
        final columns = await db.rawQuery("PRAGMA table_info($tableName)");
        final hasStatus = columns.any((column) => column['name'] == 'status');
        final hasIsActive = columns.any((column) => column['name'] == 'is_active');

        // إذا لم يكن هناك عمود status، نقوم بإضافته
        if (!hasStatus) {
          await db.execute('''
            ALTER TABLE $tableName ADD COLUMN status TEXT NOT NULL DEFAULT 'active'
          ''');
          
          if (kDebugMode) {
            print('تم إضافة عمود status إلى جدول المستخدمين');
          }
        }

        // إذا كان هناك عمود is_active، نقوم بنقل البيانات منه إلى عمود status
        if (hasIsActive) {
          // الحصول على جميع المستخدمين
          final users = await db.query(tableName);
          
          // تحديث كل مستخدم
          for (final user in users) {
            final isActive = user['is_active'] as int?;
            final status = isActive == 1 ? 'active' : 'inactive';
            
            await db.update(
              tableName,
              {'status': status},
              where: 'id = ?',
              whereArgs: [user['id']],
            );
          }
          
          if (kDebugMode) {
            print('تم نقل البيانات من عمود is_active إلى عمود status');
          }
        }

        if (kDebugMode) {
          print('تم تطبيق الهجرة بنجاح');
        }
      } else {
        if (kDebugMode) {
          print('تم تخطي الهجرة: جدول المستخدمين لا يحتاج إلى إصلاح');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء تطبيق الهجرة: $e');
      }
      rethrow;
    }
  }
}
