import 'package:flutter/material.dart';
import '../../../config/constants.dart';
import '../../../shared/models/inventory_item.dart';
import '../../../core/repositories/inventory_repository.dart';

class InventorySelectionWidget extends StatefulWidget {
  final List<int>? initialSelectedIds;
  final List<int>? initialQuantities;
  final Function(List<int>, List<int>) onSelectionChanged;
  final String title;

  const InventorySelectionWidget({
    super.key,
    this.initialSelectedIds,
    this.initialQuantities,
    required this.onSelectionChanged,
    required this.title,
  });

  @override
  State<InventorySelectionWidget> createState() => _InventorySelectionWidgetState();
}

class _InventorySelectionWidgetState extends State<InventorySelectionWidget> {
  final InventoryRepository _inventoryRepository = InventoryRepository();
  List<InventoryItem> _inventoryItems = [];
  List<int> _selectedIds = [];
  List<int> _quantities = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedIds = widget.initialSelectedIds != null ? List<int>.from(widget.initialSelectedIds!) : [];
    _quantities = widget.initialQuantities != null ? List<int>.from(widget.initialQuantities!) : List.filled(_selectedIds.length, 1);
    _loadInventoryItems();
  }

  Future<void> _loadInventoryItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final items = await _inventoryRepository.getAllInventoryItems();
      setState(() {
        _inventoryItems = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل عناصر المخزون: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<InventoryItem> get _filteredItems {
    if (_searchQuery.isEmpty) {
      return _inventoryItems;
    }

    return _inventoryItems.where((item) {
      final query = _searchQuery.toLowerCase();
      return item.name.toLowerCase().contains(query) ||
          item.code.toLowerCase().contains(query) ||
          (item.category?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  void _addItem(InventoryItem item) {
    if (!_selectedIds.contains(item.id)) {
      setState(() {
        _selectedIds.add(item.id!);
        _quantities.add(1);
      });
      widget.onSelectionChanged(_selectedIds, _quantities);
    }
  }

  void _removeItem(int index) {
    setState(() {
      _selectedIds.removeAt(index);
      _quantities.removeAt(index);
    });
    widget.onSelectionChanged(_selectedIds, _quantities);
  }

  void _updateQuantity(int index, int quantity) {
    if (quantity > 0) {
      setState(() {
        _quantities[index] = quantity;
      });
      widget.onSelectionChanged(_selectedIds, _quantities);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: AppTextStyles.heading3,
                ),
                IconButton(
                  icon: const Icon(Icons.add_circle, color: AppColors.primary),
                  onPressed: () {
                    _showInventorySelectionDialog();
                  },
                  tooltip: 'إضافة عنصر من المخزون',
                ),
              ],
            ),
            const Divider(),
            if (_selectedIds.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  child: Text('لم يتم اختيار أي عناصر من المخزون'),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _selectedIds.length,
                itemBuilder: (context, index) {
                  final itemId = _selectedIds[index];
                  final item = _inventoryItems.firstWhere(
                    (item) => item.id == itemId,
                    orElse: () => InventoryItem(
                      id: itemId,
                      code: 'UNKNOWN',
                      name: 'عنصر غير معروف',
                      quantity: 0,
                      minQuantity: 0,
                      costPrice: 0,
                      sellingPrice: 0,
                      isActive: true,
                      createdAt: DateTime.now(),
                    ),
                  );

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.name,
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  'الكود: ${item.code}',
                                  style: const TextStyle(fontSize: 12),
                                ),
                                Text(
                                  'السعر: ${item.sellingPrice} ريال',
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.remove_circle_outline, size: 20),
                                onPressed: _quantities[index] > 1
                                    ? () => _updateQuantity(index, _quantities[index] - 1)
                                    : null,
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${_quantities[index]}',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                icon: const Icon(Icons.add_circle_outline, size: 20),
                                onPressed: () => _updateQuantity(index, _quantities[index] + 1),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                              const SizedBox(width: 16),
                              IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                                onPressed: () => _removeItem(index),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showInventorySelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('اختيار عناصر من المخزون'),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      decoration: const InputDecoration(
                        hintText: 'بحث...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : _filteredItems.isEmpty
                              ? const Center(child: Text('لا توجد عناصر'))
                              : ListView.builder(
                                  itemCount: _filteredItems.length,
                                  itemBuilder: (context, index) {
                                    final item = _filteredItems[index];
                                    final isSelected = _selectedIds.contains(item.id);

                                    return ListTile(
                                      title: Text(item.name),
                                      subtitle: Text(
                                        'الكمية المتاحة: ${item.quantity} ${item.unit ?? 'قطعة'} - السعر: ${item.sellingPrice} ريال',
                                      ),
                                      trailing: isSelected
                                          ? const Icon(Icons.check_circle, color: Colors.green)
                                          : null,
                                      onTap: () {
                                        if (!isSelected) {
                                          _addItem(item);
                                          Navigator.pop(context);
                                        }
                                      },
                                    );
                                  },
                                ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
