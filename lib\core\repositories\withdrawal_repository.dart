import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/withdrawal.dart';

class WithdrawalRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all withdrawals
  Future<List<Withdrawal>> getAllWithdrawals() async {
    try {
      final db = await _dbHelper.database;

      // Join withdrawals with employees to get employee names and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT w.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM withdrawals w
        LEFT JOIN employees e ON w.employee_id = e.id
        LEFT JOIN employees pe ON w.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON w.payment_bank_account_id = ba.id
      ''');

      return List.generate(maps.length, (i) {
        final withdrawal = Withdrawal.fromMap(maps[i]);
        // Add employee name from the join
        return withdrawal.copyWith(
          employeeName: maps[i]['employee_name'] as String? ?? 'غير معروف',
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting withdrawals: $e');
      }
      return [];
    }
  }

  // Get withdrawal by ID
  Future<Withdrawal?> getWithdrawalById(int id) async {
    try {
      final db = await _dbHelper.database;

      // Join withdrawals with employees to get employee name and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT w.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM withdrawals w
        LEFT JOIN employees e ON w.employee_id = e.id
        LEFT JOIN employees pe ON w.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON w.payment_bank_account_id = ba.id
        WHERE w.id = ?
      ''', [id]);

      if (maps.isNotEmpty) {
        final withdrawal = Withdrawal.fromMap(maps[0]);
        // Add employee name from the join
        return withdrawal.copyWith(
          employeeName: maps[0]['employee_name'] as String? ?? 'غير معروف',
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting withdrawal by ID: $e');
      }
      return null;
    }
  }

  // Get withdrawals by employee ID
  Future<List<Withdrawal>> getWithdrawalsByEmployeeId(int employeeId) async {
    try {
      final db = await _dbHelper.database;

      // Join withdrawals with employees to get employee name and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT w.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM withdrawals w
        LEFT JOIN employees e ON w.employee_id = e.id
        LEFT JOIN employees pe ON w.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON w.payment_bank_account_id = ba.id
        WHERE w.employee_id = ?
      ''', [employeeId]);

      return List.generate(maps.length, (i) {
        final withdrawal = Withdrawal.fromMap(maps[i]);
        // Add employee name from the join
        return withdrawal.copyWith(
          employeeName: maps[i]['employee_name'] as String? ?? 'غير معروف',
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting withdrawals by employee ID: $e');
      }
      return [];
    }
  }

  // Get unpaid withdrawals by employee ID
  Future<List<Withdrawal>> getUnpaidWithdrawalsByEmployeeId(int employeeId) async {
    try {
      final db = await _dbHelper.database;

      // Join withdrawals with employees to get employee name and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT w.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM withdrawals w
        LEFT JOIN employees e ON w.employee_id = e.id
        LEFT JOIN employees pe ON w.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON w.payment_bank_account_id = ba.id
        WHERE w.employee_id = ? AND w.status = 'approved' AND (w.is_paid = 0 OR w.is_paid IS NULL)
      ''', [employeeId]);

      return List.generate(maps.length, (i) {
        final withdrawal = Withdrawal.fromMap(maps[i]);
        // Add employee name from the join
        return withdrawal.copyWith(
          employeeName: maps[i]['employee_name'] as String? ?? 'غير معروف',
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting unpaid withdrawals by employee ID: $e');
      }
      return [];
    }
  }

  // Mark withdrawals as paid
  Future<bool> markWithdrawalsAsPaid(List<int> withdrawalIds, int salaryId) async {
    try {
      final db = await _dbHelper.database;

      // Update all withdrawals in the list
      final batch = db.batch();
      for (final id in withdrawalIds) {
        batch.update(
          'withdrawals',
          {
            'is_paid': 1,
            'salary_id': salaryId,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [id],
        );
      }

      await batch.commit();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error marking withdrawals as paid: $e');
      }
      return false;
    }
  }

  // Insert a new withdrawal
  Future<int> insertWithdrawal(Withdrawal withdrawal) async {
    try {
      final db = await _dbHelper.database;

      // No need to get employee name, as it's not stored in the withdrawals table

      return await db.insert(
        'withdrawals',
        {
          'employee_id': withdrawal.employeeId,
          'amount': withdrawal.amount,
          'date': withdrawal.date.toIso8601String(),
          'reason': withdrawal.reason,
          'status': withdrawal.status.toString().split('.').last,
          'notes': withdrawal.notes,
          'approved_by': withdrawal.approvedBy,
          'approved_at': withdrawal.approvedAt?.toIso8601String(),
          'created_by': withdrawal.createdBy,
          'created_at': withdrawal.createdAt.toIso8601String(),
          'updated_at': withdrawal.updatedAt?.toIso8601String(),
          'payment_method': withdrawal.paymentMethod?.toString().split('.').last,
          'payment_employee_id': withdrawal.paymentEmployeeId,
          'payment_bank_account_id': withdrawal.paymentBankAccountId,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting withdrawal: $e');
      }
      return -1;
    }
  }

  // Update an existing withdrawal
  Future<int> updateWithdrawal(Withdrawal withdrawal) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'withdrawals',
        withdrawal.toMap(),
        where: 'id = ?',
        whereArgs: [withdrawal.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating withdrawal: $e');
      }
      return 0;
    }
  }

  // Delete a withdrawal
  Future<int> deleteWithdrawal(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'withdrawals',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting withdrawal: $e');
      }
      return 0;
    }
  }
}
