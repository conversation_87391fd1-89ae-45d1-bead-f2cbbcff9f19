import 'dart:convert';
import 'package:flutter/material.dart';

class ServiceCategory {
  final int? id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ServiceCategory({
    this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  factory ServiceCategory.fromMap(Map<String, dynamic> map) {
    // Default icon and color
    const defaultIcon = Icons.build;
    const defaultColor = Colors.blue;

    return ServiceCategory(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      icon: map['icon_code'] != null
          ? IconData(map['icon_code'] as int, fontFamily: 'MaterialIcons')
          : defaultIcon,
      color: map['color'] != null
          ? Color(map['color'] as int)
          : defaultColor,
      isActive: map['status'] is String
          ? (map['status'] as String) == 'active'
          : map['is_active'] is bool
              ? map['is_active'] as bool
              : (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] is DateTime
          ? map['created_at'] as DateTime
          : DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? map['updated_at'] is DateTime
              ? map['updated_at'] as DateTime
              : DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory ServiceCategory.fromJson(String source) =>
      ServiceCategory.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon_code': icon.codePoint,
      'color': color.value,
      'status': isActive ? 'active' : 'inactive',
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  ServiceCategory copyWith({
    int? id,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Static methods for mock data
  static List<ServiceCategory> getMockCategories() {
    return [
      ServiceCategory(
        id: 1,
        name: 'تركيب مكيف',
        description: 'تركيب وحدات تكييف جديدة',
        icon: Icons.ac_unit,
        color: Colors.blue,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      ServiceCategory(
        id: 2,
        name: 'صيانة دورية',
        description: 'صيانة دورية لوحدات التكييف',
        icon: Icons.settings,
        color: Colors.green,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      ServiceCategory(
        id: 3,
        name: 'إصلاح أعطال',
        description: 'إصلاح الأعطال والمشاكل في وحدات التكييف',
        icon: Icons.build,
        color: Colors.orange,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      ServiceCategory(
        id: 4,
        name: 'فحص وتشخيص',
        description: 'فحص وتشخيص مشاكل وحدات التكييف',
        icon: Icons.search,
        color: Colors.purple,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
      ServiceCategory(
        id: 5,
        name: 'تنظيف وتعقيم',
        description: 'تنظيف وتعقيم وحدات التكييف',
        icon: Icons.cleaning_services,
        color: Colors.teal,
        isActive: true,
        createdAt: DateTime(2023, 1, 1),
      ),
    ];
  }
}
