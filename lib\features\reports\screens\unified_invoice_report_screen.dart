import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/invoice.dart';
import '../../../shared/models/customer.dart';
import '../../../core/repositories/invoice_repository.dart';
import '../../../core/repositories/customer_repository.dart';
import '../utils/report_generator.dart';
import '../utils/invoice_adapter.dart';
import '../widgets/report_filter_dialog.dart';
import '../widgets/customer_filter_dialog.dart';
import 'unified_report_screen.dart';

/// شاشة تقرير الفواتير و المعاملات و طلبات الخدمة الموحدة
class UnifiedInvoiceReportScreen extends StatefulWidget {
  const UnifiedInvoiceReportScreen({super.key});

  @override
  State<UnifiedInvoiceReportScreen> createState() => _UnifiedInvoiceReportScreenState();
}

class _UnifiedInvoiceReportScreenState extends State<UnifiedInvoiceReportScreen> {
  final InvoiceRepository _invoiceRepository = InvoiceRepository();
  final CustomerRepository _customerRepository = CustomerRepository();

  bool _isLoading = false;
  List<Invoice> _invoices = [];
  List<Customer> _customers = [];

  // فلاتر التقرير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  int? _selectedCustomerId;
  String _statusFilter = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الفواتير
      final invoices = await _invoiceRepository.getInvoicesByDateRange(_startDate, _endDate);

      // تحميل العملاء
      final customers = await _customerRepository.getAllCustomers();

      if (mounted) {
        setState(() {
          _invoices = invoices;
          _customers = customers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading invoice report data: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // الفواتير المفلترة
  List<Invoice> get _filteredInvoices {
    var filtered = _invoices;

    // تطبيق فلتر العميل
    if (_selectedCustomerId != null) {
      filtered = filtered.where((invoice) => invoice.customerId == _selectedCustomerId).toList();
    }

    // تطبيق فلتر الحالة
    if (_statusFilter != 'all') {
      filtered = filtered.where((invoice) {
        final status = invoice.status.toString().split('.').last;
        return status == _statusFilter;
      }).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((invoice) {
        return invoice.invoiceNumber.toLowerCase().contains(query) ||
            invoice.customerName.toLowerCase().contains(query);
      }).toList();
    }

    return filtered;
  }

  // إجمالي المبالغ
  double get _totalAmount {
    return _filteredInvoices.fold(0, (sum, invoice) => sum + invoice.total);
  }

  // إجمالي المبالغ المدفوعة
  double get _totalPaid {
    return _filteredInvoices
        .where((invoice) => invoice.status == InvoiceStatus.paid)
        .fold(0, (sum, invoice) => sum + invoice.total);
  }

  // إجمالي المبالغ غير المدفوعة
  double get _totalUnpaid {
    return _filteredInvoices
        .where((invoice) => invoice.status != InvoiceStatus.paid)
        .fold(0, (sum, invoice) => sum + invoice.total);
  }

  // بناء عنصر فلتر الحالة
  Widget _buildStatusFilterChip(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: FilterChip(
        label: Text(label),
        selected: _statusFilter == value,
        onSelected: (selected) {
          setState(() {
            _statusFilter = selected ? value : 'all';
          });
        },
        selectedColor: AppColors.primary.withAlpha(51),
        checkmarkColor: AppColors.primary,
      ),
    );
  }

  // بناء عنصر فلتر العميل
  Widget _buildCustomerFilter() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تصفية حسب العميل',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                TextButton.icon(
                  icon: const Icon(Icons.person_search, size: 16),
                  label: const Text('اختيار عميل'),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => CustomerFilterDialog(
                        selectedCustomerId: _selectedCustomerId,
                        onApplyFilter: (customerId) {
                          setState(() {
                            _selectedCustomerId = customerId;
                          });
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
            if (_selectedCustomerId != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Chip(
                  label: Text(
                    _customers.firstWhere(
                      (c) => c.id == _selectedCustomerId,
                      orElse: () => Customer(
                        name: 'غير معروف',
                        email: '',
                        phone: '',
                        type: CustomerType.individual,
                        createdAt: DateTime.now(),
                      ),
                    ).name,
                  ),
                  deleteIcon: const Icon(Icons.close),
                  onDeleted: () {
                    setState(() {
                      _selectedCustomerId = null;
                    });
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  // بناء عنصر فلتر الحالة
  Widget _buildStatusFilter() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تصفية حسب الحالة',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildStatusFilterChip('الكل', 'all'),
                  _buildStatusFilterChip('مدفوعة', 'paid'),
                  _buildStatusFilterChip('غير مدفوعة', 'issued'),
                  _buildStatusFilterChip('مدفوعة جزئياً', 'partiallyPaid'),
                  _buildStatusFilterChip('متأخرة', 'overdue'),
                  _buildStatusFilterChip('ملغاة', 'cancelled'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء عنصر البحث
  Widget _buildSearchFilter() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: TextField(
          decoration: const InputDecoration(
            hintText: 'بحث عن فاتورة...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
      ),
    );
  }

  // بناء عنصر ملخص التقرير
  Widget _buildSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص التقرير',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي الفواتير:'),
                Text(
                  '${_filteredInvoices.length} فاتورة',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('إجمالي المبالغ:'),
                Text(
                  '${_totalAmount.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المبالغ المدفوعة:'),
                Text(
                  '${_totalPaid.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(color: Colors.green),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المبالغ غير المدفوعة:'),
                Text(
                  '${_totalUnpaid.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(color: Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء جدول الفواتير
  Widget _buildInvoicesTable() {
    return _filteredInvoices.isEmpty
        ? const Center(
            child: Text(
              'لا توجد فواتير للعرض',
              style: AppTextStyles.heading3,
            ),
          )
        : Column(
            children: [
              // عنوان الجدول
              Container(
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: const Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        'النوع',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        'الرقم المرجعي',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        'التاريخ',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        'الوصف',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        'المبلغ',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        'الحالة',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              // محتوى الجدول
              Expanded(
                child: _buildUnifiedDataTable(),
              ),
            ],
          );
  }

  // بناء محتوى الجدول الموحد
  Widget _buildUnifiedDataTable() {
    if (_filteredInvoices.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _filteredInvoices[index];

        return Container(
          decoration: BoxDecoration(
            color: index % 2 == 0 ? Colors.white : Colors.grey.shade50,
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Row(
              children: [
                // النوع
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Icon(
                      Icons.receipt,
                      color: AppColors.primary,
                    ),
                  ),
                ),

                // الرقم المرجعي
                Expanded(
                  flex: 2,
                  child: Text(
                    invoice.invoiceNumber,
                    textAlign: TextAlign.center,
                  ),
                ),

                // التاريخ
                Expanded(
                  flex: 2,
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(invoice.date),
                    textAlign: TextAlign.center,
                  ),
                ),

                // الوصف
                Expanded(
                  flex: 2,
                  child: Text(
                    invoice.customerName,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // المبلغ
                Expanded(
                  flex: 2,
                  child: Text(
                    '${invoice.total.toStringAsFixed(2)} ر.س',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: invoice.isPaid ? Colors.green : Colors.red,
                    ),
                  ),
                ),

                // الحالة
                Expanded(
                  flex: 2,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(invoice.status),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusName(invoice.status),
                        style: const TextStyle(color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.issued:
        return Colors.red;
      case InvoiceStatus.partiallyPaid:
        return Colors.orange;
      case InvoiceStatus.cancelled:
        return Colors.grey;
      case InvoiceStatus.draft:
        return Colors.grey.shade400;
      case InvoiceStatus.overdue:
        return Colors.deepOrange;
    }
  }

  String _getStatusName(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.issued:
        return 'غير مدفوعة';
      case InvoiceStatus.partiallyPaid:
        return 'مدفوعة جزئياً';
      case InvoiceStatus.cancelled:
        return 'ملغاة';
      case InvoiceStatus.draft:
        return 'مسودة';
      case InvoiceStatus.overdue:
        return 'متأخرة';
    }
  }

  @override
  Widget build(BuildContext context) {
    // بناء عناصر الفلتر
    final filterWidget = Column(
      children: [
        _buildCustomerFilter(),
        const SizedBox(height: AppDimensions.paddingS),
        _buildStatusFilter(),
        const SizedBox(height: AppDimensions.paddingS),
        _buildSearchFilter(),
      ],
    );

    // بناء عنصر الملخص
    final summaryWidget = _buildSummary();

    // بناء عنصر البيانات
    final dataWidget = _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _buildInvoicesTable();

    // استخدام شاشة التقرير الموحدة
    return UnifiedReportScreen(
      title: 'تقرير الفواتير و المعاملات و طلبات الخدمة الموحد',
      reportType: ReportType.invoice,
      entityId: _selectedCustomerId,
      filterWidget: filterWidget,
      summaryWidget: summaryWidget,
      dataWidget: dataWidget,
      getReportData: () => InvoiceAdapter.toReportModelList(_filteredInvoices),
    );
  }
}
