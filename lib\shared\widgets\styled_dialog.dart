import 'package:flutter/material.dart';
import '../../config/constants.dart';
import 'styled_button.dart';

/// حوار منسق مع أيقونة وعنوان
class StyledDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;

  /// أيقونة الحوار (اختياري)
  final IconData? icon;

  /// محتوى الحوار
  final Widget content;

  /// إجراءات الحوار (أزرار)
  final List<Widget>? actions;

  /// حجم الحوار
  final Size? size;

  /// تباعد داخلي
  final EdgeInsetsGeometry contentPadding;

  /// لون خلفية الحوار
  final Color? backgroundColor;

  /// نصف قطر الزوايا
  final double borderRadius;

  /// ارتفاع الحوار
  final double? elevation;

  /// إنشاء حوار منسق
  const StyledDialog({
    super.key,
    required this.title,
    this.icon,
    required this.content,
    this.actions,
    this.size,
    this.contentPadding = const EdgeInsets.all(AppDimensions.spacingM),
    this.backgroundColor,
    this.borderRadius = AppDimensions.radiusM,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      backgroundColor: backgroundColor ?? theme.dialogTheme.backgroundColor ?? AppColors.surface,
      elevation: elevation ?? theme.dialogTheme.elevation ?? AppDimensions.elevationM,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Container(
        width: size?.width,
        constraints: BoxConstraints(
          maxWidth: size?.width ?? 500,
          maxHeight: size?.height ?? MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عنوان الحوار
            Container(
              padding: const EdgeInsets.all(AppDimensions.spacingM),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(15),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(borderRadius),
                  topRight: Radius.circular(borderRadius),
                ),
              ),
              child: Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      color: AppColors.primary,
                      size: 24,
                    ),
                    const SizedBox(width: AppDimensions.spacingS),
                  ],
                  Expanded(
                    child: Text(
                      title,
                      style: AppTextStyles.headingSmall.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    color: AppColors.textSecondary,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // محتوى الحوار
            Flexible(
              child: SingleChildScrollView(
                padding: contentPadding,
                child: content,
              ),
            ),

            // إجراءات الحوار
            if (actions != null && actions!.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(AppDimensions.spacingM),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(borderRadius),
                    bottomRight: Radius.circular(borderRadius),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!.map((action) {
                    final index = actions!.indexOf(action);
                    return Padding(
                      padding: EdgeInsets.only(
                        right: index > 0 ? AppDimensions.spacingS : 0,
                      ),
                      child: action,
                    );
                  }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// عرض حوار منسق
Future<T?> showStyledDialog<T>({
  required BuildContext context,
  required Widget title,
  required Widget content,
  List<Widget>? actions,
  bool barrierDismissible = true,
  Color? barrierColor,
  bool useSafeArea = true,
  bool scrollable = false,
  EdgeInsetsGeometry? contentPadding,
  EdgeInsetsGeometry? titlePadding,
  EdgeInsetsGeometry? actionsPadding,
  Color? backgroundColor,
  double? elevation,
  String? barrierLabel,
  RouteSettings? routeSettings,
  Offset? anchorPoint,
  bool useRootNavigator = true,
  Clip clipBehavior = Clip.none,
}) {
  return showDialog<T>(
    context: context,
    barrierDismissible: barrierDismissible,
    barrierColor: barrierColor ?? Colors.black54,
    useSafeArea: useSafeArea,
    barrierLabel: barrierLabel,
    routeSettings: routeSettings,
    anchorPoint: anchorPoint,
    useRootNavigator: useRootNavigator,
    builder: (BuildContext context) {
      return Dialog(
        backgroundColor: backgroundColor ?? AppColors.surface,
        elevation: elevation ?? AppDimensions.elevationM,
        clipBehavior: clipBehavior,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عنوان الحوار
            Container(
              padding: titlePadding ??
                  const EdgeInsets.fromLTRB(
                    AppDimensions.spacingM,
                    AppDimensions.spacingM,
                    AppDimensions.spacingM,
                    AppDimensions.spacingS,
                  ),
              child: DefaultTextStyle(
                style: AppTextStyles.headingSmall,
                child: title,
              ),
            ),

            // محتوى الحوار
            Flexible(
              child: SingleChildScrollView(
                physics: scrollable
                    ? const BouncingScrollPhysics()
                    : const NeverScrollableScrollPhysics(),
                child: Container(
                  padding: contentPadding ??
                      const EdgeInsets.fromLTRB(
                        AppDimensions.spacingM,
                        AppDimensions.spacingS,
                        AppDimensions.spacingM,
                        AppDimensions.spacingM,
                      ),
                  child: DefaultTextStyle(
                    style: AppTextStyles.bodyMedium,
                    child: content,
                  ),
                ),
              ),
            ),

            // أزرار الإجراءات
            if (actions != null && actions.isNotEmpty)
              Container(
                padding: actionsPadding ??
                    const EdgeInsets.fromLTRB(
                      AppDimensions.spacingM,
                      AppDimensions.spacingS,
                      AppDimensions.spacingM,
                      AppDimensions.spacingM,
                    ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions,
                ),
              ),
          ],
        ),
      );
    },
  );
}

/// عرض حوار تأكيد منسق
Future<bool> showConfirmationDialog({
  required BuildContext context,
  required String title,
  required String message,
  String confirmText = 'نعم',
  String cancelText = 'إلغاء',
  Color? confirmColor,
  bool isDanger = false,
}) async {
  final result = await showStyledDialog<bool>(
    context: context,
    title: Text(title),
    content: Text(message),
    actions: [
      StyledButton(
        label: cancelText,
        type: StyledButtonType.text,
        onPressed: () => Navigator.of(context).pop(false),
      ),
      StyledButton(
        label: confirmText,
        type: isDanger ? StyledButtonType.danger : StyledButtonType.primary,
        onPressed: () => Navigator.of(context).pop(true),
      ),
    ],
  );

  return result ?? false;
}

/// عرض حوار خطأ منسق
Future<void> showErrorDialog({
  required BuildContext context,
  required String title,
  required String message,
  String buttonText = 'حسناً',
}) async {
  await showStyledDialog<void>(
    context: context,
    title: Row(
      children: [
        const Icon(
          Icons.error_outline,
          color: AppColors.error,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Text(title),
      ],
    ),
    content: Text(message),
    actions: [
      StyledButton(
        label: buttonText,
        type: StyledButtonType.primary,
        onPressed: () => Navigator.of(context).pop(),
      ),
    ],
  );
}

/// عرض حوار نجاح منسق
Future<void> showSuccessDialog({
  required BuildContext context,
  required String title,
  required String message,
  String buttonText = 'حسناً',
}) async {
  await showStyledDialog<void>(
    context: context,
    title: Row(
      children: [
        const Icon(
          Icons.check_circle_outline,
          color: AppColors.success,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Text(title),
      ],
    ),
    content: Text(message),
    actions: [
      StyledButton(
        label: buttonText,
        type: StyledButtonType.primary,
        onPressed: () => Navigator.of(context).pop(),
      ),
    ],
  );
}

/// عرض حوار تحذير منسق
Future<void> showWarningDialog({
  required BuildContext context,
  required String title,
  required String message,
  String buttonText = 'حسناً',
}) async {
  await showStyledDialog<void>(
    context: context,
    title: Row(
      children: [
        const Icon(
          Icons.warning_amber_outlined,
          color: AppColors.warning,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Text(title),
      ],
    ),
    content: Text(message),
    actions: [
      StyledButton(
        label: buttonText,
        type: StyledButtonType.primary,
        onPressed: () => Navigator.of(context).pop(),
      ),
    ],
  );
}

/// عرض حوار معلومات منسق
Future<void> showInfoDialog({
  required BuildContext context,
  required String title,
  required String message,
  String buttonText = 'حسناً',
}) async {
  await showStyledDialog<void>(
    context: context,
    title: Row(
      children: [
        const Icon(
          Icons.info_outline,
          color: AppColors.info,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Text(title),
      ],
    ),
    content: Text(message),
    actions: [
      StyledButton(
        label: buttonText,
        type: StyledButtonType.primary,
        onPressed: () => Navigator.of(context).pop(),
      ),
    ],
  );
}
