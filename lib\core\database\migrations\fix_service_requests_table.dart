import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// هجرة لإصلاح جدول طلبات الخدمة
class FixServiceRequestsTable {
  static const String tableName = 'service_requests';

  /// التحقق مما إذا كانت الهجرة مطلوبة
  static Future<bool> isNeeded(Database db) async {
    try {
      // التحقق من وجود الجدول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='$tableName'",
      );

      if (tables.isEmpty) {
        return false; // الجدول غير موجود
      }

      // التحقق من هيكل الجدول
      final columns = await db.rawQuery("PRAGMA table_info($tableName)");

      // التحقق من عمود is_active
      final hasIsActive = columns.any((column) => column['name'] == 'is_active');

      // نحتاج للهجرة إذا كان هناك عمود is_active
      return hasIsActive;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء التحقق من هيكل جدول طلبات الخدمة: $e');
      }
      return false;
    }
  }

  /// تطبيق الهجرة
  static Future<void> migrate(Database db) async {
    try {
      if (await isNeeded(db)) {
        if (kDebugMode) {
          print('تطبيق هجرة: إصلاح جدول طلبات الخدمة');
        }

        // التحقق من هيكل الجدول
        final columns = await db.rawQuery("PRAGMA table_info($tableName)");
        final hasIsActive = columns.any((column) => column['name'] == 'is_active');

        // إذا كان هناك عمود is_active، نقوم بإزالته
        if (hasIsActive) {
          // نقوم بإنشاء جدول مؤقت بدون عمود is_active
          await db.execute('''
            CREATE TABLE ${tableName}_temp (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              request_number TEXT NOT NULL UNIQUE,
              customer_id INTEGER NOT NULL,
              service_type TEXT,
              description TEXT,
              address TEXT,
              scheduled_date TEXT,
              status TEXT NOT NULL DEFAULT 'pending',
              assigned_to INTEGER,
              completion_date TEXT,
              notes TEXT,
              created_at TEXT NOT NULL,
              updated_at TEXT,
              solution TEXT,
              used_parts TEXT,
              used_inventory_ids TEXT,
              used_inventory_quantities TEXT,
              problem_images TEXT,
              solution_images TEXT,
              device_type TEXT,
              device_brand TEXT,
              device_model TEXT,
              serial_number TEXT,
              installation_date TEXT,
              warranty_info TEXT,
              technical_notes TEXT,
              category_id INTEGER,
              invoice_id INTEGER,
              created_by INTEGER,
              FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
              FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL,
              FOREIGN KEY (category_id) REFERENCES service_categories(id) ON DELETE SET NULL
            )
          ''');

          // نقل البيانات من الجدول القديم إلى الجدول المؤقت
          await db.execute('''
            INSERT INTO ${tableName}_temp
            SELECT
              id, request_number, customer_id, service_type, description, address,
              scheduled_date, status, assigned_to, completion_date, notes, created_at,
              updated_at, solution, used_parts, used_inventory_ids, used_inventory_quantities,
              problem_images, solution_images, device_type, device_brand, device_model,
              serial_number, installation_date, warranty_info, technical_notes,
              category_id, invoice_id, created_by
            FROM $tableName
          ''');

          // حذف الجدول القديم
          await db.execute('DROP TABLE $tableName');

          // إعادة تسمية الجدول المؤقت
          await db.execute('ALTER TABLE ${tableName}_temp RENAME TO $tableName');

          if (kDebugMode) {
            print('تم إزالة عمود is_active من جدول طلبات الخدمة');
          }
        }

        if (kDebugMode) {
          print('تم تطبيق الهجرة بنجاح');
        }
      } else {
        if (kDebugMode) {
          print('تم تخطي الهجرة: جدول طلبات الخدمة لا يحتاج إلى إصلاح');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء تطبيق الهجرة: $e');
      }
      rethrow;
    }
  }
}
