import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shared/models/invoice_item.dart' as invoice_item;
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import '../models/report_models.dart' as report_models;
import 'arabic_pdf_helpers.dart';
import 'arabic_text_helper.dart';
import '../../../features/settings/repositories/company_info_repository.dart';
import '../../../shared/models/customer.dart';
import '../../../shared/models/invoice.dart' as app_invoice;
import '../../../shared/models/transaction.dart' as app_transaction;
import '../../../shared/models/service_request.dart' as app_service;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import '../models/report_models.dart';
import 'arabic_pdf_helpers.dart';
import 'arabic_text_helper.dart';
import '../../../features/settings/repositories/company_info_repository.dart';
import '../../../shared/models/customer.dart';

/// A simplified PDF export utility that focuses on stability
class SimplePdfExport {
  /// Create a professional PDF invoice with enhanced formatting
  static Future<String?> createInvoicePdf({
    required app_invoice.Invoice invoice,
    required List<app_transaction.Transaction> payments,
  }) async {
    try {
      // Create a PDF document
      final pdf = pw.Document();

      // Load Arabic fonts
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        // Use a fallback font if the specified font can't be loaded
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        // Use a fallback font if the specified font can't be loaded
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // Load company info
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // Load logo image
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // Create a simple placeholder image
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // Get current user name
      final String username = 'admin'; // TODO: Get from auth state

      // Calculate financial totals
      final double totalPaid = payments.fold(0.0, (sum, payment) => sum + payment.amount);
      final double remainingAmount = invoice.total - totalPaid;

      // Get customer payment method name in Arabic
      String getPaymentMethodName(CustomerPaymentMethod? method) {
        switch (method) {
          case CustomerPaymentMethod.perService:
            return 'دفع لكل خدمة';
          case CustomerPaymentMethod.monthlySubscription:
            return 'اشتراك شهري';
          default:
            return 'غير محدد';
        }
      }

      // Create a professional PDF page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Enhanced Company Header with professional styling
                _buildProfessionalHeader(
                  companyInfo,
                  logoImageBytes,
                  arabicFont,
                  arabicBoldFont,
                ),

                pw.SizedBox(height: 15),

                // Professional Invoice Title Section
                _buildInvoiceTitle(
                  invoice,
                  arabicFont,
                  arabicBoldFont,
                ),

                pw.SizedBox(height: 20),

                // Invoice and Customer Information in enhanced layout
                _buildInvoiceAndCustomerInfo(
                  invoice,
                  getPaymentMethodName,
                  arabicFont,
                  arabicBoldFont,
                ),

                pw.SizedBox(height: 20),

                // Professional Invoice Items Table
                _buildProfessionalItemsTable(
                  invoice.items,
                  arabicFont,
                  arabicBoldFont,
                ),

                pw.SizedBox(height: 15),

                // Enhanced Financial Summary
                _buildEnhancedFinancialSummary(
                  invoice,
                  totalPaid,
                  remainingAmount,
                  arabicFont,
                  arabicBoldFont,
                ),

                pw.SizedBox(height: 15),

                // Payment History Section (if payments exist)
                if (payments.isNotEmpty)
                  _buildPaymentHistorySection(
                    payments,
                    arabicFont,
                    arabicBoldFont,
                  ),

                pw.Spacer(),

                // Professional Footer
                _buildProfessionalFooter(
                  username,
                  arabicFont,
                ),
              ],
            );
          },
        ),
      );







      // Save the PDF to a file
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/invoice_${invoice.invoiceNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF saved to: ${file.path}');
      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating invoice PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// Build professional company header with enhanced styling
  static pw.Widget _buildProfessionalHeader(
    dynamic companyInfo,
    Uint8List logoImageBytes,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [
            PdfColor.fromInt(0xFF1565C0), // Deep blue
            PdfColor.fromInt(0xFF1976D2), // Medium blue
          ],
        ),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Row(
        children: [
          // Left side - English information
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  companyInfo?.nameEn ?? 'HVAC Service Manager',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 18,
                    color: PdfColors.white,
                  ),
                ),
                pw.SizedBox(height: 5),
                if (companyInfo?.addressEn != null)
                  pw.Text(
                    companyInfo!.addressEn!,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 11,
                      color: PdfColors.white,
                    ),
                  ),
                if (companyInfo?.phoneEn != null)
                  pw.Text(
                    'Tel: ${companyInfo!.phoneEn!}',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 11,
                      color: PdfColors.white,
                    ),
                  ),
                if (companyInfo?.email != null)
                  pw.Text(
                    'Email: ${companyInfo!.email!}',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 11,
                      color: PdfColors.white,
                    ),
                  ),
              ],
            ),
          ),

          // Center - Logo with enhanced styling
          pw.Container(
            width: 100,
            height: 100,
            decoration: pw.BoxDecoration(
              color: PdfColors.white,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(50)),
              border: pw.Border.all(color: PdfColors.grey300, width: 2),
            ),
            padding: const pw.EdgeInsets.all(10),
            child: pw.Image(
              pw.MemoryImage(logoImageBytes),
              fit: pw.BoxFit.contain,
            ),
          ),

          // Right side - Arabic information
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  companyInfo?.nameAr ?? 'مدير خدمات التكييف',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 18,
                    color: PdfColors.white,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 5),
                if (companyInfo?.addressAr != null)
                  pw.Text(
                    companyInfo!.addressAr!,
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 11,
                      color: PdfColors.white,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                if (companyInfo?.phoneAr != null)
                  pw.Text(
                    'هاتف: ${companyInfo!.phoneAr!}',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 11,
                      color: PdfColors.white,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                if (companyInfo?.email != null)
                  pw.Text(
                    'بريد إلكتروني: ${companyInfo!.email!}',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 11,
                      color: PdfColors.white,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build professional invoice title section
  static pw.Widget _buildInvoiceTitle(
    app_invoice.Invoice invoice,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [
            PdfColor.fromInt(0xFF0D47A1), // Dark blue
            PdfColor.fromInt(0xFF1565C0), // Medium blue
          ],
        ),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // Invoice title and number
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'فاتورة',
                style: pw.TextStyle(
                  font: arabicBoldFont,
                  fontSize: 28,
                  color: PdfColors.white,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.Text(
                'رقم: ${invoice.invoiceNumber}',
                style: pw.TextStyle(
                  font: arabicBoldFont,
                  fontSize: 16,
                  color: const PdfColor.fromInt(0xFFE3F2FD),
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),

          // Invoice status badge
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 8),
            decoration: pw.BoxDecoration(
              color: _getStatusColor(invoice.status),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(20)),
            ),
            child: pw.Text(
              app_invoice.Invoice.getStatusName(invoice.status),
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 12,
                color: PdfColors.white,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),

          // Date information
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'تاريخ الإصدار',
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 12,
                  color: const PdfColor.fromInt(0xFFE3F2FD),
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.Text(
                DateFormat('yyyy/MM/dd').format(invoice.issueDate),
                style: pw.TextStyle(
                  font: arabicBoldFont,
                  fontSize: 14,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get status color for invoice status
  static PdfColor _getStatusColor(app_invoice.InvoiceStatus status) {
    switch (status) {
      case app_invoice.InvoiceStatus.paid:
        return const PdfColor.fromInt(0xFF4CAF50);
      case app_invoice.InvoiceStatus.issued:
        return const PdfColor.fromInt(0xFFFF9800);
      case app_invoice.InvoiceStatus.overdue:
        return const PdfColor.fromInt(0xFFF44336);
      case app_invoice.InvoiceStatus.draft:
        return const PdfColor.fromInt(0xFF9E9E9E);
      case app_invoice.InvoiceStatus.partiallyPaid:
        return const PdfColor.fromInt(0xFF2196F3);
      default:
        return const PdfColor.fromInt(0xFF2196F3);
    }
  }

  /// Build enhanced invoice and customer information section
  static pw.Widget _buildInvoiceAndCustomerInfo(
    app_invoice.Invoice invoice,
    String Function(CustomerPaymentMethod?) getPaymentMethodName,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
  ) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Invoice Information
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFFF3E5F5), // Light purple
              border: pw.Border.all(color: const PdfColor.fromInt(0xFF9C27B0)),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  decoration: pw.BoxDecoration(
                    color: const PdfColor.fromInt(0xFF9C27B0),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                  ),
                  child: pw.Text(
                    'معلومات الفاتورة',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 14,
                      color: PdfColors.white,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.SizedBox(height: 12),
                _buildEnhancedInfoRow(
                  'رقم الفاتورة:',
                  invoice.invoiceNumber,
                  arabicFont,
                  arabicBoldFont,
                ),
                _buildEnhancedInfoRow(
                  'تاريخ الإصدار:',
                  DateFormat('yyyy/MM/dd').format(invoice.issueDate),
                  arabicFont,
                  arabicBoldFont,
                ),
                _buildEnhancedInfoRow(
                  'تاريخ الاستحقاق:',
                  DateFormat('yyyy/MM/dd').format(invoice.dueDate),
                  arabicFont,
                  arabicBoldFont,
                ),
                _buildEnhancedInfoRow(
                  'حالة الفاتورة:',
                  app_invoice.Invoice.getStatusName(invoice.status),
                  arabicFont,
                  arabicBoldFont,
                ),
              ],
            ),
          ),
        ),

        pw.SizedBox(width: 15),

        // Customer Information
        pw.Expanded(
          child: pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFFE8F5E8), // Light green
              border: pw.Border.all(color: const PdfColor.fromInt(0xFF4CAF50)),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  decoration: pw.BoxDecoration(
                    color: const PdfColor.fromInt(0xFF4CAF50),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                  ),
                  child: pw.Text(
                    'معلومات العميل',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 14,
                      color: PdfColors.white,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.SizedBox(height: 12),
                _buildEnhancedInfoRow(
                  'اسم العميل:',
                  invoice.customerName,
                  arabicFont,
                  arabicBoldFont,
                ),
                if (invoice.customer.phone != null && invoice.customer.phone!.isNotEmpty)
                  _buildEnhancedInfoRow(
                    'رقم الهاتف:',
                    invoice.customer.phone!,
                    arabicFont,
                    arabicBoldFont,
                  ),
                if (invoice.customer.email != null && invoice.customer.email!.isNotEmpty)
                  _buildEnhancedInfoRow(
                    'البريد الإلكتروني:',
                    invoice.customer.email!,
                    arabicFont,
                    arabicBoldFont,
                  ),
                if (invoice.customer.address != null && invoice.customer.address!.isNotEmpty)
                  _buildEnhancedInfoRow(
                    'العنوان:',
                    invoice.customer.address!,
                    arabicFont,
                    arabicBoldFont,
                  ),
                _buildEnhancedInfoRow(
                  'طريقة الدفع:',
                  getPaymentMethodName(invoice.customer.paymentMethod),
                  arabicFont,
                  arabicBoldFont,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build professional items table with enhanced styling
  static pw.Widget _buildProfessionalItemsTable(
    List<invoice_item.InvoiceItem> items,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
  ) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: const PdfColor.fromInt(0xFF2196F3)),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        children: [
          // Enhanced Header
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              gradient: const pw.LinearGradient(
                colors: [
                  PdfColor.fromInt(0xFF1976D2),
                  PdfColor.fromInt(0xFF2196F3),
                ],
              ),
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Text(
              'عناصر الفاتورة',
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 16,
                color: PdfColors.white,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),

          // Table Header with enhanced styling
          pw.Container(
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFE3F2FD),
            ),
            padding: const pw.EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            child: pw.Row(
              children: [
                pw.Expanded(
                  flex: 1,
                  child: pw.Text(
                    '#',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 12,
                      color: const PdfColor.fromInt(0xFF1976D2),
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
                pw.Expanded(
                  flex: 4,
                  child: pw.Text(
                    'اسم الصنف/الخدمة',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 12,
                      color: const PdfColor.fromInt(0xFF1976D2),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'الكمية',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 12,
                      color: const PdfColor.fromInt(0xFF1976D2),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'سعر الوحدة',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 12,
                      color: const PdfColor.fromInt(0xFF1976D2),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'المجموع',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 12,
                      color: const PdfColor.fromInt(0xFF1976D2),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
              ],
            ),
          ),

          // Table Items with enhanced styling
          ...List.generate(items.length, (index) {
            final item = items[index];
            return pw.Container(
              decoration: pw.BoxDecoration(
                color: index % 2 == 0
                    ? PdfColors.white
                    : const PdfColor.fromInt(0xFFF8F9FA),
                border: pw.Border(
                  bottom: pw.BorderSide(
                    color: const PdfColor.fromInt(0xFFE0E0E0),
                    width: 0.5,
                  ),
                ),
              ),
              padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              child: pw.Row(
                children: [
                  pw.Expanded(
                    flex: 1,
                    child: pw.Text(
                      '${index + 1}',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 11,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 4,
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          item.name,
                          style: pw.TextStyle(
                            font: arabicBoldFont,
                            fontSize: 11,
                          ),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        if (item.description != null && item.description!.isNotEmpty)
                          pw.Text(
                            item.description!,
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 9,
                              color: PdfColors.grey700,
                            ),
                            textDirection: pw.TextDirection.rtl,
                          ),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      '${item.quantity.toStringAsFixed(item.quantity == item.quantity.toInt() ? 0 : 2)}${item.unit != null ? ' ${item.unit}' : ''}',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 11,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      '${item.unitPrice.toStringAsFixed(2)} ر.س',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 11,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      '${item.total.toStringAsFixed(2)} ر.س',
                      style: pw.TextStyle(
                        font: arabicBoldFont,
                        fontSize: 11,
                        color: const PdfColor.fromInt(0xFF1976D2),
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Build enhanced financial summary section
  static pw.Widget _buildEnhancedFinancialSummary(
    app_invoice.Invoice invoice,
    double totalPaid,
    double remainingAmount,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
  ) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [
            PdfColor.fromInt(0xFFF8F9FA),
            PdfColor.fromInt(0xFFE9ECEF),
          ],
        ),
        border: pw.Border.all(color: const PdfColor.fromInt(0xFF6C757D)),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      padding: const pw.EdgeInsets.all(15),
      child: pw.Column(
        children: [
          // Summary title
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFF6C757D),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Text(
              'الملخص المالي',
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 14,
                color: PdfColors.white,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),

          pw.SizedBox(height: 12),

          // Financial details in a grid layout
          pw.Row(
            children: [
              // Left column
              pw.Expanded(
                child: pw.Column(
                  children: [
                    _buildFinancialCard(
                      'المجموع الفرعي',
                      '${invoice.subtotal.toStringAsFixed(2)} ر.س',
                      const PdfColor.fromInt(0xFF17A2B8),
                      arabicFont,
                      arabicBoldFont,
                    ),
                    pw.SizedBox(height: 8),
                    if (invoice.discount > 0)
                      _buildFinancialCard(
                        'الخصم',
                        '${invoice.discount.toStringAsFixed(2)} ر.س',
                        const PdfColor.fromInt(0xFFDC3545),
                        arabicFont,
                        arabicBoldFont,
                      ),
                  
                  ],
                ),
              ),

              pw.SizedBox(width: 15),

              // Right column
              pw.Expanded(
                child: pw.Column(
                  children: [
                    _buildFinancialCard(
                      'الضريبة (${(invoice.taxRate * 100).toStringAsFixed(0)}%)',
                      '${invoice.taxAmount.toStringAsFixed(2)} ر.س',
                      const PdfColor.fromInt(0xFFFFC107),
                      arabicFont,
                      arabicBoldFont,
                    ),
                    pw.SizedBox(height: 8),
                    _buildFinancialCard(
                      'الإجمالي الكلي',
                      '${invoice.total.toStringAsFixed(2)} ر.س',
                      const PdfColor.fromInt(0xFF007BFF),
                      arabicFont,
                      arabicBoldFont,
                      isTotal: true,
                    ),
                    pw.SizedBox(height: 8),
                    _buildFinancialCard(
                      'المبلغ المتبقي',
                      '${remainingAmount.toStringAsFixed(2)} ر.س',
                      remainingAmount > 0
                          ? const PdfColor.fromInt(0xFFDC3545)
                          : const PdfColor.fromInt(0xFF28A745),
                      arabicFont,
                      arabicBoldFont,
                      isHighlight: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build individual financial card
  static pw.Widget _buildFinancialCard(
    String label,
    String value,
    PdfColor color,
    pw.Font arabicFont,
    pw.Font arabicBoldFont, {
    bool isTotal = false,
    bool isHighlight = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: isHighlight ? color.shade(0.1) : PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(6)),
        border: pw.Border.all(
          color: color,
          width: isTotal ? 3 : 1,
        ),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: isTotal ? 12 : 11,
              color: color,
            ),
            textDirection: pw.TextDirection.rtl,
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            value,
            style: pw.TextStyle(
              font: arabicBoldFont,
              fontSize: isTotal ? 16 : 14,
              color: isHighlight ? color : PdfColors.black,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build payment history section
  static pw.Widget _buildPaymentHistorySection(
    List<app_transaction.Transaction> payments,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
  ) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: const PdfColor.fromInt(0xFF28A745)),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Column(
        children: [
          // Header
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              gradient: const pw.LinearGradient(
                colors: [
                  PdfColor.fromInt(0xFF28A745),
                  PdfColor.fromInt(0xFF20C997),
                ],
              ),
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Text(
              'سجل المدفوعات',
              style: pw.TextStyle(
                font: arabicBoldFont,
                fontSize: 14,
                color: PdfColors.white,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),

          // Table Header
          pw.Container(
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFE8F5E8),
            ),
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 6),
            child: pw.Row(
              children: [
                pw.Expanded(
                  flex: 1,
                  child: pw.Text(
                    '#',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 11,
                      color: const PdfColor.fromInt(0xFF28A745),
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'التاريخ',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 11,
                      color: const PdfColor.fromInt(0xFF28A745),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'طريقة الدفع',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 11,
                      color: const PdfColor.fromInt(0xFF28A745),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Expanded(
                  flex: 2,
                  child: pw.Text(
                    'المبلغ',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 11,
                      color: const PdfColor.fromInt(0xFF28A745),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Expanded(
                  flex: 3,
                  child: pw.Text(
                    'ملاحظات',
                    style: pw.TextStyle(
                      font: arabicBoldFont,
                      fontSize: 11,
                      color: const PdfColor.fromInt(0xFF28A745),
                    ),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
              ],
            ),
          ),

          // Payment Items
          ...List.generate(payments.length, (index) {
            final payment = payments[index];

            // Get payment details
            String details = '';
            if (payment.paymentMethod == app_transaction.PaymentMethod.cash && payment.employeeName != null) {
              details = 'استلام: ${payment.employeeName}';
            } else if (payment.paymentMethod == app_transaction.PaymentMethod.bankTransfer && payment.bankAccountName != null) {
              details = 'حساب: ${payment.bankAccountName}';
            } else if (payment.description != null && payment.description!.isNotEmpty) {
              details = payment.description!;
            }

            return pw.Container(
              decoration: pw.BoxDecoration(
                color: index % 2 == 0
                    ? PdfColors.white
                    : const PdfColor.fromInt(0xFFF8F9FA),
                border: pw.Border(
                  bottom: pw.BorderSide(
                    color: const PdfColor.fromInt(0xFFE0E0E0),
                    width: 0.5,
                  ),
                ),
              ),
              padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 6),
              child: pw.Row(
                children: [
                  pw.Expanded(
                    flex: 1,
                    child: pw.Text(
                      '${index + 1}',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 10,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      DateFormat('yyyy/MM/dd').format(payment.date),
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 10,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      app_transaction.Transaction.getPaymentMethodName(payment.paymentMethod),
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 10,
                      ),
                      textAlign: pw.TextAlign.center,
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ),
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      '${payment.amount.toStringAsFixed(2)} ر.س',
                      style: pw.TextStyle(
                        font: arabicBoldFont,
                        fontSize: 10,
                        color: const PdfColor.fromInt(0xFF28A745),
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ),
                  pw.Expanded(
                    flex: 3,
                    child: pw.Text(
                      details,
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 9,
                        color: PdfColors.grey700,
                      ),
                      textAlign: pw.TextAlign.center,
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Build professional footer
  static pw.Widget _buildProfessionalFooter(
    String username,
    pw.Font arabicFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 12, horizontal: 15),
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [
            PdfColor.fromInt(0xFFF8F9FA),
            PdfColor.fromInt(0xFFE9ECEF),
          ],
        ),
        border: pw.Border.all(color: const PdfColor.fromInt(0xFFDEE2E6)),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'المستخدم: $username',
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 10,
                  color: const PdfColor.fromInt(0xFF6C757D),
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.Text(
                'تاريخ الطباعة: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 10,
                  color: const PdfColor.fromInt(0xFF6C757D),
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFF007BFF),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(15)),
            ),
            child: pw.Text(
              'نظام إدارة خدمات التكييف',
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 9,
                color: PdfColors.white,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  /// Build enhanced info row with better styling
  static pw.Widget _buildEnhancedInfoRow(
    String label,
    String value,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: 8,
            height: 8,
            margin: const pw.EdgeInsets.only(top: 4, left: 5),
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFF007BFF),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
          ),
          pw.Text(
            label,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 11,
              color: const PdfColor.fromInt(0xFF495057),
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(width: 8),
          pw.Expanded(
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: regularFont,
                fontSize: 11,
                color: PdfColors.black,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to build an information row in the PDF
  static pw.Widget _buildPdfInfoRow(
    String label,
    String value,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.start,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: boldFont,
              color: PdfColors.blue800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(width: 5),
          pw.Expanded(
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: regularFont,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to build a summary row in the PDF
  static pw.Widget _buildPdfSummaryRow(
    String label,
    String value,
    pw.Font regularFont,
    pw.Font boldFont,
    [PdfColor? valueColor]
  ) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: boldFont,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              font: regularFont,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Export a simple PDF report
  static Future<String?> createSimpleReport({
    required String title,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<dynamic> data,
    required Map<String, double> summaryData,
    required BuildContext context,
  }) async {
    try {
      // Create a PDF document
      final pdf = pw.Document();

      // Load Arabic fonts
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        // Use a fallback font if the specified font can't be loaded
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        // Use a fallback font if the specified font can't be loaded
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // Load company info
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // Load logo image
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // Create a simple placeholder image
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // Use the same image for watermark
      final watermarkImageBytes = logoImageBytes;

      // Get current user name
      final String username = 'admin'; // TODO: Get from auth state

      // Create a simple PDF page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Company Header with logo and bilingual information
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    border: pw.Border.all(color: PdfColors.blue200),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                  ),
                  child: pw.Row(
                    children: [
                      // Left side - English information
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text(
                              companyInfo?.nameEn ?? 'Ice Corner',
                              style: pw.TextStyle(
                                font: arabicBoldFont,
                                fontSize: 16,
                                color: PdfColors.blue900,
                              ),
                            ),
                            if (companyInfo?.addressEn != null)
                              pw.Text(
                                companyInfo!.addressEn!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                              ),
                            if (companyInfo?.phoneEn != null)
                              pw.Text(
                                companyInfo!.phoneEn!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Center - Logo
                      pw.Container(
                        width: 80,
                        height: 80,
                        child: pw.Image(
                          pw.MemoryImage(logoImageBytes),
                          fit: pw.BoxFit.contain,
                        ),
                      ),

                      // Right side - Arabic information
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                          children: [
                            pw.Text(
                              companyInfo?.nameAr ?? 'ركن الجليد',
                              style: pw.TextStyle(
                                font: arabicBoldFont,
                                fontSize: 16,
                                color: PdfColors.blue900,
                              ),
                              textDirection: pw.TextDirection.rtl,
                            ),
                            if (companyInfo?.addressAr != null)
                              pw.Text(
                                companyInfo!.addressAr!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                            if (companyInfo?.phoneAr != null)
                              pw.Text(
                                companyInfo!.phoneAr!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Report title
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  margin: const pw.EdgeInsets.only(top: 10),
                  color: PdfColors.blue100,
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        title,
                        style: pw.TextStyle(
                          font: arabicBoldFont,
                          fontSize: 20,
                          color: PdfColors.blue900,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        DateFormat('yyyy/MM/dd').format(DateTime.now()),
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Date range
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  color: PdfColors.grey100,
                  child: pw.Row(
                    children: [
                      pw.Text(
                        'الفترة: ${DateFormat('yyyy/MM/dd').format(startDate)} - ${DateFormat('yyyy/MM/dd').format(endDate)}',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Summary section
                pw.Text(
                  'ملخص التقرير',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 16,
                    color: PdfColors.blue800,
                  ),
                ),

                pw.SizedBox(height: 10),

                // Summary data
                _buildSummaryTable(reportType, summaryData, arabicFont, arabicBoldFont),

                pw.SizedBox(height: 20),

                // Data section
                pw.Text(
                  'بيانات التقرير',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 16,
                    color: PdfColors.blue800,
                  ),
                ),

                pw.SizedBox(height: 10),

                // Data table
                _buildDataTable(reportType, data, arabicFont, arabicBoldFont),

                pw.SizedBox(height: 20),

                // Watermark in the middle of the page
                pw.SizedBox(
                  height: 200, // Add space for the watermark
                  child: pw.Stack(
                    children: [
                      // Background content (empty in this case)
                      pw.Container(),

                      // Centered watermark
                      pw.Center(
                        child: pw.Opacity(
                          opacity: 0.05, // Make it very subtle
                          child: pw.Image(
                            pw.MemoryImage(watermarkImageBytes),
                            width: 150,
                            height: 150,
                            fit: pw.BoxFit.contain,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Footer with date, time and username
                pw.Container(
                  padding: const pw.EdgeInsets.only(top: 10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300)),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'المستخدم: $username',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 10,
                          color: PdfColors.grey700,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        'تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 10,
                          color: PdfColors.grey700,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );

      // Save the PDF to a file
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/report_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      debugPrint('PDF saved to: ${file.path}');
      return file.path;
    } catch (e, stackTrace) {
      debugPrint('Error creating PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// Print a PDF directly without saving to file
  static Future<bool> printReport({
    required String title,
    required String reportType,
    required DateTime startDate,
    required DateTime endDate,
    required List<dynamic> data,
    required Map<String, double> summaryData,
  }) async {
    try {
      // Create a PDF document
      final pdf = pw.Document();

      // Load Arabic fonts
      late pw.Font arabicFont;
      late pw.Font arabicBoldFont;

      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        arabicFont = pw.Font.ttf(fontData);
      } catch (e) {
        debugPrint('Error loading regular font: $e');
        // Use a fallback font if the specified font can't be loaded
        arabicFont = pw.Font.helvetica();
      }

      try {
        final fontDataBold = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        arabicBoldFont = pw.Font.ttf(fontDataBold);
      } catch (e) {
        debugPrint('Error loading bold font: $e');
        // Use a fallback font if the specified font can't be loaded
        arabicBoldFont = pw.Font.helveticaBold();
      }

      // Load company info
      final companyInfoRepo = CompanyInfoRepository();
      final companyInfo = await companyInfoRepo.getCompanyInfo();

      // Load logo image
      late Uint8List logoImageBytes;
      try {
        final logoImage = await rootBundle.load('assets/images/snowflake_logo.png');
        logoImageBytes = logoImage.buffer.asUint8List();
      } catch (e) {
        debugPrint('Error loading logo image: $e');
        // Create a simple placeholder image
        logoImageBytes = Uint8List.fromList([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
          0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
          0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
          0x42, 0x60, 0x82
        ]); // 1x1 transparent PNG
      }

      // Use the same image for watermark
      final watermarkImageBytes = logoImageBytes;

      // Get current user name
      final String username = 'admin'; // TODO: Get from auth state

      // Create a simple PDF page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Company Header with logo and bilingual information
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    border: pw.Border.all(color: PdfColors.blue200),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                  ),
                  child: pw.Row(
                    children: [
                      // Left side - English information
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Text(
                              companyInfo?.nameEn ?? 'Ice Corner',
                              style: pw.TextStyle(
                                font: arabicBoldFont,
                                fontSize: 16,
                                color: PdfColors.blue900,
                              ),
                            ),
                            if (companyInfo?.addressEn != null)
                              pw.Text(
                                companyInfo!.addressEn!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                              ),
                            if (companyInfo?.phoneEn != null)
                              pw.Text(
                                companyInfo!.phoneEn!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Center - Logo
                      pw.Container(
                        width: 80,
                        height: 80,
                        child: pw.Image(
                          pw.MemoryImage(logoImageBytes),
                          fit: pw.BoxFit.contain,
                        ),
                      ),

                      // Right side - Arabic information
                      pw.Expanded(
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                          children: [
                            pw.Text(
                              companyInfo?.nameAr ?? 'ركن الجليد',
                              style: pw.TextStyle(
                                font: arabicBoldFont,
                                fontSize: 16,
                                color: PdfColors.blue900,
                              ),
                              textDirection: pw.TextDirection.rtl,
                            ),
                            if (companyInfo?.addressAr != null)
                              pw.Text(
                                companyInfo!.addressAr!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                            if (companyInfo?.phoneAr != null)
                              pw.Text(
                                companyInfo!.phoneAr!,
                                style: pw.TextStyle(
                                  font: arabicFont,
                                  fontSize: 10,
                                ),
                                textDirection: pw.TextDirection.rtl,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Report title
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  margin: const pw.EdgeInsets.only(top: 10),
                  color: PdfColors.blue100,
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        title,
                        style: pw.TextStyle(
                          font: arabicBoldFont,
                          fontSize: 20,
                          color: PdfColors.blue900,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        DateFormat('yyyy/MM/dd').format(DateTime.now()),
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Date range
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  color: PdfColors.grey100,
                  child: pw.Row(
                    children: [
                      pw.Text(
                        'الفترة: ${DateFormat('yyyy/MM/dd').format(startDate)} - ${DateFormat('yyyy/MM/dd').format(endDate)}',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Summary section
                pw.Text(
                  'ملخص التقرير',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 16,
                    color: PdfColors.blue800,
                  ),
                ),

                pw.SizedBox(height: 10),

                // Summary data
                _buildSummaryTable(reportType, summaryData, arabicFont, arabicBoldFont),

                pw.SizedBox(height: 20),

                // Data section
                pw.Text(
                  'بيانات التقرير',
                  style: pw.TextStyle(
                    font: arabicBoldFont,
                    fontSize: 16,
                    color: PdfColors.blue800,
                  ),
                ),

                pw.SizedBox(height: 10),

                // Data table
                _buildDataTable(reportType, data, arabicFont, arabicBoldFont),

                pw.SizedBox(height: 20),

                // Watermark in the middle of the page
                pw.SizedBox(
                  height: 200, // Add space for the watermark
                  child: pw.Stack(
                    children: [
                      // Background content (empty in this case)
                      pw.Container(),

                      // Centered watermark
                      pw.Center(
                        child: pw.Opacity(
                          opacity: 0.05, // Make it very subtle
                          child: pw.Image(
                            pw.MemoryImage(watermarkImageBytes),
                            width: 150,
                            height: 150,
                            fit: pw.BoxFit.contain,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Footer with date, time and username
                pw.Container(
                  padding: const pw.EdgeInsets.only(top: 10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300)),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'المستخدم: $username',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 10,
                          color: PdfColors.grey700,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        'تاريخ التقرير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 10,
                          color: PdfColors.grey700,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );

      // Print the PDF directly
      final bytes = await pdf.save();
      final result = await Printing.layoutPdf(
        onLayout: (_) => bytes,
        name: title,
        format: PdfPageFormat.a4,
        dynamicLayout: true,
        usePrinterSettings: true,
      );

      return result;
    } catch (e, stackTrace) {
      debugPrint('Error printing PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  // Helper methods for building tables
  static pw.Widget _buildSummaryTable(
    String reportType,
    Map<String, double> summaryData,
    pw.Font regularFont,
    pw.Font boldFont
  ) {
    // Ensure all values are finite
    final cleanData = <String, double>{};
    summaryData.forEach((key, value) {
      cleanData[key] = value.isFinite ? value : 0.0;
    });

    // Create a list of summary cards
    final List<pw.Widget> cards = [];

    // Define colors
    final PdfColor primaryColor = PdfColors.blue700;
    final PdfColor incomeColor = PdfColors.green700;
    final PdfColor expenseColor = PdfColors.red700;

    // Add cards based on report type
    if (reportType == 'invoices' || reportType == 'customer') {
      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'إجمالي الفواتير',
          value: '${cleanData['total']?.toStringAsFixed(2) ?? '0.00'} ر.س',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(0.95, 0.95, 1.0), // Light blue
          valueColor: primaryColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'المدفوع',
          value: '${cleanData['paid']?.toStringAsFixed(2) ?? '0.00'} ر.س',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(0.95, 1.0, 0.95), // Light green
          valueColor: incomeColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'غير المدفوع',
          value: '${cleanData['unpaid']?.toStringAsFixed(2) ?? '0.00'} ر.س',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(1.0, 0.95, 0.95), // Light red
          valueColor: expenseColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'عدد الفواتير',
          value: '${cleanData['count']?.toInt() ?? 0}',
          titleFont: boldFont,
          valueFont: boldFont,
        ),
      );

      if (reportType == 'customer') {
        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'الخدمات المستهلكة',
            value: '${cleanData['servicesTotal']?.toInt() ?? 0}',
            titleFont: boldFont,
            valueFont: boldFont,
          ),
        );

        cards.add(
          ArabicPdfHelper.summaryCard(
            title: 'الرصيد المتبقي',
            value: '${cleanData['balance']?.toStringAsFixed(2) ?? '0.00'} ر.س',
            titleFont: boldFont,
            valueFont: boldFont,
            valueColor: (cleanData['balance'] ?? 0.0) < 0 ? expenseColor : incomeColor,
          ),
        );
      }
    } else if (reportType == 'services') {
      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'إجمالي الطلبات',
          value: '${cleanData['total']?.toInt() ?? 0}',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(0.95, 0.95, 1.0), // Light blue
          valueColor: primaryColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'المكتملة',
          value: '${cleanData['completed']?.toInt() ?? 0}',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(0.95, 1.0, 0.95), // Light green
          valueColor: incomeColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'قيد التنفيذ',
          value: '${cleanData['inProgress']?.toInt() ?? 0}',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(1.0, 0.98, 0.9), // Light yellow
          valueColor: const PdfColor(0.9, 0.6, 0.0), // Orange
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'في الانتظار',
          value: '${cleanData['pending']?.toInt() ?? 0}',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(1.0, 0.95, 0.95), // Light red
          valueColor: expenseColor,
        ),
      );
    } else if (reportType == 'transactions' || reportType == 'supplier' || reportType == 'employee') {
      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'الإيرادات',
          value: '${cleanData['income']?.toStringAsFixed(2) ?? '0.00'} ر.س',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(0.95, 1.0, 0.95), // Light green
          valueColor: incomeColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'المصروفات',
          value: '${cleanData['expense']?.toStringAsFixed(2) ?? '0.00'} ر.س',
          titleFont: boldFont,
          valueFont: boldFont,
          backgroundColor: const PdfColor(1.0, 0.95, 0.95), // Light red
          valueColor: expenseColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'الرصيد',
          value: '${cleanData['balance']?.toStringAsFixed(2) ?? '0.00'} ر.س',
          titleFont: boldFont,
          valueFont: boldFont,
          valueColor: (cleanData['balance'] ?? 0.0) < 0 ? expenseColor : incomeColor,
        ),
      );

      cards.add(
        ArabicPdfHelper.summaryCard(
          title: 'عدد المعاملات',
          value: '${cleanData['count']?.toInt() ?? 0}',
          titleFont: boldFont,
          valueFont: boldFont,
        ),
      );
    }

    // Arrange cards in a row with wrapping
    return pw.Wrap(
      spacing: 10,
      runSpacing: 10,
      children: cards,
    );
  }

  static pw.Widget _buildDataTable(
    String reportType,
    List<dynamic> data,
    pw.Font regularFont,
    pw.Font boldFont
  ) {
    if (data.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        ),
        child: pw.Center(
          child: pw.Text(
            ArabicTextHelper.processArabicText('لا توجد بيانات للعرض'),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 16,
              color: PdfColors.grey700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      );
    }

    // Check if we're using the new format (Map with structured data)
    if (reportType == 'customer' && data.isNotEmpty && data[0] is Map<String, dynamic>) {
      final reportData = data[0] as Map<String, dynamic>;
      final customer = reportData['customer'] as Customer?;
      final items = reportData['items'] as List<Map<String, dynamic>>?;

      if (customer != null) {
        // First, show customer information
        final customerInfoTable = _buildCustomerInfoSection(customer, regularFont, boldFont);

        // Create a multi-page PDF with different sections
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Customer information table
            customerInfoTable,

            pw.SizedBox(height: 20),

            // Summary section
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: const PdfColor(0.95, 1.0, 0.95),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                border: pw.Border.all(color: PdfColors.green200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    ArabicTextHelper.processArabicText('ملخص الحساب'),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.green800,
                    ),
                  ),
                  pw.SizedBox(height: 10),

                  // الفواتير والمدفوعات
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      _buildSummaryItem(
                        'إجمالي المبيعات',
                        '${(reportData['summary']['totalAmount'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        PdfColors.blue700
                      ),
                      _buildSummaryItem(
                        'المدفوعات',
                        '${(reportData['summary']['totalPaid'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        PdfColors.green700
                      ),
                      _buildSummaryItem(
                        'المستحقات',
                        '${(reportData['summary']['totalDue'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        PdfColors.orange700
                      ),
                    ],
                  ),

                  pw.SizedBox(height: 10),

                  // المعاملات المالية
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      _buildSummaryItem(
                        'إجمالي الإيرادات',
                        '${(reportData['summary']['totalIncome'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        PdfColors.green700
                      ),
                      _buildSummaryItem(
                        'إجمالي المصروفات',
                        '${(reportData['summary']['totalExpenses'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        PdfColors.red700
                      ),
                      _buildSummaryItem(
                        'الرصيد',
                        '${(reportData['summary']['transactionBalance'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        (reportData['summary']['transactionBalance'] as double) >= 0 ? PdfColors.green700 : PdfColors.red700
                      ),
                    ],
                  ),

                  pw.SizedBox(height: 10),

                  // طلبات الخدمة
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      _buildSummaryItem(
                        'إجمالي طلبات الخدمة',
                        '${reportData['summary']['serviceRequestsCount']}',
                        regularFont,
                        boldFont,
                        PdfColors.blue700
                      ),
                      _buildSummaryItem(
                        'قيمة الخدمات',
                        '${(reportData['summary']['serviceAmount'] as double).toStringAsFixed(2)} ر.س',
                        regularFont,
                        boldFont,
                        PdfColors.blue700
                      ),
                      _buildSummaryItem(
                        'طريقة الدفع',
                        '${reportData['summary']['paymentMethod']}',
                        regularFont,
                        boldFont,
                        PdfColors.blue700
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Invoices section if available
            if (items != null && items.any((item) => item['type'] == 'invoice')) ...[
              pw.Text(
                ArabicTextHelper.processArabicText('فواتير العميل'),
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 14,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 10),
              _buildLegacyInvoicesTable(items.where((item) => item['type'] == 'invoice').map((item) =>
                item['data'] as Invoice
              ).toList(), regularFont, boldFont),
              pw.SizedBox(height: 20),
            ],

            // Transactions section if available
            if (items != null && items.any((item) => item['type'] == 'transaction')) ...[
              pw.Text(
                ArabicTextHelper.processArabicText('المعاملات المالية'),
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 14,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 10),
              _buildLegacyTransactionsTable(items.where((item) => item['type'] == 'transaction').map((item) =>
                item['data'] as Transaction
              ).toList(), regularFont, boldFont),
              pw.SizedBox(height: 20),
            ],

            // Service requests section if available
            if (items != null && items.any((item) => item['type'] == 'service')) ...[
              pw.Text(
                ArabicTextHelper.processArabicText('طلبات الخدمة'),
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 14,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 10),
              _buildLegacyServicesTable(items.where((item) => item['type'] == 'service').map((item) =>
                item['data'] as ServiceRequest
              ).toList(), regularFont, boldFont),
            ],
          ],
        );
      }
    }

    if (reportType == 'customer') {
      // إذا كانت البيانات كاملة: [Customer, List<Invoice>, List<Transaction>, List<ServiceRequest>]
      if (data.length == 4 && data[0] is Customer) {
        final customer = data[0] as Customer;
        final invoices = (data[1] as List).cast<Invoice>();
        final transactions = (data[2] as List).cast<Transaction>();
        final serviceRequests = (data[3] as List).cast<ServiceRequest>();

        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            _buildCustomerInfoSection(customer, regularFont, boldFont),
            pw.SizedBox(height: 20),

            // ملخص الحساب
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: const PdfColor(0.95, 1.0, 0.95),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                border: pw.Border.all(color: PdfColors.green200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    ArabicTextHelper.processArabicText('ملخص الحساب'),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.green800,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      _buildSummaryItem('إجمالي الفواتير',
                        '${invoices.fold(0.0, (sum, i) => sum + i.total).toStringAsFixed(2)} ر.س',
                        regularFont, boldFont, PdfColors.blue700),
                      _buildSummaryItem('المدفوعات',
                        '${invoices.where((i) => i.isPaid).fold(0.0, (sum, i) => sum + i.total).toStringAsFixed(2)} ر.س',
                        regularFont, boldFont, PdfColors.green700),
                      _buildSummaryItem('المستحقات',
                        '${invoices.where((i) => !i.isPaid).fold(0.0, (sum, i) => sum + i.total).toStringAsFixed(2)} ر.س',
                        regularFont, boldFont, PdfColors.orange700),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // الفواتير
            pw.Text(
              ArabicTextHelper.processArabicText('فواتير العميل'),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 14,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 10),
            invoices.isNotEmpty
                ? _buildLegacyInvoicesTable(invoices, regularFont, boldFont)
                : pw.Text(
                    ArabicTextHelper.processArabicText('لا توجد فواتير للعرض'),
                    style: pw.TextStyle(font: regularFont, fontSize: 12),
                  ),
            pw.SizedBox(height: 20),

            // المعاملات المالية
            pw.Text(
              ArabicTextHelper.processArabicText('المعاملات المالية'),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 14,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 10),
            transactions.isNotEmpty
                ? _buildLegacyTransactionsTable(transactions, regularFont, boldFont)
                : pw.Text(
                    ArabicTextHelper.processArabicText('لا توجد معاملات مالية للعرض'),
                    style: pw.TextStyle(font: regularFont, fontSize: 12),
                  ),
            pw.SizedBox(height: 20),

            // طلبات الخدمة
            pw.Text(
              ArabicTextHelper.processArabicText('طلبات الخدمة'),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 14,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 10),
            serviceRequests.isNotEmpty
                ? _buildLegacyServicesTable(serviceRequests, regularFont, boldFont)
                : pw.Text(
                    ArabicTextHelper.processArabicText('لا توجد طلبات خدمة للعرض'),
                    style: pw.TextStyle(font: regularFont, fontSize: 12),
                  ),
          ],
        );
      }
      // في حالة تقرير العميل، نعرض معلومات العميل أولاً ثم الفواتير
      if (data.length == 1 && data[0] is Customer) {
        final customer = data[0] as Customer;

        // نفترض أن البيانات الأخرى (الفواتير، المعاملات، طلبات الخدمة) ستكون فارغة
        // في التطبيق الحقيقي، يجب جلب هذه البيانات من قاعدة البيانات

        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // معلومات العميل
            _buildCustomerInfoSection(customer, regularFont, boldFont),
            pw.SizedBox(height: 20),

            // ملخص الحساب
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                color: const PdfColor(0.95, 1.0, 0.95),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                border: pw.Border.all(color: PdfColors.green200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    ArabicTextHelper.processArabicText('ملخص الحساب'),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.green800,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      _buildSummaryItem('إجمالي الفواتير', '0.00 ر.س', regularFont, boldFont, PdfColors.blue700),
                      _buildSummaryItem('المدفوعات', '0.00 ر.س', regularFont, boldFont, PdfColors.green700),
                      _buildSummaryItem('المستحقات', '0.00 ر.س', regularFont, boldFont, PdfColors.orange700),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // عنوان قسم الفواتير
            pw.Text(
              ArabicTextHelper.processArabicText('فواتير العميل'),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 14,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 10),
            // جدول الفواتير
            pw.Text(
              ArabicTextHelper.processArabicText('لا توجد فواتير للعرض'),
              style: pw.TextStyle(font: regularFont, fontSize: 12),
            ),

            pw.SizedBox(height: 20),

            // عنوان قسم المعاملات المالية
            pw.Text(
              ArabicTextHelper.processArabicText('المعاملات المالية'),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 14,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 10),
            // جدول المعاملات
            pw.Text(
              ArabicTextHelper.processArabicText('لا توجد معاملات مالية للعرض'),
              style: pw.TextStyle(font: regularFont, fontSize: 12),
            ),

            pw.SizedBox(height: 20),

            // عنوان قسم طلبات الخدمة
            pw.Text(
              ArabicTextHelper.processArabicText('طلبات الخدمة'),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 14,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 10),
            // جدول طلبات الخدمة
            pw.Text(
              ArabicTextHelper.processArabicText('لا توجد طلبات خدمة للعرض'),
              style: pw.TextStyle(font: regularFont, fontSize: 12),
            ),
          ],
        );
      }
      return _buildLegacyInvoicesTable(data.cast<Invoice>(), regularFont, boldFont);
    } else if (reportType == 'invoices') {
      return _buildLegacyInvoicesTable(data.cast<Invoice>(), regularFont, boldFont);
    } else if (reportType == 'services') {
      return _buildLegacyServicesTable(data.cast<ServiceRequest>(), regularFont, boldFont);
    } else if (reportType == 'transactions' || reportType == 'supplier' || reportType == 'employee') {
      return _buildLegacyTransactionsTable(data.cast<Transaction>(), regularFont, boldFont);
    }

    return pw.Container();
  }

  /// بناء قسم معلومات العميل
  static pw.Widget _buildCustomerInfoSection(Customer customer, pw.Font regularFont, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: const PdfColor(0.95, 0.95, 1.0),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.symmetric(vertical: 5),
            decoration: const pw.BoxDecoration(
              color: PdfColors.blue800,
              borderRadius: pw.BorderRadius.all(pw.Radius.circular(3)),
            ),
            child: pw.Center(
              child: pw.Text(
                ArabicTextHelper.processArabicText('معلومات العميل'),
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 14,
                  color: PdfColors.white,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ),
          ),
          pw.SizedBox(height: 10),

          // اسم العميل بشكل بارز
          pw.Container(
            width: double.infinity,
            padding: const pw.EdgeInsets.symmetric(vertical: 5, horizontal: 10),
            decoration: const pw.BoxDecoration(
              color: PdfColors.blue50,
              borderRadius: pw.BorderRadius.all(pw.Radius.circular(3)),
            ),
            child: pw.Text(
              ArabicTextHelper.processArabicText(customer.name),
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 16,
                color: PdfColors.blue900,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.SizedBox(height: 10),

          // بيانات العميل في صفين
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العمود الأول
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow('نوع العميل:', customer.type == CustomerType.company ? 'شركة' : 'فرد', regularFont, boldFont),
                    if (customer.email != null && customer.email!.isNotEmpty)
                      _buildInfoRow('البريد الإلكتروني:', customer.email!, regularFont, boldFont),
                    if (customer.contactPerson != null && customer.contactPerson!.isNotEmpty)
                      _buildInfoRow('الشخص المسؤول:', customer.contactPerson!, regularFont, boldFont),
                  ],
                ),
              ),
              // العمود الثاني
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    if (customer.phone != null && customer.phone!.isNotEmpty)
                      _buildInfoRow('رقم الهاتف:', customer.phone!, regularFont, boldFont),
                    if (customer.address != null && customer.address!.isNotEmpty)
                      _buildInfoRow('العنوان:', customer.address!, regularFont, boldFont),
                  ],
                ),
              ),
            ],
          ),

          // ملاحظات العميل
          if (customer.notes != null && customer.notes!.isNotEmpty) pw.SizedBox(height: 10),
          if (customer.notes != null && customer.notes!.isNotEmpty)
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(5),
              decoration: pw.BoxDecoration(
                color: const PdfColor(1.0, 1.0, 0.9),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(3)),
                border: pw.Border.all(color: PdfColors.amber200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    ArabicTextHelper.processArabicText('ملاحظات:'),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 12,
                      color: PdfColors.amber900,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 3),
                  pw.Text(
                    ArabicTextHelper.processArabicText(customer.notes!),
                    style: pw.TextStyle(
                      font: regularFont,
                      fontSize: 10,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  static pw.Widget _buildInfoRow(String label, String value, pw.Font regularFont, pw.Font boldFont) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 5),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            ArabicTextHelper.processArabicText(label),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 10,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(width: 5),
          pw.Expanded(
            child: pw.Text(
              ArabicTextHelper.processArabicText(value),
              style: pw.TextStyle(
                font: regularFont,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص
  static pw.Widget _buildSummaryItem(String title, String value, pw.Font regularFont, pw.Font boldFont, PdfColor color) {
    return pw.Container(
      width: 120,
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
        border: pw.Border.all(color: color),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            ArabicTextHelper.processArabicText(title),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 10,
              color: color,
            ),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            ArabicTextHelper.processArabicText(value),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 12,
              color: PdfColors.black,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء جدول الفواتير من كائنات الفاتورة
  static pw.Widget _buildLegacyInvoicesTable(List<Invoice> invoices, pw.Font regularFont, pw.Font boldFont) {
    // Define headers with the new currency column
    final headers = ['الحالة', 'العملة', 'المبلغ', 'التاريخ', 'العميل', 'رقم الفاتورة'];

    // Convert invoice data to string lists for the ArabicPdfHelper
    final List<List<String>> tableData = invoices.map((invoice) => [
      invoice.isPaid ? 'مدفوعة' : 'غير مدفوعة',
      'ر.س', // Currency column (Saudi Riyal)
      invoice.total.isFinite ? NumberFormat('#,##0.00').format(invoice.total) : '0.00',
      DateFormat('yyyy/MM/dd').format(invoice.date),
      invoice.customerName,
      invoice.invoiceNumber,
    ]).toList();

    // Define custom alignments for each column
    final Map<int, pw.Alignment> customAlignments = {
      0: pw.Alignment.center,      // Status - centered
      1: pw.Alignment.center,      // Currency - centered
      2: pw.Alignment.centerLeft,  // Amount - left aligned (for numbers)
      3: pw.Alignment.center,      // Date - centered
      4: pw.Alignment.centerRight, // Customer - right aligned (for Arabic)
      5: pw.Alignment.center,      // Invoice number - centered
    };

    // Define conditional colors for status column
    final Map<int, Map<String, PdfColor>> conditionalColors = {
      0: {
        'مدفوعة': PdfColors.green700,
        'غير مدفوعة': PdfColors.orange700,
      },
    };

    // Use the ArabicPdfHelper to create a properly formatted table
    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: regularFont,
      boldFont: boldFont,
      customAlignments: customAlignments,
      headerColor: PdfColors.blue700,
      headerTextColor: PdfColors.white,
      conditionalColors: conditionalColors,
      rowColors: [
        PdfColors.white,
        const PdfColor(0.95, 0.95, 1.0), // Light blue for alternating rows
      ],
    );
  }

  /// Get service status name in Arabic
  static String _getServiceStatusName(ServiceStatus status) {
    switch (status) {
      case ServiceStatus.pending:
        return 'قيد الانتظار';
      case ServiceStatus.inProgress:
        return 'قيد التنفيذ';
      case ServiceStatus.completed:
        return 'مكتمل';
      case ServiceStatus.cancelled:
        return 'ملغي';
      default:
        return 'غير معروف';
    }
  }

  static pw.Widget _buildLegacyServicesTable(List<ServiceRequest> services, pw.Font regularFont, pw.Font boldFont) {
    // Define headers
    final headers = ['الحالة', 'التاريخ', 'الموظف الفني', 'نوع الخدمة', 'الوصف', 'المبلغ', 'العميل', 'رقم الطلب'];

    // Convert service data to string lists for the ArabicPdfHelper
    final List<List<String>> tableData = services.map((service) {
      // Handle status safely
      String status = 'قيد الانتظار';
      try {
        status = _getServiceStatusName(ServiceStatus.values[service.status.index]);
      } catch (e) {
        // Fallback to a default status if there's an error
        status = 'قيد الانتظار';
      }

      // Handle date safely
      String date = '';
      try {
        date = DateFormat('yyyy/MM/dd').format(service.scheduledDate);
      } catch (e) {
        date = DateFormat('yyyy/MM/dd').format(DateTime.now());
      }

      // Handle assigned technician safely
      String technician = 'غير محدد';
      try {
        // Try to access assignedTo property
        if (service.assignedTo != null) {
          technician = service.assignedTo.toString();
        }
      } catch (e) {
        // Ignore if property doesn't exist
      }

      // Handle service type safely
      String serviceType = 'صيانة';
      try {
        serviceType = service.serviceType;
      } catch (e) {
        // Use default value if property doesn't exist
      }

      // Handle description safely
      String description = '';
      try {
        description = service.description;
        if (description.length > 30) {
          description = '${description.substring(0, 30)}...';
        }
      } catch (e) {
        description = '';
      }

      // Handle service amount safely
      String amount = '0.00';
      // Just use default amount since we can't access the property directly

      // Handle customer name safely
      String customer = '';
      try {
        customer = service.customerName;
      } catch (e) {
        customer = 'عميل';
      }

      // Handle request number safely
      String requestNumber = '';
      try {
        requestNumber = service.requestNumber;
      } catch (e) {
        // Use empty string if property doesn't exist
        requestNumber = '';
      }

      return [
        status,
        date,
        technician,
        serviceType,
        description,
        amount,
        customer,
        requestNumber,
      ];
    }).toList();

    // Define custom alignments for each column
    final Map<int, pw.Alignment> customAlignments = {
      0: pw.Alignment.center,      // Status - centered
      1: pw.Alignment.center,      // Date - centered
      2: pw.Alignment.centerRight, // Technician - right aligned (for Arabic)
      3: pw.Alignment.centerRight, // Service type - right aligned (for Arabic)
      4: pw.Alignment.centerRight, // Description - right aligned (for Arabic)
      5: pw.Alignment.centerLeft,  // Amount - left aligned (for numbers)
      6: pw.Alignment.centerRight, // Customer - right aligned (for Arabic)
      7: pw.Alignment.center,      // Request number - centered
    };

    // Define conditional colors for status column
    final Map<int, Map<String, PdfColor>> conditionalColors = {
      0: {
        'مكتمل': PdfColors.green700,
        'قيد التنفيذ': PdfColors.blue700,
        'قيد الانتظار': PdfColors.orange700,
        'ملغي': PdfColors.red700,
      },
    };

    // Use the ArabicPdfHelper to create a properly formatted table
    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: regularFont,
      boldFont: boldFont,
      customAlignments: customAlignments,
      headerColor: PdfColors.blue700,
      headerTextColor: PdfColors.white,
      conditionalColors: conditionalColors,
      rowColors: [
        PdfColors.white,
        const PdfColor(0.95, 0.95, 1.0), // Light blue for alternating rows
      ],
    );
  }

  static pw.Widget _buildLegacyTransactionsTable(List<Transaction> transactions, pw.Font regularFont, pw.Font boldFont) {
    // Define headers with the currency column
    final headers = ['الوصف', 'التصنيف', 'النوع', 'العميل/المورد', 'المبلغ', 'التاريخ', 'المرجع'];

    // Convert transaction data to string lists for the ArabicPdfHelper
    final List<List<String>> tableData = transactions.map((transaction) {
      // Handle description safely
      String desc = '';
      try {
        desc = transaction.description;
        if (desc.length > 30) {
          desc = '${desc.substring(0, 30)}...';
        }
      } catch (e) {
        // If description is null or accessing it causes an error
        desc = '';
      }

      // Get related entity name
      String relatedEntity = 'بدون عميل/مورد';

      // Try to get the related entity name from different possible properties
      // depending on which Transaction class we're dealing with
      try {
        if (transaction.relatedEntityName != null) {
          relatedEntity = transaction.relatedEntityName!;
        }
      } catch (e) {
        // Ignore if property doesn't exist
      }

      return [
        desc,
        transaction.category ?? 'بدون تصنيف',
        transaction.type == TransactionType.income ? 'إيراد' : 'مصروف',
        relatedEntity,
        transaction.amount.isFinite ? NumberFormat('#,##0.00').format(transaction.amount) : '0.00',
        DateFormat('yyyy/MM/dd').format(transaction.date),
        transaction.reference,
      ];
    }).toList();

    // Define custom alignments for each column
    final Map<int, pw.Alignment> customAlignments = {
      0: pw.Alignment.centerRight, // Description - right aligned (for Arabic)
      1: pw.Alignment.centerRight, // Category - right aligned (for Arabic)
      2: pw.Alignment.center,      // Type - centered
      3: pw.Alignment.centerRight, // Customer/Supplier - right aligned (for Arabic)
      4: pw.Alignment.centerLeft,  // Amount - left aligned (for numbers)
      5: pw.Alignment.center,      // Date - centered
      6: pw.Alignment.center,      // Reference - centered
    };

    // Define conditional colors for transaction type column
    final Map<int, Map<String, PdfColor>> conditionalColors = {
      2: {
        'إيراد': PdfColors.green700,
        'مصروف': PdfColors.red700,
      },
    };

    // Use the ArabicPdfHelper to create a properly formatted table
    return ArabicPdfHelper.arabicTable(
      headers: headers,
      data: tableData,
      regularFont: regularFont,
      boldFont: boldFont,
      customAlignments: customAlignments,
      headerColor: PdfColors.blue700,
      headerTextColor: PdfColors.white,
      conditionalColors: conditionalColors,
      rowColors: [
        PdfColors.white,
        const PdfColor(0.95, 0.95, 1.0), // Light blue for alternating rows
      ],
    );
  }
}
