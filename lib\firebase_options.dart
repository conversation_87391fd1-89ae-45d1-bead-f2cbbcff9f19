// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBMINknzinayc1ER3TcN8_Orkbrcmvkp64',
    appId: '1:103107631157:web:placeholder',
    messagingSenderId: '103107631157',
    projectId: 'fir-db-40fbe',
    authDomain: 'fir-db-40fbe.firebaseapp.com',
    storageBucket: 'fir-db-40fbe.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBMINknzinayc1ER3TcN8_Orkbrcmvkp64',
    appId: '1:103107631157:android:4d702e1d371c790d34f97c',
    messagingSenderId: '103107631157',
    projectId: 'fir-db-40fbe',
    storageBucket: 'fir-db-40fbe.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCgTVyKSIGnfs_C47WskP1RsTkZ35fI6a4',
    appId: '1:103107631157:ios:placeholder',
    messagingSenderId: '103107631157',
    projectId: 'fir-db-40fbe',
    storageBucket: 'fir-db-40fbe.appspot.com',
    iosBundleId: 'com.icecornerhvac.appspot.com',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCgTVyKSIGnfs_C47WskP1RsTkZ35fI6a4',
    appId: '1:103107631157:macos:placeholder',
    messagingSenderId: '103107631157',
    projectId: 'fir-db-40fbe',
    storageBucket: 'fir-db-40fbe.appspot.com',
    iosBundleId: 'com.icecornerhvac.appspot.com',
  );
}
