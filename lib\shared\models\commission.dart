import 'dart:convert';
import 'package:flutter/material.dart';

/// أنواع العمولات
enum CommissionType {
  /// عمولة ثابتة
  fixed,
  
  /// عمولة نسبة مئوية
  percentage,
  
  /// عمولة متدرجة
  tiered,
}

/// حالة العمولة
enum CommissionStatus {
  /// معلقة
  pending,
  
  /// مدفوعة
  paid,
  
  /// ملغاة
  cancelled,
}

/// نموذج العمولة
class Commission {
  /// المعرف الفريد للعمولة
  final int? id;
  
  /// معرف الموظف
  final int employeeId;
  
  /// اسم الموظف
  final String employeeName;
  
  /// نوع العمولة
  final CommissionType type;
  
  /// قيمة العمولة (مبلغ ثابت أو نسبة مئوية)
  final double value;
  
  /// المبلغ النهائي للعمولة
  final double amount;
  
  /// تاريخ العمولة
  final DateTime date;
  
  /// حالة العمولة
  final CommissionStatus status;
  
  /// نوع الكيان المرتبط (فاتورة، طلب خدمة، إلخ)
  final String relatedEntityType;
  
  /// معرف الكيان المرتبط
  final int relatedEntityId;
  
  /// اسم الكيان المرتبط
  final String relatedEntityName;
  
  /// وصف العمولة
  final String? description;
  
  /// ملاحظات
  final String? notes;
  
  /// تاريخ الدفع
  final DateTime? paymentDate;
  
  /// معرف المعاملة المرتبطة بالدفع
  final int? paymentTransactionId;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ التحديث
  final DateTime? updatedAt;

  /// إنشاء نموذج عمولة جديد
  Commission({
    this.id,
    required this.employeeId,
    required this.employeeName,
    required this.type,
    required this.value,
    required this.amount,
    required this.date,
    required this.status,
    required this.relatedEntityType,
    required this.relatedEntityId,
    required this.relatedEntityName,
    this.description,
    this.notes,
    this.paymentDate,
    this.paymentTransactionId,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نموذج من خريطة بيانات
  factory Commission.fromMap(Map<String, dynamic> map) {
    return Commission(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      employeeName: map['employee_name'] as String,
      type: _parseCommissionType(map['type'] as String),
      value: map['value'] as double,
      amount: map['amount'] as double,
      date: DateTime.parse(map['date'] as String),
      status: _parseCommissionStatus(map['status'] as String),
      relatedEntityType: map['related_entity_type'] as String,
      relatedEntityId: map['related_entity_id'] as int,
      relatedEntityName: map['related_entity_name'] as String,
      description: map['description'] as String?,
      notes: map['notes'] as String?,
      paymentDate: map['payment_date'] != null
          ? DateTime.parse(map['payment_date'] as String)
          : null,
      paymentTransactionId: map['payment_transaction_id'] as int?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  /// تحويل النموذج إلى خريطة بيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'employee_name': employeeName,
      'type': type.toString().split('.').last,
      'value': value,
      'amount': amount,
      'date': date.toIso8601String(),
      'status': status.toString().split('.').last,
      'related_entity_type': relatedEntityType,
      'related_entity_id': relatedEntityId,
      'related_entity_name': relatedEntityName,
      'description': description,
      'notes': notes,
      'payment_date': paymentDate?.toIso8601String(),
      'payment_transaction_id': paymentTransactionId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// تحويل النموذج إلى سلسلة JSON
  String toJson() => json.encode(toMap());

  /// إنشاء نموذج من سلسلة JSON
  factory Commission.fromJson(String source) =>
      Commission.fromMap(json.decode(source) as Map<String, dynamic>);

  /// إنشاء نسخة معدلة من النموذج
  Commission copyWith({
    int? id,
    int? employeeId,
    String? employeeName,
    CommissionType? type,
    double? value,
    double? amount,
    DateTime? date,
    CommissionStatus? status,
    String? relatedEntityType,
    int? relatedEntityId,
    String? relatedEntityName,
    String? description,
    String? notes,
    DateTime? paymentDate,
    int? paymentTransactionId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Commission(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      type: type ?? this.type,
      value: value ?? this.value,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      status: status ?? this.status,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityName: relatedEntityName ?? this.relatedEntityName,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      paymentDate: paymentDate ?? this.paymentDate,
      paymentTransactionId: paymentTransactionId ?? this.paymentTransactionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل نوع العمولة من نص
  static CommissionType _parseCommissionType(String type) {
    switch (type) {
      case 'fixed':
        return CommissionType.fixed;
      case 'percentage':
        return CommissionType.percentage;
      case 'tiered':
        return CommissionType.tiered;
      default:
        return CommissionType.fixed;
    }
  }

  /// تحويل حالة العمولة من نص
  static CommissionStatus _parseCommissionStatus(String status) {
    switch (status) {
      case 'pending':
        return CommissionStatus.pending;
      case 'paid':
        return CommissionStatus.paid;
      case 'cancelled':
        return CommissionStatus.cancelled;
      default:
        return CommissionStatus.pending;
    }
  }

  /// الحصول على اسم نوع العمولة
  static String getCommissionTypeName(CommissionType type) {
    switch (type) {
      case CommissionType.fixed:
        return 'مبلغ ثابت';
      case CommissionType.percentage:
        return 'نسبة مئوية';
      case CommissionType.tiered:
        return 'متدرجة';
    }
  }

  /// الحصول على اسم حالة العمولة
  static String getCommissionStatusName(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return 'معلقة';
      case CommissionStatus.paid:
        return 'مدفوعة';
      case CommissionStatus.cancelled:
        return 'ملغاة';
    }
  }
}
