import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// هجرة لإضافة حقول الصلاحيات إلى جدول المستخدمين
class AddPermissionsToUsers {
  static const String tableName = 'users';

  /// التحقق مما إذا كانت الهجرة مطلوبة
  static Future<bool> isNeeded(Database db) async {
    try {
      // التحقق من وجود الجدول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='$tableName'",
      );
      
      if (tables.isEmpty) {
        return false; // الجدول غير موجود
      }

      // التحقق من وجود العمود
      final columns = await db.rawQuery("PRAGMA table_info($tableName)");
      final hasPermissions = columns.any((column) => column['name'] == 'permissions');
      
      return !hasPermissions; // الهجرة مطلوبة إذا كان العمود غير موجود
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء التحقق من حقول جدول المستخدمين: $e');
      }
      return false;
    }
  }

  /// تطبيق الهجرة
  static Future<void> migrate(Database db) async {
    try {
      if (await isNeeded(db)) {
        if (kDebugMode) {
          print('تطبيق هجرة: إضافة حقول الصلاحيات إلى جدول المستخدمين');
        }

        // إضافة عمود permissions
        await db.execute('''
          ALTER TABLE $tableName ADD COLUMN permissions TEXT
        ''');

        // تحديث المستخدم admin ليكون لديه جميع الصلاحيات
        await db.update(
          tableName,
          {
            'permissions': '{"all": true}',
          },
          where: 'username = ?',
          whereArgs: ['admin'],
        );

        if (kDebugMode) {
          print('تم تطبيق الهجرة بنجاح');
        }
      } else {
        if (kDebugMode) {
          print('تم تخطي الهجرة: حقول الصلاحيات موجودة بالفعل في جدول المستخدمين');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ أثناء تطبيق الهجرة: $e');
      }
      rethrow;
    }
  }
}
