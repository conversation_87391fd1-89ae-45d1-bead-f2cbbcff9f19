import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/constants.dart';
import '../../../shared/models/invoice_item.dart';

class InvoiceItemForm extends StatefulWidget {
  final InvoiceItem item;
  final Function(InvoiceItem) onUpdate;
  final VoidCallback onRemove;

  const InvoiceItemForm({
    super.key,
    required this.item,
    required this.onUpdate,
    required this.onRemove,
  });

  @override
  State<InvoiceItemForm> createState() => _InvoiceItemFormState();
}

class _InvoiceItemFormState extends State<InvoiceItemForm> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _quantityController;
  late TextEditingController _unitPriceController;
  late TextEditingController _discountController;
  late double _taxRate;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.item.name);
    _descriptionController = TextEditingController(text: widget.item.description ?? '');
    _quantityController = TextEditingController(text: widget.item.quantity.toString());
    _unitPriceController = TextEditingController(text: widget.item.unitPrice.toString());
    _discountController = TextEditingController(text: (widget.item.discount ?? 0).toString());
    _taxRate = widget.item.taxRate ?? 0.15; // Default to 15% VAT
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  void _updateItem() {
    final name = _nameController.text;
    final description = _descriptionController.text.isEmpty ? null : _descriptionController.text;
    final quantity = double.tryParse(_quantityController.text) ?? 1;
    final unitPrice = double.tryParse(_unitPriceController.text) ?? 0;
    final discount = double.tryParse(_discountController.text) ?? 0;

    final updatedItem = InvoiceItem(
      id: widget.item.id,
      name: name,
      description: description,
      quantity: quantity,
      unit: widget.item.unit,
      unitPrice: unitPrice,
      taxRate: _taxRate,
      discount: discount,
      inventoryItemId: widget.item.inventoryItemId,
    );

    widget.onUpdate(updatedItem);
  }

  @override
  Widget build(BuildContext context) {
    final subtotal = widget.item.subtotal;
    final taxAmount = widget.item.taxAmount;
    final total = widget.item.total;

    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Text(
                    'عنصر #${widget.item.id ?? ''}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: widget.onRemove,
                  tooltip: 'حذف العنصر',
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العنصر',
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _updateItem(),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف (اختياري)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
              onChanged: (_) => _updateItem(),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    onChanged: (_) => _updateItem(),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingS),
                Expanded(
                  child: TextFormField(
                    controller: _unitPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر الوحدة',
                      border: OutlineInputBorder(),
                      suffixText: 'ر.س',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    onChanged: (_) => _updateItem(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: DropdownButtonFormField<double>(
                    decoration: const InputDecoration(
                      labelText: 'نسبة الضريبة',
                      border: OutlineInputBorder(),
                    ),
                    value: _taxRate,
                    items: const [
                      DropdownMenuItem(
                        value: 0.0,
                        child: Text('0%'),
                      ),
                      DropdownMenuItem(
                        value: 0.05,
                        child: Text('5%'),
                      ),
                      DropdownMenuItem(
                        value: 0.15,
                        child: Text('15%'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _taxRate = value;
                        });
                        _updateItem();
                      }
                    },
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingS),
                Expanded(
                  child: TextFormField(
                    controller: _discountController,
                    decoration: const InputDecoration(
                      labelText: 'الخصم',
                      border: OutlineInputBorder(),
                      suffixText: 'ر.س',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    onChanged: (_) => _updateItem(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('المجموع الفرعي:'),
                Text('${subtotal.toStringAsFixed(2)} ر.س'),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('الضريبة (${(_taxRate * 100).toStringAsFixed(0)}%):'),
                Text('${taxAmount.toStringAsFixed(2)} ر.س'),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'الإجمالي:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${total.toStringAsFixed(2)} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
