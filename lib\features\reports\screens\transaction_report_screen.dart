import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:icecorner/shared/utils/app_colors.dart';
import 'package:icecorner/shared/utils/app_dimensions.dart';
import 'package:icecorner/shared/utils/app_text_styles.dart';
import 'package:icecorner/shared/models/transaction.dart';
import 'package:icecorner/core/repositories/transaction_repository.dart';
import 'package:icecorner/core/repositories/transaction_category_repository.dart';
import 'package:icecorner/features/reports/utils/report_generator.dart';
import 'package:icecorner/features/reports/widgets/report_widgets.dart';
import 'package:icecorner/features/reports/screens/unified_report_screen.dart';
import 'package:fl_chart/fl_chart.dart';

/// شاشة تقارير المعاملات المالية
class TransactionReportScreen extends StatefulWidget {
  final String? transactionType; // 'income' أو 'expense' أو null للكل

  const TransactionReportScreen({
    Key? key,
    this.transactionType,
  }) : super(key: key);

  @override
  State<TransactionReportScreen> createState() => _TransactionReportScreenState();
}

class _TransactionReportScreenState extends State<TransactionReportScreen> {
  // المستودعات
  final _transactionRepository = TransactionRepository();
  final _categoryRepository = TransactionCategoryRepository();

  // البيانات
  List<Transaction> _transactions = [];
  List<String> _categories = [];
  bool _isLoading = true;

  // الفلاتر
  String _searchQuery = '';
  String? _categoryFilter;
  String? _paymentMethodFilter;
  String? _typeFilter;

  @override
  void initState() {
    super.initState();
    _typeFilter = widget.transactionType;
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المعاملات
      final DateTime startDate = DateTime.now().subtract(const Duration(days: 30));
      final DateTime endDate = DateTime.now();

      final transactions = await _transactionRepository.getTransactionsByDateRange(startDate, endDate);

      // تحميل الفئات
      final categories = await _categoryRepository.getAllCategories();
      final categoryNames = categories.map((c) => c.name).toList();

      // تحديث الحالة
      if (mounted) {
        setState(() {
          _transactions = transactions;
          _categories = categoryNames;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// المعاملات المفلترة
  List<Transaction> get _filteredTransactions {
    var filtered = _transactions;

    // تطبيق فلتر النوع
    if (_typeFilter != null) {
      filtered = filtered.where((t) =>
        t.type == (_typeFilter == 'income' ? TransactionType.income : TransactionType.expense)
      ).toList();
    }

    // تطبيق فلتر الفئة
    if (_categoryFilter != null) {
      filtered = filtered.where((t) => t.category == _categoryFilter).toList();
    }

    // تطبيق فلتر طريقة الدفع
    if (_paymentMethodFilter != null) {
      PaymentMethod paymentMethod;
      switch (_paymentMethodFilter) {
        case 'cash':
          paymentMethod = PaymentMethod.cash;
          break;
        case 'bank':
          paymentMethod = PaymentMethod.bankTransfer;
          break;
        case 'card':
          paymentMethod = PaymentMethod.creditCard;
          break;
        default:
          paymentMethod = PaymentMethod.cash;
      }
      filtered = filtered.where((t) => t.paymentMethod == paymentMethod).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((t) {
        return t.reference.toLowerCase().contains(query) ||
            (t.description?.toLowerCase().contains(query) ?? false) ||
            (t.category?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    return filtered;
  }

  /// إجمالي الإيرادات
  double get _totalIncome {
    return _filteredTransactions
        .where((t) => t.type == TransactionType.income)
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  /// إجمالي المصروفات
  double get _totalExpenses {
    return _filteredTransactions
        .where((t) => t.type == TransactionType.expense)
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  /// الرصيد
  double get _balance {
    return _totalIncome - _totalExpenses;
  }

  /// بناء قسم الملخص
  Widget _buildSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // بطاقات الملخص
        SummaryRow(
          cards: [
            SummaryCardData(
              title: 'إجمالي الإيرادات',
              value: _totalIncome.isFinite
                ? '${NumberFormat('#,##0.00', 'ar').format(_totalIncome)} ر.س'
                : '0.00 ر.س',
              icon: Icons.arrow_upward,
              color: Colors.green,
            ),
            SummaryCardData(
              title: 'إجمالي المصروفات',
              value: _totalExpenses.isFinite
                ? '${NumberFormat('#,##0.00', 'ar').format(_totalExpenses)} ر.س'
                : '0.00 ر.س',
              icon: Icons.arrow_downward,
              color: Colors.red,
            ),
            SummaryCardData(
              title: 'الرصيد',
              value: _balance.isFinite
                ? '${NumberFormat('#,##0.00', 'ar').format(_balance)} ر.س'
                : '0.00 ر.س',
              icon: Icons.account_balance_wallet,
              color: _balance >= 0 ? Colors.blue : Colors.orange,
            ),
            SummaryCardData(
              title: 'عدد المعاملات',
              value: '${_filteredTransactions.length}',
              icon: Icons.receipt_long,
              color: AppColors.primary,
            ),
          ],
        ),
        const SizedBox(height: 16),

        // مخطط الإيرادات والمصروفات
        if (_filteredTransactions.isNotEmpty)
          PieChartWidget(
            title: 'توزيع المعاملات',
            totalAmount: (_totalIncome + _totalExpenses).isFinite
              ? '${NumberFormat('#,##0.00', 'ar').format(_totalIncome + _totalExpenses)} ر.س'
              : '0.00 ر.س',
            sections: [
              PieChartSectionData(
                value: _totalIncome,
                title: 'الإيرادات',
                color: Colors.green,
                radius: 60,
                titleStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              PieChartSectionData(
                value: _totalExpenses,
                title: 'المصروفات',
                color: Colors.red,
                radius: 60,
                titleStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),

            ],
          ),
        const SizedBox(height: 16),

        // مخطط الفئات
        if (_filteredTransactions.isNotEmpty)
          _buildCategoriesChart(),
      ],
    );
  }

  /// بناء مخطط الفئات
  Widget _buildCategoriesChart() {
    // تجميع المعاملات حسب الفئة
    final Map<String, double> categoryTotals = {};
    for (final transaction in _filteredTransactions) {
      final category = transaction.category ?? 'بدون فئة';
      categoryTotals[category] = (categoryTotals[category] ?? 0) + transaction.amount;
    }

    // ترتيب الفئات حسب المبلغ
    final sortedCategories = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // إنشاء بيانات المخطط الشريطي
    final barGroups = <BarChartGroupData>[];
    final xLabels = <String>[];

    // أخذ أعلى 5 فئات فقط
    final topCategories = sortedCategories.take(5).toList();
    for (int i = 0; i < topCategories.length; i++) {
      final entry = topCategories[i];
      xLabels.add(entry.key);
      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: entry.value,
              color: _typeFilter == 'income' ? Colors.green : (_typeFilter == 'expense' ? Colors.red : Colors.blue),
              width: 20,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
          ],
        ),
      );
    }

    return BarChartWidget(
      title: 'أعلى الفئات',
      barGroups: barGroups,
      xLabels: xLabels,
    );
  }

  /// بناء قائمة المعاملات
  Widget _buildTransactionsList() {
    if (_filteredTransactions.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد معاملات مطابقة للفلاتر',
          style: AppTextStyles.heading3,
        ),
      );
    }

    return Column(
      children: [
        // عنوان الجدول
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          color: Colors.blue.shade50,
          child: Row(
            children: const [
              Expanded(
                flex: 1,
                child: Text(
                  'النوع',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  'التفاصيل',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'التاريخ والفئة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'المبلغ',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),

        // قائمة المعاملات
        Expanded(
          child: ListView.builder(
            itemCount: _filteredTransactions.length,
            itemBuilder: (context, index) {
              final transaction = _filteredTransactions[index];
              final isIncome = transaction.type == TransactionType.income;
              final statusColor = isIncome ? Colors.green : Colors.red;
              final statusIcon = isIncome ? Icons.arrow_upward : Icons.arrow_downward;
              final statusText = isIncome ? 'إيراد' : 'مصروف';

              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      // نوع الإيراد
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            CircleAvatar(
                              backgroundColor: statusColor.withAlpha(51),
                              child: Icon(
                                statusIcon,
                                color: statusColor,
                                size: 20,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              statusText,
                              style: TextStyle(
                                fontSize: 12,
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // تفاصيل الإيراد
                      Expanded(
                        flex: 3,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              transaction.description ?? 'بدون وصف',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'المرجع: ${transaction.reference}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            if (transaction.customerName != null)
                              Text(
                                'العميل: ${transaction.customerName}',
                                style: const TextStyle(fontSize: 12),
                              ),
                            if (transaction.supplierName != null)
                              Text(
                                'المورد: ${transaction.supplierName}',
                                style: const TextStyle(fontSize: 12),
                              ),
                          ],
                        ),
                      ),

                      // التاريخ والفئة
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('dd/MM/yyyy').format(transaction.date),
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              'الفئة: ${transaction.category ?? 'بدون فئة'}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            Text(
                              'طريقة الدفع: ${Transaction.getPaymentMethodName(transaction.paymentMethod)}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),

                      // المبلغ
                      Expanded(
                        flex: 1,
                        child: Text(
                          transaction.amount.isFinite
                              ? '${NumberFormat('#,##0.00', 'ar').format(transaction.amount)} ر.س'
                              : '0.00 ر.س',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: statusColor,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء قسم الفلاتر
  Widget _buildFiltersSection() {
    return Column(
      children: [
        // فلاتر الصف الأول
        Row(
          children: [
            // فلتر النوع
            Expanded(
              child: DropdownButtonFormField<String?>(
                decoration: const InputDecoration(
                  labelText: 'نوع الإيراد',
                  prefixIcon: Icon(Icons.category),
                  border: OutlineInputBorder(),
                ),
                value: _typeFilter,
                items: [
                  const DropdownMenuItem<String?>(
                    value: null,
                    child: Text('الكل'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'income',
                    child: Text('إيرادات'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'expense',
                    child: Text('مصروفات'),
                  ),
                ],
                onChanged: (String? value) {
                  setState(() {
                    _typeFilter = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 16),

            // فلتر الفئة
            Expanded(
              child: DropdownButtonFormField<String?>(
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  prefixIcon: Icon(Icons.folder),
                  border: OutlineInputBorder(),
                ),
                value: _categoryFilter,
                items: [
                  const DropdownMenuItem<String?>(
                    value: null,
                    child: Text('الكل'),
                  ),
                  ..._categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }),
                ],
                onChanged: (String? value) {
                  setState(() {
                    _categoryFilter = value;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // فلاتر الصف الثاني
        Row(
          children: [
            // فلتر طريقة الدفع
            Expanded(
              child: DropdownButtonFormField<String?>(
                decoration: const InputDecoration(
                  labelText: 'طريقة الدفع',
                  prefixIcon: Icon(Icons.payment),
                  border: OutlineInputBorder(),
                ),
                value: _paymentMethodFilter,
                items: const [
                  DropdownMenuItem<String?>(
                    value: null,
                    child: Text('الكل'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'cash',
                    child: Text('نقدي'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'bank',
                    child: Text('تحويل بنكي'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'card',
                    child: Text('بطاقة ائتمان'),
                  ),
                ],
                onChanged: (String? value) {
                  setState(() {
                    _paymentMethodFilter = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 16),

            // حقل البحث
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'بحث',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // استخدام شاشة التقارير الموحدة
    return UnifiedReportScreen(
      title: _typeFilter == 'income'
          ? 'تقرير الإيرادات'
          : (_typeFilter == 'expense' ? 'تقرير المصروفات' : 'تقرير المعاملات المالية'),
      reportType: ReportType.transaction,
      filterWidget: _buildFiltersSection(),
      summaryWidget: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildSummarySection(),
      dataWidget: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildTransactionsList(),
    );
  }
}
