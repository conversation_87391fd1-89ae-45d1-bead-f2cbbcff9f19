import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

/// Migration to add payment information fields to salaries and withdrawals tables
class AddPaymentInfoToPayroll {
  static Future<void> migrate(Database db) async {
    try {
      // Check if the columns already exist in salaries table
      final List<Map<String, dynamic>> salaryColumns = await db.rawQuery(
        "PRAGMA table_info(salaries)"
      );
      
      final salaryColumnNames = salaryColumns.map((c) => c['name'] as String).toList();
      
      // Add payment_employee_id column to salaries table if it doesn't exist
      if (!salaryColumnNames.contains('payment_employee_id')) {
        await db.execute(
          'ALTER TABLE salaries ADD COLUMN payment_employee_id INTEGER'
        );
        if (kDebugMode) {
          print('Added payment_employee_id column to salaries table');
        }
      }
      
      // Add payment_bank_account_id column to salaries table if it doesn't exist
      if (!salaryColumnNames.contains('payment_bank_account_id')) {
        await db.execute(
          'ALTER TABLE salaries ADD COLUMN payment_bank_account_id INTEGER'
        );
        if (kDebugMode) {
          print('Added payment_bank_account_id column to salaries table');
        }
      }
      
      // Add payment_method column to salaries table if it doesn't exist
      if (!salaryColumnNames.contains('payment_method')) {
        await db.execute(
          'ALTER TABLE salaries ADD COLUMN payment_method TEXT'
        );
        if (kDebugMode) {
          print('Added payment_method column to salaries table');
        }
      }
      
      // Check if the columns already exist in withdrawals table
      final List<Map<String, dynamic>> withdrawalColumns = await db.rawQuery(
        "PRAGMA table_info(withdrawals)"
      );
      
      final withdrawalColumnNames = withdrawalColumns.map((c) => c['name'] as String).toList();
      
      // Add payment_employee_id column to withdrawals table if it doesn't exist
      if (!withdrawalColumnNames.contains('payment_employee_id')) {
        await db.execute(
          'ALTER TABLE withdrawals ADD COLUMN payment_employee_id INTEGER'
        );
        if (kDebugMode) {
          print('Added payment_employee_id column to withdrawals table');
        }
      }
      
      // Add payment_bank_account_id column to withdrawals table if it doesn't exist
      if (!withdrawalColumnNames.contains('payment_bank_account_id')) {
        await db.execute(
          'ALTER TABLE withdrawals ADD COLUMN payment_bank_account_id INTEGER'
        );
        if (kDebugMode) {
          print('Added payment_bank_account_id column to withdrawals table');
        }
      }
      
      // Add payment_method column to withdrawals table if it doesn't exist
      if (!withdrawalColumnNames.contains('payment_method')) {
        await db.execute(
          'ALTER TABLE withdrawals ADD COLUMN payment_method TEXT'
        );
        if (kDebugMode) {
          print('Added payment_method column to withdrawals table');
        }
      }
      
      if (kDebugMode) {
        print('Successfully added payment information fields to payroll tables');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding payment information fields to payroll tables: $e');
      }
    }
  }
}
