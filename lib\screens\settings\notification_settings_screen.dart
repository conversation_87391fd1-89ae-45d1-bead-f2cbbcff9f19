import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/services/notification_service.dart';
import '../../shared/utils/app_colors.dart';
import '../../shared/widgets/custom_app_bar.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final NotificationService _notificationService = NotificationService();

  // إعدادات الإشعارات
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _reminderEnabled = true;
  bool _popupNotificationsEnabled = true;
  bool _urgentNotificationsEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  // تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _soundEnabled = prefs.getBool('notification_sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('notification_vibration_enabled') ?? true;
      _reminderEnabled = prefs.getBool('notification_reminder_enabled') ?? true;
      _popupNotificationsEnabled = prefs.getBool('notification_popup_enabled') ?? true;
      _urgentNotificationsEnabled = prefs.getBool('notification_urgent_enabled') ?? true;
    });

    // تطبيق إعدادات الصوت
    _notificationService.setSoundEnabled(_soundEnabled);
  }

  // حفظ الإعدادات
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool('notification_sound_enabled', _soundEnabled);
    await prefs.setBool('notification_vibration_enabled', _vibrationEnabled);
    await prefs.setBool('notification_reminder_enabled', _reminderEnabled);
    await prefs.setBool('notification_popup_enabled', _popupNotificationsEnabled);
    await prefs.setBool('notification_urgent_enabled', _urgentNotificationsEnabled);

    // تطبيق إعدادات الصوت
    _notificationService.setSoundEnabled(_soundEnabled);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الإعدادات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // اختبار الإشعارات
  Future<void> _testNotification() async {
    await _notificationService.showNotification(
      id: 9999,
      title: 'إشعار تجريبي',
      body: 'هذا إشعار تجريبي للتأكد من عمل الإشعارات',
      payload: 'test',
      playSound: _soundEnabled,
      isUrgent: _urgentNotificationsEnabled,
      useFullScreenIntent: _popupNotificationsEnabled,
    );
  }

  // اختبار الإشعارات المنبثقة
  Future<void> _testPopupNotification() async {
    await _notificationService.showNotification(
      id: 9998,
      title: 'إشعار منبثق تجريبي',
      body: 'هذا إشعار منبثق تجريبي للتأكد من عمل الإشعارات المنبثقة',
      payload: 'test_popup',
      playSound: _soundEnabled,
      isUrgent: true,
      useFullScreenIntent: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'إعدادات الإشعارات',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الصوت والاهتزاز
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الصوت والاهتزاز',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تفعيل صوت الإشعارات'),
                      subtitle: const Text('تشغيل صوت عند استلام الإشعارات'),
                      value: _soundEnabled,
                      activeColor: AppColors.primary,
                      onChanged: (value) {
                        setState(() {
                          _soundEnabled = value;
                        });
                      },
                    ),
                    if (_soundEnabled)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                        child: Row(
                          children: [
                            const Text('اختبار الصوت:'),
                            const SizedBox(width: 16),
                            ElevatedButton.icon(
                              onPressed: () async {
                                await _notificationService.playNotificationSound();
                              },
                              icon: const Icon(Icons.volume_up),
                              label: const Text('تشغيل الصوت'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.secondary,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    SwitchListTile(
                      title: const Text('تفعيل الاهتزاز'),
                      subtitle: const Text('اهتزاز الجهاز عند استلام الإشعارات'),
                      value: _vibrationEnabled,
                      activeColor: AppColors.primary,
                      onChanged: (value) {
                        setState(() {
                          _vibrationEnabled = value;
                        });
                      },
                    ),
                    const Divider(),
                    SwitchListTile(
                      title: const Text('تفعيل الإشعارات المنبثقة'),
                      subtitle: const Text('عرض الإشعارات المهمة على كامل الشاشة'),
                      value: _popupNotificationsEnabled,
                      activeColor: AppColors.primary,
                      onChanged: (value) {
                        setState(() {
                          _popupNotificationsEnabled = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('تفعيل الإشعارات العاجلة'),
                      subtitle: const Text('استخدام نمط الإشعارات العاجلة للتنبيهات المهمة'),
                      value: _urgentNotificationsEnabled,
                      activeColor: AppColors.primary,
                      onChanged: (value) {
                        setState(() {
                          _urgentNotificationsEnabled = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // قسم التذكيرات
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'التذكيرات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تفعيل التذكيرات'),
                      subtitle: const Text('إرسال تذكيرات للمواعيد القادمة'),
                      value: _reminderEnabled,
                      activeColor: AppColors.primary,
                      onChanged: (value) {
                        setState(() {
                          _reminderEnabled = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // قسم اختبار الإشعارات
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'اختبار الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'اضغط على أحد الأزرار أدناه لاختبار الإشعارات:',
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• إشعار عادي: لاختبار الإشعارات العادية',
                      style: TextStyle(fontSize: 12),
                    ),
                    const Text(
                      '• إشعار منبثق: لاختبار الإشعارات المنبثقة على كامل الشاشة',
                      style: TextStyle(fontSize: 12),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _testNotification,
                          icon: const Icon(Icons.notifications_active),
                          label: const Text('إشعار عادي'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: _testPopupNotification,
                          icon: const Icon(Icons.open_in_new),
                          label: const Text('إشعار منبثق'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // زر حفظ الإعدادات
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'حفظ الإعدادات',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
