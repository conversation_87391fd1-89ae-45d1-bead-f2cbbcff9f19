import 'dart:convert';
import 'package:flutter/material.dart';

/// نوع المصدر أو الوجهة للتحويل
enum TransferEntityType {
  bankAccount,
  employee,
}

/// حالة التحويل
enum TransferStatus {
  completed,
  pending,
  cancelled,
}

/// نموذج التحويل بين الحسابات
class AccountTransfer {
  final int? id;
  final String reference;
  final TransferEntityType sourceType;
  final int sourceId;
  final String sourceName;
  final TransferEntityType destinationType;
  final int destinationId;
  final String destinationName;
  final double amount;
  final DateTime date;
  final String? reason;
  final TransferStatus status;
  final String? notes;
  final int? createdBy;
  final String? createdByName;
  final DateTime createdAt;
  final DateTime? updatedAt;

  AccountTransfer({
    this.id,
    required this.reference,
    required this.sourceType,
    required this.sourceId,
    required this.sourceName,
    required this.destinationType,
    required this.destinationId,
    required this.destinationName,
    required this.amount,
    required this.date,
    this.reason,
    required this.status,
    this.notes,
    this.createdBy,
    this.createdByName,
    required this.createdAt,
    this.updatedAt,
  });

  factory AccountTransfer.fromMap(Map<String, dynamic> map) {
    return AccountTransfer(
      id: map['id'] as int?,
      reference: map['reference'] as String,
      sourceType: _parseEntityType(map['source_type'] as String),
      sourceId: map['source_id'] as int,
      sourceName: map['source_name'] as String? ?? 'غير معروف',
      destinationType: _parseEntityType(map['destination_type'] as String),
      destinationId: map['destination_id'] as int,
      destinationName: map['destination_name'] as String? ?? 'غير معروف',
      amount: map['amount'] as double,
      date: DateTime.parse(map['date'] as String),
      reason: map['reason'] as String?,
      status: _parseStatus(map['status'] as String),
      notes: map['notes'] as String?,
      createdBy: map['created_by'] as int?,
      createdByName: map['created_by_name'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
    );
  }

  factory AccountTransfer.fromJson(String source) =>
      AccountTransfer.fromMap(json.decode(source) as Map<String, dynamic>);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'reference': reference,
      'source_type': sourceType.toString().split('.').last,
      'source_id': sourceId,
      'source_name': sourceName,
      'destination_type': destinationType.toString().split('.').last,
      'destination_id': destinationId,
      'destination_name': destinationName,
      'amount': amount,
      'date': date.toIso8601String(),
      'reason': reason,
      'status': status.toString().split('.').last,
      'notes': notes,
      'created_by': createdBy,
      'created_by_name': createdByName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  AccountTransfer copyWith({
    int? id,
    String? reference,
    TransferEntityType? sourceType,
    int? sourceId,
    String? sourceName,
    TransferEntityType? destinationType,
    int? destinationId,
    String? destinationName,
    double? amount,
    DateTime? date,
    String? reason,
    TransferStatus? status,
    String? notes,
    int? createdBy,
    String? createdByName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AccountTransfer(
      id: id ?? this.id,
      reference: reference ?? this.reference,
      sourceType: sourceType ?? this.sourceType,
      sourceId: sourceId ?? this.sourceId,
      sourceName: sourceName ?? this.sourceName,
      destinationType: destinationType ?? this.destinationType,
      destinationId: destinationId ?? this.destinationId,
      destinationName: destinationName ?? this.destinationName,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdByName: createdByName ?? this.createdByName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper method to parse entity type
  static TransferEntityType _parseEntityType(String type) {
    switch (type.toLowerCase()) {
      case 'bankaccount':
      case 'bank_account':
        return TransferEntityType.bankAccount;
      case 'employee':
        return TransferEntityType.employee;
      default:
        return TransferEntityType.bankAccount;
    }
  }

  // Helper method to parse status
  static TransferStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return TransferStatus.completed;
      case 'pending':
        return TransferStatus.pending;
      case 'cancelled':
        return TransferStatus.cancelled;
      default:
        return TransferStatus.completed;
    }
  }

  // Helper method to get entity type string
  static String getEntityTypeString(TransferEntityType type) {
    switch (type) {
      case TransferEntityType.bankAccount:
        return 'حساب بنكي';
      case TransferEntityType.employee:
        return 'موظف';
    }
  }

  // Helper method to get status string
  static String getStatusString(TransferStatus status) {
    switch (status) {
      case TransferStatus.completed:
        return 'مكتمل';
      case TransferStatus.pending:
        return 'معلق';
      case TransferStatus.cancelled:
        return 'ملغي';
    }
  }

  // Helper method to get status color
  static Color getStatusColor(TransferStatus status) {
    switch (status) {
      case TransferStatus.completed:
        return Colors.green;
      case TransferStatus.pending:
        return Colors.orange;
      case TransferStatus.cancelled:
        return Colors.red;
    }
  }
}
