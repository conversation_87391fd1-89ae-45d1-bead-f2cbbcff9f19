import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../utils/firebase_utils.dart';
import '../../shared/models/sync_model.dart';
import '../../shared/models/sync_conflict.dart';

/// Service class for handling all Firebase Firestore operations
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  /// Get Firestore instance (lazy initialization to avoid Firebase errors)
  FirebaseFirestore get _firestore {
    final firestore = FirebaseUtils.safeFirebaseOperationSync<FirebaseFirestore>(
      () => FirebaseFirestore.instance,
      operationName: 'Get Firestore instance',
      fallbackValue: null,
    );

    if (firestore == null) {
      throw Exception('Firebase Firestore not available - please ensure Firebase is initialized');
    }

    return firestore;
  }

  /// Get Firebase Auth instance (lazy initialization to avoid Firebase errors)
  FirebaseAuth get _auth {
    final auth = FirebaseUtils.safeFirebaseOperationSync<FirebaseAuth>(
      () => FirebaseAuth.instance,
      operationName: 'Get Firebase Auth instance',
      fallbackValue: null,
    );

    if (auth == null) {
      throw Exception('Firebase Auth not available - please ensure Firebase is initialized');
    }

    return auth;
  }

  /// Check if Firebase is available
  bool get isFirebaseAvailable => FirebaseUtils.isInitialized;

  // Collection names
  static const String customersCollection = 'customers';
  static const String serviceRequestsCollection = 'service_requests';
  static const String usersCollection = 'users';
  static const String invoicesCollection = 'invoices';
  static const String inventoryItemsCollection = 'inventory_items';
  static const String syncMetadataCollection = 'sync_metadata';

  /// Initialize Firebase service
  Future<void> initialize() async {
    try {
      // Enable offline persistence using the new settings approach
      const settings = Settings(persistenceEnabled: true);
      _firestore.settings = settings;
      if (kDebugMode) {
        print('✅ Firebase offline persistence enabled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Firebase offline persistence already enabled or failed: $e');
      }
    }
  }

  /// Get current user ID for security rules
  String? get currentUserId => _auth.currentUser?.uid;

  /// Check if user is authenticated
  bool get isAuthenticated => _auth.currentUser != null;

  /// Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (kDebugMode) {
        print('✅ User signed in: ${credential.user?.email}');
      }

      return credential;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error signing in: $e');
      }
      rethrow;
    }
  }

  /// Create user with email and password
  Future<UserCredential?> createUserWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (kDebugMode) {
        print('✅ User created: ${credential.user?.email}');
      }

      return credential;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating user: $e');
      }
      rethrow;
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      await _auth.signOut();

      if (kDebugMode) {
        print('✅ User signed out');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error signing out: $e');
      }
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);

      if (kDebugMode) {
        print('✅ Password reset email sent to: $email');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending password reset email: $e');
      }
      rethrow;
    }
  }

  // CRUD Operations

  /// Create a new document in Firestore
  Future<String> createDocument(String collection, Map<String, dynamic> data) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      // Add metadata
      data['created_at'] = FieldValue.serverTimestamp();
      data['updated_at'] = FieldValue.serverTimestamp();
      data['created_by'] = currentUserId;

      final docRef = await _firestore.collection(collection).add(data);
      
      if (kDebugMode) {
        print('✅ Document created in $collection with ID: ${docRef.id}');
      }
      
      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating document in $collection: $e');
      }
      rethrow;
    }
  }

  /// Update an existing document in Firestore
  Future<void> updateDocument(String collection, String documentId, Map<String, dynamic> data) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      // Add update metadata
      data['updated_at'] = FieldValue.serverTimestamp();

      await _firestore.collection(collection).doc(documentId).update(data);
      
      if (kDebugMode) {
        print('✅ Document updated in $collection with ID: $documentId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating document in $collection: $e');
      }
      rethrow;
    }
  }

  /// Delete a document from Firestore (soft delete)
  Future<void> deleteDocument(String collection, String documentId) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      await _firestore.collection(collection).doc(documentId).update({
        'is_deleted': true,
        'deleted_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      
      if (kDebugMode) {
        print('✅ Document soft deleted in $collection with ID: $documentId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting document in $collection: $e');
      }
      rethrow;
    }
  }

  /// Get a single document from Firestore
  Future<Map<String, dynamic>?> getDocument(String collection, String documentId) async {
    try {
      final doc = await _firestore.collection(collection).doc(documentId).get();
      
      if (doc.exists) {
        final data = doc.data()!;
        data['firestore_id'] = doc.id;
        return data;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting document from $collection: $e');
      }
      rethrow;
    }
  }

  /// Get all documents from a collection
  Future<List<Map<String, dynamic>>> getCollection(String collection, {
    int? limit,
    String? orderBy,
    bool descending = false,
    Map<String, dynamic>? where,
  }) async {
    try {
      Query query = _firestore.collection(collection);

      // Add where clauses
      if (where != null) {
        where.forEach((field, value) {
          query = query.where(field, isEqualTo: value);
        });
      }

      // Add ordering
      if (orderBy != null) {
        query = query.orderBy(orderBy, descending: descending);
      }

      // Add limit
      if (limit != null) {
        query = query.limit(limit);
      }

      // Exclude soft-deleted documents
      query = query.where('is_deleted', isEqualTo: false);

      final snapshot = await query.get();
      
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['firestore_id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting collection $collection: $e');
      }
      rethrow;
    }
  }

  /// Get documents modified after a specific timestamp
  Future<List<Map<String, dynamic>>> getModifiedDocuments(
    String collection,
    DateTime lastSyncTime,
  ) async {
    try {
      final query = _firestore
          .collection(collection)
          .where('updated_at', isGreaterThan: Timestamp.fromDate(lastSyncTime))
          .orderBy('updated_at');

      final snapshot = await query.get();
      
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['firestore_id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting modified documents from $collection: $e');
      }
      rethrow;
    }
  }

  // Batch Operations

  /// Perform batch write operations
  Future<void> batchWrite(List<BatchOperation> operations) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final batch = _firestore.batch();
      
      for (final operation in operations) {
        final docRef = _firestore.collection(operation.collection).doc(operation.documentId);
        
        switch (operation.type) {
          case BatchOperationType.create:
            operation.data!['created_at'] = FieldValue.serverTimestamp();
            operation.data!['updated_at'] = FieldValue.serverTimestamp();
            operation.data!['created_by'] = currentUserId;
            batch.set(docRef, operation.data!);
            break;
          case BatchOperationType.update:
            operation.data!['updated_at'] = FieldValue.serverTimestamp();
            batch.update(docRef, operation.data!);
            break;
          case BatchOperationType.delete:
            batch.update(docRef, {
              'is_deleted': true,
              'deleted_at': FieldValue.serverTimestamp(),
              'updated_at': FieldValue.serverTimestamp(),
            });
            break;
        }
      }
      
      await batch.commit();
      
      if (kDebugMode) {
        print('✅ Batch operation completed with ${operations.length} operations');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in batch operation: $e');
      }
      rethrow;
    }
  }

  // Real-time Listeners

  /// Listen to changes in a collection
  Stream<List<Map<String, dynamic>>> listenToCollection(String collection) {
    return _firestore
        .collection(collection)
        .where('is_deleted', isEqualTo: false)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['firestore_id'] = doc.id;
        return data;
      }).toList();
    });
  }

  /// Listen to changes in a specific document
  Stream<Map<String, dynamic>?> listenToDocument(String collection, String documentId) {
    return _firestore
        .collection(collection)
        .doc(documentId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        final data = doc.data()!;
        data['firestore_id'] = doc.id;
        return data;
      }
      return null;
    });
  }

  // Utility Methods

  /// Convert Firestore timestamp to DateTime
  DateTime? timestampToDateTime(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    } else if (timestamp is String) {
      return DateTime.tryParse(timestamp);
    }
    return null;
  }

  /// Convert DateTime to Firestore timestamp
  Timestamp dateTimeToTimestamp(DateTime dateTime) {
    return Timestamp.fromDate(dateTime);
  }
}

/// Enum for batch operation types
enum BatchOperationType {
  create,
  update,
  delete,
}

/// Class representing a batch operation
class BatchOperation {
  final String collection;
  final String? documentId;
  final BatchOperationType type;
  final Map<String, dynamic>? data;

  const BatchOperation({
    required this.collection,
    this.documentId,
    required this.type,
    this.data,
  });

  factory BatchOperation.create(String collection, Map<String, dynamic> data, {String? documentId}) {
    return BatchOperation(
      collection: collection,
      documentId: documentId,
      type: BatchOperationType.create,
      data: data,
    );
  }

  factory BatchOperation.update(String collection, String documentId, Map<String, dynamic> data) {
    return BatchOperation(
      collection: collection,
      documentId: documentId,
      type: BatchOperationType.update,
      data: data,
    );
  }

  factory BatchOperation.delete(String collection, String documentId) {
    return BatchOperation(
      collection: collection,
      documentId: documentId,
      type: BatchOperationType.delete,
    );
  }
}
