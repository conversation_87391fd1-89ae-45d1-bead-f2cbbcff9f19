import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../core/repositories/service_request_repository.dart';
import '../../core/repositories/employee_repository.dart';
import '../../core/repositories/service_category_repository.dart';
import '../../shared/models/service_request.dart';
import '../../shared/models/employee.dart';
import '../../shared/models/service_category.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/simple_loading_indicator.dart';
import '../../shared/utils/app_colors.dart';
import '../../core/services/notification_service.dart';
import 'service_request_form_screen.dart';

class ServiceRequestDetailsScreen extends StatefulWidget {
  final int serviceRequestId;

  const ServiceRequestDetailsScreen({super.key, required this.serviceRequestId});

  @override
  State<ServiceRequestDetailsScreen> createState() => _ServiceRequestDetailsScreenState();
}

class _ServiceRequestDetailsScreenState extends State<ServiceRequestDetailsScreen> {
  final ServiceRequestRepository _repository = ServiceRequestRepository();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final ServiceCategoryRepository _categoryRepository = ServiceCategoryRepository();

  bool _isLoading = true;
  ServiceRequest? _serviceRequest;
  List<Employee> _employees = [];
  List<ServiceCategory> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final serviceRequest = await _repository.getServiceRequestById(widget.serviceRequestId);
      final employees = await _employeeRepository.getAllEmployees();
      final categories = await _categoryRepository.getAllServiceCategories();

      setState(() {
        _serviceRequest = serviceRequest;
        _employees = employees;
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _updateStatus(ServiceRequestStatus status) async {
    try {
      final result = await _repository.updateServiceRequestStatus(
        _serviceRequest!.id!,
        status,
      );

      if (result > 0) {
        _showSuccessSnackBar('تم تحديث حالة الطلب بنجاح');
        _loadData();
      } else {
        _showErrorSnackBar('حدث خطأ أثناء تحديث حالة الطلب');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحديث حالة الطلب');
    }
  }

  Future<void> _assignEmployee() async {
    final selectedEmployeeId = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعيين فني'),
        content: DropdownButton<int>(
          isExpanded: true,
          hint: const Text('اختر فني'),
          value: _serviceRequest!.assignedTo,
          items: _employees.map((employee) {
            return DropdownMenuItem<int>(
              value: employee.id,
              child: Text(employee.name),
            );
          }).toList(),
          onChanged: (value) {
            Navigator.of(context).pop(value);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (selectedEmployeeId != null) {
      try {
        final result = await _repository.assignServiceRequest(
          _serviceRequest!.id!,
          selectedEmployeeId,
        );

        if (result > 0) {
          _showSuccessSnackBar('تم تعيين الموظف الفني بنجاح');
          _loadData();
        } else {
          _showErrorSnackBar('حدث خطأ أثناء تعيين الموظف الفني');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء تعيين الموظف الفني');
      }
    }
  }

  Future<void> _assignCategory() async {
    final selectedCategory = await showDialog<ServiceCategory>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعيين فئة الخدمة'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: category.color,
                  child: Icon(category.icon, color: Colors.white, size: 20),
                ),
                title: Text(category.name),
                onTap: () => Navigator.of(context).pop(category),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (selectedCategory != null) {
      try {
        final result = await _repository.updateServiceRequestCategory(
          _serviceRequest!.id!,
          selectedCategory.id!,
          selectedCategory.name,
        );

        if (result > 0) {
          _showSuccessSnackBar('تم تعيين فئة الخدمة بنجاح');
          _loadData();
        } else {
          _showErrorSnackBar('حدث خطأ أثناء تعيين فئة الخدمة');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء تعيين فئة الخدمة');
      }
    }
  }

  Future<void> _editServiceRequest() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceRequestFormScreen(
          serviceRequest: _serviceRequest,
        ),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  // إرسال إشعار فوري للعميل
  Future<void> _sendNotification() async {
    try {
      final notificationService = NotificationService();
      await notificationService.init();

      // إرسال إشعار فوري
      await notificationService.showNotification(
        id: _serviceRequest!.id ?? 0,
        title: 'تذكير بموعد الزيارة',
        body: 'تذكير بموعد زيارة الموظف الفني لعنوان ${_serviceRequest!.location} في ${DateFormat('yyyy/MM/dd').format(_serviceRequest!.scheduledDate)}',
        payload: 'service_request_${_serviceRequest!.reference}',
      );

      _showSuccessSnackBar('تم إرسال الإشعار بنجاح');

      if (kDebugMode) {
        print('تم إرسال إشعار لطلب الخدمة: ${_serviceRequest!.reference}');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء إرسال الإشعار');

      if (kDebugMode) {
        print('خطأ في إرسال الإشعار: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: const CustomAppBar(title: 'تفاصيل طلب الخدمة'),
        body: const SimpleLoadingIndicator(),
      );
    }

    if (_serviceRequest == null) {
      return Scaffold(
        appBar: const CustomAppBar(title: 'تفاصيل طلب الخدمة'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('لم يتم العثور على طلب الخدمة'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('العودة'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: const CustomAppBar(title: 'تفاصيل طلب الخدمة'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section with status and actions
            Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'رقم الطلب: ${_serviceRequest!.reference}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: ServiceRequest.getStatusColor(_serviceRequest!.status),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            ServiceRequest.getStatusName(_serviceRequest!.status),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd').format(_serviceRequest!.createdAt)}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4.0),
                            child: ElevatedButton.icon(
                              onPressed: _editServiceRequest,
                              icon: const Icon(Icons.edit, size: 18),
                              label: const Text('تعديل'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4.0),
                            child: ElevatedButton.icon(
                              onPressed: _assignEmployee,
                              icon: const Icon(Icons.person, size: 18),
                              label: const Text('تعيين فني'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4.0),
                            child: ElevatedButton.icon(
                              onPressed: _assignCategory,
                              icon: const Icon(Icons.category, size: 18),
                              label: const Text('تعيين فئة'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.purple,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4.0),
                            child: ElevatedButton.icon(
                              onPressed: _sendNotification,
                              icon: const Icon(Icons.notifications_active, size: 18),
                              label: const Text('إرسال إشعار'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Status update buttons
            Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تحديث الحالة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2.0),
                            child: _buildStatusButton(
                              'معلق',
                              Colors.amber,
                              ServiceRequestStatus.pending,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2.0),
                            child: _buildStatusButton(
                              'قيد التنفيذ',
                              Colors.blue,
                              ServiceRequestStatus.inProgress,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2.0),
                            child: _buildStatusButton(
                              'مكتمل',
                              Colors.green,
                              ServiceRequestStatus.completed,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2.0),
                            child: _buildStatusButton(
                              'ملغي',
                              Colors.red,
                              ServiceRequestStatus.cancelled,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Customer and service info
            Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات العميل والخدمة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow('العميل', _serviceRequest!.customerName),
                    _buildInfoRow('نوع الخدمة', ServiceRequest.getRequestTypeName(_serviceRequest!.requestType)),
                    if (_serviceRequest!.categoryName != null)
                      _buildInfoRow('فئة الخدمة', _serviceRequest!.categoryName!),
                    _buildInfoRow('الموقع', _serviceRequest!.location ?? 'غير محدد'),
                    _buildInfoRow('تاريخ الزيارة', DateFormat('yyyy-MM-dd').format(_serviceRequest!.scheduledDate)),
                    _buildInfoRow('الموظف الفني المعين', _serviceRequest!.assignedToName ?? 'غير معين'),
                    if (_serviceRequest!.completedDate != null)
                      _buildInfoRow('تاريخ الإكمال', DateFormat('yyyy-MM-dd').format(_serviceRequest!.completedDate!)),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Description
            Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'وصف المشكلة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_serviceRequest!.description),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Device information
            Card(
              margin: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات الجهاز',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow('نوع الجهاز', _serviceRequest!.deviceType ?? 'غير محدد'),
                    _buildInfoRow('ماركة الجهاز', _serviceRequest!.deviceBrand ?? 'غير محدد'),
                    _buildInfoRow('موديل الجهاز', _serviceRequest!.deviceModel ?? 'غير محدد'),
                    _buildInfoRow('الرقم التسلسلي', _serviceRequest!.serialNumber ?? 'غير محدد'),
                    _buildInfoRow('تاريخ التركيب', _serviceRequest!.installationDate ?? 'غير محدد'),
                    _buildInfoRow('معلومات الضمان', _serviceRequest!.warrantyInfo ?? 'غير محدد'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Technical notes and solution
            if (_serviceRequest!.technicalNotes != null || _serviceRequest!.solution != null)
              Card(
                margin: EdgeInsets.zero,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'ملاحظات فنية والحل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_serviceRequest!.technicalNotes != null) ...[
                        const Text(
                          'ملاحظات فنية:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(_serviceRequest!.technicalNotes!),
                        const SizedBox(height: 8),
                      ],
                      if (_serviceRequest!.solution != null) ...[
                        const Text(
                          'الحل:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(_serviceRequest!.solution!),
                      ],
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 16),

            // Notes
            if (_serviceRequest!.notes != null)
              Card(
                margin: EdgeInsets.zero,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'ملاحظات',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_serviceRequest!.notes!),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(
    String label,
    Color color,
    ServiceRequestStatus status,
  ) {
    final isCurrentStatus = _serviceRequest!.status == status;
    return SizedBox(
      height: 36,
      child: ElevatedButton(
        onPressed: isCurrentStatus ? null : () => _updateStatus(status),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          disabledBackgroundColor: color.withAlpha(178),
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
        child: Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
