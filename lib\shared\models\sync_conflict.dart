import 'dart:convert';
import 'sync_model.dart';

/// Enum representing different conflict resolution strategies
enum ConflictResolution {
  useLocal,
  useRemote,
  merge,
  manual,
}

/// Extension methods for ConflictResolution enum
extension ConflictResolutionExtension on ConflictResolution {
  String get value {
    switch (this) {
      case ConflictResolution.useLocal:
        return 'useLocal';
      case ConflictResolution.useRemote:
        return 'useRemote';
      case ConflictResolution.merge:
        return 'merge';
      case ConflictResolution.manual:
        return 'manual';
    }
  }

  static ConflictResolution fromString(String value) {
    switch (value) {
      case 'useLocal':
        return ConflictResolution.useLocal;
      case 'useRemote':
        return ConflictResolution.useRemote;
      case 'merge':
        return ConflictResolution.merge;
      case 'manual':
        return ConflictResolution.manual;
      default:
        return ConflictResolution.manual;
    }
  }
}

/// Represents a difference between local and remote data for a specific field
class ConflictDifference {
  final String fieldName;
  final dynamic localValue;
  final dynamic remoteValue;
  final String fieldType;
  final bool canAutoResolve;

  const ConflictDifference({
    required this.fieldName,
    required this.localValue,
    required this.remoteValue,
    required this.fieldType,
    this.canAutoResolve = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'fieldName': fieldName,
      'localValue': localValue,
      'remoteValue': remoteValue,
      'fieldType': fieldType,
      'canAutoResolve': canAutoResolve,
    };
  }

  factory ConflictDifference.fromMap(Map<String, dynamic> map) {
    return ConflictDifference(
      fieldName: map['fieldName'] as String,
      localValue: map['localValue'],
      remoteValue: map['remoteValue'],
      fieldType: map['fieldType'] as String,
      canAutoResolve: map['canAutoResolve'] as bool? ?? false,
    );
  }

  @override
  String toString() {
    return 'ConflictDifference(field: $fieldName, local: $localValue, remote: $remoteValue)';
  }
}

/// Represents a synchronization conflict between local and remote data
class SyncConflict {
  final String id;
  final String entityType;
  final String entityId;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final DateTime detectedAt;
  final ConflictResolution? resolution;
  final bool isResolved;
  final DateTime? resolvedAt;
  final Map<String, dynamic>? metadata;

  const SyncConflict({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.localData,
    required this.remoteData,
    required this.detectedAt,
    this.resolution,
    this.isResolved = false,
    this.resolvedAt,
    this.metadata,
  });

  /// Create a SyncConflict from two SyncModel instances
  factory SyncConflict.fromModels({
    required String entityType,
    required String entityId,
    required SyncModel localModel,
    required SyncModel remoteModel,
    Map<String, dynamic>? metadata,
  }) {
    final conflictId = '${entityType}_${entityId}_${DateTime.now().millisecondsSinceEpoch}';
    
    return SyncConflict(
      id: conflictId,
      entityType: entityType,
      entityId: entityId,
      localData: localModel.toFirestoreMap(),
      remoteData: remoteModel.toFirestoreMap(),
      detectedAt: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Convert to map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entity_type': entityType,
      'entity_id': entityId,
      'local_data': json.encode(localData),
      'remote_data': json.encode(remoteData),
      'detected_at': detectedAt.toIso8601String(),
      'resolution': resolution?.value,
      'is_resolved': isResolved ? 1 : 0,
      'resolved_at': resolvedAt?.toIso8601String(),
      'metadata': metadata != null ? json.encode(metadata) : null,
    };
  }

  /// Create from database map
  factory SyncConflict.fromMap(Map<String, dynamic> map) {
    return SyncConflict(
      id: map['id'] as String,
      entityType: map['entity_type'] as String,
      entityId: map['entity_id'] as String,
      localData: json.decode(map['local_data'] as String) as Map<String, dynamic>,
      remoteData: json.decode(map['remote_data'] as String) as Map<String, dynamic>,
      detectedAt: DateTime.parse(map['detected_at'] as String),
      resolution: map['resolution'] != null 
          ? ConflictResolutionExtension.fromString(map['resolution'] as String)
          : null,
      isResolved: (map['is_resolved'] as int? ?? 0) == 1,
      resolvedAt: map['resolved_at'] != null 
          ? DateTime.parse(map['resolved_at'] as String)
          : null,
      metadata: map['metadata'] != null 
          ? json.decode(map['metadata'] as String) as Map<String, dynamic>
          : null,
    );
  }

  /// Get list of differences between local and remote data
  List<ConflictDifference> getDifferences() {
    final differences = <ConflictDifference>[];
    final allKeys = <String>{...localData.keys, ...remoteData.keys};

    for (final key in allKeys) {
      final localValue = localData[key];
      final remoteValue = remoteData[key];

      if (localValue != remoteValue) {
        differences.add(ConflictDifference(
          fieldName: key,
          localValue: localValue,
          remoteValue: remoteValue,
          fieldType: _getFieldType(localValue ?? remoteValue),
          canAutoResolve: _canAutoResolveField(key, localValue, remoteValue),
        ));
      }
    }

    return differences;
  }

  /// Check if this conflict can be automatically resolved
  bool canAutoResolve() {
    final differences = getDifferences();
    return differences.isNotEmpty && differences.every((diff) => diff.canAutoResolve);
  }

  /// Get automatic resolution strategy if possible
  ConflictResolution? getAutoResolution() {
    if (!canAutoResolve()) return null;

    final differences = getDifferences();
    
    // If only timestamp differences, use the most recent
    if (differences.every((diff) => _isTimestampField(diff.fieldName))) {
      final localUpdated = localData['updatedAt'] as String?;
      final remoteUpdated = remoteData['updatedAt'] as String?;
      
      if (localUpdated != null && remoteUpdated != null) {
        final localTime = DateTime.parse(localUpdated);
        final remoteTime = DateTime.parse(remoteUpdated);
        return localTime.isAfter(remoteTime) 
            ? ConflictResolution.useLocal 
            : ConflictResolution.useRemote;
      }
    }

    // Default to manual resolution for complex conflicts
    return ConflictResolution.manual;
  }

  /// Create a resolved copy of this conflict
  SyncConflict resolve(ConflictResolution resolution) {
    return SyncConflict(
      id: id,
      entityType: entityType,
      entityId: entityId,
      localData: localData,
      remoteData: remoteData,
      detectedAt: detectedAt,
      resolution: resolution,
      isResolved: true,
      resolvedAt: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Helper method to determine field type
  String _getFieldType(dynamic value) {
    if (value == null) return 'null';
    if (value is String) return 'string';
    if (value is int) return 'int';
    if (value is double) return 'double';
    if (value is bool) return 'bool';
    if (value is List) return 'list';
    if (value is Map) return 'map';
    return 'unknown';
  }

  /// Helper method to check if a field can be auto-resolved
  bool _canAutoResolveField(String fieldName, dynamic localValue, dynamic remoteValue) {
    // Timestamp fields can usually be auto-resolved by taking the most recent
    if (_isTimestampField(fieldName)) return true;
    
    // Version fields can be auto-resolved by taking the higher version
    if (fieldName == 'version' && localValue is int && remoteValue is int) return true;
    
    // For now, be conservative and require manual resolution for most fields
    return false;
  }

  /// Helper method to check if a field is a timestamp
  bool _isTimestampField(String fieldName) {
    return fieldName.toLowerCase().contains('at') || 
           fieldName.toLowerCase().contains('time') ||
           fieldName.toLowerCase().contains('date');
  }

  @override
  String toString() {
    return 'SyncConflict(id: $id, type: $entityType, entityId: $entityId, resolved: $isResolved)';
  }
}
