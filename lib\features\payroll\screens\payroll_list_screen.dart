import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../config/routes.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/models/salary.dart';
import '../../../shared/models/withdrawal.dart';
import '../../../shared/models/bank_account.dart';
import '../../../shared/widgets/app_drawer.dart';
import '../widgets/salary_card.dart';
import '../widgets/withdrawal_card.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../core/repositories/salary_repository.dart';
import '../../../core/repositories/withdrawal_repository.dart';
import '../../../core/repositories/bank_account_repository.dart';
import '../../../core/repositories/employee_balance_repository.dart';
import '../../../core/repositories/payroll_transaction_repository.dart';

class PayrollListScreen extends StatefulWidget {
  const PayrollListScreen({super.key});

  @override
  State<PayrollListScreen> createState() => _PayrollListScreenState();
}

class _PayrollListScreenState extends State<PayrollListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Salary> _salaries = [];
  List<Withdrawal> _withdrawals = [];
  List<Employee> _employees = [];
  String _searchQuery = '';

  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final SalaryRepository _salaryRepository = SalaryRepository();
  final WithdrawalRepository _withdrawalRepository = WithdrawalRepository();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام الـ repositories للحصول على البيانات من قاعدة البيانات
      final employees = await _employeeRepository.getAllEmployees();
      final salaries = await _salaryRepository.getAllSalaries();
      final withdrawals = await _withdrawalRepository.getAllWithdrawals();

      if (mounted) {
        setState(() {
          _employees = employees;
          _salaries = salaries;
          _withdrawals = withdrawals;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading payroll data: $e');
      }

      if (mounted) {
        setState(() {
          // في حالة حدوث خطأ، استخدم بيانات وهمية للعرض
          _employees = [
            Employee(
              id: 1,
              name: 'أحمد محمد',
              email: '<EMAIL>',
              phone: '0501234567',
              position: 'فني تكييف',
              joinDate: DateTime(2022, 5, 15),
              salary: 5000,
              status: EmployeeStatus.active,
              createdAt: DateTime.now(),
            ),
            Employee(
              id: 2,
              name: 'محمد علي',
              email: '<EMAIL>',
              phone: '0509876543',
              position: 'فني صيانة',
              joinDate: DateTime(2021, 3, 10),
              salary: 4500,
              status: EmployeeStatus.active,
              createdAt: DateTime.now(),
            ),
          ];

          _salaries = [
            Salary(
              id: 1,
              employeeId: 1,
              employeeName: 'أحمد محمد',
              month: 5,
              year: 2023,
              basicSalary: 5000,
              allowances: 500,
              deductions: 200,
              netSalary: 5300,
              paidAmount: 5300,
              status: SalaryStatus.paid,
              createdAt: DateTime.now().subtract(const Duration(days: 30)),
            ),
            Salary(
              id: 2,
              employeeId: 2,
              employeeName: 'محمد علي',
              month: 5,
              year: 2023,
              basicSalary: 4500,
              allowances: 300,
              deductions: 150,
              netSalary: 4650,
              paidAmount: 4650,
              status: SalaryStatus.paid,
              createdAt: DateTime.now().subtract(const Duration(days: 30)),
            ),
          ];

          _withdrawals = [
            Withdrawal(
              id: 1,
              employeeId: 1,
              employeeName: 'أحمد محمد',
              amount: 1000,
              date: DateTime.now().subtract(const Duration(days: 15)),
              reason: 'سلفة شخصية',
              status: WithdrawalStatus.approved,
              createdAt: DateTime.now().subtract(const Duration(days: 15)),
            ),
            Withdrawal(
              id: 2,
              employeeId: 2,
              employeeName: 'محمد علي',
              amount: 800,
              date: DateTime.now().subtract(const Duration(days: 10)),
              reason: 'مصاريف طبية',
              status: WithdrawalStatus.approved,
              createdAt: DateTime.now().subtract(const Duration(days: 10)),
            ),
          ];

          _isLoading = false;
        });

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Salary> get _filteredSalaries {
    if (_searchQuery.isEmpty) {
      return _salaries;
    }

    return _salaries.where((salary) {
      final query = _searchQuery.toLowerCase();
      return salary.employeeName.toLowerCase().contains(query) ||
          '${salary.month}/${salary.year}'.contains(query);
    }).toList();
  }

  List<Withdrawal> get _filteredWithdrawals {
    if (_searchQuery.isEmpty) {
      return _withdrawals;
    }

    return _withdrawals.where((withdrawal) {
      final query = _searchQuery.toLowerCase();
      return withdrawal.employeeName.toLowerCase().contains(query) ||
          (withdrawal.reason?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الرواتب والسحوبات'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الرواتب'),
            Tab(text: 'السحوبات'),
          ],
        ),
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'بحث...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              // Text direction is handled automatically
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      // Salaries Tab
                      _buildSalariesTab(),

                      // Withdrawals Tab
                      _buildWithdrawalsTab(),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Show action menu based on current tab
          _showAddActionMenu(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSalariesTab() {
    return _filteredSalaries.isEmpty
        ? const Center(
            child: Text(
              'لا توجد رواتب',
              style: AppTextStyles.heading3,
            ),
          )
        : RefreshIndicator(
            onRefresh: _loadData,
            child: ListView.builder(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              itemCount: _filteredSalaries.length,
              itemBuilder: (context, index) {
                final salary = _filteredSalaries[index];
                return SalaryCard(
                  salary: salary,
                  onTap: () {
                    _showSalaryDetails(context, salary);
                  },
                );
              },
            ),
          );
  }

  Widget _buildWithdrawalsTab() {
    return _filteredWithdrawals.isEmpty
        ? const Center(
            child: Text(
              'لا توجد سحوبات',
              style: AppTextStyles.heading3,
            ),
          )
        : RefreshIndicator(
            onRefresh: _loadData,
            child: ListView.builder(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              itemCount: _filteredWithdrawals.length,
              itemBuilder: (context, index) {
                final withdrawal = _filteredWithdrawals[index];
                return WithdrawalCard(
                  withdrawal: withdrawal,
                  onTap: () {
                    _showWithdrawalDetails(context, withdrawal);
                  },
                );
              },
            ),
          );
  }

  void _showAddActionMenu(BuildContext context) {
    final currentTab = _tabController.index;

    if (currentTab == 0) {
      // Salaries tab - directly open the salary form
      _showFullSalaryForm(context);
    } else {
      // Withdrawals tab
      _showAddWithdrawalDialog(context);
    }
  }

  void _showFullSalaryForm(BuildContext context, [Employee? employee, int? month, int? year]) {
    // فتح شاشة إضافة راتب تفصيلية
    Navigator.pushNamed(
      context,
      AppRoutes.addSalary,
      arguments: {
        'employee': employee,
        'month': month ?? DateTime.now().month,
        'year': year ?? DateTime.now().year,
      },
    ).then((result) {
      if (result == true) {
        // تم إضافة راتب جديد، قم بتحديث القائمة
        _loadData();
      }
    });
  }

  void _showAddWithdrawalDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    Employee? selectedEmployee;
    double amount = 0;
    String reason = '';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تسجيل سحب نقدي'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<Employee>(
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  items: _employees.map((employee) {
                    return DropdownMenuItem<Employee>(
                      value: employee,
                      child: Text(employee.name),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الموظف';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    selectedEmployee = value;
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'المبلغ',
                    prefixIcon: Icon(Icons.money),
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    final parsedValue = double.tryParse(value);
                    if (parsedValue == null || parsedValue <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    amount = double.parse(value!);
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'سبب السحب',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال سبب السحب';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    reason = value!;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.pop(context);

                  // استخدام دالة منفصلة لإضافة طلب سحب جديد
                  _addNewWithdrawal(
                    employeeId: selectedEmployee!.id!,
                    employeeName: selectedEmployee!.name,
                    amount: amount,
                    reason: reason,
                  );
                }
              },
              child: const Text('تسجيل'),
            ),
          ],
        );
      },
    );
  }

  void _showSalaryDetails(BuildContext context, Salary salary) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تفاصيل راتب ${salary.employeeName}'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('الشهر/السنة', '${Salary.getMonthName(salary.month)}/${salary.year}'),
                _buildDetailRow('الراتب الأساسي', '${salary.basicSalary} ر.س'),
                _buildDetailRow('البدلات', '${salary.allowances} ر.س'),
                _buildDetailRow('الاستقطاعات', '${salary.deductions} ر.س'),
                _buildDetailRow('صافي الراتب', '${salary.netSalary} ر.س'),
                _buildDetailRow('المبلغ المدفوع', '${salary.paidAmount} ر.س'),
                _buildDetailRow('الحالة', Salary.getStatusName(salary.status)),
                _buildDetailRow('تاريخ الإنشاء', DateFormat('dd/MM/yyyy').format(salary.createdAt)),

                // إظهار معلومات الدفع إذا كان الراتب مدفوعاً
                if (salary.status == SalaryStatus.paid || salary.status == SalaryStatus.partiallyPaid) ...[
                  const Divider(),
                  const Padding(
                    padding: EdgeInsets.only(top: 8.0, bottom: 8.0),
                    child: Text(
                      'معلومات الدفع',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  if (salary.paymentMethod != null)
                    _buildDetailRow(
                      'طريقة الدفع',
                      Salary.getPaymentMethodName(salary.paymentMethod),
                      icon: Salary.getPaymentMethodIcon(salary.paymentMethod),
                    ),
                  if (salary.paymentMethod == PaymentMethod.cash && salary.paymentEmployeeName != null)
                    _buildDetailRow('الموظف المسلم', salary.paymentEmployeeName!),
                  if (salary.paymentMethod == PaymentMethod.bankTransfer && salary.paymentBankAccountName != null)
                    _buildDetailRow('الحساب البنكي', salary.paymentBankAccountName!),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
            if (salary.status == SalaryStatus.pending)
              TextButton(
                onPressed: () {
                  Navigator.pop(context);

                  // استخدام دالة منفصلة لتسجيل دفع الراتب
                  _markSalaryAsPaid(salary);
                },
                child: const Text('تسجيل كمدفوع'),
              ),
          ],
        );
      },
    );
  }

  Future<void> _markSalaryAsPaid(Salary salary) async {
    // عرض نافذة اختيار طريقة الدفع
    _showPaymentMethodDialog(salary);
  }

  void _showPaymentMethodDialog(Salary salary) {
    PaymentMethod paymentMethod = PaymentMethod.cash;
    int? employeeId;
    int? bankAccountId;
    double? currentBalance;

    // قائمة الموظفين
    List<Employee> employees = _employees;

    // قائمة الحسابات البنكية
    List<BankAccount> bankAccounts = [];

    // تحميل الحسابات البنكية
    final bankAccountRepository = BankAccountRepository();

    // مستودع حساب الموظف
    final employeeBalanceRepository = EmployeeBalanceRepository();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تسجيل دفع الراتب'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow('الموظف', salary.employeeName),
                    _buildDetailRow('صافي الراتب', '${salary.netSalary} ر.س'),
                    const SizedBox(height: 16),
                    const Text(
                      'طريقة الدفع',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<PaymentMethod>(
                            title: const Text('نقدي'),
                            value: PaymentMethod.cash,
                            groupValue: paymentMethod,
                            onChanged: (value) {
                              setState(() {
                                paymentMethod = value!;
                              });
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<PaymentMethod>(
                            title: const Text('تحويل بنكي'),
                            value: PaymentMethod.bankTransfer,
                            groupValue: paymentMethod,
                            onChanged: (value) {
                              setState(() {
                                paymentMethod = value!;
                              });

                              // تحميل الحسابات البنكية إذا لم تكن محملة
                              if (bankAccounts.isEmpty) {
                                bankAccountRepository.getAllBankAccounts().then((accounts) {
                                  setState(() {
                                    bankAccounts = accounts.where((account) => account.isActive).toList();
                                  });
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // عرض الرصيد الحالي إذا كان متاحاً
                    if (currentBalance != null)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: currentBalance! >= salary.netSalary ? Colors.green.withAlpha(25) : Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: currentBalance! >= salary.netSalary ? Colors.green : Colors.red,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'الرصيد الحالي:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              '${NumberFormat('#,##0.00').format(currentBalance)} ر.س',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: currentBalance! >= salary.netSalary ? Colors.green : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 16),

                    // عرض قائمة الموظفين إذا كانت طريقة الدفع نقدية
                    if (paymentMethod == PaymentMethod.cash)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الموظف المسلم للراتب',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<int>(
                                isExpanded: true,
                                hint: const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                                  child: Text('اختر الموظف'),
                                ),
                                value: employeeId,
                                items: employees.map((employee) {
                                  return DropdownMenuItem<int>(
                                    value: employee.id,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                      child: Text(employee.name),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) async {
                                  setState(() {
                                    employeeId = value;
                                  });

                                  // عرض رصيد الموظف (العهدة)
                                  if (value != null) {
                                    final balance = await employeeBalanceRepository.getEmployeeBalance(value);
                                    setState(() {
                                      currentBalance = balance;
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),

                    // عرض قائمة الحسابات البنكية إذا كانت طريقة الدفع تحويل بنكي
                    if (paymentMethod == PaymentMethod.bankTransfer)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الحساب البنكي',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          bankAccounts.isEmpty
                              ? const Center(child: CircularProgressIndicator())
                              : Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<int>(
                                      isExpanded: true,
                                      hint: const Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 8.0),
                                        child: Text('اختر الحساب البنكي'),
                                      ),
                                      value: bankAccountId,
                                      items: bankAccounts.map((account) {
                                        return DropdownMenuItem<int>(
                                          value: account.id,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                            child: Text('${account.bankName} - ${account.accountNumber}'),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          bankAccountId = value;
                                        });

                                        // عرض رصيد الحساب البنكي
                                        if (value != null) {
                                          final selectedAccount = bankAccounts.firstWhere(
                                            (account) => account.id == value,
                                            orElse: () => BankAccount(
                                              id: 0,
                                              bankName: '',
                                              accountNumber: '',
                                              accountName: '',
                                              balance: 0,
                                              isActive: true,
                                              createdAt: DateTime.now(),
                                              type: BankAccountType.checking,
                                            ),
                                          );

                                          setState(() {
                                            currentBalance = selectedAccount.balance;
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                ),
                        ],
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: (currentBalance != null && currentBalance! < salary.netSalary)
                      ? null // تعطيل الزر إذا كان الرصيد غير كافٍ
                      : () {
                          // التحقق من اختيار الموظف أو الحساب البنكي
                          if (paymentMethod == PaymentMethod.cash && employeeId == null) {
                            _showSnackBar('يرجى اختيار الموظف المسلم للراتب', isError: true);
                            return;
                          }

                          if (paymentMethod == PaymentMethod.bankTransfer && bankAccountId == null) {
                            _showSnackBar('يرجى اختيار الحساب البنكي', isError: true);
                            return;
                          }

                          Navigator.pop(context);

                          // تحديث الراتب
                          _updateSalaryPayment(
                            salary,
                            paymentMethod,
                            employeeId,
                            bankAccountId,
                          );
                        },
                  child: const Text('تسجيل الدفع'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updateSalaryPayment(
    Salary salary,
    PaymentMethod paymentMethod,
    int? employeeId,
    int? bankAccountId,
  ) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء مستودع معاملات الرواتب
      final payrollTransactionRepository = PayrollTransactionRepository();

      // التحقق من كفاية الرصيد
      final hasSufficientBalance = await payrollTransactionRepository.checkSufficientBalance(
        salary.netSalary,
        paymentMethod,
        employeeId,
        bankAccountId,
      );

      if (!hasSufficientBalance) {
        // الرصيد غير كافٍ، عرض رسالة خطأ
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // الحصول على الرصيد الحالي
          double currentBalance = 0;
          if (paymentMethod == PaymentMethod.cash && employeeId != null) {
            currentBalance = await payrollTransactionRepository.getEmployeeBalance(employeeId);
          } else if (paymentMethod == PaymentMethod.bankTransfer && bankAccountId != null) {
            currentBalance = await payrollTransactionRepository.getBankAccountBalance(bankAccountId);
          }

          // عرض رسالة خطأ مع تفاصيل الرصيد
          _showSnackBar(
            'الرصيد غير كافٍ لدفع الراتب. الرصيد الحالي: ${NumberFormat('#,##0.00').format(currentBalance)} ر.س، المبلغ المطلوب: ${NumberFormat('#,##0.00').format(salary.netSalary)} ر.س',
            isError: true,
          );
        }
        return;
      }

      // إنشاء معاملة مالية لتسجيل الدفع
      final transactionResult = await payrollTransactionRepository.createSalaryPaymentTransaction(
        salary,
        paymentMethod,
        employeeId,
        bankAccountId,
      );

      if (transactionResult <= 0) {
        // فشل في إنشاء المعاملة المالية
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          _showSnackBar('فشل في تسجيل المعاملة المالية للراتب', isError: true);
        }
        return;
      }

      // تحديث حالة الراتب إلى مدفوع
      final updatedSalary = salary.copyWith(
        status: SalaryStatus.paid,
        paidAmount: salary.netSalary,
        paymentMethod: paymentMethod,
        paymentEmployeeId: employeeId,
        paymentBankAccountId: bankAccountId,
      );

      // تحديث الراتب في قاعدة البيانات
      final result = await _salaryRepository.updateSalary(updatedSalary);

      if (mounted) {
        if (result > 0) {
          // تم التحديث بنجاح، قم بتحديث القائمة
          _loadData(); // إعادة تحميل البيانات من قاعدة البيانات

          // عرض رسالة نجاح
          _showSnackBar('تم تسجيل دفع راتب ${salary.employeeName}');
        } else {
          // فشل في التحديث
          setState(() {
            _isLoading = false;
          });

          _showSnackBar('فشل في تسجيل دفع الراتب', isError: true);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating salary: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تسجيل دفع الراتب: $e', isError: true);
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
        ),
      );
    }
  }

  void _showWithdrawalDetails(BuildContext context, Withdrawal withdrawal) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تفاصيل سحب ${withdrawal.employeeName}'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('المبلغ', '${withdrawal.amount} ر.س'),
                _buildDetailRow('التاريخ', DateFormat('dd/MM/yyyy').format(withdrawal.date)),
                _buildDetailRow('السبب', withdrawal.reason ?? 'غير محدد'),
                _buildDetailRow('الحالة', Withdrawal.getStatusName(withdrawal.status)),
                _buildDetailRow('تاريخ الإنشاء', DateFormat('dd/MM/yyyy').format(withdrawal.createdAt)),

                // إظهار معلومات الدفع إذا كان السحب مدفوعاً
                if (withdrawal.isPaid) ...[
                  const Divider(),
                  const Padding(
                    padding: EdgeInsets.only(top: 8.0, bottom: 8.0),
                    child: Text(
                      'معلومات الدفع',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  if (withdrawal.paymentMethod != null)
                    _buildDetailRow(
                      'طريقة الدفع',
                      Salary.getPaymentMethodName(withdrawal.paymentMethod),
                      icon: Salary.getPaymentMethodIcon(withdrawal.paymentMethod),
                    ),
                  if (withdrawal.paymentMethod == PaymentMethod.cash && withdrawal.paymentEmployeeName != null)
                    _buildDetailRow('الموظف المسلم', withdrawal.paymentEmployeeName!),
                  if (withdrawal.paymentMethod == PaymentMethod.bankTransfer && withdrawal.paymentBankAccountName != null)
                    _buildDetailRow('الحساب البنكي', withdrawal.paymentBankAccountName!),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إغلاق'),
            ),
            if (withdrawal.status == WithdrawalStatus.pending)
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);

                      // استخدام دالة منفصلة لرفض طلب السحب
                      _updateWithdrawalStatus(withdrawal, WithdrawalStatus.rejected);
                    },
                    child: const Text('رفض', style: TextStyle(color: Colors.red)),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);

                      // استخدام دالة منفصلة للموافقة على طلب السحب
                      _updateWithdrawalStatus(withdrawal, WithdrawalStatus.approved);
                    },
                    child: const Text('موافقة'),
                  ),
                ],
              ),
            if (withdrawal.status == WithdrawalStatus.approved && !withdrawal.isPaid)
              TextButton(
                onPressed: () {
                  Navigator.pop(context);

                  // استخدام دالة منفصلة لتسجيل دفع السحب
                  _markWithdrawalAsPaid(withdrawal);
                },
                child: const Text('تسجيل كمدفوع'),
              ),
          ],
        );
      },
    );
  }

  Future<void> _addNewWithdrawal({
    required int employeeId,
    required String employeeName,
    required double amount,
    required String reason,
  }) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء كائن طلب سحب جديد
      final newWithdrawal = Withdrawal(
        employeeId: employeeId,
        employeeName: employeeName,
        amount: amount,
        date: DateTime.now(),
        reason: reason,
        status: WithdrawalStatus.pending,
        createdAt: DateTime.now(),
      );

      // إضافة طلب السحب إلى قاعدة البيانات
      final result = await _withdrawalRepository.insertWithdrawal(newWithdrawal);

      if (mounted) {
        if (result > 0) {
          // تم الإضافة بنجاح، قم بتحديث القائمة
          _loadData(); // إعادة تحميل البيانات من قاعدة البيانات

          // عرض رسالة نجاح
          _showSnackBar('تم تسجيل سحب نقدي بمبلغ $amount ريال لـ $employeeName');
        } else {
          // فشل في الإضافة
          setState(() {
            _isLoading = false;
          });

          _showSnackBar('فشل في تسجيل طلب السحب', isError: true);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding withdrawal: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تسجيل طلب السحب: $e', isError: true);
      }
    }
  }

  Future<void> _updateWithdrawalStatus(Withdrawal withdrawal, WithdrawalStatus newStatus) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث حالة طلب السحب
      final updatedWithdrawal = withdrawal.copyWith(
        status: newStatus,
        approvedBy: 1, // استخدام معرف المستخدم الحالي (admin)
        approvedAt: DateTime.now(),
      );

      // تحديث طلب السحب في قاعدة البيانات
      final result = await _withdrawalRepository.updateWithdrawal(updatedWithdrawal);

      if (mounted) {
        if (result > 0) {
          // تم التحديث بنجاح، قم بتحديث القائمة
          _loadData(); // إعادة تحميل البيانات من قاعدة البيانات

          // عرض رسالة نجاح
          if (newStatus == WithdrawalStatus.approved) {
            _showSnackBar('تم الموافقة على طلب سحب ${withdrawal.employeeName}');
          } else {
            _showSnackBar('تم رفض طلب سحب ${withdrawal.employeeName}', isError: true);
          }
        } else {
          // فشل في التحديث
          setState(() {
            _isLoading = false;
          });

          _showSnackBar('فشل في تحديث حالة طلب السحب', isError: true);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating withdrawal status: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تحديث حالة طلب السحب: $e', isError: true);
      }
    }
  }

  Future<void> _markWithdrawalAsPaid(Withdrawal withdrawal) async {
    // عرض نافذة اختيار طريقة الدفع
    _showWithdrawalPaymentMethodDialog(withdrawal);
  }

  void _showWithdrawalPaymentMethodDialog(Withdrawal withdrawal) {
    PaymentMethod paymentMethod = PaymentMethod.cash;
    int? employeeId;
    int? bankAccountId;
    double? currentBalance;

    // قائمة الموظفين
    List<Employee> employees = _employees;

    // قائمة الحسابات البنكية
    List<BankAccount> bankAccounts = [];

    // تحميل الحسابات البنكية
    final bankAccountRepository = BankAccountRepository();

    // مستودع حساب الموظف
    final employeeBalanceRepository = EmployeeBalanceRepository();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تسجيل دفع السحب'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow('الموظف', withdrawal.employeeName),
                    _buildDetailRow('المبلغ', '${withdrawal.amount} ر.س'),
                    const SizedBox(height: 16),
                    const Text(
                      'طريقة الدفع',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<PaymentMethod>(
                            title: const Text('نقدي'),
                            value: PaymentMethod.cash,
                            groupValue: paymentMethod,
                            onChanged: (value) {
                              setState(() {
                                paymentMethod = value!;
                              });
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<PaymentMethod>(
                            title: const Text('تحويل بنكي'),
                            value: PaymentMethod.bankTransfer,
                            groupValue: paymentMethod,
                            onChanged: (value) {
                              setState(() {
                                paymentMethod = value!;
                              });

                              // تحميل الحسابات البنكية إذا لم تكن محملة
                              if (bankAccounts.isEmpty) {
                                bankAccountRepository.getAllBankAccounts().then((accounts) {
                                  setState(() {
                                    bankAccounts = accounts.where((account) => account.isActive).toList();
                                  });
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // عرض الرصيد الحالي إذا كان متاحاً
                    if (currentBalance != null)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: currentBalance! >= withdrawal.amount ? Colors.green.withAlpha(25) : Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: currentBalance! >= withdrawal.amount ? Colors.green : Colors.red,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'الرصيد الحالي:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              '${NumberFormat('#,##0.00').format(currentBalance)} ر.س',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: currentBalance! >= withdrawal.amount ? Colors.green : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(height: 16),

                    // عرض قائمة الموظفين إذا كانت طريقة الدفع نقدية
                    if (paymentMethod == PaymentMethod.cash)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الموظف المسلم للمبلغ',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<int>(
                                isExpanded: true,
                                hint: const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                                  child: Text('اختر الموظف'),
                                ),
                                value: employeeId,
                                items: employees.map((employee) {
                                  return DropdownMenuItem<int>(
                                    value: employee.id,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                      child: Text(employee.name),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) async {
                                  setState(() {
                                    employeeId = value;
                                  });

                                  // عرض رصيد الموظف (العهدة)
                                  if (value != null) {
                                    final balance = await employeeBalanceRepository.getEmployeeBalance(value);
                                    setState(() {
                                      currentBalance = balance;
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),

                    // عرض قائمة الحسابات البنكية إذا كانت طريقة الدفع تحويل بنكي
                    if (paymentMethod == PaymentMethod.bankTransfer)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الحساب البنكي',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          bankAccounts.isEmpty
                              ? const Center(child: CircularProgressIndicator())
                              : Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<int>(
                                      isExpanded: true,
                                      hint: const Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 8.0),
                                        child: Text('اختر الحساب البنكي'),
                                      ),
                                      value: bankAccountId,
                                      items: bankAccounts.map((account) {
                                        return DropdownMenuItem<int>(
                                          value: account.id,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                            child: Text('${account.bankName} - ${account.accountNumber}'),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          bankAccountId = value;
                                        });

                                        // عرض رصيد الحساب البنكي
                                        if (value != null) {
                                          final selectedAccount = bankAccounts.firstWhere(
                                            (account) => account.id == value,
                                            orElse: () => BankAccount(
                                              id: 0,
                                              bankName: '',
                                              accountNumber: '',
                                              accountName: '',
                                              balance: 0,
                                              isActive: true,
                                              createdAt: DateTime.now(),
                                              type: BankAccountType.checking,
                                            ),
                                          );

                                          setState(() {
                                            currentBalance = selectedAccount.balance;
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                ),
                        ],
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: (currentBalance != null && currentBalance! < withdrawal.amount)
                      ? null // تعطيل الزر إذا كان الرصيد غير كافٍ
                      : () {
                          // التحقق من اختيار الموظف أو الحساب البنكي
                          if (paymentMethod == PaymentMethod.cash && employeeId == null) {
                            _showSnackBar('يرجى اختيار الموظف المسلم للمبلغ', isError: true);
                            return;
                          }

                          if (paymentMethod == PaymentMethod.bankTransfer && bankAccountId == null) {
                            _showSnackBar('يرجى اختيار الحساب البنكي', isError: true);
                            return;
                          }

                          Navigator.pop(context);

                          // تحديث السحب
                          _updateWithdrawalPayment(
                            withdrawal,
                            paymentMethod,
                            employeeId,
                            bankAccountId,
                          );
                        },
                  child: const Text('تسجيل الدفع'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updateWithdrawalPayment(
    Withdrawal withdrawal,
    PaymentMethod paymentMethod,
    int? employeeId,
    int? bankAccountId,
  ) async {
    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء مستودع معاملات الرواتب
      final payrollTransactionRepository = PayrollTransactionRepository();

      // التحقق من كفاية الرصيد
      final hasSufficientBalance = await payrollTransactionRepository.checkSufficientBalance(
        withdrawal.amount,
        paymentMethod,
        employeeId,
        bankAccountId,
      );

      if (!hasSufficientBalance) {
        // الرصيد غير كافٍ، عرض رسالة خطأ
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // الحصول على الرصيد الحالي
          double currentBalance = 0;
          if (paymentMethod == PaymentMethod.cash && employeeId != null) {
            currentBalance = await payrollTransactionRepository.getEmployeeBalance(employeeId);
          } else if (paymentMethod == PaymentMethod.bankTransfer && bankAccountId != null) {
            currentBalance = await payrollTransactionRepository.getBankAccountBalance(bankAccountId);
          }

          // عرض رسالة خطأ مع تفاصيل الرصيد
          _showSnackBar(
            'الرصيد غير كافٍ لدفع السحب. الرصيد الحالي: ${NumberFormat('#,##0.00').format(currentBalance)} ر.س، المبلغ المطلوب: ${NumberFormat('#,##0.00').format(withdrawal.amount)} ر.س',
            isError: true,
          );
        }
        return;
      }

      // إنشاء معاملة مالية لتسجيل الدفع
      final transactionResult = await payrollTransactionRepository.createWithdrawalPaymentTransaction(
        withdrawal,
        paymentMethod,
        employeeId,
        bankAccountId,
      );

      if (transactionResult <= 0) {
        // فشل في إنشاء المعاملة المالية
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          _showSnackBar('فشل في تسجيل المعاملة المالية للسحب', isError: true);
        }
        return;
      }

      // تحديث حالة السحب إلى مدفوع
      final updatedWithdrawal = withdrawal.copyWith(
        isPaid: true,
        paymentMethod: paymentMethod,
        paymentEmployeeId: employeeId,
        paymentBankAccountId: bankAccountId,
      );

      // تحديث السحب في قاعدة البيانات
      final result = await _withdrawalRepository.updateWithdrawal(updatedWithdrawal);

      if (mounted) {
        if (result > 0) {
          // تم التحديث بنجاح، قم بتحديث القائمة
          _loadData(); // إعادة تحميل البيانات من قاعدة البيانات

          // عرض رسالة نجاح
          _showSnackBar('تم تسجيل دفع سحب ${withdrawal.employeeName}');
        } else {
          // فشل في التحديث
          setState(() {
            _isLoading = false;
          });

          _showSnackBar('فشل في تسجيل دفع السحب', isError: true);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating withdrawal payment: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSnackBar('حدث خطأ أثناء تسجيل دفع السحب: $e', isError: true);
      }
    }
  }

  Widget _buildDetailRow(String label, String value, {IconData? icon}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
              ],
              Text(
                value,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
