import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import '../../shared/models/sync_model.dart';

/// Service for managing sync status, progress tracking, and error handling
class SyncStatusManager {
  static final SyncStatusManager _instance = SyncStatusManager._internal();
  factory SyncStatusManager() => _instance;
  SyncStatusManager._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Stream controllers
  final StreamController<SyncStatusInfo> _statusController = 
      StreamController<SyncStatusInfo>.broadcast();
  final StreamController<SyncProgressInfo> _progressController = 
      StreamController<SyncProgressInfo>.broadcast();
  final StreamController<List<SyncError>> _errorsController = 
      StreamController<List<SyncError>>.broadcast();

  // Current state
  SyncStatusInfo _currentStatus = SyncStatusInfo.idle();
  SyncProgressInfo _currentProgress = SyncProgressInfo.initial();
  List<SyncError> _currentErrors = [];

  // Configuration
  static const String _lastSyncTimeKey = 'last_sync_time';
  static const String _syncSettingsKey = 'sync_settings';
  static const int _maxErrorHistory = 50;

  /// Initialize the sync status manager
  Future<void> initialize() async {
    try {
      await _loadSyncSettings();
      await _loadLastSyncTime();
      await _loadSyncErrors();
      
      if (kDebugMode) {
        print('✅ SyncStatusManager initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing SyncStatusManager: $e');
      }
    }
  }

  /// Dispose the sync status manager
  void dispose() {
    _statusController.close();
    _progressController.close();
    _errorsController.close();
  }

  // Getters

  /// Current sync status
  SyncStatusInfo get currentStatus => _currentStatus;

  /// Current sync progress
  SyncProgressInfo get currentProgress => _currentProgress;

  /// Current sync errors
  List<SyncError> get currentErrors => List.unmodifiable(_currentErrors);

  /// Stream of sync status changes
  Stream<SyncStatusInfo> get statusStream => _statusController.stream;

  /// Stream of sync progress updates
  Stream<SyncProgressInfo> get progressStream => _progressController.stream;

  /// Stream of sync errors
  Stream<List<SyncError>> get errorsStream => _errorsController.stream;

  // Status Management

  /// Update sync status
  void updateStatus(SyncState state, {String? message, Map<String, dynamic>? metadata}) {
    _currentStatus = SyncStatusInfo(
      state: state,
      message: message ?? _getDefaultMessage(state),
      timestamp: DateTime.now(),
      metadata: metadata,
    );

    _statusController.add(_currentStatus);

    if (kDebugMode) {
      print('📊 Sync status updated: ${_currentStatus.state} - ${_currentStatus.message}');
    }
  }

  /// Update sync progress
  void updateProgress({
    required String operation,
    required int current,
    required int total,
    String? details,
  }) {
    _currentProgress = SyncProgressInfo(
      operation: operation,
      current: current,
      total: total,
      percentage: total > 0 ? (current / total * 100).clamp(0, 100) : 0,
      details: details,
      timestamp: DateTime.now(),
    );

    _progressController.add(_currentProgress);

    if (kDebugMode) {
      print('📈 Sync progress: $operation ${_currentProgress.percentage.toStringAsFixed(1)}% ($current/$total)');
    }
  }

  /// Reset progress
  void resetProgress() {
    _currentProgress = SyncProgressInfo.initial();
    _progressController.add(_currentProgress);
  }

  // Error Management

  /// Add sync error
  void addError(SyncError error) {
    _currentErrors.insert(0, error);
    
    // Limit error history
    if (_currentErrors.length > _maxErrorHistory) {
      _currentErrors = _currentErrors.take(_maxErrorHistory).toList();
    }

    _errorsController.add(List.unmodifiable(_currentErrors));
    _saveSyncErrors();

    if (kDebugMode) {
      print('❌ Sync error added: ${error.message}');
    }
  }

  /// Add sync error with automatic error creation
  void addErrorFromException(Exception exception, {
    String? operation,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? context,
  }) {
    final error = SyncError(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      message: exception.toString(),
      operation: operation ?? 'unknown',
      entityType: entityType,
      entityId: entityId,
      timestamp: DateTime.now(),
      context: context,
    );

    addError(error);
  }

  /// Clear all errors
  void clearErrors() {
    _currentErrors.clear();
    _errorsController.add([]);
    _saveSyncErrors();

    if (kDebugMode) {
      print('🧹 All sync errors cleared');
    }
  }

  /// Clear errors older than specified duration
  void clearOldErrors({Duration olderThan = const Duration(days: 7)}) {
    final cutoffTime = DateTime.now().subtract(olderThan);
    final initialCount = _currentErrors.length;
    
    _currentErrors.removeWhere((error) => error.timestamp.isBefore(cutoffTime));
    
    if (_currentErrors.length != initialCount) {
      _errorsController.add(List.unmodifiable(_currentErrors));
      _saveSyncErrors();

      if (kDebugMode) {
        print('🧹 Cleared ${initialCount - _currentErrors.length} old sync errors');
      }
    }
  }

  /// Get errors for specific entity
  List<SyncError> getErrorsForEntity(String entityType, String entityId) {
    return _currentErrors
        .where((error) => error.entityType == entityType && error.entityId == entityId)
        .toList();
  }

  /// Get errors for specific operation
  List<SyncError> getErrorsForOperation(String operation) {
    return _currentErrors
        .where((error) => error.operation == operation)
        .toList();
  }

  // Statistics

  /// Get sync statistics
  Future<SyncStatistics> getSyncStatistics() async {
    try {
      final stats = SyncStatistics();
      
      // Get record counts by sync status
      for (final tableName in ['customers', 'service_requests', 'users', 'invoices', 'inventory_items']) {
        try {
          final db = await _dbHelper.database;
          
          // Count by sync status
          final syncedResult = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $tableName WHERE sync_status = ? AND is_deleted = ?',
            ['synced', 0],
          );
          final pendingResult = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $tableName WHERE sync_status IN (?, ?) AND is_deleted = ?',
            ['pendingUpload', 'pendingDownload', 0],
          );
          final failedResult = await db.rawQuery(
            'SELECT COUNT(*) as count FROM $tableName WHERE sync_status = ? AND is_deleted = ?',
            ['failed', 0],
          );

          final synced = syncedResult.first['count'] as int;
          final pending = pendingResult.first['count'] as int;
          final failed = failedResult.first['count'] as int;

          stats.recordCounts[tableName] = SyncRecordCount(
            synced: synced,
            pending: pending,
            failed: failed,
            total: synced + pending + failed,
          );
        } catch (e) {
          // Table might not exist yet
          if (kDebugMode) {
            print('⚠️ Could not get stats for table $tableName: $e');
          }
        }
      }

      // Get conflict count
      try {
        final conflicts = await _dbHelper.getUnresolvedConflicts();
        stats.conflictCount = conflicts.length;
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Could not get conflict count: $e');
        }
      }

      // Get queue count
      try {
        final queueItems = await _dbHelper.getPendingSyncOperations();
        stats.queueCount = queueItems.length;
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Could not get queue count: $e');
        }
      }

      stats.errorCount = _currentErrors.length;
      stats.lastSyncTime = _currentStatus.metadata?['lastSyncTime'] as DateTime?;

      return stats;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting sync statistics: $e');
      }
      return SyncStatistics();
    }
  }

  // Settings Management

  /// Save last sync time
  Future<void> saveLastSyncTime(DateTime time) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSyncTimeKey, time.toIso8601String());
      
      // Update current status metadata
      final metadata = Map<String, dynamic>.from(_currentStatus.metadata ?? {});
      metadata['lastSyncTime'] = time;
      
      updateStatus(_currentStatus.state, 
          message: _currentStatus.message, 
          metadata: metadata);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving last sync time: $e');
      }
    }
  }

  /// Load last sync time
  Future<void> _loadLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(_lastSyncTimeKey);
      
      if (timeString != null) {
        final time = DateTime.parse(timeString);
        final metadata = Map<String, dynamic>.from(_currentStatus.metadata ?? {});
        metadata['lastSyncTime'] = time;
        
        _currentStatus = _currentStatus.copyWith(metadata: metadata);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading last sync time: $e');
      }
    }
  }

  /// Save sync settings
  Future<void> saveSyncSettings(SyncSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_syncSettingsKey, json.encode(settings.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving sync settings: $e');
      }
    }
  }

  /// Load sync settings
  Future<void> _loadSyncSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsString = prefs.getString(_syncSettingsKey);
      
      if (settingsString != null) {
        final settingsMap = json.decode(settingsString) as Map<String, dynamic>;
        // TODO: Apply loaded settings to sync service
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading sync settings: $e');
      }
    }
  }

  /// Save sync errors to persistent storage
  Future<void> _saveSyncErrors() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorsJson = _currentErrors.map((error) => error.toMap()).toList();
      await prefs.setString('sync_errors', json.encode(errorsJson));
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving sync errors: $e');
      }
    }
  }

  /// Load sync errors from persistent storage
  Future<void> _loadSyncErrors() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorsString = prefs.getString('sync_errors');
      
      if (errorsString != null) {
        final errorsList = json.decode(errorsString) as List;
        _currentErrors = errorsList
            .map((errorMap) => SyncError.fromMap(errorMap as Map<String, dynamic>))
            .toList();
        
        _errorsController.add(List.unmodifiable(_currentErrors));
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading sync errors: $e');
      }
    }
  }

  /// Get default message for sync state
  String _getDefaultMessage(SyncState state) {
    switch (state) {
      case SyncState.idle:
        return 'Sync is idle';
      case SyncState.syncing:
        return 'Synchronizing data...';
      case SyncState.completed:
        return 'Sync completed successfully';
      case SyncState.failed:
        return 'Sync failed';
      case SyncState.noConnection:
        return 'No internet connection';
      case SyncState.conflict:
        return 'Sync conflicts detected';
    }
  }
}

/// Enum for sync states
enum SyncState {
  idle,
  syncing,
  completed,
  failed,
  noConnection,
  conflict,
}

/// Class representing sync status information
class SyncStatusInfo {
  final SyncState state;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const SyncStatusInfo({
    required this.state,
    required this.message,
    required this.timestamp,
    this.metadata,
  });

  factory SyncStatusInfo.idle() {
    return SyncStatusInfo(
      state: SyncState.idle,
      message: 'Sync is idle',
      timestamp: DateTime.now(),
    );
  }

  SyncStatusInfo copyWith({
    SyncState? state,
    String? message,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return SyncStatusInfo(
      state: state ?? this.state,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'SyncStatusInfo(state: $state, message: $message, timestamp: $timestamp)';
  }
}

/// Class representing sync progress information
class SyncProgressInfo {
  final String operation;
  final int current;
  final int total;
  final double percentage;
  final String? details;
  final DateTime timestamp;

  const SyncProgressInfo({
    required this.operation,
    required this.current,
    required this.total,
    required this.percentage,
    this.details,
    required this.timestamp,
  });

  factory SyncProgressInfo.initial() {
    return SyncProgressInfo(
      operation: '',
      current: 0,
      total: 0,
      percentage: 0,
      timestamp: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'SyncProgressInfo(operation: $operation, progress: ${percentage.toStringAsFixed(1)}%, current: $current, total: $total)';
  }
}

/// Class representing a sync error
class SyncError {
  final String id;
  final String message;
  final String operation;
  final String? entityType;
  final String? entityId;
  final DateTime timestamp;
  final Map<String, dynamic>? context;

  const SyncError({
    required this.id,
    required this.message,
    required this.operation,
    this.entityType,
    this.entityId,
    required this.timestamp,
    this.context,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'message': message,
      'operation': operation,
      'entity_type': entityType,
      'entity_id': entityId,
      'timestamp': timestamp.toIso8601String(),
      'context': context,
    };
  }

  factory SyncError.fromMap(Map<String, dynamic> map) {
    return SyncError(
      id: map['id'] as String,
      message: map['message'] as String,
      operation: map['operation'] as String,
      entityType: map['entity_type'] as String?,
      entityId: map['entity_id'] as String?,
      timestamp: DateTime.parse(map['timestamp'] as String),
      context: map['context'] as Map<String, dynamic>?,
    );
  }

  @override
  String toString() {
    return 'SyncError(operation: $operation, message: $message, timestamp: $timestamp)';
  }
}

/// Class representing sync statistics
class SyncStatistics {
  Map<String, SyncRecordCount> recordCounts = {};
  int conflictCount = 0;
  int queueCount = 0;
  int errorCount = 0;
  DateTime? lastSyncTime;

  @override
  String toString() {
    return 'SyncStatistics(records: ${recordCounts.length}, conflicts: $conflictCount, queue: $queueCount, errors: $errorCount)';
  }
}

/// Class representing record count by sync status
class SyncRecordCount {
  final int synced;
  final int pending;
  final int failed;
  final int total;

  const SyncRecordCount({
    required this.synced,
    required this.pending,
    required this.failed,
    required this.total,
  });

  @override
  String toString() {
    return 'SyncRecordCount(synced: $synced, pending: $pending, failed: $failed, total: $total)';
  }
}

/// Class representing sync settings
class SyncSettings {
  final bool autoSyncEnabled;
  final Duration syncInterval;
  final bool syncOnlyOnWifi;
  final bool autoResolveConflicts;

  const SyncSettings({
    this.autoSyncEnabled = true,
    this.syncInterval = const Duration(minutes: 5),
    this.syncOnlyOnWifi = false,
    this.autoResolveConflicts = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'auto_sync_enabled': autoSyncEnabled,
      'sync_interval_minutes': syncInterval.inMinutes,
      'sync_only_on_wifi': syncOnlyOnWifi,
      'auto_resolve_conflicts': autoResolveConflicts,
    };
  }

  factory SyncSettings.fromMap(Map<String, dynamic> map) {
    return SyncSettings(
      autoSyncEnabled: map['auto_sync_enabled'] as bool? ?? true,
      syncInterval: Duration(minutes: map['sync_interval_minutes'] as int? ?? 5),
      syncOnlyOnWifi: map['sync_only_on_wifi'] as bool? ?? false,
      autoResolveConflicts: map['auto_resolve_conflicts'] as bool? ?? true,
    );
  }
}
