import 'package:flutter/material.dart';
import 'package:icecorner/config/constants.dart';
import '../../../shared/models/permission.dart';

class PermissionModuleCard extends StatelessWidget {
  final PermissionModule module;
  final bool isFullyGranted;
  final bool isPartiallyGranted;
  final List<Permission> permissions;
  final Function(bool) onModuleChanged;
  final Function(PermissionOperation, bool) onPermissionChanged;

  const PermissionModuleCard({
    super.key,
    required this.module,
    required this.isFullyGranted,
    required this.isPartiallyGranted,
    required this.permissions,
    required this.onModuleChanged,
    required this.onPermissionChanged,
  });

  IconData _getModuleIcon(PermissionModule module) {
    switch (module) {
      case PermissionModule.dashboard:
        return Icons.dashboard;
      case PermissionModule.invoices:
        return Icons.receipt_long;
      case PermissionModule.transactions:
        return Icons.account_balance_wallet;
      case PermissionModule.customers:
        return Icons.people;
      case PermissionModule.serviceRequests:
        return Icons.build;
      case PermissionModule.inventory:
        return Icons.inventory_2;
      case PermissionModule.employees:
        return Icons.badge;
      case PermissionModule.suppliers:
        return Icons.local_shipping;
      case PermissionModule.payroll:
        return Icons.payments;
      case PermissionModule.reports:
        return Icons.bar_chart;
      case PermissionModule.bankAccounts:
        return Icons.account_balance;
      case PermissionModule.settings:
        return Icons.settings;
      case PermissionModule.users:
        return Icons.people_alt;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          leading: Icon(
            _getModuleIcon(module),
            color: isFullyGranted
                ? Colors.green
                : isPartiallyGranted
                    ? Colors.orange
                    : AppColors.textPrimary,
          ),
          title: Text(
            Permission.getModuleName(module),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isFullyGranted
                  ? Colors.green
                  : isPartiallyGranted
                      ? Colors.orange
                      : AppColors.textPrimary,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                value: isFullyGranted,
                tristate: true,
                activeColor: Colors.green,
                checkColor: Colors.white,
                fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                  if (states.contains(WidgetState.selected)) {
                    return Colors.green;
                  } else if (states.contains(WidgetState.disabled)) {
                    return Colors.grey;
                  } else if (isPartiallyGranted) {
                    return Colors.orange;
                  }
                  return Colors.grey;
                }),
                onChanged: (value) {
                  onModuleChanged(value ?? false);
                },
              ),
              const Icon(Icons.expand_more),
            ],
          ),
          children: [
            const Divider(height: 1),
            ...permissions.map((permission) {
              return ListTile(
                dense: true,
                leading: Icon(
                  Permission.getOperationIcon(permission.operation),
                  color: permission.granted ? Colors.green : Colors.grey,
                  size: 20,
                ),
                title: Text(
                  Permission.getOperationName(permission.operation),
                  style: TextStyle(
                    color: permission.granted ? Colors.green : AppColors.textPrimary,
                  ),
                ),
                trailing: Checkbox(
                  value: permission.granted,
                  activeColor: Colors.green,
                  onChanged: (value) {
                    onPermissionChanged(permission.operation, value ?? false);
                  },
                ),
                onTap: () {
                  onPermissionChanged(permission.operation, !permission.granted);
                },
              );
            }),
          ],
        ),
      ),
    );
  }
}
