import 'package:flutter/material.dart';
import '../../core/localization/app_localizations.dart';

/// RTL-aware widget that automatically adjusts layout based on text direction
class RTLAwareWidget extends StatelessWidget {
  final Widget child;
  final bool forceRTL;
  final bool forceLTR;

  const RTLAwareWidget({
    super.key,
    required this.child,
    this.forceRTL = false,
    this.forceLTR = false,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    TextDirection direction;
    if (forceRTL) {
      direction = TextDirection.rtl;
    } else if (forceLTR) {
      direction = TextDirection.ltr;
    } else {
      direction = (localizations?.isRTL ?? false) ? TextDirection.rtl : TextDirection.ltr;
    }

    return Directionality(
      textDirection: direction,
      child: child,
    );
  }
}

/// RTL-aware padding that adjusts based on text direction
class RTLAwarePadding extends StatelessWidget {
  final Widget child;
  final double? start;
  final double? end;
  final double? top;
  final double? bottom;
  final double? all;

  const RTLAwarePadding({
    super.key,
    required this.child,
    this.start,
    this.end,
    this.top,
    this.bottom,
    this.all,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;

    EdgeInsetsGeometry padding;
    if (all != null) {
      padding = EdgeInsets.all(all!);
    } else {
      final left = isRTL ? (end ?? 0) : (start ?? 0);
      final right = isRTL ? (start ?? 0) : (end ?? 0);
      
      padding = EdgeInsets.only(
        left: left,
        right: right,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    }

    return Padding(
      padding: padding,
      child: child,
    );
  }
}

/// RTL-aware margin that adjusts based on text direction
class RTLAwareMargin extends StatelessWidget {
  final Widget child;
  final double? start;
  final double? end;
  final double? top;
  final double? bottom;
  final double? all;

  const RTLAwareMargin({
    super.key,
    required this.child,
    this.start,
    this.end,
    this.top,
    this.bottom,
    this.all,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;

    EdgeInsetsGeometry margin;
    if (all != null) {
      margin = EdgeInsets.all(all!);
    } else {
      final left = isRTL ? (end ?? 0) : (start ?? 0);
      final right = isRTL ? (start ?? 0) : (end ?? 0);
      
      margin = EdgeInsets.only(
        left: left,
        right: right,
        top: top ?? 0,
        bottom: bottom ?? 0,
      );
    }

    return Container(
      margin: margin,
      child: child,
    );
  }
}

/// RTL-aware row that reverses children order in RTL
class RTLAwareRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool reverseInRTL;

  const RTLAwareRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.reverseInRTL = true,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;

    List<Widget> orderedChildren = children;
    if (isRTL && reverseInRTL) {
      orderedChildren = children.reversed.toList();
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: orderedChildren,
    );
  }
}

/// RTL-aware text that automatically sets text direction
class RTLAwareText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool? softWrap;

  const RTLAwareText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.softWrap,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;

    TextAlign effectiveTextAlign = textAlign ?? (isRTL ? TextAlign.right : TextAlign.left);

    return Text(
      text,
      style: style,
      textAlign: effectiveTextAlign,
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
    );
  }
}

/// RTL-aware icon button that flips icon in RTL
class RTLAwareIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final double? iconSize;
  final Color? color;
  final String? tooltip;
  final bool flipInRTL;

  const RTLAwareIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.iconSize,
    this.color,
    this.tooltip,
    this.flipInRTL = true,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;

    Widget iconWidget = Icon(
      icon,
      size: iconSize,
      color: color,
    );

    if (isRTL && flipInRTL) {
      iconWidget = Transform.scale(
        scaleX: -1,
        child: iconWidget,
      );
    }

    return IconButton(
      onPressed: onPressed,
      icon: iconWidget,
      tooltip: tooltip,
    );
  }
}

/// RTL-aware list tile that adjusts leading/trailing based on direction
class RTLAwareListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;

  const RTLAwareListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;

    return ListTile(
      leading: isRTL ? trailing : leading,
      title: title,
      subtitle: subtitle,
      trailing: isRTL ? leading : trailing,
      onTap: onTap,
      contentPadding: contentPadding,
    );
  }
}

/// Helper class for RTL-aware utilities
class RTLHelper {
  /// Get the appropriate alignment based on text direction
  static Alignment getStartAlignment(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;
    return isRTL ? Alignment.centerRight : Alignment.centerLeft;
  }

  /// Get the appropriate alignment based on text direction
  static Alignment getEndAlignment(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;
    return isRTL ? Alignment.centerLeft : Alignment.centerRight;
  }

  /// Get the appropriate text align based on text direction
  static TextAlign getStartTextAlign(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;
    return isRTL ? TextAlign.right : TextAlign.left;
  }

  /// Get the appropriate text align based on text direction
  static TextAlign getEndTextAlign(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;
    return isRTL ? TextAlign.left : TextAlign.right;
  }

  /// Get the appropriate main axis alignment for start
  static MainAxisAlignment getStartMainAxisAlignment(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;
    return isRTL ? MainAxisAlignment.end : MainAxisAlignment.start;
  }

  /// Get the appropriate main axis alignment for end
  static MainAxisAlignment getEndMainAxisAlignment(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final isRTL = localizations?.isRTL ?? false;
    return isRTL ? MainAxisAlignment.start : MainAxisAlignment.end;
  }

  /// Check if current locale is RTL
  static bool isRTL(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return localizations?.isRTL ?? false;
  }

  /// Get text direction for current locale
  static TextDirection getTextDirection(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return (localizations?.isRTL ?? false) ? TextDirection.rtl : TextDirection.ltr;
  }
}
