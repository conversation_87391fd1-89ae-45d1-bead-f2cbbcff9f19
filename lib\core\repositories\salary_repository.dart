import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../../shared/models/salary.dart';

class SalaryRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Get all salaries
  Future<List<Salary>> getAllSalaries() async {
    try {
      final db = await _dbHelper.database;

      // Join salaries with employees to get employee names and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT s.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM salaries s
        LEFT JOIN employees e ON s.employee_id = e.id
        LEFT JOIN employees pe ON s.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON s.payment_bank_account_id = ba.id
      ''');

      return List.generate(maps.length, (i) {
        return Salary(
          id: maps[i]['id'] as int?,
          employeeId: maps[i]['employee_id'] as int,
          employeeName: maps[i]['employee_name'] as String? ?? 'غير معروف',
          month: maps[i]['month'] as int,
          year: maps[i]['year'] as int,
          basicSalary: maps[i]['basic_salary'] as double,
          allowances: maps[i]['allowances'] as double,
          deductions: maps[i]['deductions'] as double,
          netSalary: maps[i]['net_salary'] as double,
          paidAmount: maps[i]['paid_amount'] as double,
          status: _parseStatus(maps[i]['status'] as String),
          notes: maps[i]['notes'] as String?,
          createdBy: maps[i]['created_by'] as int?,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          updatedAt: maps[i]['updated_at'] != null
              ? DateTime.parse(maps[i]['updated_at'] as String)
              : null,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting salaries: $e');
      }
      return [];
    }
  }

  // Get salary by ID
  Future<Salary?> getSalaryById(int id) async {
    try {
      final db = await _dbHelper.database;

      // Join salaries with employees to get employee name and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT s.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM salaries s
        LEFT JOIN employees e ON s.employee_id = e.id
        LEFT JOIN employees pe ON s.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON s.payment_bank_account_id = ba.id
        WHERE s.id = ?
      ''', [id]);

      if (maps.isNotEmpty) {
        return Salary(
          id: maps[0]['id'] as int?,
          employeeId: maps[0]['employee_id'] as int,
          employeeName: maps[0]['employee_name'] as String? ?? 'غير معروف',
          month: maps[0]['month'] as int,
          year: maps[0]['year'] as int,
          basicSalary: maps[0]['basic_salary'] as double,
          allowances: maps[0]['allowances'] as double,
          deductions: maps[0]['deductions'] as double,
          netSalary: maps[0]['net_salary'] as double,
          paidAmount: maps[0]['paid_amount'] as double,
          status: _parseStatus(maps[0]['status'] as String),
          notes: maps[0]['notes'] as String?,
          createdBy: maps[0]['created_by'] as int?,
          createdAt: DateTime.parse(maps[0]['created_at'] as String),
          updatedAt: maps[0]['updated_at'] != null
              ? DateTime.parse(maps[0]['updated_at'] as String)
              : null,
        );
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting salary by ID: $e');
      }
      return null;
    }
  }

  // Get salaries by employee ID
  Future<List<Salary>> getSalariesByEmployeeId(int employeeId) async {
    try {
      final db = await _dbHelper.database;

      // Join salaries with employees to get employee name and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT s.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM salaries s
        LEFT JOIN employees e ON s.employee_id = e.id
        LEFT JOIN employees pe ON s.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON s.payment_bank_account_id = ba.id
        WHERE s.employee_id = ?
      ''', [employeeId]);

      return List.generate(maps.length, (i) {
        return Salary(
          id: maps[i]['id'] as int?,
          employeeId: maps[i]['employee_id'] as int,
          employeeName: maps[i]['employee_name'] as String? ?? 'غير معروف',
          month: maps[i]['month'] as int,
          year: maps[i]['year'] as int,
          basicSalary: maps[i]['basic_salary'] as double,
          allowances: maps[i]['allowances'] as double,
          deductions: maps[i]['deductions'] as double,
          netSalary: maps[i]['net_salary'] as double,
          paidAmount: maps[i]['paid_amount'] as double,
          status: _parseStatus(maps[i]['status'] as String),
          notes: maps[i]['notes'] as String?,
          createdBy: maps[i]['created_by'] as int?,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          updatedAt: maps[i]['updated_at'] != null
              ? DateTime.parse(maps[i]['updated_at'] as String)
              : null,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting salaries by employee ID: $e');
      }
      return [];
    }
  }

  // Get salaries by date range
  Future<List<Salary>> getSalariesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _dbHelper.database;

      // Convert dates to ISO format for comparison
      final startDateStr = startDate.toIso8601String();
      final endDateStr = endDate.toIso8601String();

      // Join salaries with employees to get employee name and payment info
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT s.*,
               e.name as employee_name,
               pe.name as payment_employee_name,
               ba.account_name as payment_bank_account_name
        FROM salaries s
        LEFT JOIN employees e ON s.employee_id = e.id
        LEFT JOIN employees pe ON s.payment_employee_id = pe.id
        LEFT JOIN bank_accounts ba ON s.payment_bank_account_id = ba.id
        WHERE s.created_at BETWEEN ? AND ?
      ''', [startDateStr, endDateStr]);

      return List.generate(maps.length, (i) {
        return Salary(
          id: maps[i]['id'] as int?,
          employeeId: maps[i]['employee_id'] as int,
          employeeName: maps[i]['employee_name'] as String? ?? 'غير معروف',
          month: maps[i]['month'] as int,
          year: maps[i]['year'] as int,
          basicSalary: maps[i]['basic_salary'] as double,
          allowances: maps[i]['allowances'] as double,
          deductions: maps[i]['deductions'] as double,
          netSalary: maps[i]['net_salary'] as double,
          paidAmount: maps[i]['paid_amount'] as double,
          status: _parseStatus(maps[i]['status'] as String),
          notes: maps[i]['notes'] as String?,
          createdBy: maps[i]['created_by'] as int?,
          createdAt: DateTime.parse(maps[i]['created_at'] as String),
          updatedAt: maps[i]['updated_at'] != null
              ? DateTime.parse(maps[i]['updated_at'] as String)
              : null,
          paymentMethod: maps[i]['payment_method'] != null
              ? _parsePaymentMethod(maps[i]['payment_method'] as String)
              : null,
          paymentEmployeeId: maps[i]['payment_employee_id'] as int?,
          paymentEmployeeName: maps[i]['payment_employee_name'] as String?,
          paymentBankAccountId: maps[i]['payment_bank_account_id'] as int?,
          paymentBankAccountName: maps[i]['payment_bank_account_name'] as String?,
        );
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting salaries by date range: $e');
      }
      return [];
    }
  }

  // Insert a new salary
  Future<int> insertSalary(Salary salary) async {
    try {
      final db = await _dbHelper.database;

      return await db.insert(
        'salaries',
        {
          'employee_id': salary.employeeId,
          'month': salary.month,
          'year': salary.year,
          'basic_salary': salary.basicSalary,
          'allowances': salary.allowances,
          'deductions': salary.deductions,
          'net_salary': salary.netSalary,
          'paid_amount': salary.paidAmount,
          'status': salary.status.toString().split('.').last,
          'notes': salary.notes,
          'created_by': salary.createdBy,
          'created_at': DateTime.now().toIso8601String(),
          'payment_method': salary.paymentMethod?.toString().split('.').last,
          'payment_employee_id': salary.paymentEmployeeId,
          'payment_bank_account_id': salary.paymentBankAccountId,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting salary: $e');
      }
      return -1;
    }
  }

  // Update an existing salary
  Future<int> updateSalary(Salary salary) async {
    try {
      final db = await _dbHelper.database;
      return await db.update(
        'salaries',
        {
          'employee_id': salary.employeeId,
          'month': salary.month,
          'year': salary.year,
          'basic_salary': salary.basicSalary,
          'allowances': salary.allowances,
          'deductions': salary.deductions,
          'net_salary': salary.netSalary,
          'paid_amount': salary.paidAmount,
          'status': salary.status.toString().split('.').last,
          'notes': salary.notes,
          'updated_at': DateTime.now().toIso8601String(),
          'payment_method': salary.paymentMethod?.toString().split('.').last,
          'payment_employee_id': salary.paymentEmployeeId,
          'payment_bank_account_id': salary.paymentBankAccountId,
        },
        where: 'id = ?',
        whereArgs: [salary.id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating salary: $e');
      }
      return 0;
    }
  }

  // Delete a salary
  Future<int> deleteSalary(int id) async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(
        'salaries',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting salary: $e');
      }
      return 0;
    }
  }

  // Helper method to parse status
  static SalaryStatus _parseStatus(String status) {
    switch (status) {
      case 'pending':
        return SalaryStatus.pending;
      case 'paid':
        return SalaryStatus.paid;
      case 'partiallyPaid':
        return SalaryStatus.partiallyPaid;
      case 'cancelled':
        return SalaryStatus.cancelled;
      default:
        return SalaryStatus.pending;
    }
  }

  // Helper method to parse payment method
  static PaymentMethod _parsePaymentMethod(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'banktransfer':
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      default:
        return PaymentMethod.cash;
    }
  }
}
