import 'package:flutter/services.dart';
import 'package:pdf/widgets.dart' as pw;

class ArabicFonts {
  // Font cache to avoid loading the font multiple times
  static pw.Font? _arabicFont;
  static pw.Font? _arabicFontBold;

  // Get the Arabic font (regular)
  static Future<pw.Font> getArabicFont() async {
    if (_arabicFont == null) {
      try {
        // Try to load the font from assets
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        _arabicFont = pw.Font.ttf(fontData.buffer.asByteData());
      } catch (e) {
        // If font loading fails, use the embedded font data
        _arabicFont = pw.Font.ttf(await _getEmbeddedFont());
      }
    }
    return _arabicFont!;
  }

  // Alias for getArabicFont for backward compatibility
  static Future<pw.Font> getFont() async {
    return getArabicFont();
  }

  // Get the Arabic font (bold)
  static Future<pw.Font> getArabicFontBold() async {
    if (_arabicFontBold == null) {
      try {
        // Try to load the font from assets
        final fontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        _arabicFontBold = pw.Font.ttf(fontData.buffer.asByteData());
      } catch (e) {
        // If font loading fails, use the embedded font data
        _arabicFontBold = pw.Font.ttf(await _getEmbeddedFontBold());
      }
    }
    return _arabicFontBold!;
  }

  // Alias for getArabicFontBold for backward compatibility
  static Future<pw.Font> getFontBold() async {
    return getArabicFontBold();
  }

  // Embedded font data
  static Future<ByteData> _getEmbeddedFont() async {
    // We know Cairo-Regular.ttf exists in assets/fonts
    try {
      final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
      return fontData.buffer.asByteData();
    } catch (e) {
      // If that fails for some reason, try to load the default font from the PDF package
      try {
        final fontData = await rootBundle.load('packages/pdf/assets/roboto-regular.ttf');
        return fontData.buffer.asByteData();
      } catch (e) {
        // Last resort - create a minimal font data to avoid crashing
        final fontBytes = Uint8List(32);
        return ByteData.view(fontBytes.buffer);
      }
    }
  }

  // Embedded bold font data
  static Future<ByteData> _getEmbeddedFontBold() async {
    // We know Cairo-Bold.ttf exists in assets/fonts
    try {
      final fontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      return fontData.buffer.asByteData();
    } catch (e) {
      // If that fails for some reason, try to load the default font from the PDF package
      try {
        final fontData = await rootBundle.load('packages/pdf/assets/roboto-bold.ttf');
        return fontData.buffer.asByteData();
      } catch (e) {
        // Last resort - create a minimal font data to avoid crashing
        final fontBytes = Uint8List(32);
        return ByteData.view(fontBytes.buffer);
      }
    }
  }
}
