import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../../config/constants.dart';
import '../../../shared/models/employee.dart';
import '../../../shared/models/salary.dart';
import '../../../shared/models/withdrawal.dart';
import '../../../core/repositories/employee_repository.dart';
import '../../../core/repositories/salary_repository.dart';
import '../../../core/repositories/withdrawal_repository.dart';

class AddSalaryScreen extends StatefulWidget {
  final Employee? employee;
  final int? month;
  final int? year;

  const AddSalaryScreen({
    super.key,
    this.employee,
    this.month,
    this.year,
  });

  @override
  State<AddSalaryScreen> createState() => _AddSalaryScreenState();
}

class _AddSalaryScreenState extends State<AddSalaryScreen> {
  final _formKey = GlobalKey<FormState>();
  final EmployeeRepository _employeeRepository = EmployeeRepository();
  final SalaryRepository _salaryRepository = SalaryRepository();
  final WithdrawalRepository _withdrawalRepository = WithdrawalRepository();

  bool _isLoading = true;
  List<Employee> _employees = [];
  List<Withdrawal> _employeeWithdrawals = [];
  Employee? _selectedEmployee;
  int _month = DateTime.now().month;
  int _year = DateTime.now().year;
  double _basicSalary = 0;
  double _allowances = 0;
  double _deductions = 0;
  double _withdrawalsTotal = 0;
  String? _notes;

  @override
  void initState() {
    super.initState();

    // استخدام القيم المرسلة إذا كانت متوفرة
    if (widget.employee != null) {
      _selectedEmployee = widget.employee;
    }

    if (widget.month != null) {
      _month = widget.month!;
    }

    if (widget.year != null) {
      _year = widget.year!;
    }

    _loadEmployees();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final employees = await _employeeRepository.getAllEmployees();

      if (mounted) {
        setState(() {
          _employees = employees;
          _isLoading = false;

          // إذا كان الموظف محدد مسبقًا، قم بتعيين الراتب الأساسي وتحميل السحوبات
          if (_selectedEmployee != null) {
            _basicSalary = _selectedEmployee!.salary;
            _loadEmployeeWithdrawals(_selectedEmployee!.id!);
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading employees: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل بيانات الموظفين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadEmployeeWithdrawals(int employeeId) async {
    try {
      // تحميل السحوبات غير المدفوعة الخاصة بالموظف
      final withdrawals = await _withdrawalRepository.getUnpaidWithdrawalsByEmployeeId(employeeId);

      // حساب إجمالي السحوبات المعتمدة غير المدفوعة
      double total = 0;
      for (var withdrawal in withdrawals) {
        total += withdrawal.amount;
      }

      if (mounted) {
        setState(() {
          _employeeWithdrawals = withdrawals;
          _withdrawalsTotal = total;
          _deductions = total; // تعيين الاستقطاعات بناءً على السحوبات
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading employee withdrawals: $e');
      }

      // في حالة حدوث خطأ، نعين قيمة افتراضية للسحوبات
      if (mounted) {
        setState(() {
          _employeeWithdrawals = [];
          _withdrawalsTotal = 0;
          _deductions = 0;
        });
      }
    }
  }

  // Helper method to build employee info cards
  Widget _buildEmployeeInfoCard({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveSalary() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      if (_selectedEmployee == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار الموظف'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // حساب صافي الراتب
        final netSalary = _basicSalary + _allowances - _deductions;

        // إنشاء كائن الراتب
        final salary = Salary(
          employeeId: _selectedEmployee!.id!,
          employeeName: _selectedEmployee!.name,
          month: _month,
          year: _year,
          basicSalary: _basicSalary,
          allowances: _allowances,
          deductions: _deductions,
          netSalary: netSalary,
          paidAmount: 0, // لم يتم الدفع بعد
          status: SalaryStatus.pending,
          notes: _notes,
          createdAt: DateTime.now(),
        );

        // حفظ الراتب في قاعدة البيانات
        final result = await _salaryRepository.insertSalary(salary);

        if (result > 0) {
          // تم حفظ الراتب بنجاح، الآن نقوم بتحديث حالة السحوبات
          if (_employeeWithdrawals.isNotEmpty) {
            // جمع معرفات السحوبات التي سيتم تحديثها
            final withdrawalIds = _employeeWithdrawals.map((w) => w.id!).toList();

            // تحديث حالة السحوبات إلى "مدفوعة"
            await _withdrawalRepository.markWithdrawalsAsPaid(withdrawalIds, result);

            if (kDebugMode) {
              print('تم تحديث ${withdrawalIds.length} سحوبات كمدفوعة');
            }
          }
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          if (result > 0) {
            // تم الحفظ بنجاح
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إضافة راتب ${_selectedEmployee!.name} بنجاح'),
                backgroundColor: Colors.green,
              ),
            );

            // العودة للشاشة السابقة
            Navigator.pop(context, true);
          } else {
            // فشل في الحفظ
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في إضافة الراتب'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error saving salary: $e');
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء حفظ الراتب: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _selectedEmployee != null
              ? 'إضافة راتب ${_selectedEmployee!.name}'
              : 'إضافة راتب جديد',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 2,
        actions: [
          TextButton.icon(
            onPressed: _isLoading ? null : _saveSalary,
            icon: const Icon(Icons.save, color: Colors.white),
            label: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: _isLoading && _employees.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Employee selection
                    Container(
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(33, 150, 243, 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Color.fromRGBO(33, 150, 243, 0.1)),
                      ),
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.person, color: AppColors.primary),
                              const SizedBox(width: 8),
                              const Text(
                                'معلومات الموظف',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                              const Spacer(),
                              if (_selectedEmployee != null)
                                Chip(
                                  label: Text(
                                    _selectedEmployee!.status == EmployeeStatus.active ? 'نشط' : 'غير نشط',
                                    style: const TextStyle(color: Colors.white, fontSize: 12),
                                  ),
                                  backgroundColor: _selectedEmployee!.status == EmployeeStatus.active
                                      ? Colors.green
                                      : Colors.red,
                                ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                          DropdownButtonFormField<Employee>(
                            decoration: InputDecoration(
                              labelText: 'اختر الموظف',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.person),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: const BorderSide(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            value: _selectedEmployee,
                            items: _employees.map((employee) {
                              return DropdownMenuItem<Employee>(
                                value: employee,
                                child: Row(
                                  children: [
                                    const Icon(Icons.person_outline, size: 18),
                                    const SizedBox(width: 8),
                                    Text(
                                      employee.name,
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                    const Spacer(),
                                    Text(
                                      '${employee.salary} ر.س',
                                      style: TextStyle(
                                        color: Colors.grey.shade700,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedEmployee = value;
                                if (value != null) {
                                  _basicSalary = value.salary;
                                  _loadEmployeeWithdrawals(value.id!);
                                }
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار الموظف';
                              }
                              return null;
                            },
                            icon: const Icon(Icons.arrow_drop_down_circle, color: AppColors.primary),
                            isExpanded: true,
                          ),
                          if (_selectedEmployee != null) ...[
                            const SizedBox(height: AppDimensions.paddingM),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildEmployeeInfoCard(
                                    icon: Icons.work,
                                    title: 'المسمى الوظيفي',
                                    value: _selectedEmployee!.position,
                                  ),
                                ),
                                const SizedBox(width: AppDimensions.paddingM),
                                Expanded(
                                  child: _buildEmployeeInfoCard(
                                    icon: Icons.calendar_today,
                                    title: 'تاريخ التعيين',
                                    value: DateFormat('dd/MM/yyyy').format(_selectedEmployee!.joinDate),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Salary period
                    Container(
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(76, 175, 80, 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Color.fromRGBO(76, 175, 80, 0.1)),
                      ),
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.date_range, color: Colors.green),
                              const SizedBox(width: 8),
                              const Text(
                                'فترة الراتب',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade50,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(color: Colors.green.shade100),
                                ),
                                child: Text(
                                  '${Salary.getMonthName(_month)} $_year',
                                  style: TextStyle(
                                    color: Colors.green.shade800,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButtonFormField<int>(
                                  decoration: InputDecoration(
                                    labelText: 'الشهر',
                                    border: const OutlineInputBorder(),
                                    prefixIcon: const Icon(Icons.calendar_today),
                                    filled: true,
                                    fillColor: Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.grey.shade300),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(color: Colors.green),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  value: _month,
                                  items: List.generate(12, (index) {
                                    return DropdownMenuItem<int>(
                                      value: index + 1,
                                      child: Text(
                                        Salary.getMonthName(index + 1),
                                        style: const TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    );
                                  }),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _month = value;
                                      });
                                    }
                                  },
                                  icon: const Icon(Icons.arrow_drop_down_circle, color: Colors.green),
                                ),
                              ),
                              const SizedBox(width: AppDimensions.paddingM),
                              Expanded(
                                child: DropdownButtonFormField<int>(
                                  decoration: InputDecoration(
                                    labelText: 'السنة',
                                    border: const OutlineInputBorder(),
                                    prefixIcon: const Icon(Icons.calendar_today),
                                    filled: true,
                                    fillColor: Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.grey.shade300),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: const BorderSide(color: Colors.green),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  value: _year,
                                  items: List.generate(5, (index) {
                                    final year = DateTime.now().year - 2 + index;
                                    return DropdownMenuItem<int>(
                                      value: year,
                                      child: Text(
                                        year.toString(),
                                        style: const TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    );
                                  }),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _year = value;
                                      });
                                    }
                                  },
                                  icon: const Icon(Icons.arrow_drop_down_circle, color: Colors.green),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Employee withdrawals summary
                    if (_selectedEmployee != null && _withdrawalsTotal > 0)
                      Container(
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(255, 193, 7, 0.05),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Color.fromRGBO(255, 193, 7, 0.2)),
                        ),
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.warning_amber_rounded, color: Colors.amber),
                                const SizedBox(width: 8),
                                const Text(
                                  'ملخص السحوبات',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.amber,
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(Icons.remove_circle_outline, color: Colors.red, size: 16),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${_employeeWithdrawals.length} سحوبات',
                                        style: const TextStyle(
                                          color: Colors.red,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: AppDimensions.paddingM),

                            // Withdrawals list
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade200),
                              ),
                              padding: const EdgeInsets.all(AppDimensions.paddingS),
                              child: Column(
                                children: [
                                  for (var i = 0; i < _employeeWithdrawals.length; i++) ...[
                                    if (i > 0) const Divider(height: 1),
                                    ListTile(
                                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                                      dense: true,
                                      leading: CircleAvatar(
                                        backgroundColor: Colors.amber.shade100,
                                        radius: 16,
                                        child: Text(
                                          '${i + 1}',
                                          style: TextStyle(
                                            color: Colors.amber.shade900,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      title: Text(
                                        _employeeWithdrawals[i].reason ?? 'سحب نقدي',
                                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                                      ),
                                      subtitle: Text(
                                        DateFormat('dd/MM/yyyy').format(_employeeWithdrawals[i].date),
                                        style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                                      ),
                                      trailing: Text(
                                        '${_employeeWithdrawals[i].amount.toStringAsFixed(2)} ر.س',
                                        style: const TextStyle(
                                          color: Colors.red,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),

                            const SizedBox(height: AppDimensions.paddingM),

                            // Summary
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'الراتب الأساسي:',
                                        style: TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                      Text(
                                        '${_basicSalary.toStringAsFixed(2)} ر.س',
                                        style: const TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'إجمالي السحوبات:',
                                        style: TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                      Text(
                                        '${_withdrawalsTotal.toStringAsFixed(2)} ر.س',
                                        style: const TextStyle(
                                          color: Colors.red,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'المبلغ المتبقي:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '${(_basicSalary - _withdrawalsTotal).toStringAsFixed(2)} ر.س',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Salary details
                    Container(
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(33, 150, 243, 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Color.fromRGBO(33, 150, 243, 0.1)),
                      ),
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.monetization_on, color: AppColors.primary),
                              const SizedBox(width: 8),
                              const Text(
                                'تفاصيل الراتب',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(25),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  '${(_basicSalary + _allowances - _deductions).toStringAsFixed(2)} ر.س',
                                  style: const TextStyle(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingM),

                          // Basic salary
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.shade100,
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.shade50,
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(8),
                                      topRight: Radius.circular(8),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.money, color: AppColors.primary, size: 20),
                                      const SizedBox(width: 8),
                                      const Text(
                                        'الراتب الأساسي',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.primary,
                                        ),
                                      ),
                                      const Spacer(),
                                      if (_withdrawalsTotal > 0)
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.red.shade50,
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            'سحوبات: ${_withdrawalsTotal.toStringAsFixed(2)} ر.س',
                                            style: const TextStyle(
                                              color: Colors.red,
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: TextFormField(
                                    decoration: InputDecoration(
                                      labelText: 'المبلغ',
                                      border: const OutlineInputBorder(),
                                      prefixIcon: const Icon(Icons.attach_money),
                                      suffixText: 'ر.س',
                                      helperText: _withdrawalsTotal > 0
                                          ? 'المتبقي بعد السحوبات: ${(_basicSalary - _withdrawalsTotal).toStringAsFixed(2)} ر.س'
                                          : null,
                                      helperStyle: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                                      filled: true,
                                      fillColor: Colors.white,
                                    ),
                                    keyboardType: TextInputType.number,
                                    initialValue: _selectedEmployee?.salary.toString() ?? _basicSalary.toString(),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'يرجى إدخال الراتب الأساسي';
                                      }
                                      final parsedValue = double.tryParse(value);
                                      if (parsedValue == null || parsedValue < 0) {
                                        return 'يرجى إدخال قيمة صحيحة';
                                      }
                                      return null;
                                    },
                                    onChanged: (value) {
                                      setState(() {
                                        _basicSalary = double.tryParse(value) ?? 0;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: AppDimensions.paddingM),

                          // Allowances and deductions
                          Row(
                            children: [
                              // Allowances
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.shade100,
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.green.shade50,
                                          borderRadius: const BorderRadius.only(
                                            topLeft: Radius.circular(8),
                                            topRight: Radius.circular(8),
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(Icons.add_circle, color: Colors.green.shade700, size: 20),
                                            const SizedBox(width: 8),
                                            Text(
                                              'البدلات',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.green.shade700,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(12),
                                        child: TextFormField(
                                          decoration: InputDecoration(
                                            labelText: 'المبلغ',
                                            border: const OutlineInputBorder(),
                                            prefixIcon: const Icon(Icons.add),
                                            suffixText: 'ر.س',
                                            filled: true,
                                            fillColor: Colors.white,
                                          ),
                                          keyboardType: TextInputType.number,
                                          initialValue: _allowances.toString(),
                                          validator: (value) {
                                            if (value == null || value.isEmpty) {
                                              return null; // البدلات اختيارية
                                            }
                                            final parsedValue = double.tryParse(value);
                                            if (parsedValue == null || parsedValue < 0) {
                                              return 'يرجى إدخال قيمة صحيحة';
                                            }
                                            return null;
                                          },
                                          onChanged: (value) {
                                            setState(() {
                                              _allowances = double.tryParse(value) ?? 0;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              const SizedBox(width: AppDimensions.paddingM),

                              // Deductions
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.shade100,
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.red.shade50,
                                          borderRadius: const BorderRadius.only(
                                            topLeft: Radius.circular(8),
                                            topRight: Radius.circular(8),
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(Icons.remove_circle, color: Colors.red.shade700, size: 20),
                                            const SizedBox(width: 8),
                                            Text(
                                              'الاستقطاعات',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.red.shade700,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(12),
                                        child: TextFormField(
                                          decoration: InputDecoration(
                                            labelText: 'المبلغ',
                                            border: const OutlineInputBorder(),
                                            prefixIcon: const Icon(Icons.remove),
                                            suffixText: 'ر.س',
                                            filled: true,
                                            fillColor: Colors.white,
                                            helperText: _withdrawalsTotal > 0
                                                ? 'تم احتساب السحوبات تلقائياً'
                                                : null,
                                            helperStyle: TextStyle(
                                              color: Colors.grey.shade600,
                                              fontSize: 12,
                                            ),
                                          ),
                                          keyboardType: TextInputType.number,
                                          initialValue: _deductions.toString(),
                                          validator: (value) {
                                            if (value == null || value.isEmpty) {
                                              return null; // الاستقطاعات اختيارية
                                            }
                                            final parsedValue = double.tryParse(value);
                                            if (parsedValue == null || parsedValue < 0) {
                                              return 'يرجى إدخال قيمة صحيحة';
                                            }
                                            return null;
                                          },
                                          onChanged: (value) {
                                            setState(() {
                                              _deductions = double.tryParse(value) ?? _withdrawalsTotal;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: AppDimensions.paddingM),

                          // Net salary
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primary.withAlpha(50),
                                  AppColors.primary.withAlpha(10),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: AppColors.primary.withAlpha(50)),
                            ),
                            child: Column(
                              children: [
                                const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.calculate, color: AppColors.primary),
                                    SizedBox(width: 8),
                                    Text(
                                      'ملخص الراتب',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('الراتب الأساسي:'),
                                    Text(
                                      '${_basicSalary.toStringAsFixed(2)} ر.س',
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('البدلات:'),
                                    Text(
                                      '+ ${_allowances.toStringAsFixed(2)} ر.س',
                                      style: const TextStyle(
                                        color: Colors.green,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('الاستقطاعات:'),
                                    Text(
                                      '- ${_deductions.toStringAsFixed(2)} ر.س',
                                      style: const TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                const Divider(height: 24),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'صافي الراتب:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                    Text(
                                      '${(_basicSalary + _allowances - _deductions).toStringAsFixed(2)} ر.س',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingM),

                    // Notes
                    Container(
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(158, 158, 158, 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Color.fromRGBO(158, 158, 158, 0.1)),
                      ),
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.note_alt, color: Colors.grey.shade700),
                              const SizedBox(width: 8),
                              Text(
                                'ملاحظات إضافية',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppDimensions.paddingM),
                          TextFormField(
                            decoration: InputDecoration(
                              hintText: 'أضف أي ملاحظات إضافية حول الراتب...',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.all(16),
                            ),
                            maxLines: 3,
                            onChanged: (value) {
                              _notes = value;
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppDimensions.paddingL),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          children: [
            // Cancel button
            Expanded(
              flex: 1,
              child: OutlinedButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.cancel),
                label: const Text('إلغاء'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: Colors.grey.shade300),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            // Save button
            Expanded(
              flex: 2,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _saveSalary,
                icon: _isLoading
                    ? Container(
                        width: 24,
                        height: 24,
                        padding: const EdgeInsets.all(2.0),
                        child: const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      )
                    : const Icon(Icons.save),
                label: Text(
                  _isLoading ? 'جاري الحفظ...' : 'حفظ الراتب',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
