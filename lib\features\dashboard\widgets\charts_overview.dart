import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../config/constants.dart';
import '../../../shared/widgets/ui_components.dart';
import '../../../shared/animations/counter_animation.dart';

class ChartsOverview extends StatelessWidget {
  const ChartsOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return StyledCard(
      elevation: AppDimensions.elevationS,
      borderRadius: AppDimensions.radiusM,
      title: 'نظرة عامة',
      icon: Icons.bar_chart,
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          const SizedBox(height: AppDimensions.spacingM),

          // مخطط الإيرادات والمصروفات
          SizedBox(
            height: 200,
            child: _buildBar<PERSON>hart(),
          ),
          const SizedBox(height: AppDimensions.spacingL),

          // مخطط حالة طلبات الخدمة والإحصائيات
          IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: StyledCard(
                    elevation: AppDimensions.elevationXS,
                    borderRadius: AppDimensions.radiusS,
                    title: 'حالة طلبات الخدمة',
                    icon: Icons.pie_chart,
                    padding: const EdgeInsets.all(AppDimensions.spacingS),
                    child: SizedBox(
                      height: 150,
                      child: _buildPieChart(),
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                child: StyledCard(
                  elevation: AppDimensions.elevationXS,
                  borderRadius: AppDimensions.radiusS,
                  title: 'إحصائيات',
                  icon: Icons.analytics,
                  padding: const EdgeInsets.all(AppDimensions.spacingS),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatItem('عدد العملاء', 45, Icons.people, AppColors.primary),
                      _buildStatItem('عدد الفواتير', 120, Icons.receipt, AppColors.primaryLight),
                      _buildStatItem('عناصر المخزون', 78, Icons.inventory_2, Colors.teal),
                      _buildStatItem('الموظفين النشطين', 12, Icons.badge, Colors.indigo),
                    ],
                  ),
                ),
              ),
            ],
          ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 20,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: AppColors.primary.withAlpha(230),
            tooltipRoundedRadius: 8,
            tooltipPadding: const EdgeInsets.all(8),
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              String month;
              switch (group.x) {
                case 0:
                  month = 'يناير';
                  break;
                case 1:
                  month = 'فبراير';
                  break;
                case 2:
                  month = 'مارس';
                  break;
                case 3:
                  month = 'أبريل';
                  break;
                case 4:
                  month = 'مايو';
                  break;
                case 5:
                  month = 'يونيو';
                  break;
                default:
                  month = '';
              }

              final String rodType = rodIndex == 0 ? 'الإيرادات' : 'المصروفات';

              return BarTooltipItem(
                '$month - $rodType\n',
                const TextStyle(
                  fontFamily: 'Cairo',
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                children: <TextSpan>[
                  TextSpan(
                    text: '${rod.toY.round()} ألف ر.س',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      color: Colors.white.withAlpha(240),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                String text;
                switch (value.toInt()) {
                  case 0:
                    text = 'يناير';
                    break;
                  case 1:
                    text = 'فبراير';
                    break;
                  case 2:
                    text = 'مارس';
                    break;
                  case 3:
                    text = 'أبريل';
                    break;
                  case 4:
                    text = 'مايو';
                    break;
                  case 5:
                    text = 'يونيو';
                    break;
                  default:
                    text = '';
                }
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    text,
                    style: const TextStyle(
                      fontFamily: 'Cairo',
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toInt()} ألف',
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: [
          _buildBarGroup(0, 12, 8),
          _buildBarGroup(1, 14, 10),
          _buildBarGroup(2, 15, 9),
          _buildBarGroup(3, 13, 11),
          _buildBarGroup(4, 18, 12),
          _buildBarGroup(5, 16, 13),
        ],
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          drawHorizontalLine: true,
          horizontalInterval: 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.border,
              strokeWidth: 0.5,
              dashArray: [5, 5],
            );
          },
        ),
      ),
    );
  }

  BarChartGroupData _buildBarGroup(int x, double revenue, double expenses) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: revenue,
          color: AppColors.success,
          width: 12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
          backDrawRodData: BackgroundBarChartRodData(
            show: true,
            toY: 20,
            color: AppColors.success.withAlpha(15),
          ),
        ),
        BarChartRodData(
          toY: expenses,
          color: AppColors.error,
          width: 12,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
          backDrawRodData: BackgroundBarChartRodData(
            show: true,
            toY: 20,
            color: AppColors.error.withAlpha(15),
          ),
        ),
      ],
      showingTooltipIndicators: [0, 1],
    );
  }

  Widget _buildPieChart() {
    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 30,
        borderData: FlBorderData(show: false),
        sections: [
          PieChartSectionData(
            color: AppColors.success,
            value: 60,
            title: '60%',
            radius: 50,
            titleStyle: const TextStyle(
              fontFamily: 'Cairo',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            badgeWidget: _buildBadgeIcon(Icons.check_circle, AppColors.success),
            badgePositionPercentageOffset: 0.9,
          ),
          PieChartSectionData(
            color: AppColors.warning,
            value: 30,
            title: '30%',
            radius: 50,
            titleStyle: const TextStyle(
              fontFamily: 'Cairo',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            badgeWidget: _buildBadgeIcon(Icons.pending, AppColors.warning),
            badgePositionPercentageOffset: 0.9,
          ),
          PieChartSectionData(
            color: AppColors.error,
            value: 10,
            title: '10%',
            radius: 50,
            titleStyle: const TextStyle(
              fontFamily: 'Cairo',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            badgeWidget: _buildBadgeIcon(Icons.cancel, AppColors.error),
            badgePositionPercentageOffset: 0.9,
          ),
        ],
      ),
    );
  }

  Widget _buildBadgeIcon(IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(60),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        icon,
        size: 16,
        color: color,
      ),
    );
  }

  Widget _buildStatItem(String title, int value, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
              boxShadow: [
                BoxShadow(
                  color: color.withAlpha(15),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingXS),
          Flexible(
            child: Text(
              title,
              style: AppTextStyles.labelMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          CounterAnimation(
            endValue: value.toDouble(),
            duration: const Duration(milliseconds: 1500),
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
