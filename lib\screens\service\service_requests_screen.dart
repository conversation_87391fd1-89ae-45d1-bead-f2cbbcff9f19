import 'package:flutter/material.dart';
import 'package:icecorner/config/routes.dart';
import 'package:intl/intl.dart';
import '../../core/repositories/service_request_repository.dart';
import '../../core/repositories/service_category_repository.dart';
import '../../shared/models/service_request.dart';
import '../../shared/models/service_category.dart';
import '../../shared/widgets/app_drawer.dart';
import '../../shared/widgets/custom_app_bar.dart';
import '../../shared/widgets/simple_loading_indicator.dart';
import '../../shared/widgets/simple_empty_state.dart';
import '../../shared/utils/app_colors.dart';
import 'service_request_form_screen.dart';
import 'service_request_details_screen.dart';
import 'service_categories_screen.dart';

class ServiceRequestsScreen extends StatefulWidget {
  const ServiceRequestsScreen({super.key});

  @override
  State<ServiceRequestsScreen> createState() => _ServiceRequestsScreenState();
}

class _ServiceRequestsScreenState extends State<ServiceRequestsScreen> with SingleTickerProviderStateMixin {
  final ServiceRequestRepository _repository = ServiceRequestRepository();
  final ServiceCategoryRepository _categoryRepository = ServiceCategoryRepository();

  bool _isLoading = true;
  List<ServiceRequest> _serviceRequests = [];
  List<ServiceCategory> _categories = [];

  // Filters
  ServiceRequestStatus? _statusFilter;
  int? _categoryFilter;
  String _searchQuery = '';

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        switch (_tabController.index) {
          case 0:
            _statusFilter = null;
            break;
          case 1:
            _statusFilter = ServiceRequestStatus.pending;
            break;
          case 2:
            _statusFilter = ServiceRequestStatus.inProgress;
            break;
          case 3:
            _statusFilter = ServiceRequestStatus.completed;
            break;
        }
      });
      _loadServiceRequests();
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([
        _loadServiceRequests(),
        _loadCategories(),
      ]);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadServiceRequests() async {
    try {
      final serviceRequests = await _repository.getAllServiceRequests();
      setState(() {
        _serviceRequests = serviceRequests;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل طلبات الخدمة');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _categoryRepository.getAllServiceCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحميل فئات الخدمة');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  List<ServiceRequest> get _filteredRequests {
    return _serviceRequests.where((request) {
      // Apply status filter
      if (_statusFilter != null && request.status != _statusFilter) {
        return false;
      }

      // Apply category filter
      if (_categoryFilter != null && request.categoryId != _categoryFilter) {
        return false;
      }

      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return request.reference.toLowerCase().contains(query) ||
               request.customerName.toLowerCase().contains(query) ||
               (request.description.toLowerCase().contains(query)) ||
               (request.location?.toLowerCase().contains(query) ?? false) ||
               (request.assignedToName?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  Future<void> _navigateToAddServiceRequest() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ServiceRequestFormScreen(),
      ),
    );

    if (result == true) {
      _loadServiceRequests();
    }
  }

  Future<void> _navigateToServiceRequestDetails(int id) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceRequestDetailsScreen(serviceRequestId: id),
      ),
    );

    if (result == true) {
      _loadServiceRequests();
    }
  }

  Future<void> _navigateToServiceCategories() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ServiceCategoriesScreen(),
      ),
    );

    if (result == true) {
      _loadCategories();
    }
  }

  Future<void> _confirmDeleteServiceRequest(ServiceRequest request) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف طلب الخدمة "${request.reference}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    ) ?? false;

    if (confirmed) {
      try {
        final result = await _repository.deleteServiceRequest(request.id!);
        if (result > 0) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف طلب الخدمة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
          _loadServiceRequests();
        } else {
          _showErrorSnackBar('حدث خطأ أثناء حذف طلب الخدمة');
        }
      } catch (e) {
        _showErrorSnackBar('حدث خطأ أثناء حذف طلب الخدمة');
      }
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('تصفية طلبات الخدمة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Category filter
              DropdownButtonFormField<int?>(
                decoration: const InputDecoration(
                  labelText: 'فئة الخدمة',
                  border: OutlineInputBorder(),
                ),
                value: _categoryFilter,
                items: [
                  const DropdownMenuItem<int?>(
                    value: null,
                    child: Text('جميع الفئات'),
                  ),
                  ..._categories.map((category) {
                    return DropdownMenuItem<int?>(
                      value: category.id,
                      child: Row(
                        children: [
                          Icon(category.icon, color: category.color, size: 20),
                          const SizedBox(width: 8),
                          Text(category.name),
                        ],
                      ),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    _categoryFilter = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Status filter
              DropdownButtonFormField<ServiceRequestStatus?>(
                decoration: const InputDecoration(
                  labelText: 'حالة الطلب',
                  border: OutlineInputBorder(),
                ),
                value: _statusFilter,
                items: [
                  const DropdownMenuItem<ServiceRequestStatus?>(
                    value: null,
                    child: Text('جميع الحالات'),
                  ),
                  ...ServiceRequestStatus.values.map((status) {
                    return DropdownMenuItem<ServiceRequestStatus?>(
                      value: status,
                      child: Text(ServiceRequest.getStatusName(status)),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    _statusFilter = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  _categoryFilter = null;
                  _statusFilter = null;
                });
              },
              child: const Text('إعادة ضبط'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                this.setState(() {});
              },
              child: const Text('تطبيق'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'طلبات الخدمة',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.category),
            onPressed: _navigateToServiceCategories,
            tooltip: 'إدارة فئات الخدمة',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'بحث في طلبات الخدمة...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppColors.primary,
            tabs: const [
              Tab(text: 'الكل'),
              Tab(text: 'معلق'),
              Tab(text: 'قيد التنفيذ'),
              Tab(text: 'مكتمل'),
            ],
          ),

          // List of service requests
          Expanded(
            child: _isLoading
                ? const SimpleLoadingIndicator()
                : _filteredRequests.isEmpty
                    ? SimpleEmptyState(
                        message: 'لا توجد طلبات خدمة',
                        onRefresh: _loadData,
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          itemCount: _filteredRequests.length,
                          itemBuilder: (context, index) {
                            final request = _filteredRequests[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: ServiceRequest.getStatusColor(request.status),
                                  child: Text(
                                    request.reference.substring(request.reference.length - 2),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                title: Text(request.customerName),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(request.description.length > 50
                                        ? '${request.description.substring(0, 50)}...'
                                        : request.description),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                                        const SizedBox(width: 4),
                                        Text(
                                          DateFormat('yyyy-MM-dd').format(request.scheduledDate),
                                          style: TextStyle(color: Colors.grey[600], fontSize: 12),
                                        ),
                                        const SizedBox(width: 8),
                                        if (request.categoryName != null) ...[
                                          Icon(Icons.category, size: 14, color: Colors.grey[600]),
                                          const SizedBox(width: 4),
                                          Text(
                                            request.categoryName!,
                                            style: TextStyle(color: Colors.grey[600], fontSize: 12),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ],
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.delete, color: Colors.red),
                                      onPressed: () => _confirmDeleteServiceRequest(request),
                                    ),
                                  ],
                                ),
                                onTap: () => _navigateToServiceRequestDetails(request.id!),
                              ),
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to add service request screen
          Navigator.pushNamed(context, AppRoutes.serviceRequestAdd).then((_) {
            _loadServiceRequests();
          });
        },
        child: const Icon(Icons.add),
      ),
      
 
    );
  }
}
