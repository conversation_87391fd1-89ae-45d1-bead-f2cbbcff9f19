import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'database_helper.dart';

/// مساعد إعادة تعيين قاعدة البيانات
class DatabaseResetHelper {
  static const String _tag = 'DatabaseResetHelper';

  /// إعادة تعيين قاعدة البيانات مع الاحتفاظ بالبيانات المهمة
  static Future<bool> resetDatabaseWithBackup() async {
    try {
      if (kDebugMode) {
        print('$_tag: بدء إعادة تعيين قاعدة البيانات مع النسخ الاحتياطي...');
      }

      final dbHelper = DatabaseHelper();
      
      // إنشاء نسخة احتياطية من البيانات المهمة
      final backupData = await _createBackup(dbHelper);
      
      // إعادة تعيين قاعدة البيانات
      await dbHelper.resetDatabase();
      
      // استعادة البيانات المهمة
      await _restoreBackup(dbHelper, backupData);
      
      if (kDebugMode) {
        print('$_tag: ✅ تم إعادة تعيين قاعدة البيانات بنجاح');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إعادة تعيين قاعدة البيانات: $e');
      }
      return false;
    }
  }

  /// إنشاء نسخة احتياطية من البيانات المهمة
  static Future<Map<String, List<Map<String, dynamic>>>> _createBackup(DatabaseHelper dbHelper) async {
    final backupData = <String, List<Map<String, dynamic>>>{};
    
    try {
      final db = await dbHelper.database;
      
      // نسخ احتياطية للبيانات المهمة فقط
      final importantTables = [
        'users',
        'roles',
        'user_roles',
        'customers',
        'employees',
        'company_info',
      ];
      
      for (final table in importantTables) {
        try {
          final data = await db.query(table);
          backupData[table] = data;
          
          if (kDebugMode) {
            print('$_tag: تم نسخ ${data.length} سجل من جدول $table');
          }
        } catch (e) {
          if (kDebugMode) {
            print('$_tag: ❌ خطأ في نسخ جدول $table: $e');
          }
          backupData[table] = [];
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      }
    }
    
    return backupData;
  }

  /// استعادة البيانات من النسخة الاحتياطية
  static Future<void> _restoreBackup(DatabaseHelper dbHelper, Map<String, List<Map<String, dynamic>>> backupData) async {
    try {
      final db = await dbHelper.database;
      
      // استعادة البيانات بالترتيب الصحيح (بسبب المفاتيح الخارجية)
      final restoreOrder = [
        'roles',
        'users',
        'user_roles',
        'company_info',
        'customers',
        'employees',
      ];
      
      for (final table in restoreOrder) {
        if (backupData.containsKey(table)) {
          final data = backupData[table]!;
          
          for (final record in data) {
            try {
              // تنظيف البيانات قبل الإدراج
              final cleanRecord = _cleanRecordForInsertion(record);
              await db.insert(table, cleanRecord, conflictAlgorithm: ConflictAlgorithm.replace);
            } catch (e) {
              if (kDebugMode) {
                print('$_tag: ❌ خطأ في استعادة سجل من جدول $table: $e');
              }
            }
          }
          
          if (kDebugMode) {
            print('$_tag: تم استعادة ${data.length} سجل إلى جدول $table');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في استعادة النسخة الاحتياطية: $e');
      }
    }
  }

  /// تنظيف السجل للإدراج (إزالة الحقول التي قد تسبب مشاكل)
  static Map<String, dynamic> _cleanRecordForInsertion(Map<String, dynamic> record) {
    final cleanRecord = Map<String, dynamic>.from(record);
    
    // إزالة الحقول التي قد تسبب مشاكل
    cleanRecord.remove('firestore_id');
    cleanRecord.remove('last_sync_time');
    cleanRecord.remove('sync_status');
    cleanRecord.remove('version');
    cleanRecord.remove('is_deleted');
    
    // تحديث التواريخ
    cleanRecord['created_at'] = DateTime.now().toIso8601String();
    if (cleanRecord.containsKey('updated_at')) {
      cleanRecord['updated_at'] = DateTime.now().toIso8601String();
    }
    
    return cleanRecord;
  }

  /// إعادة تعيين قاعدة البيانات بالكامل (بدون نسخ احتياطي)
  static Future<bool> resetDatabaseCompletely() async {
    try {
      if (kDebugMode) {
        print('$_tag: بدء إعادة تعيين قاعدة البيانات بالكامل...');
      }

      final dbHelper = DatabaseHelper();
      await dbHelper.resetDatabase();
      
      if (kDebugMode) {
        print('$_tag: ✅ تم إعادة تعيين قاعدة البيانات بالكامل');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إعادة تعيين قاعدة البيانات: $e');
      }
      return false;
    }
  }

  /// تطبيق التحديثات على قاعدة البيانات الحالية
  static Future<bool> applyDatabaseUpdates() async {
    try {
      if (kDebugMode) {
        print('$_tag: بدء تطبيق تحديثات قاعدة البيانات...');
      }

      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;
      
      // تطبيق التحديثات المطلوبة
      await _addMissingColumns(db);
      
      if (kDebugMode) {
        print('$_tag: ✅ تم تطبيق تحديثات قاعدة البيانات بنجاح');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في تطبيق تحديثات قاعدة البيانات: $e');
      }
      return false;
    }
  }

  /// إضافة الأعمدة المفقودة
  static Future<void> _addMissingColumns(Database db) async {
    try {
      // إضافة أعمدة جدول cash_boxes
      await _addColumnIfNotExists(db, 'cash_boxes', 'currency', 'TEXT DEFAULT "SAR"');
      await _addColumnIfNotExists(db, 'cash_boxes', 'firestore_id', 'TEXT');
      await _addColumnIfNotExists(db, 'cash_boxes', 'last_sync_time', 'TEXT');
      await _addColumnIfNotExists(db, 'cash_boxes', 'sync_status', 'TEXT DEFAULT "pending"');
      await _addColumnIfNotExists(db, 'cash_boxes', 'version', 'INTEGER DEFAULT 1');
      await _addColumnIfNotExists(db, 'cash_boxes', 'is_deleted', 'INTEGER DEFAULT 0');

      // إضافة أعمدة جدول employees
      await _addColumnIfNotExists(db, 'employees', 'payment_type', 'TEXT DEFAULT "monthly"');
      await _addColumnIfNotExists(db, 'employees', 'firestore_id', 'TEXT');
      await _addColumnIfNotExists(db, 'employees', 'last_sync_time', 'TEXT');
      await _addColumnIfNotExists(db, 'employees', 'sync_status', 'TEXT DEFAULT "pending"');
      await _addColumnIfNotExists(db, 'employees', 'version', 'INTEGER DEFAULT 1');
      await _addColumnIfNotExists(db, 'employees', 'is_deleted', 'INTEGER DEFAULT 0');
      await _addColumnIfNotExists(db, 'employees', 'updated_at', 'TEXT');

      // إضافة أعمدة جدول service_requests
      await _addColumnIfNotExists(db, 'service_requests', 'technician_daily_rate', 'REAL');
      await _addColumnIfNotExists(db, 'service_requests', 'firestore_id', 'TEXT');
      await _addColumnIfNotExists(db, 'service_requests', 'last_sync_time', 'TEXT');
      await _addColumnIfNotExists(db, 'service_requests', 'sync_status', 'TEXT DEFAULT "pending"');
      await _addColumnIfNotExists(db, 'service_requests', 'version', 'INTEGER DEFAULT 1');
      await _addColumnIfNotExists(db, 'service_requests', 'is_deleted', 'INTEGER DEFAULT 0');
      await _addColumnIfNotExists(db, 'service_requests', 'updated_at', 'TEXT');

      if (kDebugMode) {
        print('$_tag: ✅ تم إضافة جميع الأعمدة المفقودة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة الأعمدة المفقودة: $e');
      }
    }
  }

  /// إضافة عمود إذا لم يكن موجوداً
  static Future<void> _addColumnIfNotExists(Database db, String tableName, String columnName, String columnDefinition) async {
    try {
      // التحقق من وجود العمود
      final columns = await _getTableColumns(db, tableName);
      
      if (!columns.contains(columnName)) {
        await db.execute('ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition');
        if (kDebugMode) {
          print('$_tag: تم إضافة عمود $columnName إلى جدول $tableName');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في إضافة عمود $columnName إلى جدول $tableName: $e');
      }
    }
  }

  /// الحصول على أعمدة الجدول
  static Future<List<String>> _getTableColumns(Database db, String tableName) async {
    try {
      final result = await db.rawQuery('PRAGMA table_info($tableName)');
      return result.map((row) => row['name'] as String).toList();
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في الحصول على أعمدة الجدول $tableName: $e');
      }
      return [];
    }
  }

  /// التحقق من صحة قاعدة البيانات
  static Future<Map<String, dynamic>> validateDatabase() async {
    final result = <String, dynamic>{
      'isValid': true,
      'errors': <String>[],
      'warnings': <String>[],
      'tableInfo': <String, dynamic>{},
    };

    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;

      // التحقق من الجداول المطلوبة
      final requiredTables = [
        'users',
        'roles',
        'user_roles',
        'customers',
        'employees',
        'service_requests',
        'cash_boxes',
        'cash_box_transactions',
        'invoices',
        'inventory',
        'suppliers',
        'categories',
        'company_info',
      ];

      for (final table in requiredTables) {
        try {
          final count = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
          final recordCount = count.first['count'] as int;
          result['tableInfo'][table] = {
            'exists': true,
            'recordCount': recordCount,
          };
        } catch (e) {
          result['isValid'] = false;
          result['errors'].add('جدول $table غير موجود أو تالف: $e');
          result['tableInfo'][table] = {
            'exists': false,
            'error': e.toString(),
          };
        }
      }

      if (kDebugMode) {
        print('$_tag: تم التحقق من صحة قاعدة البيانات');
        print('$_tag: صحيحة: ${result['isValid']}');
        print('$_tag: أخطاء: ${result['errors'].length}');
        print('$_tag: تحذيرات: ${result['warnings'].length}');
      }
    } catch (e) {
      result['isValid'] = false;
      result['errors'].add('خطأ عام في التحقق من قاعدة البيانات: $e');
      
      if (kDebugMode) {
        print('$_tag: ❌ خطأ في التحقق من صحة قاعدة البيانات: $e');
      }
    }

    return result;
  }
}
